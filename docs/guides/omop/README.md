# OMOP CDM Documentation

This section contains documentation for the OMOP Common Data Model (CDM) implementation in the FHIR-to-OMOP project. The current implementation focuses on database setup, vocabulary management, and basic FHIR-to-OMOP transformation capabilities.

## 🎯 **Current Implementation Status**

This project currently implements:
- ✅ **PostgreSQL OMOP Database Setup** - Complete CDM v5.4.2 database with 39 tables
- ✅ **Official OHDSI Vocabulary Loading** - Using the official OHDSI forum recommendation
- ✅ **Basic FHIR-to-OMOP Mappers** - Patient, Encounter, Condition, Observation mappers
- ✅ **Abu Dhabi Claims ETL Pipeline** - Complete ETL for claims data to 5 OMOP tables

📋 **Detailed Implementation Overview**: [Current Implementation](current_implementation.md)

## 📚 **Documentation Structure**

### 1. Database Setup
- [Database Overview](database/overview.md) - Overview of database options for OMOP CDM
- [PostgreSQL Setup](database/postgresql_setup.md) - Complete PostgreSQL setup guide (Steps 1-9)
- [SQLite Setup](database/sqlite_setup.md) - SQLite setup for development/testing

### 2. Vocabulary Management
- [Vocabulary Guide](vocabulary/README.md) - Complete vocabulary management workflow
- [Vocabulary Overview](vocabulary/overview.md) - Understanding OMOP vocabularies
- [Athena Setup](vocabulary/athena_setup.md) - Downloading vocabularies from OHDSI Athena
- [Vocabulary Loading](vocabulary/loading.md) - **Official OHDSI method implementation**

### 3. OMOP CDM Reference
For detailed information about OMOP CDM tables, relationships, and specifications, refer to official OHDSI sources:
- **[OMOP CDM v5.4.2 Documentation](https://ohdsi.github.io/CommonDataModel/cdm54.html)** - Complete table specifications
- **[OMOP CDM GitHub Repository](https://github.com/OHDSI/CommonDataModel)** - Official DDL scripts and documentation
- **[The Book of OHDSI: Chapter 4](https://ohdsi.github.io/TheBookOfOhdsi/CommonDataModel.html)** - Comprehensive CDM guide

### 4. Technical Analysis
- **[R vs Python Technical Analysis](../../analysis/r-vs-python-technical-analysis.md)** - Exhaustive evaluation of OHDSI official methodologies

## 🚀 **Quick Start Guide**

### Prerequisites
- PostgreSQL installed and running
- Python environment with required packages
- UMLS API key for CPT4 vocabulary

### Step 1: Database Setup
```bash
# Follow the complete PostgreSQL setup guide
# This creates 39 OMOP CDM tables and configures the database
```
📋 **Guide**: [PostgreSQL Setup](database/postgresql_setup.md)

### Step 2: Vocabulary Loading
```bash
# Load OMOP vocabularies using the official OHDSI method
python scripts/load_vocabularies.py
```
📋 **Guide**: [Vocabulary Loading](vocabulary/loading.md)

### Step 3: Test FHIR-to-OMOP Transformation
```bash
# Run the Abu Dhabi Claims ETL pipeline
cd src/fhir_omop/etl/abu_dhabi_claims_mvp/
python run_pipeline.py
```

## 📁 **Current Directory Structure**

```
docs/guides/omop/
├── README.md                   # This overview
├── introduction.md             # Introduction to OMOP CDM
├── current_implementation.md   # Detailed current implementation overview
├── database/                   # Database setup guides
│   ├── overview.md             # Database options overview
│   ├── postgresql_setup.md     # Complete PostgreSQL setup (Steps 1-9)
│   └── sqlite_setup.md         # SQLite setup for development
└── vocabulary/                 # Vocabulary management
    ├── README.md               # Complete vocabulary workflow
    ├── overview.md             # Understanding OMOP vocabularies
    ├── athena_setup.md         # Downloading from OHDSI Athena
    └── loading.md              # Official OHDSI loading method
```

## 💻 **Actual Implementation**

The project includes these functional components:

### Scripts
- `scripts/load_vocabularies.py` - **Official OHDSI vocabulary loading method**

### FHIR-to-OMOP Mappers
- `src/fhir_omop/mappers/patient_mapper.py` - Patient → Person
- `src/fhir_omop/mappers/encounter_mapper.py` - Encounter → Visit_Occurrence
- `src/fhir_omop/mappers/condition_mapper.py` - Condition → Condition_Occurrence
- `src/fhir_omop/mappers/observation_mapper.py` - Observation → Measurement/Observation

### ETL Pipeline
- `src/fhir_omop/etl/abu_dhabi_claims_mvp/` - Complete claims-to-OMOP ETL pipeline

## 📖 **Official Resources**

This implementation is based on official OHDSI and HL7 resources:

### OMOP CDM Documentation
- **[OMOP CDM v5.4.2 Specifications](https://ohdsi.github.io/CommonDataModel/cdm54.html)** - Complete table definitions, relationships, and constraints
- **[OHDSI Common Data Model Repository](https://github.com/OHDSI/CommonDataModel)** - Official DDL scripts and documentation
- **[The Book of OHDSI: Chapter 4](https://ohdsi.github.io/TheBookOfOhdsi/CommonDataModel.html)** - Comprehensive CDM guide and best practices

### OHDSI Community Resources
- **[OHDSI Forums](https://forums.ohdsi.org/)** - Community support and official guidance
- **[OHDSI Collaborative](https://www.ohdsi.org/)** - Official OHDSI organization and resources

### Vocabulary Loading Method
- **Official OHDSI Forum Recommendation**: [Foreign key constraints issue](https://forums.ohdsi.org/t/foreign-key-constraints-issue/20462)
- **Expert**: Eduard Korchmar (OHDSI Community Expert, November 2023)
- **Method**: Drop constraints → Load data → Re-create constraints

### FHIR Integration
- **[HL7 Vulcan FHIR-to-OMOP Implementation Guide](https://build.fhir.org/ig/HL7/fhir-omop-ig/)** - Official HL7 mapping guidance
- **[OHDSI FHIR-to-OMOP Working Group](https://forums.ohdsi.org/c/working-groups/fhir-to-omop/49)** - Community discussions and best practices

## 🎯 **Next Steps**

After completing the OMOP setup:

1. **Explore the ETL Pipeline**: Review the Abu Dhabi Claims MVP implementation
2. **Test with Your Data**: Adapt the mappers for your specific FHIR resources
3. **Extend Functionality**: Add additional mappers as needed for your use case

---

> **Note**: This documentation reflects the current implementation state. The project follows an incremental, pedagogical approach focused on learning OMOP fundamentals through practical implementation.
