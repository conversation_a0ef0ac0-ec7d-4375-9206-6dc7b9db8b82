# Coding and Documentation Standards

This document defines the core coding standards, documentation formats, and development practices for the FHIR to OMOP CDM Transformation Pipeline project. Following these guidelines ensures consistency across all project components.

## Technology Stack Specifications

This project uses specific versions for compatibility and stability:

- **OMOP CDM**: Version 5.4.2 for all database schemas and references
- **HAPI FHIR**: R4 with `hapiproject/hapi:latest` Docker image
- **PostgreSQL**: Version 14 (avoid v15+ due to HAPI FHIR compatibility issues)
- **Python Environment**: Conda environment 'fhir-omop'
- **Development Tools**: Context7 MCP for accessing official documentation

## Table of Contents

- [Coding and Documentation Standards](#coding-and-documentation-standards)
  - [Technology Stack Specifications](#technology-stack-specifications)
  - [Table of Contents](#table-of-contents)
  - [Documentation Standards](#documentation-standards)
    - [Markdown Style](#markdown-style)
    - [References and Citations](#references-and-citations)
  - [Code Standards](#code-standards)
    - [Python Style](#python-style)
    - [SQL Style](#sql-style)
    - [YAML Style](#yaml-style)
  - [Security and Quality Standards](#security-and-quality-standards)
    - [Database Security](#database-security)
    - [Code Quality](#code-quality)
    - [Common Pitfalls to Avoid](#common-pitfalls-to-avoid)
  - [File Organization](#file-organization)
  - [Naming Conventions](#naming-conventions)
  - [Version Control Practices](#version-control-practices)

## Documentation Standards

### Markdown Style

All documentation should be written in Markdown format with the following guidelines:

1. **Language**: All documentation should be written in English.
2. **Headers**: Use ATX-style headers (with `#` symbols). Add one space after the `#` character.
   ```markdown
   # Level 1 Header
   ## Level 2 Header
   ### Level 3 Header
   ```

3. **Lists**: Use `-` for unordered lists and numbers for ordered lists. Indent nested lists with 2 spaces.
   ```markdown
   - Item 1
     - Nested item
   - Item 2

   1. First step
   2. Second step
   ```

4. **Code Blocks**: Use triple backticks with language specification for code blocks.
   ```markdown
   ```python
   def example_function():
       return "This is an example"
   ```
   ```

5. **Line Length**: Keep lines under 100 characters when possible for better readability.

### Documentation Structure

Each documentation file should follow this structure:

1. **Title**: Start with a level 1 header (`#`) containing the document title.
2. **Brief Description**: A short paragraph explaining the purpose of the document.
3. **Table of Contents**: For documents longer than 3 sections.
4. **Main Content**: Organized in logical sections with appropriate headers.
5. **References**: If applicable, include references at the end of the document.

### Command Documentation Format

For documenting command-line instructions in documentation:

1. **Multi-line Format**: Use backslash continuation (`\`) for commands with multiple parameters to improve readability.

   **❌ Avoid (hard to read):**
   ```bash
   python scripts/data_loading/ndjson_to_bundle.py --input-file ../data/sample_fhir/bulk-export/Patient.000.ndjson --output-file ../data/generated_bundles/Patient_bundle.json
   ```

   **✅ Preferred (professional and readable):**
   ```bash
   python scripts/data_loading/ndjson_to_bundle.py \
       --input-file ../data/sample_fhir/bulk-export/Patient.000.ndjson \
       --output-file ../data/generated_bundles/Patient_bundle.json
   ```

2. **Indentation**: Use 4 spaces for parameter alignment after the backslash.

3. **Parameter Grouping**: Group related parameters together when possible:
   ```bash
   python scripts/data_loading/load_all_bundles.py \
       --bundle-dir ../data/generated_bundles/bulk_export_bundles \
       --server-url http://localhost:8080/fhir \
       --export-performance \
       --test-id "initial_load_test"
   ```

4. **Directory Context**: Always specify the working directory when commands must be run from specific locations:
   ```bash
   # From project root directory
   cd servers/fhir-server
   python scripts/data_loading/selective_loader.py \
       --data-dir ../../data/sample_fhir/bulk-export \
       --server-url http://localhost:8080/fhir \
       --verify
   ```

5. **Environment Context**: Include conda environment activation when required:
   ```bash
   # Activate environment and run tests
   conda activate fhir-omop
   python -m pytest tests/test_omop_database/ -v
   ```

### References and Citations

For references and citations:

1. **Inline Citations**: Use hyperlinked keywords directly in the text rather than numbered references.
   ```markdown
   The [OHDSI documentation](https://ohdsi.github.io/TheBookOfOhdsi/) provides guidance on...
   ```

2. **Direct Links**: Always use direct links to specific resources rather than general website links.

3. **Version Information**: When referencing software or standards, include version information.
   ```markdown
   FHIR R4 (v4.0.1) defines...
   ```

## Code Standards

### Python Style

1. **Style Guide**: Follow [PEP 8](https://www.python.org/dev/peps/pep-0008/) conventions.
2. **Docstrings**: Use [NumPy-style docstrings](https://numpydoc.readthedocs.io/en/latest/format.html).
   ```python
   def function_with_types_in_docstring(param1, param2):
       """Example function with types documented in the docstring.

       Parameters
       ----------
       param1 : int
           The first parameter.
       param2 : str
           The second parameter.

       Returns
       -------
       bool
           The return value. True for success, False otherwise.
       """
       return True
   ```

3. **Type Hints**: Use type hints for function parameters and return values.
   ```python
   def greeting(name: str) -> str:
       return f"Hello {name}"
   ```

4. **Imports**: Organize imports in the following order, with a blank line between each group:
   - Standard library imports
   - Related third-party imports
   - Local application/library specific imports

5. **Line Length**: Maximum line length of 88 characters (compatible with Black formatter).

6. **Naming Conventions**:
   - Classes: `CamelCase`
   - Functions and variables: `snake_case`
   - Constants: `UPPER_CASE_WITH_UNDERSCORES`
   - Private methods/variables: Prefix with underscore (`_private_method`)

7. **Script Structure**: All scripts should follow a consistent structure:
   - Command-line argument parsing with `argparse`
   - Environment variable support with `dotenv`
   - Configuration precedence: command-line args > environment variables > defaults

   ```python
   import os
   import argparse
   from dotenv import load_dotenv

   # Load environment variables
   load_dotenv()

   def parse_args():
       """Parse command line arguments."""
       parser = argparse.ArgumentParser(description="Script description")
       parser.add_argument(
           "--server-url",
           default=os.getenv("FHIR_SERVER_URL", "http://localhost:8080/fhir"),
           help="URL of the FHIR server"
       )
       # Add other arguments as needed
       return parser.parse_args()

   # Parse command line arguments
   args = parse_args()

   # Use arguments in the script
   SERVER_URL = args.server_url
   ```

8. **Standard Arguments**: Use consistent argument names across scripts:

   | Argument | Environment Variable | Default | Description |
   |----------|---------------------|---------|-------------|
   | `--server-url` | `FHIR_SERVER_URL` | `http://localhost:8080/fhir` | FHIR server URL |
   | `--input-file` | `INPUT_FILE` | - | Input file path |
   | `--output-file` | `OUTPUT_FILE` | - | Output file path |
   | `--data-dir` | `DATA_DIR` | - | Data directory path |

### SQL Style

1. **Keywords**: Use UPPERCASE for SQL keywords.
   ```sql
   SELECT column_name FROM table_name WHERE condition;
   ```

2. **Identifiers**: Use snake_case for table and column names.

3. **Indentation**: Indent subqueries and conditions with 4 spaces.
   ```sql
   SELECT
       t.column1,
       t.column2
   FROM table_name t
   WHERE
       t.column1 = 'value'
       AND t.column2 > 100;
   ```

4. **Aliases**: Use meaningful aliases for tables and columns.

### YAML Style

1. **Indentation**: Use 2 spaces for indentation.
   ```yaml
   parent:
     child: value
     another_child: value
   ```

2. **Lists**: Use hyphen with a space for list items.
   ```yaml
   items:
     - item1
     - item2
   ```

3. **Quotes**: Use quotes for strings with special characters or that could be interpreted as other types.
   ```yaml
   string_value: "This is a string with: special characters"
   ```

## Security and Quality Standards

### Database Security

1. **SQL Injection Prevention**: Always use parameterized queries when dealing with user input or external data.
   ```python
   # ❌ Vulnerable to SQL injection
   cur.execute(f"SELECT * FROM users WHERE name = '{user_name}'")
   cur.execute(f"CREATE USER {username} WITH PASSWORD '{password}'")
   
   # ✅ Safe with parameterized queries
   cur.execute("SELECT * FROM users WHERE name = %s", (user_name,))
   cur.execute("CREATE USER %s WITH PASSWORD %s", (username, password))
   ```

2. **Credential Security**:
   - Never log passwords or sensitive credentials
   - Store credentials in environment variables, not in code
   - Use try/finally blocks or context managers for resource management

### Code Quality

1. **Comments and Documentation**:
   - Place comments on separate lines for clarity
   - Avoid mixing code and comments on the same line for complex statements
   - Document complex logic with clear comments

   ```python
   # ❌ Poor style
   result = some_function()  # This comment makes the line too long and unclear
   if condition:
   
   # ✅ Good style
   # Process the result from the function
   result = some_function()
   
   # Check if the condition is met
   if condition:
   ```

2. **Error Handling**:
   - Use specific exception types rather than generic `Exception`
   - Provide meaningful error messages with context
   - Clean up resources in error conditions

   ```python
   # ❌ Generic error handling
   try:
       # database operation
   except Exception as e:
       print(f"Error: {e}")
   
   # ✅ Specific error handling
   try:
       # database operation
   except psycopg2.Error as e:
       logger.error(f"Database error during user creation: {e}")
   except ValueError as e:
       logger.error(f"Invalid configuration value: {e}")
   ```

3. **Configuration Validation**:
   - Validate all external inputs before processing
   - Check for required parameters and proper data types
   - Provide clear error messages for missing or invalid configuration

### Common Pitfalls to Avoid

- Never use f-strings with user-provided data in SQL queries
- Don't place comments at the end of complex Python statements
- Always validate configuration before database operations
- Use specific exception types for better error handling
- When using f-strings for DDL operations, document why parameterization isn't possible and ensure values come from controlled configuration

## File Organization

1. **Directory Structure**: Follow the project's established directory structure.

2. **File Naming**: Use descriptive, lowercase names with underscores for spaces.
   - Python files: `module_name.py`
   - Markdown files: `document_name.md`
   - Configuration files: `config_name.yml` or `config_name.yaml`

3. **File Headers**: Include a header comment in each file with:
   - Brief description
   - Author information
   - Creation date
   - License information

## Naming Conventions

1. **Files and Directories**: Use lowercase with underscores (`file_name.py`, `directory_name/`).

2. **Branches**: Use the format `type/description` (e.g., `feature/add-patient-mapper`, `bugfix/fix-date-conversion`).

3. **Documentation Files**: Use descriptive names that indicate the content (`patient_mapping_guide.md`).

## Version Control Practices

1. **Commit Messages**: Write clear, concise commit messages in the imperative mood.
   - Start with a capital letter
   - Use the imperative mood ("Add feature" not "Added feature")
   - Keep the first line under 72 characters
   - Provide more details in the commit body if necessary

   Example:
   ```
   Add Patient to Person mapper with demographic handling

   - Implements basic demographic mapping
   - Handles gender standardization
   - Preserves original FHIR IDs in source_value fields
   ```

2. **Branching Strategy**:
   - `main`: Production-ready code
   - `develop`: Integration branch for features
   - Feature branches: Created from `develop` for new features
   - Hotfix branches: Created from `main` for urgent fixes

3. **Pull Requests**: Include a description of changes, reference to related issues, and testing information.

---

This document will be updated as new standards are established or existing ones are refined. All team members should review this document regularly to ensure compliance with the project's standards.

**Recent Updates:**
- Added security and quality standards focused on database operations and SQL injection prevention
- Included PostgreSQL-specific considerations for DDL operations
- Added common pitfalls section based on lessons learned from code review

For testing-specific standards and guidelines, see [Unit Testing Standards](unit_testing_standards.md).
