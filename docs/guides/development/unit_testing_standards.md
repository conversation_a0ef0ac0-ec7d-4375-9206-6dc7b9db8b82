# Unit Testing Standards

This document defines the comprehensive testing strategy, architecture, and implementation guidelines for the FHIR-OMOP project. These standards ensure consistent, maintainable, and scalable testing practices across all project modules.

## Table of Contents

- [Testing Philosophy](#testing-philosophy)
- [Testing Architecture](#testing-architecture)
- [Framework Configuration](#framework-configuration)
- [Fixture Strategy](#fixture-strategy)
- [Mocking Strategy](#mocking-strategy)
- [Test Data Management](#test-data-management)
- [Error Testing Strategy](#error-testing-strategy)
- [Performance Testing](#performance-testing)
- [Documentation Standards](#documentation-standards)
- [Continuous Integration](#continuous-integration)
- [Maintenance Guidelines](#maintenance-guidelines)

## Testing Philosophy

The FHIR-OMOP project follows a **pragmatic testing approach** that balances thoroughness with maintainability, especially during Phase 1 development where requirements may evolve.

### Core Principles

1. **Foundation First**: Establish solid testing patterns that can scale with project growth
2. **Flexibility Over Rigidity**: Tests should adapt to evolving codebase without becoming brittle
3. **Critical Path Focus**: Prioritize testing of core functionality and error-prone areas
4. **Documentation Through Tests**: Tests serve as living documentation for system behavior

### Testing Pyramid Strategy

```
    /\
   /  \     E2E Tests (Few, High Value)
  /____\    
 /      \   Integration Tests (Some, Key Workflows)
/________\  Unit Tests (Many, Fast, Isolated)
```

- **Unit Tests (80%)**: Fast, isolated, comprehensive coverage of business logic
- **Integration Tests (15%)**: Component interactions, database operations
- **End-to-End Tests (5%)**: Complete user scenarios, critical workflows

## Testing Architecture

### Directory Structure

```
tests/
├── conftest.py                    # Global fixtures and configuration
├── test_<module_name>/           # Module-specific test packages
│   ├── __init__.py
│   ├── conftest.py               # Module-specific fixtures
│   ├── test_<component>.py       # Unit tests for components
│   ├── test_integration.py       # Integration tests
│   └── fixtures/                 # Test data and mock files
│       ├── sample_data.json
│       └── mock_responses.py
└── integration/                  # Cross-module integration tests
    ├── test_end_to_end.py
    └── test_database_integration.py
```

### Test Categories and Markers

#### Unit Tests
```python
@pytest.mark.unit
def test_pure_function():
    """Fast unit test with no external dependencies."""
    pass
```

**Characteristics:**
- Execution time: < 5 seconds per test
- No external dependencies (database, network, file system)
- High coverage of business logic
- Isolated and deterministic

#### Integration Tests
```python
@pytest.mark.integration
def test_database_interaction():
    """Integration test using test database."""
    pass
```

**Characteristics:**
- Execution time: < 30 seconds per test
- May use test databases or mock services
- Focus on critical workflows
- Test component interactions

#### Specialized Markers
```python
@pytest.mark.slow
def test_large_dataset_processing():
    """Test that processes large amounts of data."""
    pass

@pytest.mark.external
def test_api_integration():
    """Test requiring external services."""
    pass

@pytest.mark.omop
def test_omop_specific_functionality():
    """Test specific to OMOP database operations."""
    pass
```

## Framework Configuration

### pytest Configuration (pytest.ini)

```ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=src/fhir_omop
    --cov-report=term-missing
    --cov-report=html:htmlcov
markers =
    unit: Unit tests (fast, no external dependencies)
    integration: Integration tests (may use test database)
    slow: Tests that take longer than 30 seconds
    external: Tests requiring external services
    omop: Tests specific to OMOP database functionality
    fhir: Tests specific to FHIR processing
```

**Dependencies Required:**
- `pytest`: Core testing framework
- `pytest-cov`: Coverage reporting functionality

### Coverage Configuration

Target coverage levels:
- **Critical modules**: 90%+ coverage
- **Business logic**: 85%+ coverage
- **Utility functions**: 80%+ coverage
- **Integration points**: 70%+ coverage

## Fixture Strategy

### Fixture Hierarchy

#### 1. Global Fixtures (tests/conftest.py)

```python
@pytest.fixture
def mock_env_config():
    """Standard environment configuration for testing."""
    return {
        'OMOP_DB_HOST': 'localhost',
        'OMOP_DB_PORT': '5432',
        'OMOP_DB_NAME': 'test_omop_cdm'
    }

@pytest.fixture
def temp_data_dir(tmp_path):
    """Temporary directory with test data structure."""
    data_dir = tmp_path / "test_data"
    data_dir.mkdir()
    return data_dir
```

#### 2. Module Fixtures (tests/test_module/conftest.py)

```python
@pytest.fixture
def mock_database_connection():
    """Mock database connection for module tests."""
    with patch('psycopg2.connect') as mock_conn:
        yield mock_conn

@pytest.fixture
def sample_fhir_patient():
    """Sample FHIR Patient resource for testing."""
    return {
        "resourceType": "Patient",
        "id": "test-patient-123",
        "name": [{"family": "Doe", "given": ["John"]}]
    }
```

#### 3. Fixture Scope Guidelines

- **session**: Database setup, expensive resources
- **module**: Module-specific configuration
- **function**: Default scope, test-specific data
- **class**: Test class shared state

### Standard Fixture Patterns

#### Database Fixtures
```python
@pytest.fixture
def mock_db_connection():
    """Mock database connection with standard methods."""
    mock_conn = Mock()
    mock_cursor = Mock()
    mock_conn.cursor.return_value = mock_cursor
    return mock_conn, mock_cursor

@pytest.fixture
def test_database():
    """Real test database for integration tests."""
    # Setup test database
    yield db_connection
    # Cleanup test database
```

#### Data Fixtures
```python
@pytest.fixture
def sample_omop_person():
    """Standard OMOP Person record for testing."""
    return {
        "person_id": 1,
        "gender_concept_id": 8507,
        "year_of_birth": 1990
    }
```

## Mocking Strategy

### External Dependencies

#### Database Connections
```python
@patch('psycopg2.connect')
def test_database_operation(mock_connect):
    """Test database operation with mocked connection."""
    mock_conn = Mock()
    mock_connect.return_value = mock_conn
    # Test implementation
```

#### File System Operations
```python
@patch('pathlib.Path.exists')
def test_file_check(mock_exists):
    """Test file existence check."""
    mock_exists.return_value = True
    # Test implementation
```

#### Network Requests
```python
@patch('requests.get')
def test_api_call(mock_get):
    """Test external API call."""
    mock_get.return_value.json.return_value = {"status": "success"}
    # Test implementation
```

#### Subprocess Calls
```python
@patch('subprocess.run')
def test_shell_command(mock_run):
    """Test system command execution."""
    mock_run.return_value.returncode = 0
    # Test implementation
```

### Internal Dependencies

- **Prefer dependency injection** over deep mocking
- **Mock at module boundaries** rather than internal implementation
- **Use real objects** when they don't introduce external dependencies

### Mock Validation Patterns

```python
def test_function_calls_dependency_correctly():
    """Test that function calls dependency with correct parameters."""
    with patch('module.dependency') as mock_dep:
        mock_dep.return_value = "expected_result"
        
        result = function_under_test("input")
        
        mock_dep.assert_called_once_with("expected_input")
        assert result == "expected_output"
```

## Test Data Management

### Test Data Principles

1. **Minimal Viable Data**: Use smallest dataset that validates behavior
2. **Deterministic Data**: Avoid random data that can cause flaky tests
3. **Isolated Data**: Each test should have independent data
4. **Realistic Data**: Test data should represent real-world scenarios

### Test Data Organization

```python
# tests/test_module/fixtures/test_data.py
SAMPLE_PATIENT_DATA = {
    "minimal": {
        "id": "patient-1",
        "name": "John Doe"
    },
    "complete": {
        "id": "patient-2",
        "name": "Jane Smith",
        "birthDate": "1990-01-01",
        "gender": "female"
    }
}

SAMPLE_OMOP_PERSON = {
    "person_id": 1,
    "gender_concept_id": 8507,  # Male (standardized)
    "year_of_birth": 1990
}
```

### Data Factory Pattern

```python
class PatientFactory:
    """Factory for creating test patient data."""
    
    @staticmethod
    def create_minimal_patient(patient_id="test-123"):
        """Create minimal valid patient."""
        return {
            "resourceType": "Patient",
            "id": patient_id,
            "name": [{"family": "Doe", "given": ["John"]}]
        }
    
    @staticmethod
    def create_complete_patient(**overrides):
        """Create complete patient with optional overrides."""
        patient = {
            "resourceType": "Patient",
            "id": "test-123",
            "name": [{"family": "Doe", "given": ["John"]}],
            "gender": "male",
            "birthDate": "1990-01-01"
        }
        patient.update(overrides)
        return patient
```

## Error Testing Strategy

### Error Scenarios to Test

1. **Input Validation**: Invalid parameters, missing required fields
2. **External Failures**: Database connection errors, file not found
3. **Business Logic Errors**: Invalid transformations, constraint violations
4. **Resource Limitations**: Memory constraints, timeout scenarios

### Error Testing Patterns

```python
def test_invalid_input_raises_error():
    """Test that invalid input raises appropriate exception."""
    with pytest.raises(ValueError, match="Invalid patient ID"):
        process_patient(patient_id="")

def test_database_connection_failure():
    """Test graceful handling of database connection failure."""
    with patch('psycopg2.connect', side_effect=ConnectionError):
        result = database_operation()
        assert result.success is False
        assert "connection" in result.error_message.lower()

def test_partial_failure_handling():
    """Test handling of partial operation failures."""
    with patch('module.operation', side_effect=[True, False, True]):
        result = batch_operation([1, 2, 3])
        assert result.success_count == 2
        assert result.failure_count == 1
```

## Performance Testing

### Performance Benchmarks

- **Unit Tests**: < 5 seconds per test
- **Integration Tests**: < 30 seconds per test
- **Memory Usage**: Monitor for memory leaks in long-running tests

### Performance Testing Implementation

```python
import time
import pytest

def test_transformation_performance():
    """Test that transformation completes within acceptable time."""
    start_time = time.time()
    
    result = transform_large_dataset(sample_data)
    
    execution_time = time.time() - start_time
    assert execution_time < 10.0  # Should complete within 10 seconds
    assert result.success is True

@pytest.mark.slow
def test_large_dataset_processing():
    """Test processing of large datasets."""
    large_dataset = generate_test_data(size=10000)
    
    result = process_dataset(large_dataset)
    
    assert result.processed_count == 10000
    assert result.error_count == 0
```

## Documentation Standards

### Test Naming Conventions

```python
def test_<action>_<condition>_<expected_result>():
    """
    Test description following the pattern:
    Given <condition>, when <action>, then <expected_result>
    """
    pass

# Examples:
def test_create_patient_with_valid_data_returns_success():
    """Given valid patient data, when creating patient, then returns success."""
    pass

def test_load_vocabulary_with_missing_file_raises_error():
    """Given missing vocabulary file, when loading vocabulary, then raises FileNotFoundError."""
    pass
```

### Test Class Documentation

```python
class TestPatientMapper:
    """
    Test suite for Patient to Person mapping functionality.
    
    This test suite covers:
    - Basic demographic mapping
    - Gender standardization
    - Error handling for invalid data
    - Performance with large datasets
    
    Test Data:
    - Uses sample FHIR Patient resources
    - Validates against OMOP Person schema
    - Tests both minimal and complete data scenarios
    """
    
    def test_map_basic_demographics(self):
        """
        Test basic demographic mapping from FHIR Patient to OMOP Person.
        
        Validates:
        - Name extraction and formatting
        - Birth date conversion
        - Gender concept mapping
        
        Expected Behavior:
        - FHIR Patient.gender "male" maps to OMOP concept_id 8507
        - Birth date preserves year, month, day accuracy
        - Source values are preserved for traceability
        """
        # Test implementation
        pass
```

## Continuous Integration

### GitHub Actions Integration

```yaml
# .github/workflows/tests.yml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Set up Conda
      uses: conda-incubator/setup-miniconda@v2
      with:
        environment-file: environment.yml
        activate-environment: fhir-omop
    
    - name: Run Unit Tests
      run: pytest tests/ -m "unit" --cov=src/fhir_omop
    
    - name: Run Integration Tests
      run: pytest tests/ -m "integration"
    
    - name: Upload Coverage
      uses: codecov/codecov-action@v3
```

### Test Execution Strategy

```bash
# Run all tests
pytest

# Run only unit tests (fast)
pytest -m "unit"

# Run integration tests
pytest -m "integration"

# Run tests with coverage
pytest --cov=src/fhir_omop --cov-report=html

# Run specific module tests
pytest tests/test_omop_database/

# Run tests matching pattern
pytest -k "test_patient"

# Update environment with new dependencies
conda env update --file environment.yml
```

## Maintenance Guidelines

### Regular Maintenance Tasks

1. **Review Test Coverage**: Aim for 80%+ coverage on critical paths
2. **Update Test Data**: Keep test data current with schema changes
3. **Refactor Brittle Tests**: Identify and fix tests that break frequently
4. **Performance Monitoring**: Track test execution times

### Test Quality Metrics

- **Test Execution Time**: Monitor and optimize slow tests
- **Test Flakiness**: Track and eliminate flaky tests
- **Coverage Trends**: Monitor coverage changes over time
- **Test Maintenance Burden**: Assess effort required to maintain tests

### Test Lifecycle Management

- **Green Tests**: All tests should pass before merging
- **Flaky Tests**: Investigate and fix immediately
- **Obsolete Tests**: Remove tests for deprecated functionality
- **Missing Tests**: Add tests for new functionality

### Code Review Checklist for Tests

- [ ] Tests follow naming conventions
- [ ] Appropriate test markers are used
- [ ] Mocks are used correctly for external dependencies
- [ ] Test data is minimal and realistic
- [ ] Error scenarios are covered
- [ ] Performance considerations are addressed
- [ ] Documentation is clear and complete

---

These testing standards provide a comprehensive foundation for building reliable, maintainable tests throughout the FHIR-OMOP project. They should be followed by all team members and evolved as the project grows and requirements change.

## Environment Setup Notes

**Important:** Ensure `pytest-cov` is included in `environment.yml` for coverage functionality:

```yaml
# Testing dependencies
- pytest
- pytest-cov
```

Use `conda env update --file environment.yml` to install missing dependencies rather than manual `pip install` commands to maintain environment consistency.