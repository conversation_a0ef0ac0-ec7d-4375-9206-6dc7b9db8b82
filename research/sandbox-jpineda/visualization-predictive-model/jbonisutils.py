def add_age_and_fix_id_to_202501mojo(df):
    """
    Add age and fix the fact patientid contains the arm and the patient_id
    Returns patient_number and patient_arm
    """
    df['age'] = 2025 - df['Year_born'].astype(int)
    df["patient_number"] = df["patientid"].str.extract(r"(\d+)")
    df["patient_arm"] = df["patientid"].str.extract(r"(\w+)")
    df["patient_arm"] = df["patient_arm"].str.upper()
    return df



def filter_users_by_num_segments(df, subject_col="patientid", min_segments=20, verbose=0):
    by_user_id = df.groupby(subject_col).size()

    # list of users with at least 20 rows
    selected_ids = list(by_user_id[by_user_id > min_segments].index)
    if verbose > 0:
        print(f"Number of rows before filter those subjects with less than {min_segments} rows: {len(df)}")
        print(
            f"Number of subjects before filter those subjects with less than {min_segments} rows: {len(df[subject_col].unique())}")

    # Filtering data with subjects with more than min_n_subjects
    df = df[df[subject_col].isin(selected_ids)]
    if verbose > 0:
        print(f"Number of rows after filter those subjects with less than {min_segments} rows: {len(df)}")
        print(
        f"Number of subjects after filter those subjects with less than {min_segments} rows: {len(df[subject_col].unique())}")

    return df


from catboost import CatBoostRegressor
import numpy as np

def train_catboost_regressor_model(X_train, y_train, X_test, y_test):

    # Train a catboost Regressor model

    # Hyperparameter tuning: param grid
    param_grid = {
        'l2_leaf_reg': [1],  # [1, 3, 5, 7],
        'iterations': [1000],  # [500, 1000],
        'depth': [6],  # [8, 12, 16],
        'subsample': [0.5],  # [0.2, 0.3, 0.4, 0.5, 0.7, 0.9],
        'learning_rate': [0.1],  # [0.1, 0.2, 0.01,0.05, 0.1, 0.15],
        'rsm': [0.7],  # [0.3,0.5, 0.7, 0.9]
        'od_type': ['IncToDec'],
        'od_pval': [0.08]  # [0.17]
    }

    # Interface
    model = CatBoostRegressor(
        random_state=14,
        loss_function='Lq:q=2',
        silent=True
    )

    # Search the model with adjusted hyperparameters. This function trains with the grid too
    grid_search = model.grid_search(param_grid,
                                    X=X_train,
                                    y=y_train,
                                    partition_random_seed=14,
                                    verbose=False)

    # Best params config
    best_params = grid_search['params']

    # Train the model
    model = CatBoostRegressor(**best_params,
                              early_stopping_rounds=10,
                              random_state=14,
                              loss_function='Lq:q=2',
                              silent=True)

    eval_set = [(X_train, y_train), (X_test, y_test)]

    model.fit(
        X_train,
        y_train,
        eval_set=eval_set,
        verbose=False,
        use_best_model=True
    )

    # Make predictions on the test set
    y_pred = model.predict(X_test)

    # Extract results for each epoch
    results = model.get_evals_result()
    train_lq_list = results['learn']
    test_lq_list = results['validation_1']

    return model, y_test, y_pred, train_lq_list, test_lq_list, best_params


from skfda.preprocessing.dim_reduction import variable_selection
from skfda import FDataGrid

def mrmr_feature_selection(df, features, target, n_features_to_select=20):
    # Initialize the function
    mrmr = variable_selection.MinimumRedundancyMaximumRelevance(
        n_features_to_select=n_features_to_select,
        method='MID'
    )

    # MID is argmax(relevance - redundance) (diff)
    # MIQ is argmax(relevance/redundance) (quotient)

    # MID has better performance in our data instead of MIQ performance ebtter in  many cases
    # (Improved Measures of Redundancy and Relevance for mRMR Feature Selection Paper)

    # FDataGrid object for correct entry format
    X = FDataGrid(data_matrix=df[features].astype(float).values)
    y = df[target].values

    # Obtain best features
    mrmr_selector = mrmr.fit(X, y)

    # Get the indices of the selected features
    selected_indices = mrmr_selector.get_support(indices=True)

    # Get the names of the selected features
    #selected_feature_names = df.drop(target + id + dtr_target, axis=1).columns[selected_indices]
    selected_feature_names = df[features].columns[selected_indices]

    return selected_feature_names

import xgboost as xgb
from sklearn.metrics import mean_squared_error, mean_absolute_error
import numpy as np
import pandas as pd

def train_xgboost(df, subjectid_col, target_col, selected_feature_names):

    def learning_rate_decay(current_round):
                initial_lr = 0.5  # Set the initial learning rate
                decay_factor = 0.95  # Set the decay factor per round, reducing learning rate every round
                new_lr = initial_lr * (decay_factor ** current_round)
                if new_lr < 0.005:
                    new_lr = 0.005 # cannot be lower than 0.005
                return new_lr

    best_params = {'colsample_bytree': 0.7, 'eval_metric': 'rmse', 'gamma': 0.0001, 'max_depth': 9, 'n_estimators': 100, 'objective': 'reg:squarederror', 'reg_alpha': 0.5, 'reg_lambda': 0.7, 'subsample': 0.7}

    model = xgb.XGBRegressor(**best_params,
                            early_stopping_rounds=10,
                            callbacks=[xgb.callback.LearningRateScheduler(learning_rate_decay)],
                            random_state = 42)

    # Initialize the lists to store the results
    patient_number_list = []
    rmse = []
    mae = []

    overall_results = pd.DataFrame()

    # Loop over the patient_numbers
    for patient_number in df[subjectid_col].unique():
        print(f"Patient number: {patient_number}")
        # Split the data
        X_train = df[df[subjectid_col] != patient_number][selected_feature_names]
        y_train = df[df[subjectid_col] != patient_number][target_col]
        X_test = df[df[subjectid_col] == patient_number][selected_feature_names]
        y_test = df[df[subjectid_col] == patient_number][target_col]

        eval_set = [(X_train, y_train), (X_test, y_test)]
        # Fit the model
        model.fit(X_train, y_train, eval_set=eval_set, verbose=False)

        # Predict
        y_pred = model.predict(X_test)

        # Store the predictions in a dataframe with the patient_number and the real value
        overall_results = pd.concat([overall_results, pd.DataFrame({'patient_number': patient_number, 'real': y_test, 'pred': y_pred})])

        # Calculate the metrics
        patient_number_list.append(patient_number)
        rmse.append(np.sqrt(mean_squared_error(y_test, y_pred)))
        mae.append(mean_absolute_error(y_test, y_pred))

    # Store the results in a dataframe
    metrics = pd.DataFrame({'patient_number': patient_number_list, 'rmse': rmse, 'mae': mae})

    # Calculate the overall RMSE and MAE using overall_results
    overall_rmse = np.sqrt(mean_squared_error(overall_results['real'], overall_results['pred']))
    overall_mae = mean_absolute_error(overall_results['real'], overall_results['pred'])

    # Add the overall results to the dataframe
    metrics.loc[len(metrics)] = ['Overall', overall_rmse, overall_mae]

    # Now train the final model with all the data
    X = df[selected_feature_names]
    y = df['sbp_target']
    eval_set = [(X, y), (X, y)]
    model.fit(X, y, eval_set=eval_set, verbose=False)

    return metrics, overall_results, model


import matplotlib.pyplot as plt
import seaborn as sns

def scatter_pred_real(overall_results, real_col, pred_col, patient_number_col, lim_min=None, lim_max=None):
    # Set plot limits if not provided
    if lim_min is None or lim_max is None:
        real_min = overall_results[real_col].min()
        real_max = overall_results[real_col].max()
        pred_min = overall_results[pred_col].min()
        pred_max = overall_results[pred_col].max()
        lim_min = min(real_min, pred_min)
        lim_max = max(real_max, pred_max)

    # Assign a unique color to each patient_number (categorical variable)
    unique_patients = overall_results[patient_number_col].unique()
    palette = sns.color_palette('hsv', len(unique_patients))
    color_mapping = dict(zip(unique_patients, palette))

    # Map colors to each patient
    colors = overall_results[patient_number_col].map(color_mapping)

    # Plot the results
    plt.figure(figsize=(10, 10))
    plt.scatter(overall_results[real_col], overall_results[pred_col], c=colors, s=5, alpha=0.6)

    plt.plot([lim_min, lim_max], [lim_min, lim_max], 'k--')
    plt.xlim(lim_min, lim_max)
    plt.ylim(lim_min, lim_max)

    plt.xlabel('Real')
    plt.ylabel('Predicted')
    plt.title('Real vs Predicted')

    plt.show()


from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error, mean_absolute_error
import numpy as np
import pandas as pd


def run_calibration(calibration_df, overall_results_df, subject_id="patient_number", slope_thresh=(0.5, 1.5), intercept_thresh=0.5):
    patient_number_list = []
    patient_lr_model_list = []
    rmse = []
    mae = []
    slope_list = []
    intercept_list = []

    calibrated_overall_results_df = pd.DataFrame()

    for patient_num in calibration_df[subject_id].unique():
        patient_train_df = calibration_df[calibration_df[subject_id] == patient_num]
        X_train = patient_train_df["pred"].values.reshape(-1, 1)
        y_train = patient_train_df["real"]

        lr = LinearRegression()
        lr.fit(X_train, y_train)

        slope = lr.coef_[0]
        intercept = lr.intercept_

        #slope_list.append(slope)
        #intercept_list.append(intercept)
        print(f"Patient number: {patient_num}, slope: {slope}, intercept: {intercept}")

        # Check thresholds: adjust calibration if thresholds exceeded
        if (slope < slope_thresh[0]) or (slope > slope_thresh[1]) or (slope == 0.0) or (abs(intercept) > intercept_thresh):
            # Calibration is not reliable; use identity calibration (predicted = calibrated_pred)
            lr.coef_ = np.array([1])
            lr.intercept_ = 0

        patient_test_df = overall_results_df[overall_results_df[subject_id] == patient_num].copy()
        X_test = patient_test_df["pred"].values.reshape(-1, 1)
        patient_test_df["calibrated_pred"] = lr.predict(X_test)
        calibrated_overall_results_df = pd.concat([calibrated_overall_results_df, patient_test_df])

        patient_number_list.append(patient_num)
        patient_lr_model_list.append(lr)
        rmse.append(np.sqrt(mean_squared_error(patient_test_df["real"], patient_test_df['calibrated_pred'])))
        mae.append(mean_absolute_error(patient_test_df["real"], patient_test_df['calibrated_pred']))

    # Overall metrics
    patient_number_list.append("Overall")
    rmse.append(np.sqrt(
        mean_squared_error(calibrated_overall_results_df["real"], calibrated_overall_results_df["calibrated_pred"])))
    mae.append(
        mean_absolute_error(calibrated_overall_results_df["real"], calibrated_overall_results_df["calibrated_pred"]))

    calibration_metrics = pd.DataFrame({"patient_number": patient_number_list, "rmse": rmse, "mae": mae})
    #calibration_coeffs = pd.DataFrame({"patient_number": patient_number_list, "slope": slope_list, "intercept": intercept_list})

    return calibration_metrics, calibrated_overall_results_df, patient_lr_model_list


from sklearn.isotonic import IsotonicRegression
import pandas as pd
import numpy as np
from sklearn.metrics import mean_squared_error, mean_absolute_error

def run_calibration_isotonic(calibration_df, overall_results_df):

    patient_number_list = []
    patient_iso_model_list = []
    rmse = []
    mae = []

    calibrated_overall_results_df = pd.DataFrame()

    for patient_num in calibration_df["patient_number"].unique():
        patient_train_df = calibration_df[calibration_df["patient_number"] == patient_num]
        X_train = patient_train_df["pred"].values
        y_train = patient_train_df["real"].values
        iso = IsotonicRegression(out_of_bounds='nan')
        iso.fit(X_train, y_train)

        patient_test_df = overall_results_df[overall_results_df["patient_number"] == patient_num].copy()
        X_test = patient_test_df["pred"].values
        patient_test_df["calibrated_pred"] = iso.predict(X_test)

        # Drop NaNs before computing metrics
        patient_test_df_clean = patient_test_df.dropna(subset=["calibrated_pred"])

        calibrated_overall_results_df = pd.concat([calibrated_overall_results_df, patient_test_df_clean])

        patient_number_list.append(patient_num)
        patient_iso_model_list.append(iso)

        # Calculate metrics on non-NaN data
        rmse.append(np.sqrt(mean_squared_error(patient_test_df_clean["real"], patient_test_df_clean['calibrated_pred'])) )
        mae.append(mean_absolute_error(patient_test_df_clean["real"], patient_test_df_clean['calibrated_pred']))

    # Now overall metrics
    patient_number_list.append("Overall")
    rmse.append(np.sqrt(mean_squared_error(calibrated_overall_results_df["real"], calibrated_overall_results_df["calibrated_pred"])))
    mae.append(mean_absolute_error(calibrated_overall_results_df["real"], calibrated_overall_results_df["calibrated_pred"]))

    calibration_metrics = pd.DataFrame({"patient_number": patient_number_list, "rmse": rmse, "mae": mae})

    return calibration_metrics, calibrated_overall_results_df, patient_iso_model_list