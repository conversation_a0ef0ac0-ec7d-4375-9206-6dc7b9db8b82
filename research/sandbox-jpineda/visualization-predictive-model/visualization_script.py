# Importar librerías necesarias
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import jbonisutils
from sklearn.metrics import mean_squared_error, mean_absolute_error

# Cargar el CSV unificado
df = pd.read_csv("20250315-report-merged-data.csv", sep=";")

# Mostrar información básica del dataset
print("=== INFORMACIÓN DEL DATASET ===")
print(f"Número total de filas: {df.shape[0]}")
print(f"Número total de columnas: {df.shape[1]}")
print("\nColumnas:")
print(df.columns.tolist())
print("\nModelos únicos:")
print(df['model'].unique())

# Crear diccionario de DataFrames por modelo
data_dict = {
    'no_refpp_overall_results': df[df['model'] == 'no_refpp'].copy(),
    'refpp_overall_results': df[df['model'] == 'refpp'].copy(),
    'calibration_2_overall_results': df[df['model'] == 'calib_2'].copy(),
    'calibration_3_overall_results': df[df['model'] == 'calib_3'].copy(),
    'calibration_4_overall_results': df[df['model'] == 'calib_4'].copy(),
    'calibration_5_overall_results': df[df['model'] == 'calib_5'].copy()
}

# Verificar la distribución de datos por modelo
print("\n=== DISTRIBUCIÓN POR MODELO ===")
for name, data in data_dict.items():
    print(f"{name}: {len(data)} registros")

# Generar scatter plots para cada modelo
print("\n=== GENERANDO SCATTER PLOTS ===")
for title in data_dict.keys():
    df_model = data_dict[title]
    print(f"\nGenerando gráfico para {title}...")
    jbonisutils.scatter_pred_real(
        overall_results=df_model,
        real_col="real",
        pred_col="pred",
        patient_number_col="patient_number"
    )
    plt.title(title)
    plt.show()

# Calcular métricas para cada modelo
metrics_results = []
for model_name, model_data in data_dict.items():
    rmse = np.sqrt(mean_squared_error(model_data['real'], model_data['pred']))
    mae = mean_absolute_error(model_data['real'], model_data['pred'])
    metrics_results.append({
        'model': model_name,
        'rmse': rmse,
        'mae': mae
    })

metrics_results_df = pd.DataFrame(metrics_results)
print("\n=== MÉTRICAS POR MODELO ===")
print(metrics_results_df)

# Función para visualizar comparación de calibración
def scatterplot_calibration_comparison(df_base, df_calibrated, patient_number):
    plt.figure(figsize=(10, 10))
    
    base_data = df_base[df_base['patient_number'] == patient_number]
    calibrated_data = df_calibrated[df_calibrated['patient_number'] == patient_number]
    
    lim_min = min(base_data['real'].min(), calibrated_data['real'].min()) - 5
    lim_max = max(base_data['real'].max(), calibrated_data['real'].max()) + 5
    
    plt.plot([lim_min, lim_max], [lim_min, lim_max], 'w--', linewidth=2, alpha=0.5)
    plt.xlim(lim_min, lim_max)
    plt.ylim(lim_min, lim_max)
    
    plt.scatter(base_data['real'], base_data['pred'],
               color='blue', label='No calibrado', alpha=0.8)
    plt.scatter(calibrated_data['real'], calibrated_data['pred'],
               color='red', label='Calibrado', alpha=0.8)
    
    plt.ylabel('Valores Predichos')
    plt.xlabel('Valores Reales')
    plt.title(f'Comparación de Predicciones - Paciente {patient_number}')
    plt.legend()
    plt.grid(True)
    plt.gca().set_facecolor('black')
    plt.show()

# Visualizar comparación para algunos pacientes
print("\n=== COMPARACIÓN DE CALIBRACIÓN ===")
for patient in list(df['patient_number'].unique())[:3]:  # Primeros 3 pacientes
    print(f"\nVisualizando paciente {patient}")
    scatterplot_calibration_comparison(
        data_dict['no_refpp_overall_results'],
        data_dict['calibration_5_overall_results'],
        patient
    )

# Función para visualizar progresión de calibración
def plot_calibration_progression(patient_number):
    plt.figure(figsize=(12, 8))
    
    models = ['no_refpp', 'calib_2', 'calib_3', 'calib_4', 'calib_5']
    colors = ['blue', 'purple', 'green', 'orange', 'red']
    
    for model, color in zip(models, colors):
        model_key = f'{model}_overall_results' if model == 'no_refpp' else f'calibration_{model[-1]}_overall_results'
        data = data_dict[model_key]
        patient_data = data[data['patient_number'] == patient_number]
        
        plt.scatter(patient_data['real'], patient_data['pred'],
                   color=color, label=model, alpha=0.7)
    
    lim_min = 70
    lim_max = 180
    plt.plot([lim_min, lim_max], [lim_min, lim_max], 'w--', linewidth=2, alpha=0.5)
    plt.xlim(lim_min, lim_max)
    plt.ylim(lim_min, lim_max)
    
    plt.ylabel('Valores Predichos')
    plt.xlabel('Valores Reales')
    plt.title(f'Progresión de Calibración - Paciente {patient_number}')
    plt.legend()
    plt.grid(True)
    plt.gca().set_facecolor('black')
    plt.show()

# Visualizar progresión de calibración para algunos pacientes
print("\n=== PROGRESIÓN DE CALIBRACIÓN ===")
for patient in list(df['patient_number'].unique())[:3]:  # Primeros 3 pacientes
    print(f"\nVisualizando progresión para paciente {patient}")
    plot_calibration_progression(patient) 