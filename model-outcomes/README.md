# AIO - Model Outcomes Visualizers

This directory contains interactive applications designed to visualize and analyze the performance of predictive models developed within the Artificial Intelligence Orchestrator (AIO) framework.

## Available Visualizers

1.  **[Model Outcomes - Classification](./model-outcomes-classification/README.md)**
    - Focuses on evaluating binary classification models.
    - Features: Comprehensive metrics dashboard, interactive confusion matrix, statistical testing (Chi-square, <PERSON>'s), ROC/PR curve analysis, feature importance, flexible data loading.

2.  **[Model Outcomes - Regression](./model-outcomes-regression/README.md)**
    - Focuses on evaluating regression models.
    - Features: Prediction vs. Real value plots, multi-model/multi-patient comparisons, key regression metrics (RMSE, MAE, Correlation).

## Overview

These visualizers provide a consistent interface for assessing model performance, enabling users to:
- Upload prediction data from various sources.
- Interact with visualizations to explore model behavior.
- Filter data by model or patient/subject.
- Compare different models or analyze performance across different patient groups.
- Gain insights into model strengths and weaknesses.

## Getting Started

Please refer to the `README.md` file within each specific visualizer's directory for detailed installation and usage instructions:

- **Classification**: [model-outcomes-classification/README.md](./model-outcomes-classification/README.md)
- **Regression**: [model-outcomes-regression/README.md](./model-outcomes-regression/README.md)


## License

These projects are currently under development for the Artificial Intelligence Orchestrator (AIO) and are not yet licensed for public use. 