name: modeloutcomes
channels:
  - conda-forge
  - defaults
dependencies:
  - python=3.11
  - pip
  # Core dependencies for visualization apps
  - streamlit>=1.42.0
  - pandas>=2.0.0
  - numpy>=1.26.0
  - plotly>=5.19.0
  - scikit-learn>=1.3.0
  - scipy>=1.12.0
  # Jupyter and interactive analysis
  - jupyter>=1.0.0
  - ipywidgets>=8.0.0
  - kaleido>=0.2.1
  # Testing and development dependencies
  - pytest>=7.4.0
  - pytest-cov>=4.1.0
  - pytest-mock>=3.11.0
  - coverage>=7.2.0
  # Additional development tools via pip
  - pip:
    - black>=23.0.0          # Code formatting
    - flake8>=6.0.0          # Code linting
    - mypy>=1.5.0            # Type checking
    - pytest-streamlit>=1.1.0  # Streamlit-specific testing utilities 