# Model Outcomes - Regression

Interactive application for visualizing and analyzing model predictions for regression tasks, with AIO (Artificial Intelligence Orchestrator) integration.

## Features

- **Dynamic Single Plot Visualization:**
    - Visualize model predictions against real values in an interactive scatter plot.
    - Compare multiple models for a single patient (points colored by model).
    - Compare multiple patients for a single model (points colored by patient).
    - "-- ALL --" option for easy selection of all models or patients.
    - Square aspect ratio enforced for visual consistency.
- **Multi-Plot Comparison:**
    - Compare multiple models side-by-side for a selected patient.
    - Compare multiple patients side-by-side for a selected model.
    - Optimized layout for efficient space usage.
- **Key Metrics Calculation:** Calculate and display RMSE, MAE, Correlation, and R-squared consistent with the active view.
- **User Experience:**
    - User authentication and session management.
    - Dark mode / Light mode compatibility with theme-adaptive design.
    - AIO branding integration.
    - Context-aware sidebar showing relevant controls.
    - Persistent control selections.
    - Personalized welcome screen.
    - Template download for easy data formatting.
- **Maintainability:**
    - Modular architecture.
    - Unit testing framework (`pytest`).

### Dashboard
![App Screenshot](static/app_screenshot_1.png)

## Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd model-outcomes
   ```

2. Create and activate the Conda environment:
   ```bash
   # Create the environment from environment.yml
   conda env create -f environment.yml

   # Activate the environment
   conda activate modeloutcomes
   ```

3. Navigate to the regression app directory:
   ```bash
   cd model-outcomes-regression
   ```

## Usage

1. Ensure you're in the correct directory and the environment is activated:
   ```bash
   conda activate modeloutcomes
   cd model-outcomes-regression
   ```

2. Run the application with Streamlit:
   ```bash
   streamlit run app.py
   ```

3. Access the application in your browser (typically at http://localhost:8501)

![Login Screenshot](static/app_screenshot_login.png)

### Default Login Credentials

- **Username**: `admin`
- **Password**: `admin123`

## Data Format

The application requires CSV files with the following columns:
- `model`: Model identifier (string)
- `patient_number`: Patient identifier (numeric or string)
- `real`: Actual/ground truth values (numeric)
- `pred`: Predicted values (numeric)

Example:
```csv
model,patient_number,real,pred
model_a,1,120,118
model_a,2,130,132
model_b,1,125,127
model_b,2,135,130
```

## Project Structure

```
model-outcomes-regression/
├── app.py                 # Main application entry point
├── modules/               # Core application modules
│   ├── __init__.py
│   ├── auth.py            # Authentication
│   ├── data_handling.py   # Data loading/validation
│   ├── regression_metrics.py # Metrics calculation logic
│   ├── ui_components.py   # Reusable UI elements (summary, welcome)
│   ├── ui_sidebar.py      # Sidebar layout and controls
│   ├── ui_tabs.py         # Main area tab content logic
│   ├── utils.py           # Utility functions
│   └── visualization.py   # Plotting functions
├── static/                # Static assets (images)
├── data/                  # Sample data files (optional)
├── tests/                 # Unit tests
│   ├── __init__.py
│   ├── conftest.py        # Pytest fixtures
│   ├── test_modules/      # Tests per module
│   ├── README.md          # Test directory overview
│   └── Guide_Regression_Testing.md # Detailed testing guide
├── users.csv              # User credentials file (default)
├── requirements.txt       # Main dependencies
├── .cursorrules           # AI guidance for development
├── README.md              # This file
└── CHANGELOG.md           # Version history
```

## Visualization Tabs

1.  **📊 Single Visualization:**
    *   Displays a single, interactive scatter plot (Real vs. Predicted).
    *   Allows dynamic comparison of multiple models (for one patient) or multiple patients (for one model) within the same plot using color coding.
    *   Shows overall performance metrics (RMSE, MAE, Corr, R²) for the displayed data.
    *   If comparing multiple items, shows a detailed breakdown (table + bar plot) of metrics per model/patient below the main scatter plot.

2.  **📈 Multi-Plot Comparison:**
    *   Displays a grid of smaller scatter plots.
    *   Allows side-by-side comparison of different models (for one patient) or different patients (for one model).
    *   Each plot includes its own compact metrics annotation.

## Contributing

Contributions are welcome. Please follow standard Git workflow (fork, branch, commit, pull request). Ensure new code adheres to the standards outlined in `.cursorrules` and includes relevant unit tests (see `tests/README.md`).

## Metrics Calculation

The application calculates the following performance metrics:

### Root Mean Squared Error (RMSE)
Measures the square root of the average squared differences between predicted and actual values. Lower is better.
*Formula:* `sqrt(mean((real - predicted)²))`

### Mean Absolute Error (MAE)
Measures the average absolute difference between predicted and actual values. Less sensitive to outliers than RMSE. Lower is better.
*Formula:* `mean(|real - predicted|)`

### Correlation (Pearson)
Measures the linear correlation between predicted and actual values. Values closer to 1 indicate a stronger positive linear relationship.
*Implementation:* `df['real'].corr(df['pred'])`

### R-squared (R²)
Represents the proportion of the variance in the dependent variable (`real`) that is predictable from the independent variable (`pred`). Higher (closer to 1) is generally better.
*Formula:* `1 - (SS_residual / SS_total)`
*Implementation:* Uses `sklearn.metrics.r2_score` or equivalent calculation.

## Version History

See [CHANGELOG.md](CHANGELOG.md).

## License

(Details omitted for brevity - assume standard license info here)

## Acknowledgments

- Developed for AIO.
- Built with Streamlit. 