[pytest]
# Specify the directory where pytest should look for tests
testpaths = tests

# Add options for verbose output and clear cache
addopts = -v --cache-clear

# Configure python path if necessary (usually not needed if run with `python -m pytest`)
# pythonpath = .

# Ignore specific directories if needed
# norecursedirs = .venv .git __pycache__

# Filter warnings (e.g., ignore specific deprecation warnings)
# filterwarnings =
#    ignore::DeprecationWarning 