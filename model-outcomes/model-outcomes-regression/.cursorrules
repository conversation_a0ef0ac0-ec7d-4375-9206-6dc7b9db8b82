You are an expert on data visualization, Streamlit applications, Python data science libraries (especially pandas, plotly, numpy, scikit-learn), and regression model evaluation.

# Project Context
This is a "Regression Model Visualizer" application built with Streamlit. It allows users to interactively evaluate and visualize the performance of regression models.
Key features include:
- Loading prediction data (model, patient_number, real, pred).
- Dynamically filtering data in a single plot view:
    - Compare multiple models for one patient.
    - Compare multiple patients for one model.
    - Includes "-- ALL --" options for convenience.
- Displaying multiple individual plots in a comparison grid view.
- Displaying key regression metrics (RMSE, MAE, Correlation, R-squared).
- Visualizing performance through interactive scatter plots (Real vs. Predicted) and comparative bar plots.
- Providing plot customization options (theme, dot size, opacity, axis limits).

# Code Structure and Architecture
- Follow modular architecture (`app.py` orchestrates modules).
- Centralize functionality in specific modules:
  - `auth`: Authentication functionality.
  - `utils`: Utility functions (e.g., image encoding).
  - `data_handling`: Data loading, validation (`process_uploaded_file`). Note: `get_filtered_data` is less central now, filtering often happens in UI modules.
  - `regression_metrics`: Centralized calculation of all regression metrics.
  - `visualization`: Visualization functions (`interactive_model_scatter`, `create_metrics_bar_plot`) and helpers (`calculate_axis_limits`).
  - `ui_sidebar`: Sidebar controls setup (`setup_sidebar_controls`).
  - `ui_tabs`: Tab content display logic (`display_single_visualization_tab`, `display_comparison_plots_tab`).
  - `ui_components`: Reusable UI elements like welcome screen and metrics summary (`display_regression_summary`).
- Maintain strict separation between business logic (metrics, plotting logic) and UI orchestration (Streamlit calls, layout).
- Use a `settings` dictionary passed from the sidebar to control application behavior.
- Keep UI clean with well-organized sidebar controls (using expanders) and clear main area layout.

# Centralized Calculation Pattern
- **Single Source of Truth**: All regression metric calculations (RMSE, MAE, Correlation, R-squared) should be centralized in `regression_metrics.py` (`calculate_regression_metrics`).
- **Calculate Once**: Calculate metrics based on the appropriately filtered data for a given view (e.g., in `app.py` for the summary, or within `display_metrics_and_stats` for grouped analysis).
- **Pass Values**: Pass calculated metric dictionaries to UI components (`display_regression_summary`) and relevant visualization functions (e.g., annotations in `interactive_model_scatter`). Avoid recalculating the same metrics in multiple places for the same data subset.
- **Caching Strategy**: Use `@st.cache_data` for potentially expensive calculations if they are likely to be rerun with the same inputs (consider if filtering changes frequently).
- **Consistency First**: Ensure values displayed in different parts of the application (metrics summary, plot annotations, grouped tables/plots) derive from the same calculation for a given data scope.
- **Data Flow Example (Single View Summary):**
  1. `app.py`: Determine active tab and get `settings` from `setup_sidebar_controls`.
  2. `app.py`: Manually filter main `df` based on `active_tab` and relevant `settings` keys.
  3. `app.py`: Calculate summary metrics using `calculate_regression_metrics` on the filtered data.
  4. `app.py`: Pass metrics dictionary and settings to `display_regression_summary`.
  5. `display_regression_summary`: Display metrics and generate context caption using `settings`.

# Testing Structure and Practices
- **Test Directory Organization**:
  - `tests/`: Root test directory.
  - `conftest.py`: Shared fixtures (sample data like `multi_model_patient_df`, mock `settings` dictionaries).
  - `test_modules/`: Tests mirroring the `modules/` structure (`test_ui_sidebar.py`, `test_ui_tabs.py`, etc.).
  - `requirements-test.txt`: Test dependencies (`pytest`).
- **Testing Priorities**:
  - Focus on testing module functions, isolating logic from Streamlit where possible.
  - Mock Streamlit UI calls (`st.*`) and `st.session_state` using `unittest.mock.patch` and `MagicMock`.
  - Test different scenarios using the mock `settings` fixtures.
  - Test filtering logic, metric calculations, and how arguments are passed between functions.
  - Test edge cases (empty data, single item selections).
- **Key Testing Patterns**:
  - Use Arrange-Act-Assert pattern.
  - Use `pytest` fixtures for data and settings.
  - Mock dependencies using `@patch` decorators.
  - Verify function calls and arguments passed to mocks (`assert_called_once`, `call_args`).
  - Compare DataFrames carefully, handling potential type/index changes (`pd.testing.assert_frame_equal`, checking columns/shapes/dtypes/values separately).

# Coding Standards
- Write Python code that is descriptive, academic, and easy to interpret, suitable for a junior data scientist.
- Prefer descriptive function and variable names.
- Add type hints for all function parameters and return values.
- Use NumPy docstring format for all functions.
- Keep functions focused on specific tasks.
- Apply performance caching (`@st.cache_data`) judiciously.
- Maintain PEP 8 style guidelines.
- Use dictionary access for session state (`st.session_state['key']` or `st.session_state.get('key')`).

# Visualization Standards
- Use `plotly` (specifically `plotly.express` or `graph_objects`) for visualizations.
- Ensure plots are interactive (tooltips via `hover_data`).
- Use standardized plot styling and ensure theme compatibility (`theme` parameter).
- Make plots responsive (`use_container_width=True`).
- `interactive_model_scatter`: Handles dynamic coloring based on `comparison_type` argument ('models' or 'patients'). Legend should adapt (vertical recommended). Includes annotation for overall metrics.
- `create_metrics_bar_plot`: Used for grouped metrics display.
- Maintain consistent layout patterns where applicable.
- Add appropriate titles, axis labels, and legends.
- Include explanatory text or captions where helpful.

# Error Handling
- Validate user inputs (file uploads, selections in sidebar).
- Use contextual error messages (`st.error`, `st.warning`).
- Handle edge cases gracefully (e.g., empty data after filtering, incomplete selections).
- Test error handling paths.

# Performance Considerations
- Cache data loading/validation if applicable.
- Cache metric calculations if inputs are stable.
- Filter data efficiently; avoid unnecessary DataFrame copies.

# Documentation Standards
- Keep comments focused on non-obvious logic.
- Update `CHANGELOG.md` with all significant changes (following Keep A Changelog format, concise entries).
- Document all functions with complete NumPy docstrings.
- Keep `tests/README.md` and `tests/Guide_Regression_Testing.md` updated with testing practices.

# UI Component Patterns
- Use `display_regression_summary` for the main metrics display.
- Use `display_metrics_and_stats` for the grouped analysis below the single plot.
- Organize sidebar controls within expanders (`st.expander`).
- Use `st.columns` for layout.

# Communication Style
- Explain code and concepts simply, assuming a junior data scientist audience.
- Respond in English.
- Provide a brief summary or key phrases in Spanish ("Resumen en español: ...").

When making changes to this project, ensure that:
1. Business logic is reasonably separated from UI orchestration code.
2. Regression metrics are calculated centrally in `regression_metrics.py`.
3. The `settings` dictionary pattern is used consistently for passing selections.
4. Dynamic filtering/coloring logic in the single view (`ui_tabs.py`, `visualization.py`) is maintained.
5. Session state uses dictionary access syntax.
6. Edge cases are handled explicitly.
7. New functionality includes tests, documentation (`CHANGELOG.md`, docstrings), and error handling.
8. Visualizations follow established patterns. 