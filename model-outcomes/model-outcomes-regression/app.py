import streamlit as st
import pandas as pd
import numpy as np
from typing import Optional, List, Dict, Any
import os

# Import modules for authentication, welcome screen, utility functions, data handling, metrics, and visualization   
from modules.auth import initialize_session_state, display_login_page, logout_user
from modules.ui_components import display_welcome_screen, display_regression_summary
from modules.utils import get_base64_encoded_image, extend_session_state
from modules.data_handling import get_filtered_data, process_uploaded_file, filter_data_for_summary
from modules.regression_metrics import calculate_regression_metrics
from modules.visualization import interactive_model_scatter, create_metrics_bar_plot
from modules.ui_sidebar import display_sidebar_header, setup_sidebar_controls
from modules.ui_tabs import display_single_visualization_tab, display_comparison_plots_tab, display_feature_analysis_tab

# Set page configuration
st.set_page_config(
    page_title="Model Outcomes | Regression 📉",
    page_icon="📉",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Add custom styling
st.markdown("""
<style>
    .header-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
        padding: 1rem 0;
    }
    
    .header-title {
        font-size: 2.5rem;
        font-weight: 600;
        color: var(--header-color, #1E3A8A);
        margin: 0;
    }
    
    .header-logo {
        width: 100px;
        height: auto;
        opacity: 0.9;
    }
    
    .description {
        font-size: 1rem;
        margin-bottom: 2rem;
        color: var(--subtext-color, #64748B);
    }

    .stTabs [data-baseweb="tab-list"] {
        gap: 24px;
    }
    .stTabs [data-baseweb="tab"] {
        height: 50px;
        white-space: pre-wrap;
        border-radius: 4px 4px 0 0;
        gap: 1px;
        padding-top: 10px;
        padding-bottom: 10px;
    }
    
    .stPlotlyChart {
        margin-bottom: 20px;
        border: 1px solid var(--plot-border-color, #f0f0f0);
        border-radius: 8px;
        padding: 10px;
        background-color: var(--plot-bg-color, transparent) !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }
    .plot-container {
        margin-bottom: 20px;
        background-color: var(--plot-container-bg, transparent);
        border-radius: 8px;
        padding: 15px;
        border: 1px solid var(--plot-container-border, #e0e0e0);
    }
    
    h2, h3 {
        margin-top: 1.5rem !important;
        margin-bottom: 1rem !important;
        color: var(--header-color, #1E3A8A);
    }
    
    .stButton > button {
        font-weight: 500 !important;
    }
    
    div[data-testid="column"] {
        padding: 10px;
        background-color: var(--column-bg-color, transparent);
        border-radius: 5px;
        margin: 5px;
    }
    
    [data-testid="stHorizontalBlock"] {
        gap: 20px !important;
        margin-bottom: 20px !important;
    }
    
    :root {
        --plot-border-color: rgba(240, 240, 240, 0.2);
        --plot-bg-color: transparent;
        --plot-container-bg: transparent;
        --plot-container-border: rgba(224, 224, 224, 0.2);
        --header-color: #1E3A8A;
        --column-bg-color: transparent;
    }
    
    [data-theme="dark"] {
        --plot-border-color: rgba(80, 80, 80, 0.2);
        --plot-container-border: rgba(80, 80, 80, 0.2);
        --header-color: #4F8DF5;
    }
</style>
""", unsafe_allow_html=True)



def main():
    """Main function running the Streamlit application."""
    initialize_session_state()
    extend_session_state(
        current_file=None,
        uploaded_data=None,
        active_tab="single"
    )

    if not st.session_state.get('is_authenticated', False):
        display_login_page()
        return
    
    # Page Header
    st.markdown("""
        <div class="header-container">
            <h1 class="header-title"> Model Outcomes | Regression 📉</h1>
        </div>
    """, unsafe_allow_html=True)
    
    # ===== SIDEBAR =====
    with st.sidebar:
        uploaded_file = display_sidebar_header()
            
    # Process uploaded file
    df = process_uploaded_file(uploaded_file)

    if df is None or df.empty:
        display_welcome_screen()
        return
    
    # ===== TAB SELECTION =====
    tab_titles = ["📊 Single Visualization", "📈 Multi-Plot Comparison", "🔍 Feature Analysis"]
    selected_tab_title = st.radio(
        "Select View:", 
        tab_titles, 
        index=st.session_state.get('active_tab_index', 0), 
        key="main_view_selector",
        horizontal=True,
        label_visibility="collapsed"
    )
    
    st.session_state['active_tab_index'] = tab_titles.index(selected_tab_title)
    tab_id_map = {0: "single", 1: "comparison", 2: "feature_analysis"}
    active_tab_id = tab_id_map.get(st.session_state['active_tab_index'], "single")
    
    # Setup sidebar controls
    with st.sidebar:
        settings = setup_sidebar_controls(df, active_tab=active_tab_id)
    
    # ===== MAIN CONTENT AREA =====
    try:
        # Display content based on selected tab
        if active_tab_id == "single":
            display_single_visualization_tab(df, settings)
        elif active_tab_id == "comparison":
            display_comparison_plots_tab(df, settings)
        elif active_tab_id == "feature_analysis":
            display_feature_analysis_tab(df, settings)

        # Calculate metrics for summary using centralized filtering
        summary_filtered_df = filter_data_for_summary(df, active_tab_id, settings)
        summary_metrics = calculate_regression_metrics(summary_filtered_df)
        summary_points = len(summary_filtered_df)

        # Metrics Summary
        with st.expander(f"📉 Metrics Summary ({active_tab_id.capitalize()} View)", expanded=True):
            display_regression_summary(
                metrics=summary_metrics, 
                total_points=summary_points,
                filtered_df=summary_filtered_df,
                total_df_points=len(df),
                active_tab=active_tab_id,
                settings=settings
            )
    
    except Exception as e:
        st.error(f"An error occurred in the main area: {str(e)}")
        st.exception(e)
        st.info("Please check your data or selections.")


if __name__ == "__main__":
    main() 