# Changelog

All notable changes to the Model Prediction Visualizer app will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.5.1] - 2025-01-11
### Added 
- **Advanced Statistical Analysis in Feature Analysis:** Enhanced Feature Analysis with comprehensive statistical testing and effect size calculations:
    - **Statistical Significance Testing:** Automatic detection and execution of appropriate statistical tests:
        - Independent t-test for normally distributed data (two groups)
        - Mann-Whitney U test for non-parametric data (two groups)
        - One-way ANOVA for normally distributed multi-group data
        - Kruskal-Wallis test for non-parametric multi-group data
    - **Effect Size Calculations:** Quantification of practical significance:
        - <PERSON>'s d for parametric tests with interpretation (negligible, small, medium, large)
        - Rank-biserial correlation for non-parametric tests
        - Automatic effect size interpretation and classification
    - **Statistical Annotations:** Real-time statistical test results displayed on visualizations:
        - Test name, p-value formatting, significance indicators
        - Integrated seamlessly into existing distribution plots

### Enhanced
- **Feature Analysis Core Engine:** Major improvements to `calculate_feature_group_statistics()`:
    - **Automatic Normality Testing:** Uses Shapiro-Wilk test for appropriate statistical test selection
    - **Comprehensive Group Statistics:** Extended statistics including median, min, max for all features
    - **Robust Error Handling:** Graceful handling of edge cases and invalid data
    - **Performance Optimization:** Uses `@st.cache_data` for statistical calculations
    - **Detailed Results Structure:** Returns grouped data, statistics, tests, and summary in unified format

### Fixed
- **Dark Mode Plot Contrast:** Resolved annotation visibility issues in dark themes by implementing transparent backgrounds
- **Statistical Annotation Styling:** Optimized annotation styling for cross-theme compatibility

## [1.5.0] - 2025-01-09
### Changed
- **Feature Analysis Layout Optimization:** Completely restructured Feature Analysis tab interface for better space utilization:
    - **Compact Header:** Context information and data metrics (points, patients, models) in optimized single row
    - **Organized Controls:** Analysis Perspective, Feature Selection, and Method Description in balanced 3-column layout
    - **Enhanced Results Section:** Statistics table with key insights alongside tabbed visualizations (Distribution/Box Plots)
    - **Quick Feature Info:** Real-time range and mean statistics for selected features
    - **Key Insights Display:** Automatic calculation and display of feature and SBP differences between groups

### Added
- **Tabbed Visualizations:** Distribution and Box Plot comparisons in separate tabs for better space usage
- **Contextual Tooltips:** Help text for data metrics and interactive elements
- **Statistical Insights:** Automated calculation of key differences between analysis groups

## [1.4.0] - 2025-01-09
### Added
- **Dedicated Feature Analysis Tab:** Optimized Feature Analysis by creating a dedicated tab at the main navigation level:
    - **Separated from Single Visualization:** Moved Feature Analysis from nested expander to standalone tab
    - **Improved Interface:** Enhanced layout with better organization and more space for visualizations
    - **Advanced Analysis Section:** Added expandable section for multi-feature comparison
    - **Better User Experience:** More prominent controls and clearer data context information

### Changed
- **UI Structure:** Updated main navigation to include "🔍 Feature Analysis" as third primary tab
- **Single Visualization:** Cleaned up Single Visualization tab by removing nested Feature Analysis section
- **Sidebar Logic:** Extended sidebar controls to support Feature Analysis tab using same filtering logic

## [1.3.0] - 2025-01-09
### Added
- **Feature Analysis for Regression:** Implemented comprehensive feature analysis functionality adapted from the classification app Issue #34:
    - **Prediction-based grouping:** Three analysis perspectives for regression contexts:
        - "By Prediction Range": Groups data by high/low predictions using median split
        - "By Error Level": Groups data by high/low error using median split of absolute errors  
        - "By Quartiles": Groups data into quartiles based on prediction values
    - **Interactive Feature Analysis UI:** Added expandable "🔍 Feature Analysis" section in Single Visualization tab:
        - Radio button selector for analysis perspective
        - Feature dropdown for selecting columns to analyze
        - Automatic detection of available feature columns (excludes model, patient_number, real, pred)
        - Group statistics table showing count, mean, and standard deviation by group
    - **Feature Visualizations:** Two complementary plots for feature analysis:
        - **Distribution Comparison**: Histogram overlays showing feature distributions across prediction groups
        - **Box Plot Comparison**: Box plots comparing feature values between groups with outlier detection
    - **Smart Feature Detection:** Graceful handling when no additional features are available with informative messaging

### Added (Technical Implementation)
- **New functions in `regression_metrics.py`:**
    - `get_available_features()`: Detects feature columns excluding standard regression columns
    - `create_prediction_groups()`: Creates grouping based on median_split, error_based, or quartiles methods
- **New functions in `visualization.py`:**
    - `plot_feature_distribution_by_prediction_range()`: Creates histogram overlays by prediction groups
    - `plot_feature_comparison_by_prediction_range()`: Creates box plot comparisons with subplot support
- **Enhanced UI in `ui_tabs.py`:**
    - Integrated feature analysis section with dynamic content based on available features
    - Added error handling and graceful degradation for datasets without additional features

### Changed
- **Import structure:** Updated module imports to include new feature analysis functions
- **Color schemes:** Implemented consistent semantic color mapping for regression groups (blue, orange, green, red)
- **Layout optimization:** Feature analysis uses two-column layout for efficient space utilization

### Technical Details
- **Caching:** All grouping functions use `@st.cache_data` for performance optimization
- **Error handling:** Comprehensive error handling with user-friendly messages for edge cases
- **Theme compatibility:** All new visualizations support existing theme system (plotly_white, plotly_dark)
- **Code standards:** Maintains existing code style, documentation standards, and modular architecture

## [1.2.1] - 2024-07-26
### Added
- **Grouped Analysis in Multi-Plot Tab:** Added the "Analysis Details by Group" section (using `display_metrics_and_stats`) below the plot grid in the 'Multi-Plot Comparison' tab (`display_comparison_plots_tab`). This provides aggregate statistics for the entire set of models or patients being compared in that view.

### Changed
- **Scatter Plot Layout Optimization:** Improved the layout of the `interactive_model_scatter` plot:
    - Moved the legend to the right side with a vertical orientation (`orientation="v"`, `x=1.02`).
    - Enabled `automargin=True` for x and y axes to better accommodate labels.
    - Increased the right margin (`r=150`) to provide space for the legend.
    - Adjusted the metrics annotation text (font size 11, right alignment, background opacity) for better readability and positioning.

## [1.2.0] - 2024-04-09
### Added
- **Dynamic Single Visualization:** Replaced rigid modes (Mode 1/2/3) in the "Single Visualization" tab with dynamic controls:
    - Users can now compare multiple models for a single patient OR multiple patients for a single model within the same plot.
    - Points are automatically colored by the comparison dimension (model or patient).
    - Added "-- ALL --" option to model/patient multi-selectors for convenience.
- **Persistent Multi-Select:** Selections in the single view's model/patient multi-selectors now persist when the context (patient/model) is changed.
- **Unit Tests:** Added comprehensive unit tests for refactored UI modules (`ui_sidebar`, `ui_tabs`, `ui_components`) and updated tests for `visualization`.

### Changed
- **Refactored Single View Logic:** Updated `ui_sidebar.py`, `ui_tabs.py`, `visualization.py`, `ui_components.py` and `app.py` to support the new dynamic single visualization.
    - `setup_sidebar_controls` uses new widgets and settings keys for the single view.
    - `display_single_visualization_tab` filters data based on dynamic settings and passes `comparison_type` to the plot.
    - `interactive_model_scatter` accepts `comparison_type`, uses pre-filtered data, and colors points dynamically.
    - `display_metrics_and_stats` adapted to accept pre-filtered data and `comparison_type` to show relevant grouped metrics.
- **Unified Summary Metrics:** The main "Metrics Summary" section now correctly reflects the data and context selected in the dynamic "Single Visualization" tab.
- **Plot Layout & Axes:**
    - Single view scatter plot now enforces a square aspect ratio (`scaleanchor='x'`).
    - Custom axis limits are now strictly enforced (using conditional `automargin` and `constrain='domain'`).
    - Multi-plot comparison view uses optimized margins and hides legends for subplots.
- **README Updated:** Main `README.md` updated to reflect current features and architecture.
- **`.cursorrules` Updated:** AI development guidance updated with current architecture, testing practices, and features.

### Fixed
- **Multi-Plot Comparison Rendering:** Fixed `TypeError` caused by `interactive_model_scatter` signature changes.
- **Session State Error:** Resolved `StreamlitAPIException` after multiselect widget instantiation.
- **Test Failures:** Corrected various unit test failures related to mocking (`st.columns`, `__getattr__`) and DataFrame comparisons (dtype changes, added index).
- **Indentation Error:** Fixed `IndentationError` in `app.py` filtering logic.

## [1.1.0] - 2024-04-08

### Added
- **Unit Testing Framework Setup:** Established the basic structure for unit testing using pytest:
  - Created `tests/` directory with `__init__.py`.
  - Created `tests/test_modules/` directory with `__init__.py` to mirror the `modules/` structure.
  - Added `pytest.ini` for pytest configuration.
  - Added `tests/requirements-test.txt` specifying pytest dependency.
  - Created `tests/conftest.py` for shared test fixtures (regression data).
  - Created initial test file `tests/test_modules/test_regression_metrics.py` and added tests for metrics functions.
  - Added test file `tests/test_modules/test_data_handling.py` with tests for `get_filtered_data`.
  - Added test file `tests/test_modules/test_visualization.py` with tests for helper functions.
- **Flexible Column Mapping:** Implemented optional column mapping UI (similar to classification app) that appears if standard columns (`model`, `patient_number`, `real`, `pred`) are not found. Includes heuristics for suggesting columns.
- **Auto-Generate Model Column:** Added an option `[Auto-generate]` in the column mapping UI to handle datasets containing results from only one model (creates a 'Default Model' column).

### Changed
- **Refactored Core UI Logic:** Separated UI concerns from the main application script (`app.py`):
  - **Metrics Module (`regression_metrics.py`):** Extracted metric calculation logic (RMSE, MAE, Correlation). Renamed from `metrics.py`. Removed visualization functions.
  - **Visualization Module (`visualization.py`):** Consolidated all plotting functions, including `create_metrics_bar_plot` (moved from metrics module). Corrected internal imports and improved code quality.
  - **Sidebar Module (`ui_sidebar.py`):** Created module and moved all sidebar logic (header display, control setup for single plot, multi-plot comparison, and visualization settings) from `app.py`.
  - **Tabs Module (`ui_tabs.py`):** Created module and moved logic for rendering content within each main tab (`Single Visualization`, `Multi-Plot Comparison`, `Data Overview`) from `app.py`.
- **Simplified `app.py`:** The main application script now primarily handles initialization, authentication, data loading orchestration, tab definition, and calling functions from the new UI modules (`ui_sidebar`, `ui_tabs`).
- **Moved Welcome Screen:** Relocated `display_welcome_screen` function from `modules/auth.py` to the new `modules/ui_components.py` for better separation of concerns. Updated relevant imports in `app.py`.
- **Refined Data Handling Module:** Refactored `process_uploaded_file` into helper functions (`_load_regression_dataframe`, `_validate_regression_dataframe`) for better structure and added robust type validation. Improved `get_filtered_data` to explicitly handle "All" selections and simplified internal logic. Added basic internal validation to `interactive_model_scatter` in `visualization.py`.
- **Consolidated Utility Functions:** Ensured utility functions (`get_base64_encoded_image`, `extend_session_state`) are centralized in `modules/utils.py` and improved their implementation.

### Fixed
- **Comparison Tab Logic:** Ensured `display_comparison_plots_tab` uses settings derived from the 'Multi-Plot Comparison Controls' sidebar section, restoring correct functionality.
- **Type Hint Error:** Corrected return type hint in `display_sidebar_header` within `ui_sidebar.py`.
- **Missing Sidebar Controls:** Restored missing 'Multi-Plot Comparison Controls' section in `ui_sidebar.py` that was lost during initial refactoring.
- **Handling of "All" Selection:** Corrected logic in data filtering and validation functions to properly handle "All" selections without errors.
- **Column Mapping Recursion:** Fixed recursion error in `get_candidate_columns`.
- **Column Mapping Form:** Added missing submit button (`st.form_submit_button`) to the column mapping UI.

### Removed
- **Obsolete Functions:** Removed unused functions `validate_visualization_inputs` and `filter_data_for_visualization` from `modules/data_handling.py` after refactoring dependent functions.
- **Redundant Code:** Removed duplicated function definitions and unnecessary code blocks during refactoring.

## [1.0.0] - 2024-04-01

### Added
- Improved sidebar design with user avatar and minimalist styling
- Enhanced user profile display with initial-based avatar
- Compact file indicator showing currently loaded file
- Collapsible file upload area to save space after loading
- Created `utils.py` module for general utility functions like image encoding and session state management
- Created `data_handling.py` module for data processing, filtering, and validation functions
- New `process_uploaded_file()` function that centralizes CSV loading and validation logic
- Created `metrics.py` module for performance metrics calculation and visualization
- Centralized implementation of RMSE, MAE, and correlation calculation functions
- Unified metrics bar plot generation with flexible options for comparison views
- Created `visualization.py` module for interactive plot generation and visualization utilities
- Consolidated visualization functions for better code organization and reusability
- Standardized plotting functions with comprehensive documentation
- Reorganized welcome screen with personalized user greeting and contextual information
- Enhanced welcome screen with collapsible information sections
- Added visualization modes section with clear descriptions of available analysis options
- Improved user experience with clearer next step guidance

### Changed
- Redesigned "Data Upload" section to use minimal space with simpler label
- Reorganized sidebar with more compact and efficient layout
- Optimized file uploader interface with adaptive display based on state
- Improved file upload section with cleaner visual hierarchy
- Re-positioned logout button to same row as username for better vertical space usage
- Moved company logo from header to top of sidebar for better visual hierarchy and branding
- Refactored code to improve modularity by extracting utility functions to dedicated modules
- Enhanced code organization by moving data handling functions to a dedicated module
- Refactored metrics calculation and visualization to dedicated module for better maintainability
- Improved code maintainability by moving visualization functions to a dedicated module
- Applied consistent patterns for visualization parameter handling across the application
- Updated welcome screen to provide contextual information based on authentication and data status
- Simplified welcome screen interface with more intuitive organization of information
- Improved template CSV download option with better descriptions

### Fixed
- CSV file loading mechanism to properly handle loading new files during the same session
- Improved detection of newly uploaded files by tracking and comparing filenames
- Fixed layout issues with sidebar elements for more consistent spacing
- Centralized file validation to ensure consistent behavior across the application
- Improved session state tracking and management for better user experience
- Fixed various UI inconsistencies for improved visual cohesion
- Enhanced theme compatibility across all interface elements

## [0.7.2-beta] - 2024-03-28

### Added
- Company logo to the main application interface header
- Theme-adaptive styling for better dark mode compatibility
- CSS variables for consistent theme adaptation across components

### Changed
- Improved login form with transparent background and blur effect for dark mode
- Enhanced UI consistency between login and main application
- Optimized header layout with flexible positioning

### Fixed
- Dark mode visual issues with white backgrounds
- Form styling inconsistencies in different themes
- Button and input styling in dark mode

## [0.7.1-beta] - 2024-03-27

### Added
- AIO company logo to the login page
- Improved login page styling with centered layout and modern design
- Custom styling for login form with better visual hierarchy

### Changed
- Updated login page layout with 3-column structure for better centering
- Enhanced visual styling of login form with shadows and rounded corners
- Improved spacing and typography in login page
- Fixed deprecated `use_column_width` parameter to `use_container_width`

### Fixed
- Logo centering issues in login page
- Authentication system error related to user name field
- Session state management for user authentication

## [0.7.0-beta] - 2024-03-27

### Added
- Authentication system with login page using CSV-based user credentials
- Session state management to track login status and current page
- Partial code modularization with focus on authentication and welcome screen
  - `modules/auth.py` for authentication functionality
  - `modules/welcome.py` for welcome screen

### Fixed
- CSV delimiter detection issue by storing DataFrame in session state
- Error that occurred when switching visualization modes
- Data persistence between UI state changes

### Changed
- Updated main app.py to import modularized authentication and welcome components
- Improved session state management for better data persistence

## [0.6.1-beta] - 2023-07-15

### Changed
- Unified metric selector (RMSE/MAE) into a single control in the Visualization Settings section
- Renamed "Plot Style Options" to "Visualization Settings" for better clarity of purpose
- Improved UI flow by reducing redundant controls and streamlining the visualization workflow
- Enhanced maintainability through centralized metric selection logic

## [0.6.0-beta] - 2024-07-11

### Added
- Detailed description of required data format in welcome screen
- Downloadable CSV template with the correct structure
- Validation to verify if comparison elements are selected
- Utility functions for data validation, filtering, and visualization
- Performance caching for expensive calculations and filtering operations
- Complete implementation of centralized data filtering across all app components

### Changed
- Improved welcome message with clearer instructions
- Implemented automatic generation of comparison plots when parameters change
- Improved informational messages during plot generation
- Refactored the large interactive_model_scatter function into smaller, more focused functions
- Enhanced metrics calculation with better error handling
- Refactored display_comparison_plots to use centralized get_filtered_data function
- Updated display_metrics_and_stats to use consistent filtering approach
- Modified Data Overview section to use centralized filtering

### Removed
- Sample visualization from welcome screen
- "Generate Comparison" button in favor of automatic updates
- Redundant code and calculations in visualization functions
- Duplicate filtering logic across multiple functions

### Improved
- Consistent data filtering throughout the application
- Better performance through shared caching of filtered datasets
- More maintainable codebase with reduced code duplication
- Enhanced readability with standardized data access patterns
- Developer experience through documentation and coding standards

## [0.5.0-beta] - 2025-03-25
### Added
- Dual-axis visualization with data points count overlay for bar plots
- Toggle in sidebar to enable/disable data points visualization
- Centralized metric type selection in sidebar for better workflow

### Changed
- Improved comparison summary visualization with 1:3 layout ratio
- Enhanced bar plots with full theme support and simplified design
- Added informative captions about data points distribution
- Better color scheme with improved readability in all themes

### Fixed
- Fixed issues with plot theming inconsistency
- Resolved compatibility issues in bar plots for all themes

## [0.4.0-beta] - 2025-03-24
### Added
- Interactive RMSE and MAE bar plots for visual metrics comparison
- Toggle switch to alternate between RMSE and MAE visualizations
- Highlighted best performers in bar plots for quick identification
- Improved 3-column layout for Modes 2 and 3 with optimal space allocation

### Changed
- Enhanced metrics display with better formatting and organization
- Optimized layout with more space allocated to visualizations
- Improved highlighting of best performers in metrics tables

## [0.3.8-beta] - 2025-03-24
### Changed
- Combined Visualization Mode and Data Selection into a single "Single Plot Controls" expander
- Renamed "Comparison Settings" to "Multi-Plot Comparison Controls" for clearer relationship with tabs
- Simplified data display options to just "Show 10 rows" and "Show all rows"
- Optimized metrics display for data scientists with more compact formatting

### Improved
- More logical grouping of controls that affect the same visualization
- Streamlined interface with focused controls for technical users
- Enhanced space utilization in the Data Overview section
- Cleaner presentation of dataset information and performance metrics

## [0.3.7-beta] - 2025-03-24
### Changed
- Standardized sidebar controls with consistent expander sections
- Reorganized UI components for better visual hierarchy
- Added visual icons to sidebar sections for improved navigation
- Moved Plot Styling section to the bottom for better workflow

### Improved
- Enhanced consistency of user interface elements
- Optimized sidebar organization for a more intuitive user experience

## [0.3.6-beta] - 2025-03-24
### Changed
- Completely redesigned UI logic for more intuitive control flow
- Centralized all visualization controls in the sidebar
- Made sidebar context-aware with auto-expanding relevant sections based on active tab
- Restructured comparison settings to maintain consistency with single visualization

### Added
- Tab state tracking to optimize user interface
- Automatic sharing of model/patient selections between views
- Improved data row selection with more granular options
- Current configuration summary in comparison view

### Fixed
- Eliminated redundant controls between different views
- Improved user flow between single visualization and comparison modes

## [0.3.5-beta] - 2025-03-24
### Added
- Comprehensive documentation of metrics calculation methodologies
- Detailed formulas and implementation details for RMSE and MAE
- Explanation of correlation calculation and interpretation

### Changed
- Enhanced README with metrics calculation section for better transparency
- Improved documentation for data scientists and researchers

## [0.3.4-beta] - 2025-03-24
### Added
- Enhanced Data Overview table with advanced interactive features
- Row count selector for controlling number of displayed rows
- Data download feature for exporting filtered data to CSV
- Improved column formatting with tooltips and data type hints

### Changed
- Standardized table display format across the application
- Improved data exploration capabilities with sortable and filterable tables

## [0.3.3-beta] - 2025-03-24
### Fixed
- Fixed dark mode compatibility issues with plots and containers
- Improved plot layout with better legend positioning
- Enhanced visual consistency across both light and dark themes
- Optimized plot margins and background for seamless theme integration

### Changed
- Made plot elements (borders, backgrounds) theme-adaptive
- Moved legend to horizontal position above plots to prevent distortion
- Made plot backgrounds transparent to better match the app theme

## [0.3.2-beta] - 2025-03-24
### Fixed
- Fixed dark mode compatibility issue with Comparison Configuration section
- Improved UI elements to adapt to both light and dark themes
- Enhanced visual consistency across different Streamlit color modes

## [0.3.1-beta] - 2025-03-24
### Added
- Per-patient metrics in Mode 2 with best patient highlighting
- Detailed explanations for metrics in Quick Metrics section

### Fixed
- Fixed issue with filtered data not updating in Data Overview section
- Improved organization of app initialization to ensure proper state management
- Enhanced metrics display with better formatting in Mode 1

## [0.3.0-beta] - 2025-03-24
### Added
- Symmetrical aspect ratio for scatter plots providing equal visual scale on axes
- New toggle in Data Overview to switch between filtered and full dataset views
- Per-model metrics in Mode 3 with best model highlighting
- Quick metrics in Data Overview section for selected data

### Changed
- Data Overview section now shows data filtered by current selection
- Enhanced metrics display with better formatting
- Improved visual consistency between plot aspect ratio and data interpretation

## [0.2.0-beta] - 2025-03-24
### Changed
- Improved comparison plots layout with a responsive grid (max 3 plots per row)
- Enhanced visual design with better plot spacing and borders
- Reorganized app structure for better usability
- Improved data overview with more statistics and value ranges
- Added warnings for excessive plot selections

### Fixed
- Fixed issue with crowded plots in comparison view
- Improved plot responsiveness and stability
- Enhanced error handling with more user-friendly messages

## [0.1.0-beta] - 2025-03-24
### Added
- Initial beta release with core visualization functionality
- CSV file upload with format validation
- Interactive scatter plots for model prediction visualization
- Three visualization modes:
  - Mode 1: One model, one patient
  - Mode 2: One model, all patients (color by patient)
  - Mode 3: One patient, all models (color by model)
- Automatic metrics calculation (RMSE, MAE, Correlation)
- Plot customization options (theme, dot size, opacity)
- Custom axis limits option
- Multi-plot comparison view for comparing models or patients
- Sample data and visualization for demonstration

## Changelog Guidelines

### Version Format
- Format: `[X.Y.Z-suffix]` where:
  - X: Major version (breaking changes)
  - Y: Minor version (new features)
  - Z: Patch version (bug fixes)
  - suffix: Indicates pre-release status (e.g., beta, alpha)

### Categories
- **Added**: New features
- **Changed**: Changes to existing functionality
- **Fixed**: Bug fixes
- **Removed**: Removed features
- **Security**: Security fixes
- **Planned**: Upcoming features (for Unreleased section)

### Workflow
1. Add changes to "Unreleased" section during development
2. Move to a versioned section when releasing
3. Update version and date in app metadata