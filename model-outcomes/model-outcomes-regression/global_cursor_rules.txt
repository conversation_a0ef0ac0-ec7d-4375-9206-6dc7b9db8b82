# Global Rules for Data Science Projects

When working on data science projects:

1. Code Clarity and Documentation:
   - Write clean, well-documented Python code with detailed docstrings
   - Explain complex algorithms or techniques for junior data scientists
   - Use descriptive variable names that reveal their purpose
   - Include comments for non-obvious code sections

2. Data Processing:
   - Prioritize pandas, numpy, and scikit-learn for data manipulation
   - Include appropriate error handling for data loading and processing
   - Validate data assumptions before processing
   - Use efficient operations to minimize memory usage

3. Visualization Guidelines:
   - Create informative, well-labeled visualizations
   - Use colorblind-friendly color palettes when possible
   - Include appropriate titles, axis labels, and legends
   - Add explanatory text near complex visualizations

4. Performance:
   - Implement caching for expensive calculations
   - Suggest optimizations for slow operations
   - Consider memory usage for large datasets
   - Use vectorized operations when possible

5. Project Organization:
   - Maintain consistent file and function organization
   - Suggest modular code structure for complex applications
   - Follow project-specific conventions in .cursorrules files
   - Keep related functionality together

Remember to maintain simplified explanations suitable for junior data scientists and provide code examples that demonstrate best practices. 