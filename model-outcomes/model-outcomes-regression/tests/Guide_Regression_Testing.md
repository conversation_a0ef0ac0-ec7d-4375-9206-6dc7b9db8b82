# Comprehensive Guide to Unit Testing - Regression Model Visualizer

## Overview

This guide provides structured approaches to building effective unit tests for the Regression Model Visualizer Streamlit application. It focuses on isolating business logic from UI components while covering regression-specific testing patterns including statistical analysis, feature analysis, and predictive model evaluation.

## Project Structure for Testing

```
model-outcomes-regression/
├── modules/                   # Core business logic modules
│   ├── __init__.py
│   ├── regression_metrics.py  # Regression calculations and feature analysis
│   ├── data_handling.py       # Data processing and validation  
│   ├── visualization.py       # Plotting functions (Plotly-based)
│   ├── ui_components.py       # Reusable UI rendering components
│   ├── ui_sidebar.py          # Sidebar controls and settings
│   └── ui_tabs.py             # Tab display logic and orchestration
├── app.py                     # Main Streamlit application
├── tests/                     # Comprehensive test suite
│   ├── __init__.py
│   ├── conftest.py            # Shared fixtures and test data
│   ├── pytest.ini             # Pytest configuration
│   ├── test_modules/          # Module-specific tests
│   │   ├── test_regression_metrics.py  # Statistical calculations
│   │   ├── test_data_handling.py       # Data processing
│   │   ├── test_visualization.py       # Plotting functions
│   │   ├── test_ui_components.py       # UI component logic
│   │   ├── test_ui_sidebar.py          # Sidebar interactions
│   │   └── test_ui_tabs.py             # Tab orchestration
│   └── Guide_Regression_Testing.md     # This guide
└── static/                    # Static assets
```

## Configuration Files

### pytest.ini
```ini
[pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::RuntimeWarning:streamlit.*
markers =
    unit: mark a unit test
    integration: mark an integration test
    slow: mark a slow test
```

### conftest.py Key Patterns
```python
import pytest
import pandas as pd
import numpy as np

@pytest.fixture
def sample_regression_data_df() -> pd.DataFrame:
    """Provides basic regression data for testing."""
    return pd.DataFrame({
        'real': [10.0, 12.0, 15.0, 8.0, 9.5],
        'pred': [9.8, 12.5, 14.5, 8.2, 9.0],
        'model': ['A'] * 5,
        'patient_number': [1] * 5
    })

@pytest.fixture
def feature_analysis_df() -> pd.DataFrame:
    """Provides data with features for feature analysis testing."""
    return pd.DataFrame({
        'real': [10.0, 12.0, 15.0, 8.0, 9.5, 14.0, 11.0, 13.0],
        'pred': [9.8, 12.5, 14.5, 8.2, 9.0, 13.8, 11.2, 12.8],
        'model': ['A'] * 8,
        'patient_number': [1] * 8,
        'feature_A': [1.2, 2.1, 3.5, 0.8, 1.0, 3.2, 1.8, 2.7]
    })
```

## Testing Patterns for Regression Applications

### 1. Testing Pure Statistical Functions

```python
import pytest
import numpy as np
from modules.regression_metrics import _calculate_regression_metrics_raw

def test_raw_metrics_basic(regression_arrays_basic):
    """Test regression metrics calculation with valid data."""
    real, pred = regression_arrays_basic
    metrics = _calculate_regression_metrics_raw(real, pred)
    
    # Verify structure
    assert isinstance(metrics, dict)
    assert 'rmse' in metrics
    assert 'mae' in metrics  
    assert 'correlation' in metrics
    
    # Verify calculations
    expected_rmse = np.sqrt(np.mean((real - pred)**2))
    assert math.isclose(metrics['rmse'], expected_rmse, rel_tol=1e-6)
```

### 2. Testing Feature Analysis Functions

```python
def test_get_available_features_basic(feature_analysis_df):
    """Test feature detection excludes standard columns."""
    features = get_available_features(feature_analysis_df)
    expected_features = ['feature_A', 'feature_B', 'feature_C']
    assert features == expected_features
    assert len(features) == 3

def test_create_prediction_groups_median_split(feature_analysis_df):
    """Test prediction-based grouping for analysis."""
    result = create_prediction_groups(feature_analysis_df, method="median_split")
    
    assert 'group' in result.columns
    assert 'group_label' in result.columns
    
    # Verify grouping logic
    median_pred = feature_analysis_df['pred'].median()
    expected_groups = (feature_analysis_df['pred'] >= median_pred).astype(int)
    pd.testing.assert_series_equal(result['group'], expected_groups, check_names=False)
```

### 3. Testing Statistical Tests and Effect Sizes

```python
def test_calculate_feature_group_statistics_basic(feature_analysis_two_groups):
    """Test comprehensive statistical analysis."""
    result = calculate_feature_group_statistics(
        feature_analysis_two_groups, 
        feature='feature_A',
        grouping_method='median_split'
    )
    
    # Verify structure
    assert 'grouped_df' in result
    assert 'group_stats' in result  
    assert 'statistical_tests' in result
    assert 'summary' in result
    
    # Verify statistical tests
    statistical_tests = result['statistical_tests']
    assert 'two_group' in statistical_tests
    
    two_group_test = statistical_tests['two_group']
    assert 'test_name' in two_group_test
    assert 'p_value' in two_group_test
    assert 'effect_size' in two_group_test
    
    # Verify effect size calculation
    effect_size = two_group_test['effect_size']
    assert 'name' in effect_size
    assert 'value' in effect_size
    assert 'interpretation' in effect_size
    
    valid_interpretations = ['negligible', 'small', 'medium', 'large']
    assert effect_size['interpretation'] in valid_interpretations
```

### 4. Testing UI Components with Streamlit Mocking

```python
@patch('modules.ui_components.st')
def test_summary_caption_single_compare_models(mock_st, settings_single_compare_models):
    """Test UI component rendering with mocked Streamlit."""
    # Arrange
    metrics = {'rmse': 1, 'mae': 1, 'correlation': 1, 'r_squared': 1}
    total_points = 4
    settings = settings_single_compare_models
    
    # Act
    display_regression_summary(metrics, total_points, active_tab="single", settings=settings)
    
    # Assert
    caption_call = None
    for call_args in mock_st.caption.call_args_list:
        if "📊 **Context:**" in call_args[0][0]:
            caption_call = call_args
            break
    
    assert caption_call is not None
    expected_caption = "📊 **Context:** Metrics for 2 model(s) on Patient 1"
    assert caption_call[0][0] == expected_caption
```

### 5. Testing Data Filtering and Tab Logic

```python
@patch('modules.ui_tabs.interactive_model_scatter')
def test_single_tab_filtering_compare_models(
    mock_scatter, 
    multi_model_patient_df, 
    settings_single_compare_models
):
    """Test data filtering and function calls in tab logic."""
    # Arrange
    input_df = multi_model_patient_df
    settings = settings_single_compare_models
    
    # Act
    display_single_visualization_tab(input_df, settings)
    
    # Assert function called correctly
    mock_scatter.assert_called_once()
    call_args = mock_scatter.call_args[1]
    
    assert call_args['comparison_type'] == 'models'
    
    # Verify DataFrame filtering
    filtered_df = call_args['df']
    assert len(filtered_df) == 4  # Expected filtered size
    assert filtered_df['patient_number'].unique().tolist() == [1]
    assert sorted(filtered_df['model'].unique()) == ['A', 'B']
```

### 6. Testing Visualization Functions

```python
@patch('modules.visualization.px.scatter')
def test_scatter_color_by_model(mock_px_scatter, multi_model_patient_df):
    """Test visualization function calls plotting library correctly."""
    # Arrange
    pre_filtered_df = multi_model_patient_df[
        (multi_model_patient_df['patient_number'] == 1) & 
        (multi_model_patient_df['model'].isin(['A', 'B']))
    ].copy()
    
    # Act
    interactive_model_scatter(
        df=pre_filtered_df, 
        comparison_type='models', 
        title="Test Title"
    )
    
    # Assert
    mock_px_scatter.assert_called_once()
    call_args = mock_px_scatter.call_args[1]
    assert call_args['color'] == 'model'
```

## Regression-Specific Testing Patterns

### 1. Edge Cases for Regression Metrics
```python
def test_raw_metrics_zero_variance(regression_arrays_zero_variance):
    """Test correlation with zero variance data."""
    real, pred = regression_arrays_zero_variance
    metrics = _calculate_regression_metrics_raw(real, pred)
    
    assert not np.isnan(metrics['rmse'])  # RMSE should calculate
    assert not np.isnan(metrics['mae'])   # MAE should calculate  
    assert np.isnan(metrics['correlation'])  # Correlation undefined
```

### 2. Testing Statistical Test Selection
```python
def test_calculate_feature_group_statistics_normality_handling(feature_analysis_df):
    """Test automatic statistical test selection based on normality."""
    result = calculate_feature_group_statistics(feature_analysis_df, feature='feature_A')
    
    if 'two_group' in result['statistical_tests']:
        two_group_test = result['statistical_tests']['two_group']
        assert 'is_normal' in two_group_test
        
        # Test selection should match normality assumption
        if two_group_test['is_normal']:
            assert two_group_test['test_name'] == 'Independent t-test'
            assert two_group_test['effect_size']['name'] == "Cohen's d"
        else:
            assert two_group_test['test_name'] == 'Mann-Whitney U test'
            assert two_group_test['effect_size']['name'] == 'rank-biserial correlation'
```

### 3. Testing Multi-Group Analysis
```python
def test_calculate_feature_group_statistics_quartiles(feature_analysis_quartiles):
    """Test multi-group statistical analysis."""
    result = calculate_feature_group_statistics(
        feature_analysis_quartiles,
        feature='feature_A', 
        grouping_method='quartiles'
    )
    
    assert result['summary']['num_groups'] == 4
    
    # Should use multi-group tests
    statistical_tests = result['statistical_tests']
    assert 'multi_group' in statistical_tests
    
    multi_group_test = statistical_tests['multi_group']
    assert multi_group_test['test_name'] in ['One-way ANOVA', 'Kruskal-Wallis test']
```

## Advanced Testing Techniques

### 1. Parameterized Tests for Multiple Grouping Methods
```python
@pytest.mark.parametrize("method,expected_groups", [
    ("median_split", 2),
    ("quartiles", 4),
    ("error_based", [1, 2])  # Variable groups
])
def test_create_prediction_groups_methods(feature_analysis_df, method, expected_groups):
    """Test different grouping methods."""
    result = create_prediction_groups(feature_analysis_df, method=method)
    
    if isinstance(expected_groups, int):
        assert len(result['group'].unique()) == expected_groups
    else:
        assert len(result['group'].unique()) in expected_groups
```

### 2. Testing P-Value Formatting
```python
@pytest.mark.parametrize("p_value,expected", [
    (0.0001, "1.00e-04"),  # Scientific notation
    (0.005, "0.0050"),     # 4 decimal places
    (0.05, "0.050"),       # 3 decimal places
    (np.nan, "N/A")        # NaN handling
])
def test_format_p_value_ranges(p_value, expected):
    """Test p-value formatting across different ranges."""
    assert format_p_value(p_value) == expected
```

### 3. Testing Session State Integration
```python
@patch.object(st, 'session_state', {})
def test_session_state_integration():
    """Test functions that use Streamlit session state."""
    # Session state mocked as empty dictionary
    # Test session state-dependent logic
    pass
```

## Best Practices for Regression App Testing

### 1. **Statistical Accuracy**
- Always test statistical calculations against known values
- Include edge cases like zero variance, perfect correlation
- Verify statistical test selection logic
- Test effect size calculations and interpretations

### 2. **Data Integrity** 
- Test feature detection logic
- Verify grouping algorithms
- Test handling of missing values (NaN)
- Validate data filtering accuracy

### 3. **UI Component Isolation**
- Mock all Streamlit functions (`@patch('module.st')`)
- Test logic separately from rendering
- Verify correct parameter passing to UI elements
- Test conditional UI logic (tabs, comparisons)

### 4. **Fixture Design**
- Create realistic test data reflecting actual use cases
- Include edge cases in fixtures (identical values, NaN, single groups)
- Use descriptive fixture names
- Design fixtures for specific testing scenarios

### 5. **Error Handling**
- Test empty DataFrames
- Test missing required columns
- Test invalid grouping methods
- Test statistical analysis failures

## Common Testing Patterns

### AAA Pattern (Arrange-Act-Assert)
```python
def test_function_name():
    # Arrange - Set up test data and conditions
    input_data = create_test_data()
    expected_result = calculate_expected()
    
    # Act - Execute the function under test
    actual_result = function_under_test(input_data)
    
    # Assert - Verify the results
    assert actual_result == expected_result
```

### Fixture Usage
```python
def test_with_fixture(sample_regression_data_df):
    """Use fixtures for consistent test data."""
    result = process_data(sample_regression_data_df)
    assert len(result) > 0
```

### Mock Configuration
```python
@patch('module.external_function')
def test_with_mock(mock_external):
    """Configure mocks for external dependencies."""
    mock_external.return_value = expected_value
    result = function_that_calls_external()
    mock_external.assert_called_once()
```

## Running Tests

### Basic Test Execution
```bash
# Activate environment
conda activate modeloutcomes

# Navigate to regression app directory  
cd model-outcomes/model-outcomes-regression

# Run all tests
pytest

# Run with verbose output
pytest -v

# Run specific test file
pytest tests/test_modules/test_regression_metrics.py

# Run specific test function
pytest tests/test_modules/test_regression_metrics.py::test_raw_metrics_basic
```

### Test Coverage
```bash
# Run with coverage report
pytest --cov=modules

# Generate HTML coverage report
pytest --cov=modules --cov-report=html
```

## Debugging Test Issues

### 1. **Streamlit Context Warnings**
```
ScriptRunContext missing warnings can be ignored in test environment
Add to pytest.ini: ignore::RuntimeWarning:streamlit.*
```

### 2. **Mock Configuration Issues**
```python
# Ensure mocks return appropriate types
mock_st.columns.return_value = [MagicMock(), MagicMock()]

# Use side_effect for multiple calls
mock_function.side_effect = [result1, result2, result3]
```

### 3. **Fixture Scope Issues**
```python
# Use appropriate fixture scope
@pytest.fixture(scope="function")  # Default, new instance per test
@pytest.fixture(scope="module")    # Shared across module
```

## Integration with CI/CD

### GitHub Actions Example
```yaml
- name: Run Tests
  run: |
    conda activate modeloutcomes
    cd model-outcomes-regression
    pytest -v --cov=modules
```

## Conclusion

This guide establishes testing patterns specifically tailored for regression model visualization applications. The key principles are:

1. **Separate business logic from UI rendering**
2. **Test statistical calculations thoroughly**
3. **Mock Streamlit components appropriately** 
4. **Use realistic test data and edge cases**
5. **Maintain consistent testing patterns**

Following these patterns ensures robust, maintainable tests that support continued development of the regression visualization application while maintaining statistical accuracy and reliability.

## Future Enhancements

- Add integration tests for complete workflows
- Implement property-based testing for statistical functions
- Add performance benchmarking tests
- Expand test coverage for error conditions
- Add visual regression testing for plots 