# Regression Model Visualizer - Tests (`tests/`)

This directory contains the unit tests for the Regression Model Visualizer application.

## Overview

The primary goal of these tests is to ensure the correctness and stability of the application's core logic, data handling, and utility functions. We follow a philosophy of separating business logic from Streamlit UI components as much as possible to enhance testability.

**Framework**: Tests are written using the `pytest` framework and leverage `unittest.mock` for isolating components.

**Standardization**: This testing approach is **fully aligned** with the classification app, ensuring consistent patterns across the entire model-outcomes project.

## Key Components

*   **`pytest.ini`**: Configures pytest settings, including test paths, file patterns, and warning filters.
*   **`conftest.py`**: Defines shared fixtures used across multiple test files. This includes sample DataFrames (e.g., `multi_model_patient_df`) and mock settings dictionaries (`settings_single_compare_models`, etc.) representing different UI states.
*   **`test_modules/`**: Contains test files organized mirroring the structure of the main `modules/` directory. Each `test_*.py` file focuses on testing the corresponding `*.py` module.
    *   `test_regression_metrics.py`: Tests metric calculation functions.
    *   `test_data_handling.py`: Tests data loading, validation, and filtering.
    *   `test_visualization.py`: Tests visualization helper functions (e.g., axis calculation) and verifies calls to plotting libraries within main plotting functions.
    *   `test_ui_sidebar.py`: Tests the logic within `setup_sidebar_controls` for generating the settings dictionary based on mocked UI interactions.
    *   `test_ui_tabs.py`: Tests the logic within tab display functions (e.g., `display_single_visualization_tab`), focusing on data filtering and calls to sub-components.
    *   `test_ui_components.py`: Tests helper UI functions like `display_regression_summary`, verifying content based on input.
*   **`requirements-test.txt`**: Specifies test-specific dependencies (primarily `pytest`).
*   **`Guide_Regression_Testing.md`**: This detailed guide explaining the testing approach and patterns used in this project.

## Running Tests

1.  Ensure you have the conda environment activated:
    ```bash
    conda activate modeloutcomes
    ```
    The test dependencies (pytest) are already included in the conda environment.
2.  Navigate to the `model-outcomes/model-outcomes-regression/` directory in your terminal.
3.  Run pytest:
    ```bash
    # Run all tests
    pytest
    
    # Run tests with detailed output
    pytest -v
    
    # Run tests for a specific module
    pytest tests/test_modules/test_regression_metrics.py
    
    # Run tests with coverage report
    pytest --cov=modules tests/
    ```

## Contribution

When adding new features or fixing bugs, please include corresponding unit tests. Follow the existing structure and patterns outlined in the `Guide_Regression_Testing.md`.

## Framework Consistency

This regression app testing framework is **fully standardized** with the classification app:

- **Same Framework**: Both apps use pytest exclusively
- **Consistent Patterns**: Fixture usage, naming conventions, and test structure are aligned
- **Unified Documentation**: Both apps follow the same documentation structure and examples
- **Cross-App Maintainability**: Developers can work across both applications with the same testing knowledge

**Total Test Coverage**: 110 tests across both applications (65 regression + 45 classification) all using pytest patterns. 