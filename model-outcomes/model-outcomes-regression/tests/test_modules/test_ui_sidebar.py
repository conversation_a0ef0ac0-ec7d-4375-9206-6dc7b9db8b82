import pytest
import pandas as pd
from unittest.mock import patch, MagicMock

# Module to test
from modules.ui_sidebar import setup_sidebar_controls

# Constants used in the module (ensure these match the module)
ALL_MODELS_OPTION = "-- ALL MODELS --"
ALL_PATIENTS_OPTION = "-- ALL PATIENTS --"

@pytest.fixture
def mock_st():
    """Fixture to mock streamlit functions used in setup_sidebar_controls."""
    # Use patch context manager directly for easier configuration
    with patch('modules.ui_sidebar.st', autospec=True) as mock_st_obj:
        # Configure mocks for widgets to return specified values
        # Use side_effect for functions called multiple times with different args if needed
        # Default returns for simplicity in basic tests
        mock_st_obj.radio.return_value = "Compare Models"
        mock_st_obj.selectbox.return_value = 1 
        mock_st_obj.multiselect.return_value = ['A'] 
        mock_st_obj.number_input.return_value = 8
        mock_st_obj.slider.return_value = 0.7
        mock_st_obj.checkbox.return_value = False
        mock_st_obj.expander.return_value.__enter__.return_value = mock_st_obj # Simulate context manager
        mock_st_obj.expander.return_value.__exit__.return_value = None
        
        # --- FIX: Mock st.columns --- 
        # Make st.columns return an iterable of mocks when called
        mock_col1 = MagicMock()
        mock_col2 = MagicMock()
        mock_st_obj.columns.return_value = [mock_col1, mock_col2] 
        # Allow widgets within columns to be mocked via the column mocks if needed
        # Example: mock_col1.number_input.return_value = 5 

        # Mock session state as a dictionary
        mock_st_obj.session_state = MagicMock() 
        session_state_dict = {} # Use a real dict to back the MagicMock
        mock_st_obj.session_state.__contains__.side_effect = session_state_dict.__contains__
        mock_st_obj.session_state.__getitem__.side_effect = session_state_dict.__getitem__
        mock_st_obj.session_state.__setitem__.side_effect = session_state_dict.__setitem__
        mock_st_obj.session_state.get.side_effect = session_state_dict.get

        yield mock_st_obj

# --- Test Cases for setup_sidebar_controls (Single View) ---

def test_setup_sidebar_single_compare_models(mock_st, multi_model_patient_df):
    """Test settings generation for single view - comparing specific models."""
    # Arrange: Simulate user selecting 'Compare Models', patient 1, models A & C
    mock_st.radio.return_value = "Compare Models"
    mock_st.selectbox.return_value = 1 # Patient Context
    mock_st.multiselect.return_value = ['A', 'C'] # Models to Compare
    
    # Act
    settings = setup_sidebar_controls(multi_model_patient_df, active_tab="single")
    
    # Assert
    assert settings['single_plot_comparison_type'] == 'models'
    assert settings['single_plot_context_patient'] == 1
    assert settings['single_plot_models_to_compare'] == ['A', 'C']
    assert settings['single_plot_context_model'] is None
    assert settings['single_plot_patients_to_compare'] == []
    assert 'available_models' in settings # Ensure base settings are present
    assert 'available_patients' in settings

def test_setup_sidebar_single_compare_patients(mock_st, multi_model_patient_df):
    """Test settings generation for single view - comparing specific patients."""
    # Arrange: Simulate user selecting 'Compare Patients', model B, patients 1 & 2
    mock_st.radio.return_value = "Compare Patients"
    mock_st.selectbox.return_value = 'B' # Model Context
    mock_st.multiselect.return_value = [1, 2] # Patients to Compare
    
    # Act
    settings = setup_sidebar_controls(multi_model_patient_df, active_tab="single")
    
    # Assert
    assert settings['single_plot_comparison_type'] == 'patients'
    assert settings['single_plot_context_model'] == 'B'
    assert settings['single_plot_patients_to_compare'] == [1, 2]
    assert settings['single_plot_context_patient'] is None
    assert settings['single_plot_models_to_compare'] == []

def test_setup_sidebar_single_compare_all_models(mock_st, multi_model_patient_df):
    """Test settings generation for single view - comparing ALL models."""
    # Arrange: Simulate user selecting 'Compare Models', patient 2, ALL models
    mock_st.radio.return_value = "Compare Models"
    mock_st.selectbox.return_value = 2 # Patient Context
    mock_st.multiselect.return_value = [ALL_MODELS_OPTION] # Simulate selecting ALL
    
    # Act
    settings = setup_sidebar_controls(multi_model_patient_df, active_tab="single")
    
    # Assert
    assert settings['single_plot_comparison_type'] == 'models'
    assert settings['single_plot_context_patient'] == 2
    # Check that the *actual* full list is stored, not the '-- ALL --' string
    assert settings['single_plot_models_to_compare'] == ['A', 'B', 'C'] 
    assert settings['single_plot_context_model'] is None
    assert settings['single_plot_patients_to_compare'] == []

def test_setup_sidebar_single_compare_all_patients(mock_st, multi_model_patient_df):
    """Test settings generation for single view - comparing ALL patients."""
    # Arrange: Simulate user selecting 'Compare Patients', model A, ALL patients
    mock_st.radio.return_value = "Compare Patients"
    mock_st.selectbox.return_value = 'A' # Model Context
    mock_st.multiselect.return_value = [ALL_PATIENTS_OPTION] # Simulate selecting ALL
    
    # Act
    settings = setup_sidebar_controls(multi_model_patient_df, active_tab="single")
    
    # Assert
    assert settings['single_plot_comparison_type'] == 'patients'
    assert settings['single_plot_context_model'] == 'A'
    # Check that the *actual* full list is stored
    assert settings['single_plot_patients_to_compare'] == [1, 2] 
    assert settings['single_plot_context_patient'] is None
    assert settings['single_plot_models_to_compare'] == []

def test_setup_sidebar_single_one_item_selected(mock_st, multi_model_patient_df):
    """Test settings generation when only one model/patient is selected."""
    # Arrange: Simulate user selecting 'Compare Models', patient 1, only model B
    mock_st.radio.return_value = "Compare Models"
    mock_st.selectbox.return_value = 1 # Patient Context
    mock_st.multiselect.return_value = ['B'] # Only one model
    
    # Act
    settings = setup_sidebar_controls(multi_model_patient_df, active_tab="single")
    
    # Assert
    assert settings['single_plot_comparison_type'] == 'models'
    assert settings['single_plot_context_patient'] == 1
    assert settings['single_plot_models_to_compare'] == ['B']
    assert settings['single_plot_context_model'] is None
    assert settings['single_plot_patients_to_compare'] == []

# Example test for appearance settings (assuming they are always present)
def test_setup_sidebar_appearance_settings(mock_st, multi_model_patient_df):
     """Test that appearance settings are included."""
     # Arrange (minimal mocking needed as these are unconditional)
     mock_st.radio.side_effect = ["Compare Models", "RMSE"] # First call for type, second for metric
     mock_st.selectbox.side_effect = [1, "plotly_white"] # First for context, second for theme
     mock_st.multiselect.return_value = ['A']
     mock_st.number_input.return_value = 8 # dot_size
     mock_st.slider.return_value = 0.7 # opacity
     
     # Act
     settings = setup_sidebar_controls(multi_model_patient_df, active_tab="single")

     # Assert presence of keys
     assert 'theme' in settings
     assert 'dot_size' in settings
     assert 'opacity' in settings
     assert 'metric_type' in settings
 