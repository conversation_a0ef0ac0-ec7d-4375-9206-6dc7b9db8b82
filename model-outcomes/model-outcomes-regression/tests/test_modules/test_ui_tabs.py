import pytest
import pandas as pd
from unittest.mock import patch, MagicMock, call

# Modules to test
from modules.ui_tabs import display_single_visualization_tab, display_metrics_and_stats

# --- Tests for display_single_visualization_tab ---

@patch('modules.ui_tabs.display_metrics_and_stats') # Patch the dependent function
@patch('modules.ui_tabs.interactive_model_scatter') # Patch the plotting function
def test_single_tab_filtering_compare_models(
    mock_scatter, 
    mock_metrics_display, 
    multi_model_patient_df, 
    settings_single_compare_models # Use fixture from conftest
):
    """Test filtering and function calls when comparing models in single view."""
    # Arrange
    input_df = multi_model_patient_df
    settings = settings_single_compare_models
    
    # Act
    display_single_visualization_tab(input_df, settings)
    
    # Assert mock_scatter was called
    mock_scatter.assert_called_once()
    call_args = mock_scatter.call_args[1] # Get keyword arguments
    
    # Assert correct comparison_type passed
    assert call_args['comparison_type'] == 'models'
    
    # Assert DataFrame passed to scatter is correctly filtered
    filtered_df_scatter = call_args['df']
    assert isinstance(filtered_df_scatter, pd.DataFrame)
    assert len(filtered_df_scatter) == 4 # Model A Patient 1 (2 rows) + Model B Patient 1 (2 rows)
    assert filtered_df_scatter['patient_number'].unique().tolist() == [1]
    assert sorted(filtered_df_scatter['model'].unique()) == ['A', 'B']
    
    # Assert mock_metrics_display was called
    if mock_metrics_display.called:
        call_args_metrics = mock_metrics_display.call_args[1]
        assert call_args_metrics['comparison_type'] == 'models'
        pd.testing.assert_frame_equal(filtered_df_scatter, call_args_metrics['filtered_df'])
        assert call_args_metrics['settings'] == settings

@patch('modules.ui_tabs.display_metrics_and_stats')
@patch('modules.ui_tabs.interactive_model_scatter')
def test_single_tab_filtering_compare_patients(
    mock_scatter, 
    mock_metrics_display, 
    multi_model_patient_df, 
    settings_single_compare_patients
):
    """Test filtering and function calls when comparing patients in single view."""
    # Arrange
    input_df = multi_model_patient_df
    settings = settings_single_compare_patients
    
    # Act
    display_single_visualization_tab(input_df, settings)
    
    # Assert mock_scatter was called
    mock_scatter.assert_called_once()
    call_args = mock_scatter.call_args[1]
    assert call_args['comparison_type'] == 'patients'
    
    # Assert DataFrame passed to scatter is correctly filtered
    filtered_df_scatter = call_args['df']
    assert len(filtered_df_scatter) == 4 # Model C Patient 1 (2 rows) + Model C Patient 2 (2 rows)
    assert filtered_df_scatter['model'].unique().tolist() == ['C']
    assert sorted(filtered_df_scatter['patient_number'].unique()) == [1, 2]

    # Assert mock_metrics_display was called
    if mock_metrics_display.called:
        call_args_metrics = mock_metrics_display.call_args[1]
        assert call_args_metrics['comparison_type'] == 'patients'
        pd.testing.assert_frame_equal(filtered_df_scatter, call_args_metrics['filtered_df'])
        assert call_args_metrics['settings'] == settings

@patch('modules.ui_tabs.display_metrics_and_stats')
@patch('modules.ui_tabs.interactive_model_scatter')
def test_single_tab_filtering_one_item(
    mock_scatter, 
    mock_metrics_display, 
    multi_model_patient_df, 
    settings_single_one_model_one_patient
):
    """Test filtering and function calls when only one item is selected (no comparison)."""
    # Arrange
    input_df = multi_model_patient_df
    settings = settings_single_one_model_one_patient
    
    # Act
    display_single_visualization_tab(input_df, settings)
    
    # Assert mock_scatter was called
    mock_scatter.assert_called_once()
    call_args = mock_scatter.call_args[1]
    # Crucially, comparison_type should be None as only one model is compared
    assert call_args['comparison_type'] is None 
    
    # Assert DataFrame passed to scatter is correctly filtered
    filtered_df_scatter = call_args['df']
    assert len(filtered_df_scatter) == 2 # Model B Patient 1 (2 rows)
    assert filtered_df_scatter['model'].unique().tolist() == ['B']
    assert filtered_df_scatter['patient_number'].unique().tolist() == [1]

    # Assert mock_metrics_display was called
    if mock_metrics_display.called:
        call_args_metrics = mock_metrics_display.call_args[1]
        # Comparison type should also be None for metrics display
        assert call_args_metrics['comparison_type'] is None
        pd.testing.assert_frame_equal(filtered_df_scatter, call_args_metrics['filtered_df'])
        assert call_args_metrics['settings'] == settings

# --- Tests for display_metrics_and_stats ---

@patch('modules.ui_tabs.create_metrics_bar_plot') # Mock the plotting function it calls
@patch('modules.ui_tabs.calculate_regression_metrics') # Mock metrics calculation
@patch('modules.ui_tabs.st') # Mock streamlit elements like columns, markdown, etc.
def test_metrics_display_compare_models(
    mock_st, 
    mock_calculate_metrics, 
    mock_bar_plot,
    settings_single_compare_models # Use settings for theme etc.
):
    """Test display_metrics_and_stats when comparing models."""
    # Arrange
    # Create a pre-filtered df matching the settings
    filtered_df = pd.DataFrame({
        'real': [10, 11, 20, 21],
        'pred': [9.8, 11.2, 19.5, 21.5],
        'model': ['A', 'A', 'B', 'B'],
        'patient_number': [1, 1, 1, 1] 
    })
    comparison_type = 'models'
    settings = settings_single_compare_models
    
    # --- FIX: Configure mock_st.columns to return 3 mocks --- 
    mock_col1, mock_col2, mock_col3 = MagicMock(), MagicMock(), MagicMock()
    mock_st.columns.return_value = [mock_col1, mock_col2, mock_col3]
    
    # Mock return value for metrics calculation
    mock_calculate_metrics.side_effect = [
        {'rmse': 1.0, 'mae': 0.9, 'correlation': 0.95, 'r_squared': 0.9}, # Metrics for Model A
        {'rmse': 1.2, 'mae': 1.1, 'correlation': 0.93, 'r_squared': 0.88}  # Metrics for Model B
    ]
    
    # Act
    display_metrics_and_stats(filtered_df, comparison_type, settings)
    
    # Assert
    # Check metrics calculation called once per model
    assert mock_calculate_metrics.call_count == 2 
    # Check bar plot creation called once
    mock_bar_plot.assert_called_once()
    plot_call_args = mock_bar_plot.call_args[1]
    assert plot_call_args['comparison_type'] == 'models'
    assert len(plot_call_args['metrics_data']) == 2 # Data for 2 models passed
    assert plot_call_args['metrics_data'][0]['Model'] == 'A'
    assert plot_call_args['metrics_data'][1]['Model'] == 'B'
    
    # Check streamlit elements were called (basic checks)
    mock_st.columns.assert_called_once_with([1, 2, 3]) # Verify columns created correctly
    assert mock_st.markdown.call_count >= 1 
    # Check calls within columns (optional but good)
    # Example: mock_col2.dataframe.assert_called_once() 
    # Example: mock_col3.plotly_chart.assert_called_once()

@patch('modules.ui_tabs.create_metrics_bar_plot')
@patch('modules.ui_tabs.calculate_regression_metrics')
@patch('modules.ui_tabs.st')
def test_metrics_display_compare_patients(
    mock_st, 
    mock_calculate_metrics, 
    mock_bar_plot,
    settings_single_compare_patients
):
    """Test display_metrics_and_stats when comparing patients."""
    # Arrange
    filtered_df = pd.DataFrame({
        'real': [12, 13, 32, 33],
        'pred': [12.1, 12.8, 32.5, 33.3],
        'model': ['C', 'C', 'C', 'C'],
        'patient_number': [1, 1, 2, 2] 
    })
    comparison_type = 'patients'
    settings = settings_single_compare_patients
    
    # --- FIX: Configure mock_st.columns to return 3 mocks --- 
    mock_col1, mock_col2, mock_col3 = MagicMock(), MagicMock(), MagicMock()
    mock_st.columns.return_value = [mock_col1, mock_col2, mock_col3]
    
    mock_calculate_metrics.side_effect = [
        {'rmse': 0.5, 'mae': 0.4, 'correlation': 0.99, 'r_squared': 0.98}, # Patient 1
        {'rmse': 0.3, 'mae': 0.2, 'correlation': 0.97, 'r_squared': 0.96}  # Patient 2
    ]
    
    # Act
    display_metrics_and_stats(filtered_df, comparison_type, settings)
    
    # Assert
    assert mock_calculate_metrics.call_count == 2
    mock_bar_plot.assert_called_once()
    plot_call_args = mock_bar_plot.call_args[1]
    assert plot_call_args['comparison_type'] == 'patients'
    assert len(plot_call_args['metrics_data']) == 2
    assert plot_call_args['metrics_data'][0]['Patient'] == 1
    assert plot_call_args['metrics_data'][1]['Patient'] == 2
    # Basic checks for st calls
    mock_st.columns.assert_called_once_with([1, 2, 3])
    assert mock_st.markdown.call_count >= 1

@patch('modules.ui_tabs.create_metrics_bar_plot')
@patch('modules.ui_tabs.calculate_regression_metrics')
@patch('modules.ui_tabs.st')
def test_metrics_display_no_comparison(
    mock_st, 
    mock_calculate_metrics, 
    mock_bar_plot,
    settings_single_one_model_one_patient
):
    """Test display_metrics_and_stats when no comparison is active."""
    # Arrange
    filtered_df = pd.DataFrame({
        'real': [20, 21],
        'pred': [19.5, 21.5],
        'model': ['B', 'B'],
        'patient_number': [1, 1] 
    })
    comparison_type = None # No active comparison
    settings = settings_single_one_model_one_patient
    
    # Act
    display_metrics_and_stats(filtered_df, comparison_type, settings)
    
    # Assert 
    # Grouping logic should be skipped
    mock_calculate_metrics.assert_not_called() 
    mock_bar_plot.assert_not_called()
    assert mock_st.columns.call_count == 0 # Column layout for grouped view shouldn't be created
    # Check that the appropriate caption is shown
    mock_st.caption.assert_called_with("Grouped analysis requires selecting multiple models or patients.") 