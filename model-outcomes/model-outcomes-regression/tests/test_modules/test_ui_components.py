import pytest
from unittest.mock import patch, MagicMock

# Module to test
from modules.ui_components import display_regression_summary

@patch('modules.ui_components.st') # Mock streamlit functions
def test_summary_caption_single_compare_models(mock_st, settings_single_compare_models):
    """Test context caption for single view comparing models."""
    # Arrange
    metrics = {'rmse': 1, 'mae': 1, 'correlation': 1, 'r_squared': 1}
    total_points = 4
    settings = settings_single_compare_models
    active_tab = "single"
    
    # Act
    display_regression_summary(metrics, total_points, active_tab=active_tab, settings=settings)
    
    # Assert
    # Find the call to st.caption that contains the context message
    caption_call = None
    for call_args in mock_st.caption.call_args_list:
        if "📊 **Context:**" in call_args[0][0]:
            caption_call = call_args
            break
    
    assert caption_call is not None, "Context caption call not found"
    expected_caption = "📊 **Context:** Metrics for 2 model(s) on Patient 1"
    assert caption_call[0][0] == expected_caption

@patch('modules.ui_components.st')
def test_summary_caption_single_compare_patients(mock_st, settings_single_compare_patients):
    """Test context caption for single view comparing patients."""
    # Arrange
    metrics = {'rmse': 1, 'mae': 1, 'correlation': 1, 'r_squared': 1}
    total_points = 4
    settings = settings_single_compare_patients
    active_tab = "single"
    
    # Act
    display_regression_summary(metrics, total_points, active_tab=active_tab, settings=settings)
    
    # Assert
    caption_call = None
    for call_args in mock_st.caption.call_args_list:
        if "📊 **Context:**" in call_args[0][0]:
            caption_call = call_args
            break
    assert caption_call is not None, "Context caption call not found"
    expected_caption = "📊 **Context:** Metrics for 2 patient(s) on Model C"
    assert caption_call[0][0] == expected_caption

@patch('modules.ui_components.st')
def test_summary_caption_single_one_item(mock_st, settings_single_one_model_one_patient):
    """Test context caption for single view with only one item selected."""
    # Arrange
    metrics = {'rmse': 1, 'mae': 1, 'correlation': 1, 'r_squared': 1}
    total_points = 2
    settings = settings_single_one_model_one_patient
    active_tab = "single"
    
    # Act
    display_regression_summary(metrics, total_points, active_tab=active_tab, settings=settings)
    
    # Assert
    caption_call = None
    for call_args in mock_st.caption.call_args_list:
        if "📊 **Context:**" in call_args[0][0]:
            caption_call = call_args
            break
    assert caption_call is not None, "Context caption call not found"
    expected_caption = "📊 **Context:** Metrics for Model B on Patient 1"
    assert caption_call[0][0] == expected_caption

@patch('modules.ui_components.st')
def test_summary_caption_comparison_tab(mock_st, settings_comparison_tab):
    """Test context caption for the comparison tab."""
     # Arrange
    metrics = {'rmse': 1, 'mae': 1, 'correlation': 1, 'r_squared': 1}
    total_points = 4
    settings = settings_comparison_tab # Use comparison tab settings fixture
    active_tab = "comparison"
    
    # Act
    display_regression_summary(metrics, total_points, active_tab=active_tab, settings=settings)
    
    # Assert
    caption_call = None
    for call_args in mock_st.caption.call_args_list:
        if "📊 **Context:**" in call_args[0][0]:
            caption_call = call_args
            break
    assert caption_call is not None, "Context caption call not found"
    expected_caption = "📊 **Context:** Aggregate metrics for comparing models (2 selected) on Patient **2**"
    assert caption_call[0][0] == expected_caption 