"""
Unit tests for the visualization module's core functions.
"""
import pytest
import pandas as pd
import numpy as np
from unittest.mock import patch, MagicMock

# Import the functions to be tested
from modules.visualization import interactive_model_scatter, create_metrics_bar_plot


# === Tests for interactive_model_scatter (focus on coloring and metrics) ===

@patch('modules.visualization.calculate_regression_metrics') # Mock metrics calculation
@patch('modules.visualization.px.scatter') # Mock the core plotly express function
def test_scatter_color_by_model(
    mock_px_scatter,
    mock_calc_metrics,
    multi_model_patient_df # Use the fixture with multiple models/patients
):
    """Test if scatter plot is called with color='model' when comparison_type='models'."""
    # Arrange
    # Simulate a pre-filtered DataFrame (e.g., comparing models A & B for patient 1)
    pre_filtered_df = multi_model_patient_df[
        (multi_model_patient_df['patient_number'] == 1) & 
        (multi_model_patient_df['model'].isin(['A', 'B']))
    ].copy()
    pre_filtered_df_with_index = pre_filtered_df.reset_index().rename(columns={'index': 'point_index'})
    mock_calc_metrics.return_value = {'rmse': 1, 'mae': 1, 'correlation': 1, 'r_squared': 1} # Dummy metrics
    
    # Act
    interactive_model_scatter(
        df=pre_filtered_df, 
        comparison_type='models', 
        title="Test Title"
    )
    
    # Assert
    mock_px_scatter.assert_called_once()
    call_args = mock_px_scatter.call_args[1] # Get keyword args
    assert call_args['color'] == 'model'
    pd.testing.assert_frame_equal(pre_filtered_df_with_index, mock_px_scatter.call_args[0][0]) 
    mock_calc_metrics.assert_called_once()
    pd.testing.assert_frame_equal(pre_filtered_df_with_index, mock_calc_metrics.call_args[0][0])


@patch('modules.visualization.calculate_regression_metrics')
@patch('modules.visualization.px.scatter')
def test_scatter_color_by_patient(
    mock_px_scatter,
    mock_calc_metrics,
    multi_model_patient_df
):
    """Test if scatter plot is called with color='patient_number' and conversion."""
    # Arrange
    # Simulate a pre-filtered DataFrame (e.g., comparing patients 1 & 2 for model C)
    pre_filtered_df = multi_model_patient_df[
        (multi_model_patient_df['model'] == 'C') & 
        (multi_model_patient_df['patient_number'].isin([1, 2]))
    ].copy()
    pre_filtered_df_with_index = pre_filtered_df.reset_index().rename(columns={'index': 'point_index'})
    mock_calc_metrics.return_value = {'rmse': 1, 'mae': 1, 'correlation': 1, 'r_squared': 1}
    
    # Act
    interactive_model_scatter(
        df=pre_filtered_df, 
        comparison_type='patients', 
        title="Test Title"
    )
    
    # Assert
    mock_px_scatter.assert_called_once()
    call_args = mock_px_scatter.call_args[1]
    assert call_args['color'] == 'patient_number'
    
    df_passed_to_plot = mock_px_scatter.call_args[0][0]
    
    # Check shape and columns
    assert df_passed_to_plot.shape == pre_filtered_df_with_index.shape
    assert sorted(df_passed_to_plot.columns) == sorted(pre_filtered_df_with_index.columns)
    # Check patient_number column type in the plotted df
    assert pd.api.types.is_string_dtype(df_passed_to_plot['patient_number'])
    # Compare values of other columns
    other_cols = [col for col in pre_filtered_df_with_index.columns if col != 'patient_number']
    pd.testing.assert_frame_equal(
        pre_filtered_df_with_index[other_cols],
        df_passed_to_plot[other_cols]
    )

    # Assert metrics called with df that HAS the index
    mock_calc_metrics.assert_called_once()
    df_passed_to_metrics = mock_calc_metrics.call_args[0][0]

    # Check shape and columns for metrics df
    assert df_passed_to_metrics.shape == pre_filtered_df_with_index.shape
    assert sorted(df_passed_to_metrics.columns) == sorted(pre_filtered_df_with_index.columns)
    # Check dtypes
    assert pd.api.types.is_string_dtype(df_passed_to_metrics['patient_number'])
    assert pd.api.types.is_integer_dtype(df_passed_to_metrics['point_index'])
    assert pd.api.types.is_numeric_dtype(df_passed_to_metrics['real'])
    assert pd.api.types.is_numeric_dtype(df_passed_to_metrics['pred'])
    assert pd.api.types.is_string_dtype(df_passed_to_metrics['model'])
    
    # Compare values using numpy after type alignment for patient_number
    expected_values = pre_filtered_df_with_index.copy()
    # Ensure patient_number is string in the expected df for comparison
    if 'patient_number' in expected_values.columns:
         expected_values['patient_number'] = expected_values['patient_number'].astype(str) 
    
    np.testing.assert_array_equal(expected_values.values, df_passed_to_metrics.values)


@patch('modules.visualization.calculate_regression_metrics')
@patch('modules.visualization.px.scatter')
def test_scatter_no_color(
    mock_px_scatter,
    mock_calc_metrics,
    multi_model_patient_df
):
    """Test if scatter plot is called without color arg when comparison_type=None."""
    # Arrange
    pre_filtered_df = multi_model_patient_df[
        (multi_model_patient_df['model'] == 'B') & 
        (multi_model_patient_df['patient_number'] == 1)
    ].copy()
    pre_filtered_df_with_index = pre_filtered_df.reset_index().rename(columns={'index': 'point_index'})
    mock_calc_metrics.return_value = {'rmse': 1, 'mae': 1, 'correlation': 1, 'r_squared': 1}
    
    # Act
    interactive_model_scatter(
        df=pre_filtered_df, 
        comparison_type=None, 
        title="Test Title"
    )
    
    # Assert
    mock_px_scatter.assert_called_once()
    call_args = mock_px_scatter.call_args[1]
    assert 'color' not in call_args or call_args['color'] is None
    # Assert df passed to scatter HAS the index
    pd.testing.assert_frame_equal(pre_filtered_df_with_index, mock_px_scatter.call_args[0][0])
    # Assert metrics called with df that HAS the index
    mock_calc_metrics.assert_called_once()
    pd.testing.assert_frame_equal(pre_filtered_df_with_index, mock_calc_metrics.call_args[0][0])


@patch('modules.visualization.calculate_regression_metrics')
@patch('modules.visualization.px.scatter')
def test_scatter_metrics_calculation(mock_px_scatter, mock_calc_metrics, multi_model_patient_df):
    """Verify that metrics are calculated on the passed DataFrame."""
    # Arrange
    pre_filtered_df = multi_model_patient_df.head(5).copy()
    pre_filtered_df_with_index = pre_filtered_df.reset_index().rename(columns={'index': 'point_index'})
    mock_calc_metrics.return_value = {'rmse': 1, 'mae': 1, 'correlation': 1, 'r_squared': 1}
    
    # Act
    interactive_model_scatter(df=pre_filtered_df, comparison_type=None, title="Test")
    
    # Assert
    mock_calc_metrics.assert_called_once()
    # Check that the DataFrame passed to metrics calculation is the same as input (with index)
    pd.testing.assert_frame_equal(pre_filtered_df_with_index, mock_calc_metrics.call_args[0][0]) 


@patch('modules.visualization.px.scatter')
def test_scatter_auto_axis_limits(mock_px_scatter, multi_model_patient_df):
    """Test that scatter plot correctly calculates automatic axis limits."""
    # Arrange
    pre_filtered_df = multi_model_patient_df.head(5).copy()
    mock_fig = MagicMock()
    mock_px_scatter.return_value = mock_fig
    
    # Act
    result = interactive_model_scatter(df=pre_filtered_df, comparison_type=None, title="Test")
    
    # Assert
    mock_px_scatter.assert_called_once()
    # Verify update_layout was called to set axis ranges
    mock_fig.update_layout.assert_called_once()
    layout_args = mock_fig.update_layout.call_args[1]
    
    # Check that yaxis and xaxis have range settings
    assert 'yaxis' in layout_args
    assert 'xaxis' in layout_args
    assert 'range' in layout_args['yaxis']
    assert 'range' in layout_args['xaxis']


def test_scatter_empty_dataframe():
    """Test scatter plot handles empty DataFrame gracefully."""
    # Arrange
    empty_df = pd.DataFrame(columns=['model', 'patient_number', 'real', 'pred'])
    
    # Act
    result = interactive_model_scatter(df=empty_df, comparison_type=None, title="Empty Test")
    
    # Assert - should return a figure without errors
    assert result is not None
    # Verify it's a plotly figure object
    assert hasattr(result, 'update_layout')


def test_scatter_missing_required_columns():
    """Test scatter plot handles missing required columns gracefully."""
    # Arrange
    invalid_df = pd.DataFrame({'x': [1, 2, 3], 'y': [4, 5, 6]})
    
    # Act
    result = interactive_model_scatter(df=invalid_df, comparison_type=None, title="Invalid Test")
    
    # Assert - should return a figure with error message
    assert result is not None
    assert hasattr(result, 'update_layout')


# === Tests for create_metrics_bar_plot ===

def test_metrics_bar_plot_basic():
    """Test basic functionality of metrics bar plot."""
    # Arrange
    metrics_data = [
        {"Model": "A", "RMSE": 1.2, "MAE": 0.8, "Data Points": 100},
        {"Model": "B", "RMSE": 1.5, "MAE": 1.0, "Data Points": 95}
    ]
    
    # Act
    result = create_metrics_bar_plot(
        metrics_data=metrics_data,
        metric_type="RMSE",
        comparison_type="models"
    )
    
    # Assert
    assert result is not None
    assert hasattr(result, 'update_layout')


def test_metrics_bar_plot_patients():
    """Test metrics bar plot for patient comparison."""
    # Arrange
    metrics_data = [
        {"Patient": 1, "RMSE": 1.2, "MAE": 0.8, "Data Points": 50},
        {"Patient": 2, "RMSE": 1.5, "MAE": 1.0, "Data Points": 45}
    ]
    
    # Act
    result = create_metrics_bar_plot(
        metrics_data=metrics_data,
        metric_type="MAE",
        comparison_type="patients"
    )
    
    # Assert
    assert result is not None
    assert hasattr(result, 'update_layout')


def test_metrics_bar_plot_empty_data():
    """Test metrics bar plot handles empty data gracefully."""
    # Arrange
    empty_data = []
    
    # Act
    result = create_metrics_bar_plot(
        metrics_data=empty_data,
        metric_type="RMSE",
        comparison_type="models"
    )
    
    # Assert
    assert result is not None
    assert hasattr(result, 'update_layout') 