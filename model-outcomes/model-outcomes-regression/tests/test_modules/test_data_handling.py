"""
Unit tests for the data_handling module, focusing on get_filtered_data.
"""

import pytest
import pandas as pd
import numpy as np

# Import the function to be tested
from modules.data_handling import get_filtered_data

# Test cases using the multi_model_patient_df fixture

def test_get_filtered_data_mode1(multi_model_patient_df):
    """Test filtering for Mode 1 (specific model and patient)."""
    df = multi_model_patient_df
    settings = {'selected_mode': 1, 'selected_model': 'B', 'selected_patient': 2}
    filtered = get_filtered_data(df, settings=settings)
    assert len(filtered) == 2
    assert all(filtered['model'] == 'B')
    assert all(filtered['patient_number'] == 2)
    assert 'point_index' in filtered.columns # Check index column is added

def test_get_filtered_data_mode2(multi_model_patient_df):
    """Test filtering for Mode 2 (specific model, all patients)."""
    df = multi_model_patient_df
    settings = {'selected_mode': 2, 'selected_model': 'A', 'selected_patient': 'All'}
    filtered = get_filtered_data(df, settings=settings)
    assert len(filtered) == 4
    assert all(filtered['model'] == 'A')

def test_get_filtered_data_mode3(multi_model_patient_df):
    """Test filtering for Mode 3 (specific patient, all models)."""
    df = multi_model_patient_df
    settings = {'selected_mode': 3, 'selected_model': 'All', 'selected_patient': 1}
    filtered = get_filtered_data(df, settings=settings)
    assert len(filtered) == 6
    assert all(filtered['patient_number'] == 1)

def test_get_filtered_data_no_mode_all_selected(multi_model_patient_df):
    """Test filtering when no mode is specified but model/patient are 'All'."""
    df = multi_model_patient_df
    settings = {'selected_model': 'All', 'selected_patient': 'All'}
    filtered = get_filtered_data(df, settings=settings)
    assert len(filtered) == len(df) # Should return the full dataframe

def test_get_filtered_data_no_mode_specific_model(multi_model_patient_df):
    """Test filtering by specific model when no mode is set (should not filter based on current logic)."""
    df = multi_model_patient_df
    settings = {'selected_model': 'C', 'selected_patient': 'All'}
    # Mode is implicitly None, so no filtering should happen based *only* on model/patient
    # unless a specific mode (1, 2, or 3) is provided.
    filtered = get_filtered_data(df, settings=settings) 
    assert len(filtered) == len(df) # Expect full dataframe if mode isn't 1, 2, or 3

def test_get_filtered_data_mode1_not_found(multi_model_patient_df):
    """Test filtering for Mode 1 when model/patient combo doesn't exist."""
    df = multi_model_patient_df
    settings = {'selected_mode': 1, 'selected_model': 'A', 'selected_patient': 3} # Patient 3 doesn't exist
    filtered = get_filtered_data(df, settings=settings)
    assert len(filtered) == 0

def test_get_filtered_data_empty_df():
    """Test filtering with an empty DataFrame."""
    empty_df = pd.DataFrame({'real': [], 'pred': [], 'model': [], 'patient_number': []})
    settings = {'selected_mode': 1, 'selected_model': 'A', 'selected_patient': 1}
    filtered = get_filtered_data(empty_df, settings=settings)
    assert len(filtered) == 0 