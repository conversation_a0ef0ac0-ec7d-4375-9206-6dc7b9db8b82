"""
Unit tests for the regression_metrics module.
"""

import pytest
import numpy as np
import pandas as pd
import math

# Import the functions to be tested
from modules.regression_metrics import (
    calculate_regression_metrics, 
    _calculate_regression_metrics_raw,
    get_available_features,
    create_prediction_groups,
    calculate_feature_group_statistics,
    format_p_value
)

# === Tests for _calculate_regression_metrics_raw ===

def test_raw_metrics_basic(regression_arrays_basic):
    """Test calculation with basic valid arrays."""
    real, pred = regression_arrays_basic
    metrics = _calculate_regression_metrics_raw(real, pred)
    assert isinstance(metrics, dict)
    assert 'rmse' in metrics
    assert 'mae' in metrics
    assert 'correlation' in metrics
    # Add approximate assertions based on expected values for this fixture
    # Example: RMSE calculation -> sqrt(mean([(10-9.8)^2, (12-12.5)^2, ...]))
    expected_rmse = np.sqrt(np.mean((real - pred)**2))
    expected_mae = np.mean(np.abs(real - pred))
    expected_corr = np.corrcoef(real, pred)[0, 1]
    assert math.isclose(metrics['rmse'], expected_rmse, rel_tol=1e-6)
    assert math.isclose(metrics['mae'], expected_mae, rel_tol=1e-6)
    assert math.isclose(metrics['correlation'], expected_corr, rel_tol=1e-6)

def test_raw_metrics_perfect(regression_arrays_perfect):
    """Test calculation with perfect predictions."""
    real, pred = regression_arrays_perfect
    metrics = _calculate_regression_metrics_raw(real, pred)
    assert math.isclose(metrics['rmse'], 0.0, abs_tol=1e-9)
    assert math.isclose(metrics['mae'], 0.0, abs_tol=1e-9)
    assert math.isclose(metrics['correlation'], 1.0, rel_tol=1e-9)

def test_raw_metrics_with_nan(regression_arrays_with_nan):
    """Test calculation ignoring NaN values."""
    real, pred = regression_arrays_with_nan
    metrics = _calculate_regression_metrics_raw(real, pred)
    # Calculate expected values manually ignoring NaNs
    valid_mask = ~np.isnan(real) & ~np.isnan(pred)
    real_valid = real[valid_mask]
    pred_valid = pred[valid_mask]
    expected_rmse = np.sqrt(np.mean((real_valid - pred_valid)**2))
    expected_mae = np.mean(np.abs(real_valid - pred_valid))
    expected_corr = np.corrcoef(real_valid, pred_valid)[0, 1]
    assert math.isclose(metrics['rmse'], expected_rmse, rel_tol=1e-6)
    assert math.isclose(metrics['mae'], expected_mae, rel_tol=1e-6)
    assert math.isclose(metrics['correlation'], expected_corr, rel_tol=1e-6)

def test_raw_metrics_empty(regression_arrays_empty):
    """Test calculation with empty arrays."""
    real, pred = regression_arrays_empty
    metrics = _calculate_regression_metrics_raw(real, pred)
    assert np.isnan(metrics['rmse'])
    assert np.isnan(metrics['mae'])
    assert np.isnan(metrics['correlation'])

def test_raw_metrics_zero_variance(regression_arrays_zero_variance):
    """Test correlation calculation when one array has zero variance."""
    real, pred = regression_arrays_zero_variance
    metrics = _calculate_regression_metrics_raw(real, pred)
    assert not np.isnan(metrics['rmse']) # RMSE and MAE should still calculate
    assert not np.isnan(metrics['mae'])
    assert np.isnan(metrics['correlation']) # Correlation should be NaN

# === Tests for calculate_regression_metrics (DataFrame wrapper) ===

def test_df_metrics_basic(sample_regression_data_df, regression_arrays_basic):
    """Test the DataFrame wrapper function with valid data."""
    df = sample_regression_data_df
    metrics = calculate_regression_metrics(df)
    # Compare against the raw calculation using the equivalent numpy arrays
    real_np, pred_np = regression_arrays_basic
    raw_metrics = _calculate_regression_metrics_raw(real_np, pred_np)
    assert math.isclose(metrics['rmse'], raw_metrics['rmse'], rel_tol=1e-6)
    assert math.isclose(metrics['mae'], raw_metrics['mae'], rel_tol=1e-6)
    assert math.isclose(metrics['correlation'], raw_metrics['correlation'], rel_tol=1e-6)

def test_df_metrics_empty():
    """Test the DataFrame wrapper with an empty DataFrame."""
    empty_df = pd.DataFrame({'real': [], 'pred': []})
    metrics = calculate_regression_metrics(empty_df)
    assert np.isnan(metrics['rmse'])
    assert np.isnan(metrics['mae'])
    assert np.isnan(metrics['correlation'])

def test_df_metrics_missing_columns():
    """Test the DataFrame wrapper with missing required columns."""
    df_missing_pred = pd.DataFrame({'real': [1, 2, 3]})
    metrics_missing_pred = calculate_regression_metrics(df_missing_pred)
    assert np.isnan(metrics_missing_pred['rmse'])
    assert np.isnan(metrics_missing_pred['mae'])
    assert np.isnan(metrics_missing_pred['correlation'])

    df_missing_real = pd.DataFrame({'pred': [1, 2, 3]})
    metrics_missing_real = calculate_regression_metrics(df_missing_real)
    assert np.isnan(metrics_missing_real['rmse'])
    assert np.isnan(metrics_missing_real['mae'])
    assert np.isnan(metrics_missing_real['correlation'])

def test_df_metrics_with_nan_in_df():
    """Test the DataFrame wrapper handles NaNs within columns."""
    df_nan = pd.DataFrame({
        'real': [10.0, 12.0, np.nan, 8.0, 9.5],
        'pred': [9.8, 12.5, 14.5, np.nan, 9.0]
    })
    metrics = calculate_regression_metrics(df_nan)
    # Expected calculation ignores rows where either real or pred is NaN
    real_valid = np.array([10.0, 12.0, 9.5])
    pred_valid = np.array([9.8, 12.5, 9.0])
    expected_rmse = np.sqrt(np.mean((real_valid - pred_valid)**2))
    expected_mae = np.mean(np.abs(real_valid - pred_valid))
    expected_corr = np.corrcoef(real_valid, pred_valid)[0, 1]

    assert math.isclose(metrics['rmse'], expected_rmse, rel_tol=1e-6)
    assert math.isclose(metrics['mae'], expected_mae, rel_tol=1e-6)
    assert math.isclose(metrics['correlation'], expected_corr, rel_tol=1e-6)

 

# === Tests for get_available_features ===

def test_get_available_features_basic(feature_analysis_df):
    """Test getting available features from a valid DataFrame."""
    features = get_available_features(feature_analysis_df)
    expected_features = ['feature_A', 'feature_B', 'feature_C']
    assert features == expected_features
    assert len(features) == 3

def test_get_available_features_empty_df():
    """Test with empty DataFrame."""
    empty_df = pd.DataFrame()
    features = get_available_features(empty_df)
    assert features == []

def test_get_available_features_none():
    """Test with None input."""
    features = get_available_features(None)
    assert features == []

def test_get_available_features_only_standard_columns():
    """Test with DataFrame containing only standard columns."""
    df = pd.DataFrame({
        'real': [1, 2, 3],
        'pred': [1.1, 2.1, 3.1],
        'model': ['A'] * 3,
        'patient_number': [1] * 3
    })
    features = get_available_features(df)
    assert features == []

def test_get_available_features_mixed_columns():
    """Test with mixed standard and feature columns."""
    df = pd.DataFrame({
        'real': [1, 2, 3],
        'pred': [1.1, 2.1, 3.1],
        'model': ['A'] * 3,
        'patient_number': [1] * 3,
        'custom_feature': [0.1, 0.2, 0.3],
        'group': [0, 1, 0],  # Should be excluded
        'point_index': [1, 2, 3]  # Should be excluded
    })
    features = get_available_features(df)
    assert features == ['custom_feature']

# === Tests for create_prediction_groups ===

def test_create_prediction_groups_median_split(feature_analysis_df):
    """Test median split grouping method."""
    result = create_prediction_groups(feature_analysis_df, method="median_split")
    
    assert 'group' in result.columns
    assert 'group_label' in result.columns
    assert len(result) == len(feature_analysis_df)
    
    # Check that groups are 0 and 1
    unique_groups = sorted(result['group'].unique())
    assert unique_groups == [0, 1]
    
    # Verify grouping logic
    median_pred = feature_analysis_df['pred'].median()
    expected_groups = (feature_analysis_df['pred'] >= median_pred).astype(int)
    pd.testing.assert_series_equal(result['group'], expected_groups, check_names=False)

def test_create_prediction_groups_error_based(feature_analysis_df):
    """Test error-based grouping method."""
    result = create_prediction_groups(feature_analysis_df, method="error_based")
    
    assert 'group' in result.columns
    assert 'group_label' in result.columns
    assert len(result) == len(feature_analysis_df)
    
    # Check that abs_error column is removed
    assert 'abs_error' not in result.columns
    
    # Verify groups - we should have at least 1 group, possibly 2
    unique_groups = sorted(result['group'].unique())
    assert len(unique_groups) >= 1
    assert len(unique_groups) <= 2
    assert all(group in [0, 1] for group in unique_groups)

def test_create_prediction_groups_quartiles(feature_analysis_quartiles):
    """Test quartiles grouping method."""
    result = create_prediction_groups(feature_analysis_quartiles, method="quartiles")
    
    assert 'group' in result.columns
    assert 'group_label' in result.columns
    assert len(result) == len(feature_analysis_quartiles)
    
    # Check that we have 4 groups (0, 1, 2, 3)
    unique_groups = sorted(result['group'].unique())
    assert unique_groups == [0, 1, 2, 3]

def test_create_prediction_groups_empty_df():
    """Test with empty DataFrame."""
    empty_df = pd.DataFrame()
    result = create_prediction_groups(empty_df)
    assert result.empty

def test_create_prediction_groups_none():
    """Test with None input."""
    result = create_prediction_groups(None)
    assert result.empty

def test_create_prediction_groups_missing_columns():
    """Test with missing required columns."""
    df_no_pred = pd.DataFrame({'real': [1, 2, 3]})
    result = create_prediction_groups(df_no_pred)
    # Should return original DataFrame
    pd.testing.assert_frame_equal(result, df_no_pred)

def test_create_prediction_groups_invalid_method(feature_analysis_df):
    """Test with invalid grouping method."""
    result = create_prediction_groups(feature_analysis_df, method="invalid_method")
    # Should return DataFrame without group columns
    assert 'group' not in result.columns
    assert 'group_label' not in result.columns

def test_create_prediction_groups_identical_predictions(feature_analysis_identical_predictions):
    """Test with identical predictions (edge case)."""
    result = create_prediction_groups(feature_analysis_identical_predictions, method="median_split")
    
    assert 'group' in result.columns
    assert 'group_label' in result.columns
    # All predictions are identical, so all should be in same group
    unique_groups = result['group'].unique()
    assert len(unique_groups) == 1

# === Tests for format_p_value ===

def test_format_p_value_ranges():
    """Test p-value formatting for different ranges."""
    assert format_p_value(0.0001) == "1.00e-04"  # Scientific notation
    assert format_p_value(0.005) == "0.0050"     # 4 decimal places
    assert format_p_value(0.05) == "0.050"       # 3 decimal places
    assert format_p_value(0.123) == "0.123"      # 3 decimal places

def test_format_p_value_edge_cases():
    """Test p-value formatting edge cases."""
    assert format_p_value(np.nan) == "N/A"
    assert format_p_value(pd.NA) == "N/A"
    assert format_p_value(0.0) == "0.00e+00"
    assert format_p_value(1.0) == "1.000"

# === Tests for calculate_feature_group_statistics ===

def test_calculate_feature_group_statistics_basic(feature_analysis_two_groups):
    """Test basic feature group statistics calculation."""
    result = calculate_feature_group_statistics(
        feature_analysis_two_groups, 
        feature='feature_A',
        grouping_method='median_split'
    )
    
    # Check main structure
    assert 'grouped_df' in result
    assert 'group_stats' in result
    assert 'statistical_tests' in result
    assert 'summary' in result
    assert 'error' not in result
    
    # Check grouped DataFrame
    grouped_df = result['grouped_df']
    assert 'group' in grouped_df.columns
    assert 'group_label' in grouped_df.columns
    
    # Check group statistics
    group_stats = result['group_stats']
    assert len(group_stats) == 2  # Two groups
    assert 'group' in group_stats.columns
    assert 'group_label' in group_stats.columns
    
    # Check statistical tests
    statistical_tests = result['statistical_tests']
    assert 'two_group' in statistical_tests
    two_group_test = statistical_tests['two_group']
    assert 'test_name' in two_group_test
    assert 'p_value' in two_group_test
    assert 'significant' in two_group_test
    assert 'effect_size' in two_group_test
    
    # Check summary
    summary = result['summary']
    assert summary['feature_name'] == 'feature_A'
    assert summary['grouping_method'] == 'median_split'
    assert summary['num_groups'] == 2

def test_calculate_feature_group_statistics_quartiles(feature_analysis_quartiles):
    """Test feature group statistics with quartiles method."""
    result = calculate_feature_group_statistics(
        feature_analysis_quartiles,
        feature='feature_A',
        grouping_method='quartiles'
    )
    
    assert 'error' not in result
    assert result['summary']['num_groups'] == 4
    
    # Should have multi-group test instead of two-group
    statistical_tests = result['statistical_tests']
    assert 'multi_group' in statistical_tests
    multi_group_test = statistical_tests['multi_group']
    assert 'test_name' in multi_group_test
    assert multi_group_test['test_name'] in ['One-way ANOVA', 'Kruskal-Wallis test']

def test_calculate_feature_group_statistics_empty_df():
    """Test with empty DataFrame."""
    result = calculate_feature_group_statistics(pd.DataFrame(), 'feature_A')
    assert 'error' in result
    assert result['error'] == 'Empty DataFrame'

def test_calculate_feature_group_statistics_none():
    """Test with None input."""
    result = calculate_feature_group_statistics(None, 'feature_A')
    assert 'error' in result
    assert result['error'] == 'Empty DataFrame'

def test_calculate_feature_group_statistics_missing_feature(feature_analysis_df):
    """Test with missing feature column."""
    result = calculate_feature_group_statistics(feature_analysis_df, 'nonexistent_feature')
    assert 'error' in result
    assert 'not found' in result['error']

def test_calculate_feature_group_statistics_with_nan(feature_analysis_df_with_nan):
    """Test feature group statistics with NaN values."""
    result = calculate_feature_group_statistics(
        feature_analysis_df_with_nan,
        feature='feature_A'
    )
    
    # Should handle NaN values gracefully
    assert 'error' not in result
    assert 'grouped_df' in result
    
    # Check that statistics are calculated despite NaN values
    group_stats = result['group_stats']
    assert len(group_stats) >= 1

def test_calculate_feature_group_statistics_effect_sizes(feature_analysis_two_groups):
    """Test that effect sizes are calculated correctly."""
    result = calculate_feature_group_statistics(
        feature_analysis_two_groups,
        feature='feature_A'
    )
    
    statistical_tests = result['statistical_tests']
    assert 'two_group' in statistical_tests
    
    effect_size = statistical_tests['two_group']['effect_size']
    assert 'name' in effect_size
    assert 'value' in effect_size
    assert 'interpretation' in effect_size
    
    # Effect size interpretation should be one of the expected values
    valid_interpretations = ['negligible', 'small', 'medium', 'large']
    assert effect_size['interpretation'] in valid_interpretations

def test_calculate_feature_group_statistics_normality_handling(feature_analysis_df):
    """Test that normality testing affects test selection."""
    result = calculate_feature_group_statistics(
        feature_analysis_df,
        feature='feature_A'
    )
    
    if 'two_group' in result['statistical_tests']:
        two_group_test = result['statistical_tests']['two_group']
        assert 'is_normal' in two_group_test
        
        # Test name should match normality assumption
        if two_group_test['is_normal']:
            assert two_group_test['test_name'] == 'Independent t-test'
            assert two_group_test['effect_size']['name'] == "Cohen's d"
        else:
            assert two_group_test['test_name'] == 'Mann-Whitney U test'
            assert two_group_test['effect_size']['name'] == 'rank-biserial correlation' 