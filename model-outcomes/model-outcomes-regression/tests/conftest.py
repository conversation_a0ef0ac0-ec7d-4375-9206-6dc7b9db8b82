"""
Configuration file for pytest.

Defines shared fixtures used across different test modules.
"""

import pytest
import pandas as pd
import numpy as np

@pytest.fixture
def sample_regression_data_df() -> pd.DataFrame:
    """Provides a simple DataFrame for regression tests."""
    return pd.DataFrame({
        'real': [10.0, 12.0, 15.0, 8.0, 9.5],
        'pred': [9.8, 12.5, 14.5, 8.2, 9.0],
        'model': ['A'] * 5, # Add other columns if needed by functions
        'patient_number': [1] * 5
    })

@pytest.fixture
def regression_arrays_basic() -> tuple[np.ndarray, np.ndarray]:
    """Provides basic numpy arrays for regression tests."""
    real = np.array([10.0, 12.0, 15.0, 8.0, 9.5])
    pred = np.array([9.8, 12.5, 14.5, 8.2, 9.0])
    return real, pred

@pytest.fixture
def regression_arrays_perfect() -> tuple[np.ndarray, np.ndarray]:
    """Provides numpy arrays with perfect predictions."""
    real = np.array([5.0, 10.0, 15.0])
    pred = np.array([5.0, 10.0, 15.0])
    return real, pred

@pytest.fixture
def regression_arrays_with_nan() -> tuple[np.ndarray, np.ndarray]:
    """Provides numpy arrays with NaN values."""
    real = np.array([10.0, 12.0, np.nan, 8.0, 9.5])
    pred = np.array([9.8, 12.5, 14.5, np.nan, 9.0])
    return real, pred

@pytest.fixture
def regression_arrays_empty() -> tuple[np.ndarray, np.ndarray]:
    """Provides empty numpy arrays."""
    real = np.array([])
    pred = np.array([])
    return real, pred

@pytest.fixture
def regression_arrays_zero_variance() -> tuple[np.ndarray, np.ndarray]:
    """Provides numpy arrays where one has zero variance (for correlation test)."""
    real = np.array([10.0, 10.0, 10.0])
    pred = np.array([9.8, 10.1, 10.0])
    return real, pred

@pytest.fixture
def multi_model_patient_df() -> pd.DataFrame:
    """Provides a DataFrame with multiple models and patients for filtering tests."""
    data = {
        'real': [10, 11, 12, 13, 20, 21, 22, 23, 30, 31, 32, 33],
        'pred': [9.8, 11.2, 12.1, 12.8, 19.5, 21.5, 22.3, 23.1, 29.9, 30.5, 32.5, 33.3],
        'model': ['A', 'A', 'A', 'A', 'B', 'B', 'B', 'B', 'C', 'C', 'C', 'C'],
        'patient_number': [1, 1, 2, 2, 1, 1, 2, 2, 1, 1, 2, 2]
    }
    return pd.DataFrame(data)

# --- MOCK SETTINGS FIXTURES --- 

@pytest.fixture
def base_settings() -> dict:
    """Base settings dictionary with common appearance settings."""
    return {
        'theme': 'plotly_white',
        'dot_size': 8,
        'opacity': 0.7,
        'metric_type': 'RMSE',
        'show_data_points': True,
        # Include available models/patients derived from multi_model_patient_df
        'available_models': ['A', 'B', 'C'],
        'available_patients': [1, 2]
    }

@pytest.fixture
def settings_single_compare_models(base_settings) -> dict:
    """Settings mimicking single view: comparing models A & B for patient 1."""
    settings = base_settings.copy()
    settings.update({
        'single_plot_comparison_type': 'models',
        'single_plot_context_patient': 1,
        'single_plot_models_to_compare': ['A', 'B'],
        'single_plot_context_model': None,
        'single_plot_patients_to_compare': []
    })
    return settings

@pytest.fixture
def settings_single_compare_patients(base_settings) -> dict:
    """Settings mimicking single view: comparing patients 1 & 2 for model C."""
    settings = base_settings.copy()
    settings.update({
        'single_plot_comparison_type': 'patients',
        'single_plot_context_model': 'C',
        'single_plot_patients_to_compare': [1, 2],
        'single_plot_context_patient': None,
        'single_plot_models_to_compare': []
    })
    return settings

@pytest.fixture
def settings_single_compare_all_models(base_settings) -> dict:
    """Settings mimicking single view: comparing ALL models for patient 2."""
    settings = base_settings.copy()
    settings.update({
        'single_plot_comparison_type': 'models',
        'single_plot_context_patient': 2,
        'single_plot_models_to_compare': ['A', 'B', 'C'], # Represents selecting "-- ALL --"
        'single_plot_context_model': None,
        'single_plot_patients_to_compare': []
    })
    return settings

@pytest.fixture
def settings_single_one_model_one_patient(base_settings) -> dict:
    """Settings mimicking single view: showing only model B for patient 1."""
    settings = base_settings.copy()
    settings.update({
        'single_plot_comparison_type': 'models', # Type is models
        'single_plot_context_patient': 1,      # Context is patient 1
        'single_plot_models_to_compare': ['B'],  # Only one model selected
        'single_plot_context_model': None,
        'single_plot_patients_to_compare': []
    })
    return settings

@pytest.fixture
def settings_comparison_tab(base_settings) -> dict:
    """Settings mimicking the multi-plot comparison tab: models A, C for patient 2."""
    settings = base_settings.copy()
    settings.update({
        'comparison_type': 'models',
        'comparison_patient': 2,
        'models_to_compare': ['A', 'C'],
        'comparison_model': None, 
        'patients_to_compare': []
    })
    return settings

# Add fixtures here later, e.g.:
# @pytest.fixture
# def sample_regression_data():
#     return pd.DataFrame({...}) 

# === FEATURE ANALYSIS FIXTURES ===

@pytest.fixture
def feature_analysis_df() -> pd.DataFrame:
    """Provides a DataFrame with features for feature analysis testing."""
    return pd.DataFrame({
        'real': [10.0, 12.0, 15.0, 8.0, 9.5, 14.0, 11.0, 13.0],
        'pred': [9.8, 12.5, 14.5, 8.2, 9.0, 13.8, 11.2, 12.8],
        'model': ['A'] * 8,
        'patient_number': [1] * 8,
        'feature_A': [1.2, 2.1, 3.5, 0.8, 1.0, 3.2, 1.8, 2.7],
        'feature_B': [10, 20, 35, 8, 10, 32, 18, 27],
        'feature_C': [0.1, 0.2, 0.4, 0.1, 0.1, 0.3, 0.2, 0.3]
    })

@pytest.fixture 
def feature_analysis_df_with_nan() -> pd.DataFrame:
    """Provides a DataFrame with NaN values for robust testing."""
    return pd.DataFrame({
        'real': [10.0, 12.0, np.nan, 8.0, 9.5],
        'pred': [9.8, 12.5, 14.5, np.nan, 9.0],
        'model': ['A'] * 5,
        'patient_number': [1] * 5,
        'feature_A': [1.2, np.nan, 3.5, 0.8, 1.0],
        'feature_B': [10, 20, 35, 8, np.nan]
    })

@pytest.fixture
def feature_analysis_identical_predictions() -> pd.DataFrame:
    """Provides a DataFrame with identical predictions for edge case testing."""
    return pd.DataFrame({
        'real': [10.0, 10.1, 9.9, 10.0, 10.2],
        'pred': [10.0, 10.0, 10.0, 10.0, 10.0],  # All identical
        'model': ['A'] * 5,
        'patient_number': [1] * 5,
        'feature_A': [1.0, 2.0, 3.0, 4.0, 5.0]
    })

@pytest.fixture
def feature_analysis_two_groups() -> pd.DataFrame:
    """Provides a DataFrame designed for clear two-group separation."""
    return pd.DataFrame({
        'real': [5.0, 5.1, 4.9, 15.0, 15.1, 14.9],
        'pred': [5.0, 5.0, 5.0, 15.0, 15.0, 15.0],  # Clear low/high split
        'model': ['A'] * 6,
        'patient_number': [1] * 6,
        'feature_A': [1.0, 1.1, 0.9, 5.0, 5.1, 4.9]  # Should show difference
    })

@pytest.fixture
def feature_analysis_quartiles() -> pd.DataFrame:
    """Provides a DataFrame suitable for quartile analysis."""
    np.random.seed(42)  # For reproducible results
    n_samples = 20
    pred_values = np.linspace(1, 20, n_samples)  # Evenly spaced for quartiles
    real_values = pred_values + np.random.normal(0, 0.5, n_samples)
    feature_values = pred_values * 0.5 + np.random.normal(0, 0.2, n_samples)
    
    return pd.DataFrame({
        'real': real_values,
        'pred': pred_values,
        'model': ['A'] * n_samples,
        'patient_number': [1] * n_samples,
        'feature_A': feature_values
    }) 