import streamlit as st
import pandas as pd
import os
import math # Needed for ceil
from typing import Optional, List, Dict, Any, Tuple

# Direct import from the centralized utils module
from modules.utils import get_base64_encoded_image

# --- display_sidebar_header (Moved from app.py) ---
def display_sidebar_header() -> Optional[Any]:
    """
    Display an improved sidebar header with user profile and file upload section.
    Creates a more minimalist and visually organized sidebar.

    Returns:
    --------
    Optional[Any]
        The uploaded file object if a file was uploaded, None otherwise.
    """
    # Get logo path for sidebar
    # Use relative path from this file's location
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # Go up one level to the parent directory of 'modules' then into 'static'
    logo_path = os.path.join(current_dir, '..', 'static', 'AIO-logo.png')

    # Add custom CSS for sidebar header styling
    st.markdown("""
    <style>
        /* Logo styling */
        .sidebar-logo {
            width: 100%;
            text-align: center;
            padding: 0.7rem 0;
            margin-bottom: 0.5rem;
            border-bottom: 1px solid rgba(250, 250, 250, 0.1);
        }
        .sidebar-logo img {
            max-width: 120px;
            height: auto;
        }

        /* User profile card */
        .user-profile {
            display: flex;
            align-items: center;
            padding: 0.7rem 0;
            border-bottom: 1px solid rgba(250, 250, 250, 0.1);
            margin-bottom: 1rem;
        }
        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: #4F8DF5;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        .user-info {
            flex: 1;
        }
        .user-name {
            font-weight: 600;
            margin: 0;
            font-size: 1rem;
        }
        .user-role {
            color: #94A3B8;
            font-size: 0.85rem;
            margin: 0;
        }

        /* Upload section */
        .upload-section {
            margin-top: 1rem;
            margin-bottom: 1rem;
        }
        .upload-icon-button {
            display: inline-flex;
            align-items: center;
            background: transparent;
            color: #94A3B8;
            border: none;
            padding: 0;
            font-size: 0.85rem;
            cursor: pointer;
            margin-bottom: 0.5rem;
        }
        .upload-icon {
            margin-right: 6px;
            color: #4F8DF5;
        }

        /* Loaded file indicator */
        .loaded-file {
            display: flex;
            align-items: center;
            padding: 0.4rem 0.6rem;
            background: rgba(79, 141, 245, 0.1);
            border-radius: 4px;
            margin-top: 0.5rem;
            font-size: 0.8rem;
        }
        .file-icon {
            margin-right: 0.5rem;
            color: #4F8DF5;
        }
        .file-name {
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Help text styling */
        .help-text {
            font-size: 0.75rem;
            color: #94A3B8;
            margin-top: 0.3rem;
        }

        /* Custom expander styling */
        .streamlit-expanderHeader {
            font-size: 0.85rem !important;
            padding: 0.3rem 0.5rem !important;
        }
        div.streamlit-expanderContent {
            padding: 0.5rem 0 !important;
        }
    </style>
    """, unsafe_allow_html=True)

    # Display company logo at the top of sidebar
    try:
        # This call will use the imported function or the fallback if import failed
        logo_base64 = get_base64_encoded_image(logo_path)
        if logo_base64:
             st.markdown(f"""
             <div class="sidebar-logo">
                 <img src="data:image/png;base64,{logo_base64}" alt="AIO Logo">
             </div>
             """, unsafe_allow_html=True)
        else:
             st.warning("Could not load logo image.")
    except Exception as e:
        st.warning(f"Error loading logo: {e}")


    # Get user info from session state
    user_info = st.session_state.get('user_info', {'name': 'N/A', 'role': 'N/A'})
    name = user_info.get('name', 'Unknown')
    role = user_info.get('role', 'user')

    # Display user profile with logout in the same row
    initial = name[0].upper() if name and len(name) > 0 else "?"

    # Create a 3-column layout: Avatar | Name+Role | Logout
    cols = st.columns([0.5, 2.3, 1.2])

    # Column 1: Avatar
    with cols[0]:
        st.markdown(f"""
        <div style="display: flex; justify-content: center; align-items: center;
                    width: 36px; height: 36px; background-color: #4F8DF5;
                    color: white; border-radius: 50%; font-weight: bold;">
            {initial}
        </div>
        """, unsafe_allow_html=True)

    # Column 2: Name and Role
    with cols[1]:
        st.markdown(f"""
        <div style="margin: 0; padding: 0;">
            <p style="font-weight: 600; margin: 0; font-size: 1rem;">{name}</p>
            <p style="color: #94A3B8; font-size: 0.85rem; margin: 0;">{role}</p>
        </div>
        """, unsafe_allow_html=True)

    # Column 3: Logout Button
    with cols[2]:
        # Assuming logout_user function exists and handles session state
        try:
            from modules.auth import logout_user
            if st.button("Log Out", type="secondary", key="sidebar_logout_btn", use_container_width=True):
                logout_user()
                st.rerun()
        except ImportError:
             st.warning("Logout functionality not available (auth module missing).")


    # Divider after user profile
    st.markdown("---")

    # File Uploader Section
    st.markdown("#### Data Upload")

    # Check if there's already a file loaded from a previous interaction
    uploaded_file: Optional[Any] = None
    current_file_name = st.session_state.get('current_file', None)

    if current_file_name:
        # Show file indicator
        st.markdown(f"""
        <div class="loaded-file">
            <span class="file-icon">📄</span>
            <span class="file-name">{current_file_name}</span>
        </div>
        """, unsafe_allow_html=True)

        # Add option to load a different file in an expander
        with st.expander("Change file", expanded=False):
            uploaded_file = st.file_uploader(
                "Upload new CSV",
                type=["csv"],
                key="sidebar_file_uploader",
                label_visibility="collapsed"
            )
            st.markdown("""
            <div class="help-text">
                CSV with columns: model, patient_number, real, pred
            </div>
            """, unsafe_allow_html=True)
    else:
        # When no file is loaded, show the standard uploader prominently
        uploaded_file = st.file_uploader(
            "Upload CSV",
            type=["csv"],
            key="sidebar_file_uploader",
            label_visibility="collapsed"
        )
        st.markdown("""
        <div class="help-text">
            CSV with columns: model, patient_number, real, pred
        </div>
        """, unsafe_allow_html=True)

    # Add another divider before controls
    st.markdown("---")

    # Return the potentially newly uploaded file
    return uploaded_file


# --- setup_sidebar_controls (Corrected Function) ---
def setup_sidebar_controls(df: pd.DataFrame, active_tab: str) -> Dict[str, Any]:
    """
    Setup sidebar controls conditionally based on the active tab.
    
    Parameters:
    -----------
    df : pd.DataFrame
        The main DataFrame.
    active_tab : str
        The identifier of the currently active main tab ("single", "comparison", or "feature_analysis").
    
    Returns:
    --------
    Dict[str, Any]
        Dictionary containing settings from the VISIBLE controls.
    """
    settings = {}
    
    # Get available models and patients (needed regardless of tab)
    available_models = sorted(df['model'].unique())
    available_patients = sorted(df['patient_number'].unique())
    settings['available_models'] = available_models
    settings['available_patients'] = available_patients
    
    # ===== 1. SINGLE PLOT CONTROLS (Conditional) =====
    # Show for both "single" and "feature_analysis" tabs (they use the same filtering logic)
    if active_tab in ["single", "feature_analysis"]:
        with st.expander("📈 Single Plot Controls", expanded=True): # Always expand if visible
            # --- NEW DYNAMIC CONTROLS --- 
            st.markdown("#### Dynamic Comparison Settings")

            # Unique keys for session state
            single_comp_type_key = "single_plot_comparison_type_radio"
            single_context_patient_key = "single_plot_context_patient_selector"
            single_models_key = "single_plot_models_to_compare_multiselect"
            single_context_model_key = "single_plot_context_model_selector"
            single_patients_key = "single_plot_patients_to_compare_multiselect"

            comparison_type_options = ["Compare Models", "Compare Patients"]
            comparison_type = st.radio(
                "Comparison Type",
                comparison_type_options,
                horizontal=True,
                key=single_comp_type_key,
                index=st.session_state.get(f'{single_comp_type_key}_index', 0),
                help="Compare multiple models for one patient OR multiple patients for one model."
            )
            st.session_state[f'{single_comp_type_key}_index'] = comparison_type_options.index(comparison_type)
            settings['single_plot_comparison_type'] = "models" if comparison_type == "Compare Models" else "patients"

            if settings['single_plot_comparison_type'] == "models":
                # Select ONE patient context
                default_patient_index = 0
                if single_context_patient_key in st.session_state and st.session_state[single_context_patient_key] in available_patients:
                     default_patient_index = available_patients.index(st.session_state[single_context_patient_key])
                settings['single_plot_context_patient'] = st.selectbox(
                    "Select Patient Context",
                    options=available_patients,
                    index=default_patient_index,
                    key=single_context_patient_key,
                    help="Select the patient whose data will be shown for different models."
                )
                if single_context_patient_key not in st.session_state or st.session_state[single_context_patient_key] != settings['single_plot_context_patient']:
                     st.session_state[single_context_patient_key] = settings['single_plot_context_patient']
                
                # Select MULTIPLE models to compare (with "ALL" option)
                ALL_MODELS_OPTION = "-- ALL MODELS --"
                model_options_with_all = [ALL_MODELS_OPTION] + available_models
                
                # Determine default selection for multiselect, prioritizing last state
                last_selected_models = st.session_state.get(single_models_key)
                if last_selected_models is not None:
                    # Ensure it's a list
                    if not isinstance(last_selected_models, list): last_selected_models = [last_selected_models]
                    # Validate that the saved selection items are still valid options
                    valid_last_selection = [m for m in last_selected_models if m == ALL_MODELS_OPTION or m in available_models]
                    if valid_last_selection: # Use the validated last selection if any remain valid
                         default_selection = valid_last_selection
                    else: # If last selection is now completely invalid, fallback
                         default_selection = available_models[:min(1, len(available_models))] if available_models else []
                else:
                    # No previous state, calculate initial default (first model or empty)
                    default_selection = available_models[:min(1, len(available_models))] if available_models else []

                selected_models_option = st.multiselect(
                    "Select Models to Compare", 
                    options=model_options_with_all,
                    key=single_models_key,
                    default=default_selection, # Use the smarter default logic
                    help="Select one or more models, or choose '-- ALL MODELS --' to include all."
                )
                
                # Handle "ALL" selection
                if ALL_MODELS_OPTION in selected_models_option:
                    actual_models_to_compare = available_models # Use the full list
                else:
                    actual_models_to_compare = selected_models_option # Use the specific selection
                
                settings['single_plot_models_to_compare'] = actual_models_to_compare # Store the actual list used for filtering

                if len(settings.get('single_plot_models_to_compare', [])) > 6 and ALL_MODELS_OPTION not in selected_models_option:
                    st.warning("⚠️ Selecting > 6 specific models might make the plot hard to read.")
                # Ensure other settings are None
                settings['single_plot_context_model'] = None
                settings['single_plot_patients_to_compare'] = []

            else: # settings['single_plot_comparison_type'] == "patients"
                # Select ONE model context
                default_model_index = 0
                if single_context_model_key in st.session_state and st.session_state[single_context_model_key] in available_models:
                    default_model_index = available_models.index(st.session_state[single_context_model_key])
                settings['single_plot_context_model'] = st.selectbox(
                    "Select Model Context",
                    options=available_models,
                    index=default_model_index,
                    key=single_context_model_key,
                    help="Select the model whose predictions will be shown for different patients."
                )
                if single_context_model_key not in st.session_state or st.session_state[single_context_model_key] != settings['single_plot_context_model']:
                     st.session_state[single_context_model_key] = settings['single_plot_context_model']

                # Select MULTIPLE patients to compare (with "ALL" option)
                ALL_PATIENTS_OPTION = "-- ALL PATIENTS --"
                patient_options_with_all = [ALL_PATIENTS_OPTION] + available_patients
                
                # Determine default selection for multiselect, prioritizing last state
                last_selected_patients = st.session_state.get(single_patients_key)
                if last_selected_patients is not None:
                    if not isinstance(last_selected_patients, list): last_selected_patients = [last_selected_patients]
                    valid_last_selection = [p for p in last_selected_patients if p == ALL_PATIENTS_OPTION or p in available_patients]
                    if valid_last_selection:
                         default_selection = valid_last_selection
                    else:
                         default_selection = available_patients[:min(1, len(available_patients))] if available_patients else []
                else:
                     default_selection = available_patients[:min(1, len(available_patients))] if available_patients else []

                selected_patients_option = st.multiselect(
                    "Select Patients to Compare", 
                    options=patient_options_with_all,
                    key=single_patients_key,
                    default=default_selection, # Use the smarter default logic
                    help="Select one or more patients, or choose '-- ALL PATIENTS --' to include all."
                )
                
                # Handle "ALL" selection
                if ALL_PATIENTS_OPTION in selected_patients_option:
                    actual_patients_to_compare = available_patients
                else:
                    actual_patients_to_compare = selected_patients_option
                    
                settings['single_plot_patients_to_compare'] = actual_patients_to_compare

                if len(settings.get('single_plot_patients_to_compare', [])) > 6 and ALL_PATIENTS_OPTION not in selected_patients_option:
                    st.warning("⚠️ Selecting > 6 specific patients might make the plot hard to read.")
                # Ensure other settings are None
                settings['single_plot_context_patient'] = None
                settings['single_plot_models_to_compare'] = []

    # ===== 2. MULTI-PLOT COMPARISON CONTROLS (Conditional) =====
    # Only show if the "comparison" tab is active
    elif active_tab == "comparison":
        with st.expander("🔄 Multi-Plot Comparison Controls", expanded=True): # Always expand if visible
            # Use session state keys for comparison controls
            comparison_type_key = "comparison_type_radio"
            comparison_patient_key = "comparison_patient_selector"
            models_to_compare_key = "models_to_compare_multiselect"
            comparison_model_key = "comparison_model_selector"
            patients_to_compare_key = "patients_to_compare_multiselect"
            show_dp_key = "comparison_show_dp_checkbox"
            
            comparison_type_options = ["Compare Models", "Compare Patients"]
            comparison_type = st.radio(
                "What would you like to compare?",
                comparison_type_options,
                horizontal=True,
                key=comparison_type_key,
                index=st.session_state.get('comparison_type_index', 0),
                help="Models: Compare different models on the same patient | Patients: Compare different patients on the same model"
            )
            st.session_state['comparison_type_index'] = comparison_type_options.index(comparison_type)
            settings['comparison_type'] = "models" if comparison_type == "Compare Models" else "patients"

            if settings['comparison_type'] == "models":
                default_patient_index = 0
                if comparison_patient_key in st.session_state and st.session_state[comparison_patient_key] in available_patients:
                     default_patient_index = available_patients.index(st.session_state[comparison_patient_key])
                settings['comparison_patient'] = st.selectbox(
                    "Select patient context",
                    options=available_patients,
                    index=default_patient_index,
                    key=comparison_patient_key,
                    help="Data from this patient will be used for all model comparisons"
                )
                if comparison_patient_key not in st.session_state or st.session_state[comparison_patient_key] != settings['comparison_patient']:
                     st.session_state[comparison_patient_key] = settings['comparison_patient']

                settings['models_to_compare'] = st.multiselect(
                    "Select models to compare", 
                    options=available_models,
                    key=models_to_compare_key,
                    default=st.session_state.get(models_to_compare_key, available_models[:min(3, len(available_models))]),
                    help="You can select multiple models to compare side-by-side"
                )
                if models_to_compare_key not in st.session_state or st.session_state[models_to_compare_key] != settings['models_to_compare']:
                     st.session_state[models_to_compare_key] = settings['models_to_compare']

                if len(settings['models_to_compare']) > 6:
                    st.warning("⚠️ Selecting > 6 models may clutter visualizations.")

            else: # settings['comparison_type'] == "patients"
                default_model_index = 0
                if comparison_model_key in st.session_state and st.session_state[comparison_model_key] in available_models:
                    default_model_index = available_models.index(st.session_state[comparison_model_key])
                settings['comparison_model'] = st.selectbox(
                    "Select model context",
                    options=available_models,
                    index=default_model_index,
                    key=comparison_model_key,
                    help="Data from this model will be used for all patient comparisons"
                )
                if comparison_model_key not in st.session_state or st.session_state[comparison_model_key] != settings['comparison_model']:
                     st.session_state[comparison_model_key] = settings['comparison_model']

                settings['patients_to_compare'] = st.multiselect(
                    "Select patients to compare", 
                    options=available_patients,
                    key=patients_to_compare_key,
                    default=st.session_state.get(patients_to_compare_key, available_patients[:min(3, len(available_patients))]),
                    help="You can select multiple patients to compare side-by-side"
                )
                if patients_to_compare_key not in st.session_state or st.session_state[patients_to_compare_key] != settings['patients_to_compare']:
                     st.session_state[patients_to_compare_key] = settings['patients_to_compare']

                if len(settings['patients_to_compare']) > 6:
                    st.warning("⚠️ Selecting > 6 patients may clutter visualizations.")

            settings['show_data_points'] = st.checkbox(
                "Show Data Points on bar plot", 
                key=show_dp_key,
                value=st.session_state.get(show_dp_key, True),
                help="Show number of data points as a line on secondary axis"
            )
            if show_dp_key not in st.session_state or st.session_state[show_dp_key] != settings['show_data_points']:
                 st.session_state[show_dp_key] = settings['show_data_points']

    # ===== 3. FEATURE ANALYSIS CONTROLS (Conditional) =====
    # Only show if the "feature_analysis" tab is active
    if active_tab == "feature_analysis":
        with st.expander("🔍 Feature Analysis Controls", expanded=True):
            from modules.regression_metrics import get_available_features
            
            # Get available features for the current dataset
            available_features = get_available_features(df)
            
            if available_features:
                # Analysis perspective selection
                analysis_type_key = "feature_analysis_type_sidebar"
                analysis_type_options = ["By Prediction Range", "By Error Level", "By Quartiles"]
                
                settings['feature_analysis_type'] = st.radio(
                    "📊 Analysis Perspective",
                    options=analysis_type_options,
                    index=st.session_state.get(f'{analysis_type_key}_index', 0),
                    key=analysis_type_key,
                    help="Choose how to group data for feature analysis:\n• Prediction Range: High/low predictions\n• Error Level: High/low errors\n• Quartiles: Four prediction groups"
                )
                st.session_state[f'{analysis_type_key}_index'] = analysis_type_options.index(settings['feature_analysis_type'])
                
                # Feature selection
                feature_selection_key = "feature_analysis_feature_sidebar"
                default_feature_index = 0
                if feature_selection_key in st.session_state and st.session_state[feature_selection_key] in available_features:
                    default_feature_index = available_features.index(st.session_state[feature_selection_key])
                
                settings['feature_analysis_feature'] = st.selectbox(
                    "🎯 Feature to Analyze",
                    options=available_features,
                    index=default_feature_index,
                    key=feature_selection_key,
                    help="Choose which feature to analyze across prediction groups"
                )
                if feature_selection_key not in st.session_state or st.session_state[feature_selection_key] != settings['feature_analysis_feature']:
                    st.session_state[feature_selection_key] = settings['feature_analysis_feature']
                
                # Display quick feature statistics
                if settings['feature_analysis_feature'] in df.columns:
                    feature_data = df[settings['feature_analysis_feature']]
                    st.markdown("**📈 Feature Info:**")
                    
                    col1, col2 = st.columns(2)
                    with col1:
                        st.metric(
                            "Range", 
                            f"{feature_data.min():.1f} - {feature_data.max():.1f}",
                            help="Minimum and maximum values"
                        )
                    with col2:
                        st.metric(
                            "Mean ± Std", 
                            f"{feature_data.mean():.1f} ± {feature_data.std():.1f}",
                            help="Mean and standard deviation"
                        )
                
                # Analysis method description
                method_descriptions = {
                    "By Prediction Range": "Groups data by median split of predictions (high/low SBP)",
                    "By Error Level": "Groups data by median split of absolute errors (high/low error)", 
                    "By Quartiles": "Divides data into quartiles based on prediction values"
                }
                st.info(f"**Method:** {method_descriptions[settings['feature_analysis_type']]}")
                
            else:
                # No features available - show helpful message
                st.warning("🚫 **No Additional Features**")
                st.info("Upload a dataset with additional feature columns to enable Feature Analysis.")
                
                # Set default values when no features available
                settings['feature_analysis_type'] = "By Prediction Range"
                settings['feature_analysis_feature'] = None

    # ===== 4. VISUALIZATION APPEARANCE (Always Visible) =====
    with st.expander("🎨 Visualization Appearance", expanded=False):
        # Use session state keys for appearance settings
        metric_key = "appearance_metric_radio"
        theme_key = "appearance_theme_select"
        dotsize_key = "appearance_dotsize_number"
        opacity_key = "appearance_opacity_slider"
        
        metric_options = ["RMSE", "MAE", "Correlation", "R²"]
        # Default metric based on what's relevant. RMSE/MAE common.
        default_metric_index = 0 
        if metric_key in st.session_state:
            try:
                default_metric_index = metric_options.index(st.session_state[metric_key])
            except ValueError:
                 default_metric_index = 0 # Fallback if saved value is invalid
        settings['metric_type'] = st.radio(
            "Metric for Bar Plot:",
            metric_options,
            key=metric_key,
            index=default_metric_index,
            horizontal=True
        )
        if metric_key not in st.session_state or st.session_state[metric_key] != settings['metric_type']:
            st.session_state[metric_key] = settings['metric_type']
            
        theme_options = ["plotly_white", "plotly_dark", "ggplot2", "seaborn"]
        default_theme_index = 0
        if theme_key in st.session_state:
            try:
                default_theme_index = theme_options.index(st.session_state[theme_key])
            except ValueError:
                 default_theme_index = 0
        settings['theme'] = st.selectbox(
            "Plot Theme",
            options=theme_options,
            key=theme_key,
            index=default_theme_index
        )
        if theme_key not in st.session_state or st.session_state[theme_key] != settings['theme']:
            st.session_state[theme_key] = settings['theme']
        
        col1, col2 = st.columns(2)
        with col1:
            settings['dot_size'] = st.number_input("Dot Size (Scatter)", min_value=3, max_value=20, key=dotsize_key, value=st.session_state.get(dotsize_key, 8))
            if dotsize_key not in st.session_state or st.session_state[dotsize_key] != settings['dot_size']:
                 st.session_state[dotsize_key] = settings['dot_size']
        with col2:
            settings['opacity'] = st.slider("Opacity (Scatter)", min_value=0.1, max_value=1.0, step=0.1, key=opacity_key, value=st.session_state.get(opacity_key, 0.7))
            if opacity_key not in st.session_state or st.session_state[opacity_key] != settings['opacity']:
                 st.session_state[opacity_key] = settings['opacity']
        
        # Removed custom axis limits as plots now auto-calculate optimal ranges

    return settings
