"""
Authentication module for the Model Prediction Visualizer.
This module handles user authentication using CSV-based user credentials.
"""
import os
import pandas as pd
import streamlit as st
import base64
from typing import Dict, Optional, Tuple, List

def load_users(users_file: str = "users.csv") -> pd.DataFrame:
    """
    Load user credentials from a CSV file.
    
    Parameters:
    -----------
    users_file : str
        Path to the CSV file containing user credentials
        
    Returns:
    --------
    pd.DataFrame
        DataFrame containing user credentials
    """
    # Get the directory of the current file
    current_dir = os.path.dirname(os.path.abspath(__file__))
    users_path = os.path.join(current_dir, users_file)
    
    if not os.path.exists(users_path):
        # Create a default admin user if file doesn't exist
        users_df = pd.DataFrame({
            'username': ['admin'],
            'password': ['admin123'],
            'name': ['Administrator'],
            'role': ['admin']
        })
        try:
            users_df.to_csv(users_path, index=False)
        except Exception as e:
            st.error(f"Error creating users file: {str(e)}")
            # Return the default DataFrame even if file creation fails
            return users_df
        return users_df
    
    try:
        return pd.read_csv(users_path)
    except Exception as e:
        st.error(f"Error reading users file: {str(e)}")
        # Return default DataFrame if file read fails
        return pd.DataFrame({
            'username': ['admin'],
            'password': ['admin123'],
            'name': ['Administrator'],
            'role': ['admin']
        })

def verify_credentials(username: str, password: str, users_df: pd.DataFrame) -> Tuple[bool, Dict]:
    """
    Verify user credentials against the user database.
    
    Parameters:
    -----------
    username : str
        Username to verify
    password : str
        Password to verify
    users_df : pd.DataFrame
        DataFrame containing user credentials
        
    Returns:
    --------
    Tuple[bool, Dict]
        (is_authenticated, user_info)
    """
    # Check if username exists
    user_row = users_df[users_df['username'] == username]
    if user_row.empty:
        return False, {}
    
    # Check password
    if user_row.iloc[0]['password'] == password:
        # Create user info dictionary
        user_info = {
            'username': username,
            'name': user_row.iloc[0]['name'],
            'role': user_row.iloc[0]['role'] if 'role' in user_row.columns else 'user'
        }
        return True, user_info
    
    return False, {}

def initialize_session_state():
    """Initialize session state variables for authentication"""
    # Authentication state
    if 'is_authenticated' not in st.session_state:
        st.session_state.is_authenticated = False
    
    # User info
    if 'user_info' not in st.session_state:
        st.session_state.user_info = {}
    
    # Current page/view
    if 'current_page' not in st.session_state:
        st.session_state.current_page = 'login'

def login_user(username: str, password: str, users_file: str = "users.csv") -> bool:
    """
    Attempt to log in a user with the provided credentials.
    Updates session state if successful.
    
    Parameters:
    -----------
    username : str
        Username to verify
    password : str
        Password to verify
    users_file : str
        Path to the CSV file containing user credentials
        
    Returns:
    --------
    bool
        True if login successful, False otherwise
    """
    # Load user database
    users_df = load_users(users_file)
    
    # Verify credentials
    is_authenticated, user_info = verify_credentials(username, password, users_df)
    
    # Update session state
    if is_authenticated:
        st.session_state.is_authenticated = True
        st.session_state.user_info = user_info
        st.session_state.current_page = 'main'
        return True
    
    return False

def logout_user():
    """Log out the current user by resetting session state"""
    st.session_state.is_authenticated = False
    st.session_state.user_info = {}
    st.session_state.current_page = 'login'

def display_login_page():
    """Display the login page and handle login form submission"""
    # Get the directory of the current file
    current_dir = os.path.dirname(os.path.abspath(__file__))
    static_dir = os.path.join(os.path.dirname(current_dir), 'static')
    logo_path = os.path.join(static_dir, 'AIO-logo.png')
    
    # Add simplified styling - focus on functionality over fancy effects
    st.markdown("""
        <style>
        /* Theme-adaptive styling */
        :root {
            --primary-color: #1E3A8A;
            --primary-color-light: #4F8DF5;
            --text-color: #333333;
            --form-bg: rgba(255, 255, 255, 0.15);
            --form-border: rgba(255, 255, 255, 0.2);
        }

        [data-theme="dark"] {
            --primary-color: #4F8DF5;
            --primary-color-light: #60A5FA;
            --text-color: #E2E8F0;
            --form-bg: rgba(30, 41, 59, 0.25);
            --form-border: rgba(255, 255, 255, 0.1);
        }

        /* Transparent form styling with backdrop filter */
        .stForm {
            background-color: var(--form-bg) !important;
            border: 1px solid var(--form-border) !important;
            border-radius: 10px !important;
            padding: 2rem !important;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15) !important;
            backdrop-filter: blur(10px) !important;
            -webkit-backdrop-filter: blur(10px) !important;
        }

        /* Button styling */
        .stButton > button {
            background-color: var(--primary-color) !important;
            color: white !important;
            border: none !important;
            border-radius: 4px !important;
            padding: 0.5rem 1rem !important;
            font-weight: 600 !important;
            transition: all 0.2s ease !important;
        }

        .stButton > button:hover {
            background-color: var(--primary-color-light) !important;
            transform: translateY(-1px) !important;
        }

        /* Logo container */
        .logo-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 auto 2rem auto;
            width: 100%;
        }
        
        /* Title styling */
        .login-title {
            text-align: center;
            margin-bottom: 1.5rem;
            color: var(--primary-color);
        }

        .login-subtitle {
            text-align: center;
            color: var(--text-color);
            margin-bottom: 2rem;
            font-size: 1.1rem;
        }

        /* Code styling */
        .login-code {
            background: rgba(0,0,0,0.05);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
        }
        </style>
    """, unsafe_allow_html=True)
    
    # Create a centered layout using Streamlit columns
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        # Display logo with HTML to ensure proper centering
        if os.path.exists(logo_path):
            # Convert image to base64 for direct embedding
            with open(logo_path, "rb") as image_file:
                encoded_image = base64.b64encode(image_file.read()).decode()
            
            st.markdown(f"""
                <div class="logo-container">
                    <img src="data:image/png;base64,{encoded_image}" alt="AIO Logo" style="max-width: 300px; width: 100%; margin: 0 auto;">
                </div>
            """, unsafe_allow_html=True)
        
        # Display title with more direct styling
        st.markdown(
            """
            <h1 class="login-title">Model Outcomes | Regression </h1>
            <p class="login-subtitle">Log in to access the application</p>
            """, 
            unsafe_allow_html=True
        )
        
        # Create a simple login form
        with st.form("login_form"):
            username = st.text_input("Username", placeholder="Enter your username")
            password = st.text_input("Password", type="password", placeholder="Enter your password")
            
            # Add some spacing
            st.markdown("<br>", unsafe_allow_html=True)
            
            # Submit button
            submit_button = st.form_submit_button(
                "Log In",
                use_container_width=True
            )
            
            if submit_button:
                if login_user(username, password):
                    st.success("Login successful!")
                    st.rerun()  # Refresh the app to show the main interface
                else:
                    st.error("Invalid username or password. Please try again.")
        
        # Add a note about default credentials
        st.markdown("""
            <div style='text-align: center; margin-top: 1rem;'>
                <p style='color: var(--text-color); font-size: 0.9rem; opacity: 0.9;'>
                    Default login: <code class="login-code">username = 'admin'</code>, 
                    <code class="login-code">password = 'admin123'</code>
                </p>
            </div>
        """, unsafe_allow_html=True) 