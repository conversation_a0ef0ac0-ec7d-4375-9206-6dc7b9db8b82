"""
    Utility functions for data processing and visualization in the Model Prediction Visualizer application.
"""

import pandas as pd
import streamlit as st
from typing import Optional, Dict, List, Any, Tuple

# --- Column Mapping Helpers (Corrected get_candidate_columns) --- 
def get_candidate_columns(df: pd.DataFrame, target_type: str) -> List[str]:
    """
    Get candidate columns based on heuristics, avoiding cross-recursion.
    """
    if df is None or df.empty:
        return []

    candidates = []
    col_map = {col.lower().replace(' ', '_').replace('-', '_'): col for col in df.columns}

    if target_type == 'patient_number':
        id_patterns = ['id', 'patient', 'subject', 'participant', 'case', 'person', 'number']
        for pattern in id_patterns:
            for col_norm, col_orig in col_map.items():
                if pattern in col_norm and col_orig not in candidates:
                    candidates.append(col_orig)
        for col in df.columns:
             if col not in candidates and (pd.api.types.is_numeric_dtype(df[col]) or pd.api.types.is_object_dtype(df[col])):
                  if df[col].nunique() > len(df) * 0.5:
                       candidates.append(col)

    elif target_type == 'model':
        model_patterns = ['model', 'algorithm', 'method', 'technique', 'type']
        for pattern in model_patterns:
            for col_norm, col_orig in col_map.items():
                if pattern in col_norm and col_orig not in candidates:
                    candidates.append(col_orig)
        for col in df.columns:
            if col not in candidates and df[col].dtype == 'object' and 1 < df[col].nunique() < 20:
                candidates.append(col)

    elif target_type == 'real':
        real_patterns = ['real', 'true', 'actual', 'ground_truth', 'target', 'y', 'outcome', 'value']
        # Find based on patterns
        for pattern in real_patterns:
            for col_norm, col_orig in col_map.items():
                if pattern in col_norm and col_orig not in candidates:
                    candidates.append(col_orig)
        # Also add numeric types as candidates
        for col in df.columns:
            if col not in candidates and pd.api.types.is_numeric_dtype(df[col]):
                candidates.append(col)

    elif target_type == 'pred':
        pred_patterns = ['pred', 'prediction', 'score', 'output', 'y_pred', 'value']
        # Find based on patterns
        for pattern in pred_patterns:
            for col_norm, col_orig in col_map.items():
                if pattern in col_norm and col_orig not in candidates:
                    candidates.append(col_orig)
        # Also add numeric types as candidates
        for col in df.columns:
            if col not in candidates and pd.api.types.is_numeric_dtype(df[col]):
                 candidates.append(col)

    return list(pd.unique(candidates))

def display_column_mapping_ui(df: pd.DataFrame) -> Dict[str, Optional[str]]:
    """
    Display UI for mapping columns in the dataframe to required regression columns.
    Allows auto-generation of the 'model' column.

    Parameters:
    -----------
    df : pd.DataFrame
        Dataframe to map columns from.

    Returns:
    --------
    Dict[str, Optional[str]]
        Mapping from required column names ('model', 'patient_number', 'real', 'pred')
        to actual column names selected by the user. 'model' can be None if auto-generate is chosen.
    """
    st.markdown("#### Column Mapping Required")
    st.info("Could not find the standard columns (`model`, `patient_number`, `real`, `pred`). Please map the columns from your file.")

    required_cols_map = {
        'model': "Model Identifier",
        'patient_number': "Patient Identifier",
        'real': "Real (Actual) Values",
        'pred': "Predicted Values"
    }
    column_mapping: Dict[str, Optional[str]] = {}
    available_cols = list(df.columns)

    cols = st.columns(len(required_cols_map))

    for i, (req_col, display_name) in enumerate(required_cols_map.items()):
        with cols[i]:
            candidates = get_candidate_columns(df, req_col)
            default_idx = 0
            options = list(available_cols) # Start with available columns

            # --- Add Auto-generate option specifically for 'model' --- 
            if req_col == 'model':
                options = ["[Auto-generate]"] + options
                # Adjust default index if a candidate exists and is not auto-generate
                if candidates and candidates[0] in options[1:]: # Check against original cols
                     default_idx = options.index(candidates[0])
                # Otherwise default_idx remains 0, selecting [Auto-generate]
            # --- End modification for 'model' --- 
            else:
                 # Default selection logic for other columns
                 if candidates and candidates[0] in available_cols:
                     default_idx = available_cols.index(candidates[0])
            
            st.markdown(f"**{display_name}**")
            if candidates:
                # Suggest the first candidate that is an actual column
                first_real_candidate = next((c for c in candidates if c in available_cols), None)
                if first_real_candidate:
                    st.caption(f"Suggest: {first_real_candidate}")

            selected = st.selectbox(
                f"Select column for '{display_name}'",
                options=options, # Use potentially modified options list
                index=default_idx,
                key=f"column_map_{req_col}"
            )
            
            # Store None if [Auto-generate] is selected for model
            if req_col == 'model' and selected == "[Auto-generate]":
                column_mapping[req_col] = None
            else:
                column_mapping[req_col] = selected

    return column_mapping

# --- Data Loading --- 
def _load_regression_dataframe(uploaded_file) -> Optional[pd.DataFrame]:
    """Loads a DataFrame from the uploaded file (currently supports CSV)."""
    try:
        # TODO: Add file format detection later if needed (e.g., Excel)
        df = pd.read_csv(uploaded_file, sep=None, engine='python')
        return df
    except pd.errors.EmptyDataError:
        st.error("The uploaded CSV file is empty.")
        return None
    except Exception as e:
        st.error(f"Error reading CSV file: {e}. Please ensure it's a valid CSV.")
        return None

# --- Data Validation (Handles Auto-Generated Model) --- 
def _validate_regression_dataframe(df: pd.DataFrame, column_mapping: Dict[str, Optional[str]]) -> Tuple[Optional[pd.DataFrame], Optional[str]]:
    """
    Validates the structure and data types of the regression DataFrame AFTER potential column renaming.
    Handles auto-generation of the 'model' column if specified in mapping.

    Parameters:
    -----------
    df : pd.DataFrame
         The raw DataFrame loaded from the file.
    column_mapping : Dict[str, Optional[str]]
         Mapping from required standard names ('model', etc.) to original column names in df.
         If mapping['model'] is None, a default model column will be generated.

    Returns:
    --------
    Tuple[Optional[pd.DataFrame], Optional[str]]
        (Validated and potentially renamed DataFrame or None, Error message or None)
    """
    if df is None:
        return None, "Internal error: No DataFrame provided for validation."

    # --- Handle optional auto-generation of 'model' column --- 
    generate_model_col = False
    if 'model' in column_mapping and column_mapping['model'] is None:
        generate_model_col = True
        # Remove model from mapping dict temporarily to avoid renaming errors
        original_model_mapping = column_mapping.pop('model') 
    # ----------------------------------------------------------

    # 1. Apply Renaming based on mapping
    try:
        # Rename based on the potentially modified mapping
        rename_dict = {v: k for k, v in column_mapping.items() if v is not None and v in df.columns}
        validated_df = df.rename(columns=rename_dict)
    except Exception as e:
         # Restore mapping if needed before returning
         if generate_model_col: column_mapping['model'] = original_model_mapping
         return None, f"Error applying column mapping: {e}"

    # --- Add generated model column if requested --- 
    if generate_model_col:
         validated_df['model'] = "Default Model" # Assign default value
         # Restore mapping key for subsequent checks if needed, though rename_dict handled it
         column_mapping['model'] = 'model' 
    # --------------------------------------------- 

    # 2. Check required standard columns *after* renaming and potential generation
    required_cols = ['model', 'patient_number', 'real', 'pred']
    missing_cols = [col for col in required_cols if col not in validated_df.columns]
    if missing_cols:
        error_msg = f"Required columns (after mapping/generation) not found: {', '.join(missing_cols)}. Please check mapping."
        return None, error_msg

    # 3. Validate and convert data types
    try:
        validated_df['real'] = pd.to_numeric(validated_df['real'], errors='coerce')
        validated_df['pred'] = pd.to_numeric(validated_df['pred'], errors='coerce')
        if validated_df['real'].isnull().any(): return None, "Non-numeric values in mapped 'real' column."
        if validated_df['pred'].isnull().any(): return None, "Non-numeric values in mapped 'pred' column."
        if 'patient_number' in validated_df.columns:
            try: validated_df['patient_number'] = pd.to_numeric(validated_df['patient_number'], errors='raise').astype(int)
            except: validated_df['patient_number'] = validated_df['patient_number'].astype(str)
    except Exception as e:
        return None, f"Error processing data types after mapping: {e}"

    return validated_df, None

# --- Main Processing Function (Added Submit Button) --- 
def process_uploaded_file(uploaded_file) -> Optional[pd.DataFrame]:
    """
    Orchestrates the processing of an uploaded file: load, map (if needed), validate, manage state.
    """
    if uploaded_file is None:
        return st.session_state.get('uploaded_data', None)

    current_file_name = uploaded_file.name

    # Re-process if it's a new file or no data is currently stored
    if ('current_file' not in st.session_state or
            st.session_state.current_file != current_file_name or
            'uploaded_data' not in st.session_state or
            st.session_state.uploaded_data is None):

        st.info(f"Processing uploaded file: {current_file_name}...")

        # Step 1: Load Data
        raw_df = _load_regression_dataframe(uploaded_file)
        if raw_df is None:
            st.session_state.uploaded_data = None
            st.session_state.current_file = None
            return None

        # Step 2: Check for Standard Columns / Trigger Mapping UI
        required_cols = ['model', 'patient_number', 'real', 'pred']
        standard_columns_found = all(col in raw_df.columns for col in required_cols)
        column_mapping = {col: col for col in required_cols} # Default mapping
        proceed_to_validate = False
        user_submitted_mapping = False # Flag to track if form was submitted

        if standard_columns_found:
             proceed_to_validate = True # Standard columns exist, proceed directly
        else:
             # Columns missing, display mapping UI within a form
             with st.form("column_mapping_form"):
                 st.markdown("### Column Mapping Required")
                 st.info("Standard columns (`model`, `patient_number`, `real`, `pred`) not found. Please map the columns from your file.")
                 user_mapping = display_column_mapping_ui(raw_df)
                 submitted = st.form_submit_button("Apply Mapping and Validate")
                 if submitted:
                     column_mapping = user_mapping # Use user's mapping
                     proceed_to_validate = True
                     user_submitted_mapping = True # Set flag on submission
                 # Removed st.stop() - let Streamlit handle form re-run logic

        # Step 3: Validate Data (using default or user-provided mapping)
        if proceed_to_validate:
            validated_df, error_message = _validate_regression_dataframe(raw_df, column_mapping)
            if error_message:
                st.error(error_message)
                st.session_state.uploaded_data = None
                st.session_state.current_file = None
                # If validation fails *after* user submitted mapping, we should show the error
                # but not necessarily return None immediately, allow user to see the form again?
                # For now, returning None to maintain previous behavior.
                return None

            # Step 4: Store Validated Data
            st.session_state.uploaded_data = validated_df
            st.session_state.current_file = current_file_name
            st.success(f"File '{current_file_name}' processed successfully.")
            # Rerun only if the user successfully submitted the mapping form
            if user_submitted_mapping:
                 st.rerun()
            return validated_df
        else:
             # If standard columns not found and form not submitted, we are waiting.
             # Returning None here would break the flow if st.stop() isn't used.
             # Let Streamlit handle the wait state implicitly.
             pass # Or return None explicitly if needed elsewhere
             return None # Explicitly return None to signify waiting or non-validated state

    else:
        # It's the same file, return the already validated data
        return st.session_state.uploaded_data

@st.cache_data
def get_filtered_data(
    df: pd.DataFrame,
    mode: Optional[int] = None,
    selected_model: Optional[str] = None,
    selected_patient: Optional[Any] = None,
    settings: Optional[Dict[str, Any]] = None
) -> pd.DataFrame:
    """
    Central function to get filtered data based on specified mode and selections.

    Handles the special string value "All" for selected_model or selected_patient
    by not applying the corresponding filter.

    Parameters:
    -----------
    df : pd.DataFrame
        Original DataFrame containing all data.
    mode : int, optional
        Visualization mode (1=one model+patient, 2=one model all patients, 3=one patient all models).
        If None, derived from settings.
    selected_model : str or "All", optional
        Selected model identifier or "All".
        If None, derived from settings.
    selected_patient : Any or "All", optional
        Selected patient identifier or "All".
        If None, derived from settings.
    settings : Dict[str, Any], optional
        Dictionary containing settings (used if mode/model/patient not provided explicitly).

    Returns:
    --------
    pd.DataFrame
        Filtered DataFrame based on specified parameters. Returns a copy of the original
        DataFrame if no filters are applicable.
    """
    if df is None or df.empty:
        return pd.DataFrame() # Return empty DataFrame

    # If settings dictionary provided, extract parameters from it, prioritizing explicit args
    if settings is not None:
        mode = mode if mode is not None else settings.get('selected_mode')
        selected_model = selected_model if selected_model is not None else settings.get('selected_model')
        selected_patient = selected_patient if selected_patient is not None else settings.get('selected_patient')

    # Start with a copy of the full DataFrame
    filtered_df = df.copy()

    # Apply filters based on mode and selections (handle "All")
    try:
        apply_model_filter = selected_model is not None and selected_model != "All"
        apply_patient_filter = selected_patient is not None and selected_patient != "All"

        # Convert selected_patient to the same type as the DataFrame column for comparison
        patient_col_type = df['patient_number'].dtype
        patient_filter_value = selected_patient
        if apply_patient_filter:
             try:
                  patient_filter_value = patient_col_type.type(selected_patient)
             except (ValueError, TypeError):
                   st.warning(f"Could not convert selected patient '{selected_patient}' to type {patient_col_type}. Filtering might fail.")
                   # Decide how to handle: return empty, or try string comparison? For now, let it proceed.

        if mode == 1: # One model, one patient
            if not apply_model_filter or not apply_patient_filter:
                 st.warning("Mode 1 requires a specific model and patient selection.")
                 return pd.DataFrame()
            filtered_df = filtered_df[
                 (filtered_df['model'] == selected_model) & 
                 (filtered_df['patient_number'] == patient_filter_value)
            ]
        
        elif mode == 2: # One model, all patients
            if not apply_model_filter:
                 st.warning("Mode 2 requires a specific model selection.")
                 return pd.DataFrame()
            # Filter only by model
            filtered_df = filtered_df[filtered_df['model'] == selected_model]
            # selected_patient is implicitly "All"

        elif mode == 3: # One patient, all models
            if not apply_patient_filter:
                 st.warning("Mode 3 requires a specific patient selection.")
                 return pd.DataFrame()
            # Filter only by patient
            filtered_df = filtered_df[filtered_df['patient_number'] == patient_filter_value]
            # selected_model is implicitly "All"
            
        # Removed the redundant elif conditions that only checked model or patient
        # Rely on the mode to determine the correct filtering logic.
        # If mode is None or not 1, 2, 3, no filtering is applied here (returns full copy).

    except KeyError as e:
        st.error(f"Filtering error: Column '{e}' not found in the DataFrame. Please check data format.")
        return pd.DataFrame()
    except Exception as e:
        st.error(f"An unexpected error occurred during data filtering: {e}")
        return pd.DataFrame()

    # Add index as column for reference, if not already present
    if 'point_index' not in filtered_df.columns:
        # Use reset_index carefully if original index is meaningful
        # Here we assume it's just a row number reference
        filtered_df = filtered_df.reset_index().rename(columns={'index': 'point_index'})

    return filtered_df

def filter_data_for_summary(df: pd.DataFrame, active_tab_id: str, settings: Dict[str, Any]) -> pd.DataFrame:
    """
    Centralized filtering logic for summary metrics calculation.
    
    This function provides consistent data filtering across different tabs
    for generating summary metrics and displays.
    
    Parameters:
    -----------
    df : pd.DataFrame
        Original DataFrame containing all data.
    active_tab_id : str
        Active tab identifier ("single", "comparison", "feature_analysis").
    settings : Dict[str, Any]
        UI settings dictionary containing user selections.
        
    Returns:
    --------
    pd.DataFrame
        Filtered DataFrame for summary calculations.
    """
    if df is None or df.empty:
        return pd.DataFrame()
    
    temp_df = df.copy()
    
    try:
        if active_tab_id in ["single", "feature_analysis"]:
            # Single and Feature Analysis tabs use same filtering logic
            comparison_type = settings.get('single_plot_comparison_type')
            context_patient = settings.get('single_plot_context_patient')
            models_to_compare = settings.get('single_plot_models_to_compare', [])
            context_model = settings.get('single_plot_context_model')
            patients_to_compare = settings.get('single_plot_patients_to_compare', [])
            
            if comparison_type == "models" and context_patient and models_to_compare:
                patient_col_type = df['patient_number'].dtype
                context_patient_typed = patient_col_type.type(context_patient)
                temp_df = temp_df[
                    (temp_df['patient_number'] == context_patient_typed) & 
                    (temp_df['model'].isin(models_to_compare))
                ]
            elif comparison_type == "patients" and context_model and patients_to_compare:
                patient_col_type = df['patient_number'].dtype
                patients_to_compare_typed = [patient_col_type.type(p) for p in patients_to_compare]
                temp_df = temp_df[
                    (temp_df['model'] == context_model) & 
                    (temp_df['patient_number'].isin(patients_to_compare_typed))
                ]
            else:
                temp_df = pd.DataFrame()
                
        elif active_tab_id == "comparison":
            # Comparison tab logic
            comparison_type = settings.get('comparison_type')
            
            if comparison_type == "models":
                patient_context = settings.get('comparison_patient')
                models_to_compare = settings.get('models_to_compare', [])
                
                if patient_context and models_to_compare:
                    patient_col_type = df['patient_number'].dtype
                    patient_context_typed = patient_col_type.type(patient_context)
                    temp_df = temp_df[
                        (temp_df['patient_number'] == patient_context_typed) & 
                        (temp_df['model'].isin(models_to_compare))
                    ]
                else:
                    temp_df = pd.DataFrame()
                    
            elif comparison_type == "patients":
                model_context = settings.get('comparison_model')
                patients_to_compare = settings.get('patients_to_compare', [])
                
                if model_context and patients_to_compare:
                    patient_col_type = df['patient_number'].dtype
                    patients_to_compare_typed = [patient_col_type.type(p) for p in patients_to_compare]
                    temp_df = temp_df[
                        (temp_df['model'] == model_context) & 
                        (temp_df['patient_number'].isin(patients_to_compare_typed))
                    ]
                else:
                    temp_df = pd.DataFrame()
            else:
                temp_df = pd.DataFrame()
        else:
            temp_df = pd.DataFrame()
            
    except (KeyError, ValueError, TypeError) as e:
        st.warning(f"Could not apply filter for {active_tab_id}: {e}")
        temp_df = pd.DataFrame()
    
    return temp_df
