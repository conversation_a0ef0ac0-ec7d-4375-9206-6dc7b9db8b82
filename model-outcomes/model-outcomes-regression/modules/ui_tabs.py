import streamlit as st
import pandas as pd
import numpy as np
from typing import Dict, Any

# Direct imports from other modules
from modules.regression_metrics import calculate_regression_metrics, get_available_features
from modules.visualization import interactive_model_scatter, create_metrics_bar_plot, plot_feature_distribution_by_prediction_range, plot_feature_comparison_by_prediction_range
from modules.data_handling import get_filtered_data

# --- Functions moved from app.py ---

def display_metrics_and_stats(filtered_df: pd.DataFrame, comparison_type: str | None, settings: Dict[str, Any]):
    """
    Display metrics and statistics relevant to the currently viewed data.
    If a comparison is active (multiple models or patients), it shows grouped metrics.

    Parameters:
    -----------
    filtered_df : pd.DataFrame
        The DataFrame already filtered based on the sidebar selections for the single plot.
    comparison_type : str or None
        Indicates the type of comparison being shown ('models' or 'patients') or None.
    settings : Dict[str, Any]
        Dictionary containing sidebar settings (needed for 'metric_type', 'theme', 'show_data_points').
    """
    # --- REMOVED INTERNAL FILTERING --- 
    # filtered_df = get_filtered_data(df, settings=settings)
    # Input df is already filtered.

    # Only display detailed grouped analysis if comparing models or patients
    if comparison_type in ['models', 'patients']:
        st.markdown("#### Analysis Details by Group")
        metric_type = settings.get('metric_type', 'RMSE')
        show_dp = settings.get('show_data_points', True)
        theme = settings.get('theme', 'plotly_white') # Pass theme to bar plot

        # Create columns for Stats, Table, Bar Plot
        col1, col2, col3 = st.columns([1, 2, 3])

        with col1:
            st.markdown("**Data Overview**")
            # Determine entity and grouping column based on comparison_type
            entity = "Patients" if comparison_type == 'patients' else "Models"
            group_col_name = 'patient_number' if comparison_type == 'patients' else 'model'
            unique_count = 0
            if group_col_name in filtered_df.columns:
                # Ensure the column exists before counting unique values
                unique_count = filtered_df[group_col_name].nunique()
            
            st.metric(f"Unique {entity} Shown", unique_count)
            st.metric("Total Data Points Shown", len(filtered_df))

        # Prepare data for table and plot
        metrics_data = []
        group_col = group_col_name # Use the determined group column
        entity_key = "Patient" if comparison_type == 'patients' else "Model"

        if not filtered_df.empty and group_col in filtered_df.columns:
            try:
                grouped = filtered_df.groupby(group_col)
                for name, group_df in grouped:
                    if not group_df.empty:
                        metrics = calculate_regression_metrics(group_df)
                        metrics_data.append({
                            entity_key: name,
                            "RMSE": metrics.get('rmse', float('nan')),
                            "MAE": metrics.get('mae', float('nan')),
                            "Correlation": metrics.get('correlation', float('nan')),
                            "R²": metrics.get('r_squared', float('nan')),
                            "Data Points": len(group_df)
                        })
            except Exception as e:
                 st.warning(f"Could not calculate grouped metrics: {e}")

        with col2:
            st.markdown(f"**Metrics by {entity_key}**")
            if metrics_data:
                metrics_df = pd.DataFrame(metrics_data)
                # Format columns (handle potential non-numeric values from calculation)
                for col in ["RMSE", "MAE"]:
                     metrics_df[col] = metrics_df[col].apply(lambda x: f"{x:.2f}" if pd.notna(x) and isinstance(x, (int, float)) else "N/A")
                for col in ["Correlation", "R²"]:
                     metrics_df[col] = metrics_df[col].apply(lambda x: f"{x:.4f}" if pd.notna(x) and isinstance(x, (int, float)) else "N/A")
                metrics_df = metrics_df.set_index(entity_key)
                st.dataframe(metrics_df, height=250) # Adjust height as needed
            else:
                st.caption("No grouped metrics data to display.")

        with col3:
            st.markdown(f"**{metric_type} by {entity_key}**")
            if metrics_data:
                 raw_plot_data = metrics_data
                 try:
                    fig_bar = create_metrics_bar_plot(
                        metrics_data=raw_plot_data,
                        metric_type=metric_type,
                        comparison_type=comparison_type, # Pass the correct comparison type
                        theme=theme, # Use theme from settings
                        show_data_points=show_dp,
                        height=300 # Adjust height as needed
                    )
                    st.plotly_chart(fig_bar, use_container_width=True)
                 except Exception as e:
                      st.error(f"Failed to create bar plot: {e}")
            else:
                 st.caption("No data for bar plot.")
    else:
        # Optionally, add a small note if no comparison is active (e.g., only one model/patient selected)
        st.caption("Grouped analysis requires selecting multiple models or patients.")


def display_single_visualization_tab(df: pd.DataFrame, settings: Dict[str, Any]):
    """
    Display the content for the 'Single Visualization' tab using dynamic filtering.

    Parameters:
    -----------
    df : pd.DataFrame
        The main DataFrame.
    settings : Dict[str, Any]
        The dictionary containing settings from the sidebar, including dynamic single plot settings.
    """
    # st.markdown("### Dynamic Single Plot Visualization")
    # st.info("Select comparison type and items in the 'Single Plot Controls' in the sidebar.")
    
    # --- 1. Extract New Dynamic Settings ---
    comparison_type = settings.get('single_plot_comparison_type') # 'models' or 'patients'
    context_patient = settings.get('single_plot_context_patient')
    models_to_compare = settings.get('single_plot_models_to_compare', [])
    context_model = settings.get('single_plot_context_model')
    patients_to_compare = settings.get('single_plot_patients_to_compare', [])
    
    # --- 2. Filter Data Based on New Settings ---
    filtered_df = pd.DataFrame() # Initialize empty DataFrame
    title_info = "" # For dynamic plot title
    
    try:
        active_comparison = False # Flag to track if a multi-item comparison is active
        if comparison_type == "models":
            if context_patient and models_to_compare:
                filtered_df = df[
                    (df['patient_number'] == context_patient) & 
                    (df['model'].isin(models_to_compare))
                ].copy() 
                
                # --- Adjust Title for ALL --- 
                if set(models_to_compare) == set(settings.get('available_models', [])):
                    model_list_str = "All Models"
                else:
                    model_list_str = f"Models [{', '.join(map(str, models_to_compare))}]"
                title_info = f"Comparing {model_list_str} for Patient {context_patient}"
                # --- End Adjust --- 
                
                if len(models_to_compare) > 1: active_comparison = True
            else:
                st.warning("Please select a patient context and at least one model in the sidebar.")
                return

        elif comparison_type == "patients":
            if context_model and patients_to_compare:
                filtered_df = df[
                    (df['model'] == context_model) & 
                    (df['patient_number'].isin(patients_to_compare))
                ].copy()
                
                # --- Adjust Title for ALL --- 
                if set(patients_to_compare) == set(settings.get('available_patients', [])):
                    patient_list_str = "All Patients"
                else:
                    patient_list_str = f"Patients [{', '.join(map(str, patients_to_compare))}]"
                title_info = f"Comparing {patient_list_str} for Model {context_model}"
                # --- End Adjust ---
                
                if len(patients_to_compare) > 1: active_comparison = True
            else:
                st.warning("Please select a model context and at least one patient in the sidebar.")
                return
        else:
            st.error("Invalid comparison type selected.")
            return
            
        if filtered_df.empty:
             st.warning("No data matches the current selection.")
             return

        # --- 3. Generate and Display Plot (Modified Call) ---
        fig = interactive_model_scatter(
            df=filtered_df, # Pass the PRE-FILTERED DataFrame
            comparison_type=comparison_type if active_comparison else None, # Only color if comparing >1 item
            theme=settings.get('theme', 'plotly_white'),
            title=title_info, # Use dynamic title
            dot_size=settings.get('dot_size', 8),
            opacity=settings.get('opacity', 0.7)
        )

        st.plotly_chart(fig, use_container_width=True)

        # --- 4. Display Adapted Metrics ---
        # Pass the filtered data and comparison type to the adapted function
        display_metrics_and_stats(filtered_df=filtered_df, comparison_type=comparison_type if active_comparison else None, settings=settings)
        
        # Feature Analysis has been moved to its own dedicated tab

    except Exception as e:
        st.error(f"Error generating dynamic single visualization: {str(e)}")
        st.exception(e) # Show traceback for debugging


def display_comparison_plots_tab(df: pd.DataFrame, settings: Dict[str, Any]):
    """
    Display the content for the 'Multi-Plot Comparison' tab.

    Parameters:
    -----------
    df : pd.DataFrame
        The main DataFrame.
    settings : Dict[str, Any]
        The dictionary containing settings from the sidebar.
    """
    # st.markdown("### Comparison Visualization")
    # st.info("Select models or patients to compare using the 'Multi-Plot Comparison Controls' in the sidebar.")

    comparison_list = []
    comparison_entity = ""
    # --- Correction Start: Use comparison_type from settings --- 
    comparison_type = settings.get('comparison_type') # e.g., 'models' or 'patients'
    
    if comparison_type == "models": # Compare models for a selected patient
        selected_comparison_patient = settings.get('comparison_patient')
        models_to_compare = settings.get('models_to_compare', [])
        if selected_comparison_patient:
            if models_to_compare:
                 comparison_list = models_to_compare
                 comparison_entity = "Model"
                 st.markdown(f"<h2 style='font-size: 24px;'>Comparing models for <strong>Patient: {selected_comparison_patient}</strong></h2>", unsafe_allow_html=True)
            else:
                 st.warning("Please select one or more models in the sidebar under 'Multi-Plot Comparison Controls'.")
                 return
        else:
             st.warning("Please select a patient in the sidebar under 'Multi-Plot Comparison Controls' to compare models.")
             return
             
    elif comparison_type == "patients": # Compare patients for a selected model
        selected_comparison_model = settings.get('comparison_model')
        patients_to_compare = settings.get('patients_to_compare', [])
        if selected_comparison_model:
            if patients_to_compare:
                 comparison_list = patients_to_compare
                 comparison_entity = "Patient"
                 st.markdown(f"<h2 style='font-size: 24px;'>Comparing patients for <strong>Model: {selected_comparison_model}</strong></h2>", unsafe_allow_html=True)
            else:
                 st.warning("Please select one or more patients in the sidebar under 'Multi-Plot Comparison Controls'.")
                 return
        else:
             st.warning("Please select a model in the sidebar under 'Multi-Plot Comparison Controls' to compare patients.")
             return
    else: # Should not happen if sidebar logic is correct, but handle defensively
        st.error("Invalid comparison type selected.")
        return
    # --- Correction End ---

    # --- Filter Aggregate Data for Grouped Analysis --- 
    comparison_aggregate_df = pd.DataFrame()
    try:
        if comparison_type == "models":
            if selected_comparison_patient and models_to_compare:
                comparison_aggregate_df = df[
                    (df['patient_number'] == selected_comparison_patient) & 
                    (df['model'].isin(models_to_compare))
                ].copy()
        elif comparison_type == "patients":
             if selected_comparison_model and patients_to_compare:
                comparison_aggregate_df = df[
                    (df['model'] == selected_comparison_model) & 
                    (df['patient_number'].isin(patients_to_compare))
                ].copy()
    except Exception as e:
        st.warning(f"Could not pre-filter aggregate data for analysis: {e}")
        # Allow execution to continue, display_metrics_and_stats handles empty df

    # --- Display Plot Grid --- 
    if not comparison_list:
        st.warning(f"No {comparison_entity}s selected for comparison.") 
        return

    # Display plots in a grid
    num_items = len(comparison_list)
    cols = st.columns(min(num_items, 3)) # Max 3 columns

    for i, item in enumerate(comparison_list):
        with cols[i % 3]:
            # st.markdown(f"##### {comparison_entity}: {item}")
            try:
                # Filter data for the specific item
                # Create a temporary settings dict for filtering this specific plot
                item_settings = settings.copy()
                item_settings['selected_mode'] = 1 # Treat as single plot for filtering
                
                # Set the correct selected_model/selected_patient for the *individual plot filter*
                if comparison_type == "models":
                     item_settings['selected_model'] = item # The model being plotted
                     item_settings['selected_patient'] = selected_comparison_patient # The patient context
                else: # comparison_type == "patients"
                     item_settings['selected_patient'] = item # The patient being plotted
                     item_settings['selected_model'] = selected_comparison_model # The model context

                temp_df = get_filtered_data(df, settings=item_settings)

                if temp_df is not None and not temp_df.empty:
                    # Generate and display the scatter plot for this item - USE NEW SIGNATURE
                    fig = interactive_model_scatter(
                        df=temp_df, # Pass the already filtered df for this item
                        comparison_type=None, # No within-plot coloring needed for comparison grid
                        theme=settings.get('theme', 'plotly_white'),
                        title=f"{comparison_entity}: {item}", # Specific title
                        dot_size=settings.get('dot_size', 8),
                        opacity=settings.get('opacity', 0.7),
                        height=400, # Smaller height for comparison plots
                        # Override margin for multi-plot grid (remove large right margin)
                        margin=dict(l=40, r=40, t=60, b=40), 
                        showlegend=False # Hide legend for subplots
                    )
                    st.plotly_chart(fig, use_container_width=True)

                    # Display compact metrics below each plot
                    metrics = calculate_regression_metrics(temp_df)
                    # st.caption(f"RMSE: {metrics.get('rmse', float('nan')):.2f} | MAE: {metrics.get('mae', float('nan')):.2f} | Corr: {metrics.get('correlation', float('nan')):.4f} | R²: {metrics.get('r_squared', float('nan')):.4f}")
                else:
                    st.caption(f"No data for {comparison_entity} {item}.")

            except Exception as e:
                st.error(f"Error generating plot for {comparison_entity} {item}: {str(e)}")
                # Optionally add st.exception(e) for debugging

    # --- Display Grouped Analysis Section --- 
    # st.markdown("--- ") # Add a separator
    # st.markdown("### Aggregate Analysis for Comparison")
    if not comparison_aggregate_df.empty:
         display_metrics_and_stats(
             filtered_df=comparison_aggregate_df, 
             comparison_type=comparison_type, # 'models' or 'patients'
             settings=settings
         )
    else:
         st.caption("No aggregate data available for detailed analysis based on current selection.")


def display_feature_analysis_tab(df: pd.DataFrame, settings: Dict[str, Any]):
    """
    Display the content for the 'Feature Analysis' tab.
    
    This tab provides dedicated space for analyzing how features behave 
    across different prediction ranges, error levels, or quartiles.

    Parameters:
    -----------
    df : pd.DataFrame
        The main DataFrame.
    settings : Dict[str, Any]
        The dictionary containing settings from the sidebar.
    """
    st.markdown("### 🔍 Feature Analysis")
    st.markdown("Analyze how features behave across different prediction ranges using the same filtering options from the sidebar.")
    
    # --- 1. Extract Dynamic Settings (same as single tab) ---
    comparison_type = settings.get('single_plot_comparison_type')
    context_patient = settings.get('single_plot_context_patient')
    models_to_compare = settings.get('single_plot_models_to_compare', [])
    context_model = settings.get('single_plot_context_model')
    patients_to_compare = settings.get('single_plot_patients_to_compare', [])
    
    # --- 2. Filter Data Based on Settings ---
    filtered_df = pd.DataFrame()
    context_info = ""
    
    try:
        if comparison_type == "models":
            if context_patient and models_to_compare:
                filtered_df = df[
                    (df['patient_number'] == context_patient) & 
                    (df['model'].isin(models_to_compare))
                ].copy()
                
                if set(models_to_compare) == set(settings.get('available_models', [])):
                    model_list_str = "All Models"
                else:
                    model_list_str = f"Models [{', '.join(map(str, models_to_compare))}]"
                context_info = f"Analyzing {model_list_str} for Patient {context_patient}"
            else:
                st.warning("Please select a patient context and at least one model in the sidebar under 'Single Plot Controls'.")
                return

        elif comparison_type == "patients":
            if context_model and patients_to_compare:
                filtered_df = df[
                    (df['model'] == context_model) & 
                    (df['patient_number'].isin(patients_to_compare))
                ].copy()
                
                if set(patients_to_compare) == set(settings.get('available_patients', [])):
                    patient_list_str = "All Patients"
                else:
                    patient_list_str = f"Patients [{', '.join(map(str, patients_to_compare))}]"
                context_info = f"Analyzing {patient_list_str} for Model {context_model}"
            else:
                st.warning("Please select a model context and at least one patient in the sidebar under 'Single Plot Controls'.")
                return
        else:
            st.error("Invalid comparison type selected in sidebar.")
            return
            
        if filtered_df.empty:
            st.warning("No data matches the current selection.")
            return

        # --- 3. Display Context Information and Controls ---
        # Compact header with context and data summary in one row
        col_context, col_data = st.columns([2, 1])
        
        with col_context:
            st.info(f"📊 **Context:** {context_info}")
        
        with col_data:
            # Data summary in compact metrics row
            subcol1, subcol2, subcol3 = st.columns(3)
            with subcol1:
                st.metric("Data Points", len(filtered_df), help="Total number of data points being analyzed")
            with subcol2:
                if 'patient_number' in filtered_df.columns:
                    st.metric("Patients", filtered_df['patient_number'].nunique(), help="Number of unique patients")
            with subcol3:
                if 'model' in filtered_df.columns:
                    st.metric("Models", filtered_df['model'].nunique(), help="Number of unique models")
        
        # --- 4. Feature Analysis Results Section ---
        # Get analysis settings from sidebar
        analysis_type = settings.get('feature_analysis_type', 'By Prediction Range')
        selected_feature = settings.get('feature_analysis_feature', None)
        
        # Check if we have features available and a feature selected
        available_features = get_available_features(filtered_df)
        
        if available_features and selected_feature and selected_feature in available_features:
            # Map analysis type to grouping method
            method_map = {
                "By Prediction Range": "median_split",
                "By Error Level": "error_based", 
                "By Quartiles": "quartiles"
            }
            grouping_method = method_map[analysis_type]
            
            try:
                # Use centralized feature analysis
                from modules.regression_metrics import calculate_feature_group_statistics, format_p_value
                
                analysis_results = calculate_feature_group_statistics(
                    df=filtered_df,
                    feature=selected_feature,
                    grouping_method=grouping_method
                )
                
                if 'error' not in analysis_results:
                    grouped_df = analysis_results['grouped_df']
                    group_stats = analysis_results['group_stats']
                    statistical_tests = analysis_results.get('statistical_tests', {})
                    summary = analysis_results.get('summary', {})
                    
                    # Rename columns for display
                    display_stats = group_stats.copy()
                    feature_cols = [col for col in display_stats.columns if selected_feature in col]
                    for col in feature_cols:
                        new_name = col.replace(f'_{selected_feature}', f' {selected_feature}').replace('_', ' ').title()
                        display_stats = display_stats.rename(columns={col: new_name})
                    
                    display_stats = display_stats.rename(columns={
                        'mean_real': 'Mean Real SBP',
                        'mean_pred': 'Mean Pred SBP',
                        'std_real': 'Std Real SBP',
                        'std_pred': 'Std Pred SBP'
                    })
                    
                    # --- Results Section ---
                    st.markdown("---")
                    st.markdown("### 📊 Analysis Results")
                    
                    # Statistics and visualizations in optimized layout
                    stats_col, viz_space = st.columns([1, 2])
                    
                    with stats_col:
                        st.markdown("**📈 Group Statistics**")
                        # Display compact stats table
                        st.dataframe(display_stats.drop(['group'], axis=1, errors='ignore'), use_container_width=True, height=200)
                        
                        # Statistical significance results
                        if statistical_tests:
                            st.markdown("**📊 Statistical Tests:**")
                            
                            if 'two_group' in statistical_tests:
                                test = statistical_tests['two_group']
                                sig_indicator = "✅ Significant" if test['significant'] else "❌ Not significant"
                                st.caption(f"• {test['test_name']}: p = {format_p_value(test['p_value'])} ({sig_indicator})")
                                
                                if 'effect_size' in test:
                                    effect = test['effect_size']
                                    st.caption(f"• {effect['name']}: {effect['value']:.3f} ({effect['interpretation']})")
                            
                            elif 'multi_group' in statistical_tests:
                                test = statistical_tests['multi_group']
                                sig_indicator = "✅ Significant" if test['significant'] else "❌ Not significant"
                                st.caption(f"• {test['test_name']}: p = {format_p_value(test['p_value'])} ({sig_indicator})")
                        
                        # Key insights
                        st.markdown("**🔍 Key Insights:**")
                        if len(display_stats) >= 2:
                            # Calculate difference between groups for the selected feature
                            feature_mean_col = f'Mean {selected_feature}'
                            if feature_mean_col in display_stats.columns:
                                group_values = display_stats[feature_mean_col].values
                                diff = abs(group_values[0] - group_values[1]) if len(group_values) >= 2 else 0
                                st.caption(f"• Feature difference: {diff:.2f}")
                            
                            # SBP difference
                            if 'Mean Real SBP' in display_stats.columns:
                                sbp_values = display_stats['Mean Real SBP'].values
                                sbp_diff = abs(sbp_values[0] - sbp_values[1]) if len(sbp_values) >= 2 else 0
                                st.caption(f"• SBP difference: {sbp_diff:.2f} mmHg")
                        
                        # Summary info
                        if summary:
                            st.caption(f"• Total samples: {summary.get('total_samples', 'N/A')}")
                            if summary.get('has_significant_difference', False):
                                st.caption("• ⭐ Statistically significant difference detected!")
                    
                    with viz_space:
                        # Visualizations in tabbed interface for better space usage
                        viz_tab1, viz_tab2 = st.tabs(["📈 Distribution", "📦 Box Plots"])
                        
                        with viz_tab1:
                            fig_dist = plot_feature_distribution_by_prediction_range(
                                df=grouped_df,
                                feature=selected_feature,
                                grouping_method=grouping_method,
                                theme=settings.get('theme', 'plotly_white')
                            )
                            st.plotly_chart(fig_dist, use_container_width=True)
                        
                        with viz_tab2:
                            fig_box = plot_feature_comparison_by_prediction_range(
                                df=grouped_df,
                                features=[selected_feature],
                                grouping_method=grouping_method,
                                theme=settings.get('theme', 'plotly_white')
                            )
                            st.plotly_chart(fig_box, use_container_width=True)
                    
                    # Additional analysis section
                    with st.expander("🔬 Advanced Analysis", expanded=False):
                        st.markdown("**Multiple Feature Comparison:**")
                        
                        # Allow selection of multiple features for comparison
                        multi_features = st.multiselect(
                            "Select multiple features to compare:",
                            options=available_features,
                            default=[selected_feature] if selected_feature in available_features else [],
                            key="multi_feature_analysis",
                            help="Compare multiple features side by side"
                        )
                        
                        if len(multi_features) > 1:
                            fig_multi = plot_feature_comparison_by_prediction_range(
                                df=grouped_df,
                                features=multi_features,
                                grouping_method=grouping_method,
                                theme=settings.get('theme', 'plotly_white')
                            )
                            st.plotly_chart(fig_multi, use_container_width=True)
                        else:
                            st.info("Select at least 2 features to see multi-feature comparison.")
                            
                else:
                    st.error(f"Feature analysis failed: {analysis_results.get('error', 'Unknown error')}")
                    
            except Exception as e:
                st.error(f"Error in feature analysis: {e}")
                st.exception(e)  # Show traceback for debugging
        else:
            # Handle cases where no features are available or none selected
            if not available_features:
                st.warning("🚫 **No Additional Features Available**")
                st.info("The dataset only contains the standard regression columns (model, patient_number, real, pred). To use Feature Analysis, please upload a dataset with additional feature columns such as demographics or derived variables.")
                
                # Show example of expected format
                with st.expander("💡 Expected Dataset Format", expanded=False):
                    st.markdown("""
                    **Your dataset should include additional columns like:**
                    - Demographic features: age, sex, weight, height, bmi
                    - Physiological features: heart_rate, blood_pressure_baseline
                    - Signal-derived features: ppg_amplitude, signal_quality
                    - Clinical features: medication_status, risk_factors
                    
                    **Example dataset structure:**
                    ```
                    model | patient_number | real | pred | age | sex | bmi | heart_rate | ...
                    model1|      001       | 120  | 118  | 45  |  1  |25.3|     72     | ...
                    model1|      001       | 122  | 119  | 45  |  1  |25.3|     74     | ...
                    ```
                    """)
            else:
                # Features are available but none selected
                st.info("🎯 **Select a Feature to Analyze**")
                st.markdown("Use the **Feature Analysis Controls** in the sidebar to:")
                st.markdown("• Choose an analysis perspective (prediction range, error level, or quartiles)")
                st.markdown("• Select a feature to analyze from the available options")
                st.markdown(f"**Available features:** {', '.join(available_features)}")

    except Exception as e:
        st.error(f"Error in feature analysis tab: {str(e)}")
        st.exception(e)


def display_data_overview_tab(df: pd.DataFrame, settings: Dict[str, Any]):
    """
    Display the content for the 'Data Overview' tab.

    Parameters:
    -----------
    df : pd.DataFrame
        The main DataFrame.
    settings : Dict[str, Any]
        The dictionary containing settings from the sidebar.
    """
    st.markdown("### Data Overview")
    st.info("Showing data based on filters selected in the sidebar (if Mode 1, 2, or 3 is active).")

    # Use get_filtered_data to respect sidebar selections
    filtered_overview_df = get_filtered_data(df, settings=settings)

    # Display summary metrics for the filtered data
    if filtered_overview_df is not None and not filtered_overview_df.empty:
        st.markdown("#### Filtered Data Summary")
        col1, col2 = st.columns(2)
        with col1:
            st.metric("Data Points Shown", len(filtered_overview_df))
            model_count = 0
            patient_count = 0
            if 'model' in filtered_overview_df.columns:
                 model_count = filtered_overview_df['model'].nunique()
            if 'patient_number' in filtered_overview_df.columns:
                 patient_count = filtered_overview_df['patient_number'].nunique()

            st.metric("Unique Models Shown", model_count)
            st.metric("Unique Patients Shown", patient_count)
        with col2:
             st.markdown("**Performance Metrics (Filtered Data)**")
             metrics = calculate_regression_metrics(filtered_overview_df)
             st.markdown(f"- **RMSE:** {metrics.get('rmse', float('nan')):.2f}")
             st.markdown(f"- **MAE:** {metrics.get('mae', float('nan')):.2f}")
             st.markdown(f"- **Correlation:** {metrics.get('correlation', float('nan')):.4f}")

        st.markdown("#### Filtered Data Table")
        # Add options for showing rows later if needed
        st.dataframe(filtered_overview_df, use_container_width=True)
    else:
        st.warning("No data matches the current filter selection.") 