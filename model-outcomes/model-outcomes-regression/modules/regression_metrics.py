"""
Module for regression metrics calculation.

This module provides functions to calculate performance metrics for regression tasks,
such as Root Mean Squared Error (RMSE), Mean Absolute Error (MAE), and Pearson Correlation Coefficient.
It also provides centralized feature analysis functions following the pattern from classification_metrics.py.
"""
import streamlit as st
import pandas as pd
import numpy as np
import math
from typing import Dict, Optional, List, Tuple, Any
from scipy import stats

@st.cache_data
def _calculate_regression_metrics_raw(real: np.ndarray, pred: np.ndarray) -> Dict[str, float]:
    """
    Calculates core regression performance metrics with caching.

    Parameters:
    -----------
    real : np.ndarray
        Array of ground truth numerical values.
    pred : np.ndarray
        Array of predicted numerical values.

    Returns:
    --------
    Dict[str, float]
        Dictionary containing calculated metrics:
        - 'rmse': Root Mean Squared Error
        - 'mae': Mean Absolute Error
        - 'correlation': Pearson Correlation Coefficient (NaN if < 2 data points or zero variance)
        - 'r_squared': R-squared (coefficient of determination)
    """
    if not isinstance(real, np.ndarray):
        real = np.array(real)
    if not isinstance(pred, np.ndarray):
        pred = np.array(pred)

    if real.shape != pred.shape:
        raise ValueError("Input arrays 'real' and 'pred' must have the same shape.")
    if len(real) == 0:
        return {'rmse': float('nan'), 'mae': float('nan'), 'correlation': float('nan'), 'r_squared': float('nan')}

    metrics = {}
    valid_mask = ~np.isnan(real) & ~np.isnan(pred)
    real_valid = real[valid_mask]
    pred_valid = pred[valid_mask]

    if len(real_valid) == 0:
         return {'rmse': float('nan'), 'mae': float('nan'), 'correlation': float('nan'), 'r_squared': float('nan')}

    metrics['rmse'] = math.sqrt(np.mean((real_valid - pred_valid)**2))
    metrics['mae'] = np.mean(np.abs(real_valid - pred_valid))

    if len(real_valid) > 1:
        if np.std(real_valid) > 1e-9 and np.std(pred_valid) > 1e-9:
             metrics['correlation'] = np.corrcoef(real_valid, pred_valid)[0, 1]
             
             ss_residual = np.sum((real_valid - pred_valid) ** 2)
             ss_total = np.sum((real_valid - np.mean(real_valid)) ** 2)
             
             if ss_total > 1e-9:
                 metrics['r_squared'] = 1 - (ss_residual / ss_total)
             else:
                 metrics['r_squared'] = float('nan')
        else:
             metrics['correlation'] = float('nan')
             metrics['r_squared'] = float('nan')
    else:
        metrics['correlation'] = float('nan')
        metrics['r_squared'] = float('nan')

    return metrics


def calculate_regression_metrics(df: pd.DataFrame, real_col: str = 'real', pred_col: str = 'pred') -> Optional[Dict[str, float]]:
    """
    Calculate performance metrics for a DataFrame with specified real and predicted columns.

    Parameters:
    -----------
    df : pd.DataFrame
        DataFrame containing the real and predicted values.
    real_col : str, optional
        Name of the column containing the real (ground truth) values. Defaults to 'real'.
    pred_col : str, optional
        Name of the column containing the predicted values. Defaults to 'pred'.

    Returns:
    --------
    Optional[Dict[str, float]]
        Dictionary with calculated metrics ('rmse', 'mae', 'correlation', 'r_squared') if successful.
        Returns dictionary with NaNs if input is invalid, empty, or calculation is not possible.
    """
    if df is None or df.empty:
        return {'rmse': float('nan'), 'mae': float('nan'), 'correlation': float('nan'), 'r_squared': float('nan')}

    if real_col not in df.columns or pred_col not in df.columns:
        return {'rmse': float('nan'), 'mae': float('nan'), 'correlation': float('nan'), 'r_squared': float('nan')}

    real_values = df[real_col].values
    pred_values = df[pred_col].values

    try:
        return _calculate_regression_metrics_raw(real_values, pred_values)
    except ValueError as e:
        st.error(f"Error calculating metrics: {e}")
        return {'rmse': float('nan'), 'mae': float('nan'), 'correlation': float('nan'), 'r_squared': float('nan')}


def get_available_features(df: pd.DataFrame) -> List[str]:
    """
    Get list of available feature columns for analysis.
    
    Excludes standard regression columns (model, patient_number, real, pred).
    
    Parameters:
    -----------
    df : pd.DataFrame
        Input DataFrame to analyze.
        
    Returns:
    --------
    List[str]
        List of feature column names available for analysis.
    """
    if df is None or df.empty:
        return []
    
    standard_cols = {'model', 'patient_number', 'real', 'pred', 'point_index', 'group', 'group_label'}
    available_features = [col for col in df.columns if col not in standard_cols]
    
    return available_features


@st.cache_data
def create_prediction_groups(
    df: pd.DataFrame,
    method: str = "median_split",
    real_col: str = 'real',
    pred_col: str = 'pred'
) -> pd.DataFrame:
    """
    Create groups for feature analysis based on regression predictions.
    
    Parameters:
    -----------
    df : pd.DataFrame
        Input DataFrame containing prediction data.
    method : str, optional
        Grouping method to use:
        - "median_split": High/Low based on prediction median
        - "error_based": High/Low error based on absolute error median
        - "quartiles": 4 groups based on prediction quartiles
        Defaults to "median_split".
    real_col : str, optional
        Name of the real values column. Defaults to 'real'.
    pred_col : str, optional
        Name of the predicted values column. Defaults to 'pred'.
        
    Returns:
    --------
    pd.DataFrame
        DataFrame with added 'group' and 'group_label' columns.
    """
    if df is None or df.empty:
        return pd.DataFrame()
    
    if real_col not in df.columns or pred_col not in df.columns:
        st.warning(f"Required columns '{real_col}' and/or '{pred_col}' not found.")
        return df.copy()
    
    result_df = df.copy()
    
    try:
        if method == "median_split":
            median_pred = result_df[pred_col].median()
            result_df['group'] = (result_df[pred_col] >= median_pred).astype(int)
            result_df['group_label'] = result_df['group'].map({
                0: f"Low Predictions (< {median_pred:.2f})",
                1: f"High Predictions (≥ {median_pred:.2f})"
            })
            
        elif method == "error_based":
            result_df['abs_error'] = np.abs(result_df[real_col] - result_df[pred_col])
            median_error = result_df['abs_error'].median()
            result_df['group'] = (result_df['abs_error'] >= median_error).astype(int)
            result_df['group_label'] = result_df['group'].map({
                0: f"Low Error (< {median_error:.2f})",
                1: f"High Error (≥ {median_error:.2f})"
            })
            result_df = result_df.drop('abs_error', axis=1)
            
        elif method == "quartiles":
            result_df['group'] = pd.qcut(result_df[pred_col], q=4, labels=False)
            quartile_ranges = pd.qcut(result_df[pred_col], q=4, retbins=True)[1]
            result_df['group_label'] = result_df['group'].map({
                0: f"Q1 ({quartile_ranges[0]:.2f}-{quartile_ranges[1]:.2f})",
                1: f"Q2 ({quartile_ranges[1]:.2f}-{quartile_ranges[2]:.2f})",
                2: f"Q3 ({quartile_ranges[2]:.2f}-{quartile_ranges[3]:.2f})",
                3: f"Q4 ({quartile_ranges[3]:.2f}-{quartile_ranges[4]:.2f})"
            })
            
        else:
            st.warning(f"Unknown grouping method: {method}")
            return result_df
            
    except Exception as e:
        st.error(f"Error creating prediction groups: {e}")
        return result_df
    
    return result_df


def format_p_value(p_value: float) -> str:
    """
    Format p-value for display with appropriate scientific notation.
    
    Parameters:
    -----------
    p_value : float
        P-value to format.
        
    Returns:
    --------
    str
        Formatted p-value string.
    """
    if pd.isna(p_value):
        return "N/A"
    elif p_value < 0.001:
        return f"{p_value:.2e}"
    elif p_value < 0.01:
        return f"{p_value:.4f}"
    else:
        return f"{p_value:.3f}"


@st.cache_data 
def calculate_feature_group_statistics(
    df: pd.DataFrame,
    feature: str,
    grouping_method: str = "median_split"
) -> Dict[str, Any]:
    """
    Calculate comprehensive statistics for feature analysis by prediction groups.
    
    Centralized function following classification_metrics.py pattern for consistent
    statistical calculations across the application.
    
    Parameters:
    -----------
    df : pd.DataFrame
        DataFrame with prediction data.
    feature : str
        Name of the feature column to analyze.
    grouping_method : str, optional
        Grouping method to use. Defaults to "median_split".
        
    Returns:
    --------
    Dict[str, Any]
        Dictionary containing:
        - 'grouped_df': DataFrame with group columns added
        - 'group_stats': Statistics by group
        - 'statistical_tests': Statistical significance tests
        - 'effect_sizes': Effect size calculations
        - 'summary': Overall summary statistics
    """
    if df is None or df.empty:
        return {'error': 'Empty DataFrame'}
    
    if feature not in df.columns:
        return {'error': f'Feature {feature} not found'}
    
    try:
        grouped_df = create_prediction_groups(df, method=grouping_method)
        
        if grouped_df.empty or 'group' not in grouped_df.columns:
            return {'error': 'Failed to create groups'}
        
        # Calculate group statistics
        group_stats = grouped_df.groupby(['group', 'group_label']).agg({
            feature: ['count', 'mean', 'std', 'median', 'min', 'max'],
            'real': ['mean', 'std'],
            'pred': ['mean', 'std']
        }).round(4)
        
        # Flatten column names
        group_stats.columns = [f"{col[1]}_{col[0]}" if col[1] else col[0] for col in group_stats.columns]
        group_stats = group_stats.reset_index()
        
        # Statistical significance tests
        statistical_tests = {}
        groups = sorted(grouped_df['group'].unique())
        
        if len(groups) == 2:
            # Two-group comparison
            group_0_data = grouped_df[grouped_df['group'] == groups[0]][feature].dropna()
            group_1_data = grouped_df[grouped_df['group'] == groups[1]][feature].dropna()
            
            if len(group_0_data) > 1 and len(group_1_data) > 1:
                # Test for normality
                try:
                    if len(group_0_data) <= 5000 and len(group_1_data) <= 5000:
                        _, p_norm_0 = stats.shapiro(group_0_data)
                        _, p_norm_1 = stats.shapiro(group_1_data)
                        is_normal = p_norm_0 > 0.05 and p_norm_1 > 0.05
                    else:
                        is_normal = False
                except:
                    is_normal = False
                
                # Choose appropriate test
                if is_normal:
                    statistic, p_value = stats.ttest_ind(group_0_data, group_1_data)
                    test_name = "Independent t-test"
                    statistic_name = "t-statistic"
                else:
                    statistic, p_value = stats.mannwhitneyu(group_0_data, group_1_data, alternative='two-sided')
                    test_name = "Mann-Whitney U test"
                    statistic_name = "U-statistic"
                
                # Calculate effect size
                if not is_normal:
                    # Rank-biserial correlation for Mann-Whitney U
                    n1, n2 = len(group_0_data), len(group_1_data)
                    effect_size_val = 1 - (2 * statistic) / (n1 * n2)
                    effect_size_name = "rank-biserial correlation"
                else:
                    # Cohen's d for t-test
                    pooled_std = np.sqrt(((len(group_0_data) - 1) * group_0_data.var() + 
                                        (len(group_1_data) - 1) * group_1_data.var()) / 
                                       (len(group_0_data) + len(group_1_data) - 2))
                    effect_size_val = (group_1_data.mean() - group_0_data.mean()) / pooled_std
                    effect_size_name = "Cohen's d"
                
                # Effect size interpretation
                if abs(effect_size_val) < 0.1:
                    interpretation = "negligible"
                elif abs(effect_size_val) < 0.3:
                    interpretation = "small"
                elif abs(effect_size_val) < 0.5:
                    interpretation = "medium"
                else:
                    interpretation = "large"
                
                statistical_tests['two_group'] = {
                    'test_name': test_name,
                    'statistic_name': statistic_name,
                    'statistic': statistic,
                    'p_value': p_value,
                    'significant': p_value < 0.05,
                    'is_normal': is_normal,
                    'effect_size': {
                        'name': effect_size_name,
                        'value': effect_size_val,
                        'interpretation': interpretation
                    }
                }
                
        elif len(groups) > 2:
            # Multi-group comparison
            group_data = [grouped_df[grouped_df['group'] == g][feature].dropna() for g in groups]
            group_data = [g for g in group_data if len(g) > 0]
            
            if len(group_data) > 1 and all(len(g) > 1 for g in group_data):
                try:
                    normality_tests = [stats.shapiro(g)[1] for g in group_data if len(g) <= 5000]
                    is_normal = all(p > 0.05 for p in normality_tests) if normality_tests else False
                except:
                    is_normal = False
                
                if is_normal:
                    statistic, p_value = stats.f_oneway(*group_data)
                    test_name = "One-way ANOVA"
                    statistic_name = "F-statistic"
                else:
                    statistic, p_value = stats.kruskal(*group_data)
                    test_name = "Kruskal-Wallis test"
                    statistic_name = "H-statistic"
                
                statistical_tests['multi_group'] = {
                    'test_name': test_name,
                    'statistic_name': statistic_name,
                    'statistic': statistic,
                    'p_value': p_value,
                    'significant': p_value < 0.05,
                    'is_normal': is_normal
                }
        
        # Summary statistics
        summary = {
            'total_samples': len(grouped_df),
            'feature_name': feature,
            'grouping_method': grouping_method,
            'num_groups': len(groups),
            'groups': groups,
            'has_significant_difference': any(
                test.get('significant', False) 
                for test in statistical_tests.values()
            )
        }
        
        return {
            'grouped_df': grouped_df,
            'group_stats': group_stats,
            'statistical_tests': statistical_tests,
            'summary': summary
        }
        
    except Exception as e:
        return {'error': f'Analysis failed: {str(e)}'} 