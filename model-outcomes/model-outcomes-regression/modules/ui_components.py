"""
Reusable UI components for the Model Prediction Visualizer application.
"""
import streamlit as st
import pandas as pd
from typing import Dict, Any, Optional
import base64
from pathlib import Path

# --- display_welcome_screen (Moved from auth.py) ---
def display_welcome_screen():
    """
    Display welcome information based on user authentication status and data loading state.
    Provides personalized experience and clear path to next actions.
    """
    # Get user info and check if there's a loaded file
    user_name = st.session_state.user_info.get("name", "User") if hasattr(st.session_state, "user_info") else "User"
    # Check based on session state key used in process_uploaded_file
    has_data = "uploaded_data" in st.session_state and st.session_state.uploaded_data is not None
    
    # Section 1: Personalized Welcome with Status
    st.markdown(f"## Welcome, {user_name}!")
    
    # Show appropriate system status message
    if has_data:
        st.success("✅ Data loaded successfully. You can now explore the visualizations.")
    else:
        st.info("🔍 No data loaded yet. Please upload a file to begin analysis.")
    
    # Section 2: Quick Start Panel
    st.markdown("### 🚀 Quick Start")
    
    quick_start_col1, quick_start_col2 = st.columns([1, 1])
    
    with quick_start_col1:
        st.markdown("""
        **Actions:**
        1. Upload your prediction data file using the sidebar
        2. Select visualization options
        3. Interact with the charts to gain insights
        """)
    
    with quick_start_col2:
        # Show different options based on data state
        if has_data:
            st.markdown("**Current Options:**")
            # TODO: Implement clear data functionality if needed
            # if st.button("Clear Current Data", key="clear_data", help="Remove the currently loaded data to start fresh"):
            #     if 'uploaded_data' in st.session_state: del st.session_state['uploaded_data']
            #     if 'current_file' in st.session_state: del st.session_state['current_file']
            #     st.rerun()
            st.caption("To load new data, use the sidebar.") # Placeholder

            # Additional option for users with data (Example - not implemented)
            # if "last_settings" in st.session_state:
            #     st.button("Restore Previous Settings", key="restore_settings",
            #              help="Restore your last visualization settings")
        else:
            st.markdown("**Need Sample Data?**")
            sample_data = pd.DataFrame({
                'model': ['model_a', 'model_a', 'model_b', 'model_b'],
                'patient_number': [1, 2, 1, 2],
                'real': [120, 130, 125, 135],
                'pred': [118, 132, 127, 130]
            })
            
            # Template download option (mantenemos solo esta opción)
            csv = sample_data.to_csv(index=False).encode('utf-8')
            st.download_button(
                "Download CSV Template",
                csv,
                "prediction_template.csv",
                "text/csv",
                key='download-template',
                help="Download a template CSV file with the required structure"
            )
    
    # Section 3: Data Requirements (collapsible)
    with st.expander("📋 Data Format Requirements", expanded=not has_data):
        req_col1, req_col2 = st.columns([3, 2])
        
        with req_col1:
            st.markdown("""
            Your CSV file must contain these columns:
            
            | Column | Description | Type |
            | ------ | ----------- | ---- |
            | **model** | Prediction model identifier | string |
            | **patient_number** | Patient identifier | integer |
            | **real** | Actual/ground truth values | float |
            | **pred** | Predicted values | float |
            """)
        
        with req_col2:
            # Show compact example
            st.markdown("**Example Data:**")
            try:
                st.dataframe(
                    sample_data.head(3),
                    hide_index=True,
                    column_config={
                        "model": st.column_config.TextColumn("Model"),
                        "patient_number": st.column_config.NumberColumn("Patient"),
                        "real": st.column_config.NumberColumn("Real", format="%.1f"),
                        "pred": st.column_config.NumberColumn("Pred", format="%.1f"),
                    },
                    height=130
                )
            except NameError:
                 st.caption("Sample data not available for display.") # Handle case where sample_data wasn't defined
    
    # Section 4: Visualization Modes (collapsible)
    with st.expander("🔍 Available Visualization Modes", expanded=False):
        st.markdown("""
        This tool offers multiple ways to analyze your data:
        
        - **Single Model, Single Patient**: Detailed view of predictions for one patient
        - **Single Model, All Patients**: Compare performance across patients
        - **Single Patient, All Models**: Compare different models for the same patient
        - **Comparison View**: Side-by-side comparison with customizable metrics
        
        Each mode provides different insights into your model's performance.
        """)
        
        # Add visual cues for different visualization modes
        modes_col1, modes_col2 = st.columns([1, 1])
        
        with modes_col1:
            st.markdown("**Performance Metrics:**")
            st.markdown("- Root Mean Squared Error (RMSE)")
            st.markdown("- Mean Absolute Error (MAE)")
            st.markdown("- Correlation Analysis")
        
        with modes_col2:
            st.markdown("**Visualization Types:**")
            st.markdown("- Scatter plots with reference lines")
            st.markdown("- Comparative bar charts")
            # Prediction distribution not typically used in regression visualization
            # st.markdown("- Data point distribution views")
    
    # Section 5: Next Steps Guide
    st.markdown("### ⏭️ Next Steps")
    
    if not has_data:
        st.markdown("""
        1. **Upload a CSV file** using the file uploader in the sidebar
        2. Once data is loaded, you'll be able to select visualization options
        3. Explore different visualization modes to gain insights
        
        Need help? Expand the sections above for detailed information.
        """)
    else:
        st.markdown("""
        1. **Select visualization options** from the sidebar
        2. Interact with the charts to explore your data
        3. Try different visualization modes for deeper insights
        
        💡 **Tip**: Toggle between dark and light themes using the menu in the top right corner.
        """)
        
    # Update session state for tracking user's journey
    if "welcome_screen_viewed" not in st.session_state:
        st.session_state.welcome_screen_viewed = True 

# --- New Function: display_regression_summary ---
def display_regression_summary(metrics: Dict[str, float], total_points: int, filtered_df=None, total_df_points: int = None, active_tab: str = None, settings: Dict[str, Any] = None):
    """
    Display a summary of regression metrics based on the current view's filtered data.
    
    Parameters:
    -----------
    metrics : Dict[str, float]
        Dictionary of metrics, must include 'rmse', 'mae', 'correlation', and 'r_squared'.
    total_points : int
        Number of data points used for the metrics calculation (reflects filtering).
    filtered_df : pd.DataFrame, optional
        The filtered DataFrame used to calculate these metrics. (Used internally, not displayed).
    total_df_points : int, optional
        Total number of data points in the original dataset before filtering.
    active_tab : str, optional
        Current active tab in the application ("single" or "comparison").
    settings : Dict[str, Any], optional
        Dictionary with current sidebar settings, used to show the context caption.
    """
    # Create a 5-column metrics display
    cols = st.columns(5)
    
    with cols[0]:
        st.metric("RMSE", f"{metrics.get('rmse', float('nan')):.3f}")
    with cols[1]:
        st.metric("MAE", f"{metrics.get('mae', float('nan')):.3f}")
    with cols[2]:
        st.metric("Correlation", f"{metrics.get('correlation', float('nan')):.4f}")
    with cols[3]:
        st.metric("R²", f"{metrics.get('r_squared', float('nan')):.4f}")
    with cols[4]:
        st.metric("Data Points", f"{total_points}")
    
    # Show only the filtering context caption
    if active_tab and settings:
        context_cols = st.columns([3, 1])
        with context_cols[0]:
            context_message = "📊 **Context:** "
            if active_tab == "single":
                # --- Use NEW settings keys for single tab context ---
                comparison_type = settings.get('single_plot_comparison_type')
                context_patient = settings.get('single_plot_context_patient')
                models_to_compare = settings.get('single_plot_models_to_compare', [])
                context_model = settings.get('single_plot_context_model')
                patients_to_compare = settings.get('single_plot_patients_to_compare', [])
                
                if comparison_type == "models":
                    model_str = f"{len(models_to_compare)} model(s)" if len(models_to_compare) > 1 else f"Model {models_to_compare[0] if models_to_compare else 'N/A'}"
                    patient_str = f"Patient {context_patient if context_patient else 'N/A'}"
                    context_message += f"Metrics for {model_str} on {patient_str}"
                elif comparison_type == "patients":
                    model_str = f"Model {context_model if context_model else 'N/A'}"
                    patient_str = f"{len(patients_to_compare)} patient(s)" if len(patients_to_compare) > 1 else f"Patient {patients_to_compare[0] if patients_to_compare else 'N/A'}"
                    context_message += f"Metrics for {patient_str} on {model_str}"
                else:
                    context_message += "Invalid single view selection."
                    
                st.caption(context_message)
                # --- REMOVED OLD logic based on mode ---
                # mode = settings.get('selected_mode', 1)
                # if mode == 1:
                #     st.caption(f"📊 **Context:** Metrics for Model: **{settings.get('selected_model', 'N/A')}** / Patient: **{settings.get('selected_patient', 'N/A')}**")
                # elif mode == 2:
                #     st.caption(f"📊 **Context:** Metrics for Model: **{settings.get('selected_model', 'N/A')}** across all patients")
                # elif mode == 3:
                #     st.caption(f"📊 **Context:** Metrics for Patient: **{settings.get('selected_patient', 'N/A')}** across all models")
            elif active_tab == "comparison":
                # --- Keep existing logic for comparison tab --- 
                comparison_type = settings.get('comparison_type')
                if comparison_type == "models":
                    st.caption(f"📊 **Context:** Aggregate metrics for comparing models ({len(settings.get('models_to_compare',[]))} selected) on Patient **{settings.get('comparison_patient', 'N/A')}**")
                elif comparison_type == "patients":
                    st.caption(f"📊 **Context:** Aggregate metrics for comparing patients ({len(settings.get('patients_to_compare',[]))} selected) on Model **{settings.get('comparison_model', 'N/A')}**")
                else: 
                     st.caption("📊 **Context:** Comparison view selected.") # Fallback caption
            else:
                 st.caption("📊 **Context:** Unknown view")
        
        with context_cols[1]: # Keep percentage display
            if total_df_points and total_points < total_df_points:
                 percent = int((total_points / total_df_points) * 100) if total_df_points > 0 else 0
                 st.caption(f"⚠️ Using {percent}% of data")
    
    # Removed the data preview section (dataframe, download, checkbox)
    # Display a warning if no data points are available for the calculated metrics
    if total_points == 0:
         st.warning("No data points match the current filter settings for metric calculation. Try adjusting filters.") 