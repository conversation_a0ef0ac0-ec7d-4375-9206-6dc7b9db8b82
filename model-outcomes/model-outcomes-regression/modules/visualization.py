"""
Module for data visualization functions.

This module provides functions to create interactive visualizations for model
predictions, including scatter plots and metrics bar plots.
"""
import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
import plotly.io as pio
from typing import Dict, List, Any, Optional, Tuple
import math

from modules.regression_metrics import calculate_regression_metrics, calculate_feature_group_statistics, format_p_value


def interactive_model_scatter(
    df: pd.DataFrame,
    comparison_type: Optional[str] = None,
    title: Optional[str] = None,
    dot_size: int = 8,
    opacity: float = 0.7,
    theme: str = "plotly_white",
    height: Optional[int] = None,
    width: Optional[int] = None,
    margin: Optional[Dict[str, int]] = None,
    showlegend: bool = True,
    enable_automargin: bool = True
) -> go.Figure:
    """
    Creates an interactive scatter plot of real vs predicted values.

    Parameters:
    -----------
    df : pd.DataFrame
        Pre-filtered DataFrame containing the data to plot.
    comparison_type : str, optional
        Type of comparison being shown ('models', 'patients', or None).
    title : str, optional
        Plot title. If None, uses a default title.
    dot_size : int, optional
        Size of scatter plot points. Defaults to 8.
    opacity : float, optional
        Opacity of scatter plot points (0-1). Defaults to 0.7.
    theme : str, optional
        Plotly theme template. Defaults to "plotly_white".
    height : int, optional
        Plot height in pixels.
    width : int, optional
        Plot width in pixels.
    margin : Dict[str, int], optional
        Plot margins. If None, uses default margins.
    showlegend : bool, optional
        Whether to show the legend. Defaults to True.
    enable_automargin : bool, optional
        Whether to enable automatic margins. Defaults to True.

    Returns:
    --------
    go.Figure
        Plotly figure object containing the scatter plot.
    """
    # Validation
    available_templates = list(pio.templates)
    if theme not in available_templates:
        st.warning(f"Theme '{theme}' not available. Using default theme.")
        theme = "plotly_white"
    if dot_size <= 0: 
        dot_size = 8
    if not 0 <= opacity <= 1: 
        opacity = 0.7
    
    required_cols = ['real', 'pred']
    if not all(col in df.columns for col in required_cols):
        st.error(f"Missing required columns ['real', 'pred'] in DataFrame.")
        return go.Figure().update_layout(title="Error: Missing required columns")

    plot_df = df.copy()

    # Add point index if missing
    if 'point_index' not in plot_df.columns:
         plot_df = plot_df.reset_index().rename(columns={'index': 'point_index'})

    # Handle empty input data
    if plot_df.empty:
        st.warning("No data provided for plotting.")
        fig = go.Figure()
        fig.update_layout(
            title=(title or "No Data Available"), 
            xaxis={'visible': False}, 
            yaxis={'visible': False},
            annotations=[{
                'text': "No data matching selection.", 
                'xref': "paper", 
                'yref': "paper", 
                'showarrow': False, 
                'font': {'size': 16}
            }],
            height=height, 
            width=width, 
            template=theme
        )
        return fig

    # Determine coloring logic
    color_col: Optional[str] = None
    if comparison_type == "models":
        if 'model' in plot_df.columns:
            color_col = 'model'
        else:
            st.warning("Coloring by 'model' requested, but 'model' column is missing.")
    elif comparison_type == "patients":
        if 'patient_number' in plot_df.columns:
            color_col = 'patient_number'
            # Convert patient_number to string for discrete coloring
            if pd.api.types.is_numeric_dtype(plot_df[color_col]):
                 plot_df[color_col] = plot_df[color_col].astype(str)
        else:
            st.warning("Coloring by 'patient_number' requested, but 'patient_number' column is missing.")

    # Create the scatter plot
    common_params = {
        'x': 'real',
        'y': 'pred',
        'title': title,
        'labels': {
            'real': 'Real Value', 
            'pred': 'Predicted Value', 
            'point_index': 'Index', 
            'model':'Model', 
            'patient_number':'Patient'
        },
        'hover_data': ['point_index', 'patient_number', 'model', 'real', 'pred'],
        'template': theme,
        'opacity': opacity,
        'height': height,
        'width': width
    }

    if color_col:
        fig = px.scatter(plot_df, color=color_col, size_max=dot_size, **common_params)
    else:
        fig = px.scatter(plot_df, size_max=dot_size, **common_params)

    # Calculate axis limits
    valid_values = pd.concat([plot_df['real'], plot_df['pred']]).dropna()
    if valid_values.empty:
        display_lim_min, display_lim_max = 0, 1
    else:
        data_min = valid_values.min()
        data_max = valid_values.max()
        if data_min >= data_max:
            display_lim_max = data_min + 1 if not np.isnan(data_min) else 1
            display_lim_min = data_min if not np.isnan(data_min) else 0
        else:
            display_lim_min, display_lim_max = data_min, data_max

    fig.update_layout(
        yaxis=dict(
            title=dict(text='Predicted', standoff=2),
            range=[display_lim_min, display_lim_max], 
            constrain="domain",
            showgrid=True, 
            gridcolor='rgba(128,128,128,0.1)',
            automargin=enable_automargin
        ),
        xaxis=dict(
            title=dict(text='Real', standoff=2),
            range=[display_lim_min, display_lim_max], 
            constrain="domain",
            showgrid=True, 
            gridcolor='rgba(128,128,128,0.1)',
            automargin=enable_automargin
        ),
        legend=dict(
            orientation="v",
            yanchor="top", 
            y=1.0,
            xanchor="left", 
            x=1.02, 
            bgcolor="rgba(0,0,0,0)", 
            bordercolor="rgba(0,0,0,0)"
        ),
        margin=margin if margin is not None else dict(l=50, r=170, t=80, b=50, pad=4),
        paper_bgcolor='rgba(0,0,0,0)',
        plot_bgcolor='rgba(0,0,0,0)',
        showlegend=showlegend
    )

    # Add reference line (y=x)
    diagonal_range = [display_lim_min, display_lim_max] 
    fig.add_trace(
        go.Scatter(
            x=diagonal_range, 
            y=diagonal_range, 
            mode='lines',
            line=dict(color='rgba(128,128,128,0.5)', width=2, dash='dash'),
            name='y = x', 
            showlegend=False
        )
    )

    # Calculate and add metrics annotation
    metrics = calculate_regression_metrics(plot_df)
    if metrics:
        rmse = metrics.get('rmse', np.nan)
        mae = metrics.get('mae', np.nan)
        correlation = metrics.get('correlation', np.nan)
        r_squared = metrics.get('r_squared', np.nan)
        
        annotation_text = (
            f"RMSE: {rmse:.3f}<br>"
            f"MAE: {mae:.3f}<br>"
            f"Correlation: {correlation:.4f}<br>"
            f"R²: {r_squared:.4f}"
        )
        
        fig.add_annotation(
            x=0.02, y=0.98,
            xref="paper", yref="paper",
            text=annotation_text,
            showarrow=False,
            font=dict(size=12),
            bgcolor="rgba(0,0,0,0)",
            bordercolor="rgba(0,0,0,0)",
            borderwidth=0,
            xanchor="left", yanchor="top"
        )

    return fig


def create_metrics_bar_plot(
    metrics_data: List[Dict[str, Any]],
    metric_type: str = "RMSE",
    comparison_type: str = "models",
    height: int = 300,
    width: int = 500,
    theme: str = "plotly_white",
    show_data_points: bool = True
) -> go.Figure:
    """
    Creates a bar plot for comparing metrics across models or patients.

    Parameters:
    -----------
    metrics_data : List[Dict[str, Any]]
        List of dictionaries containing metrics data for each entity.
    metric_type : str, optional
        Type of metric to plot ('RMSE', 'MAE', etc.). Defaults to "RMSE".
    comparison_type : str, optional
        Type of comparison ('models' or 'patients'). Defaults to "models".
    height : int, optional
        Plot height in pixels. Defaults to 300.
    width : int, optional
        Plot width in pixels. Defaults to 500.
    theme : str, optional
        Plotly theme template. Defaults to "plotly_white".
    show_data_points : bool, optional
        Whether to show data points as secondary axis. Defaults to True.

    Returns:
    --------
    go.Figure
        Plotly figure object containing the bar plot.
    """
    entity_key = "Model" if comparison_type == "models" else "Patient"
    x_label = "Model" if comparison_type == "models" else "Patient"
    y_label = metric_type

    try:
        # Prepare data for plotting
        plot_data = []
        for item in metrics_data:
            entity = item.get(entity_key)
            metric_value = item.get(metric_type)
            data_points_val = item.get("Data Points")

            if entity is not None and metric_value is not None and data_points_val is not None:
                try:
                    numeric_metric = float(metric_value) if not pd.isna(metric_value) else np.nan
                    numeric_dp = int(data_points_val) if not pd.isna(data_points_val) else 0

                    plot_data.append({
                        "entity": str(entity),
                        "metric": numeric_metric,
                        "data_points": numeric_dp
                    })
                except (ValueError, TypeError):
                    st.warning(f"Skipping item with invalid data: {item}")
                    continue

        if not plot_data:
             fig = go.Figure()
             fig.update_layout(title=f"No Valid Data for {metric_type} Comparison")
             return fig

        # Create DataFrame for easier sorting and plotting
        plot_df = pd.DataFrame(plot_data)

        # Sort by metric value (ascending for error metrics like RMSE/MAE)
        is_error_metric = metric_type.upper() in ["RMSE", "MAE"]
        plot_df = plot_df.sort_values("metric", ascending=is_error_metric)

        # Create the figure
        fig = go.Figure()

        # Add bar plot
        fig.add_trace(
            go.Bar(
                x=plot_df["entity"],
                y=plot_df["metric"],
                name=metric_type,
                marker_color='steelblue',
                text=plot_df["metric"].round(3),
                textposition='outside',
                yaxis='y'
            )
        )

        # Add data points as secondary y-axis if requested
        if show_data_points:
            fig.add_trace(
                go.Scatter(
                    x=plot_df["entity"],
                    y=plot_df["data_points"],
                    mode='lines+markers',
                    name='Data Points',
                    line=dict(color='orange', width=2),
                    marker=dict(size=6),
                    yaxis='y2'
                )
            )

        # Update layout
        layout_update = {
            'title': f'{metric_type} Comparison by {x_label}',
            'xaxis': {'title': x_label},
            'yaxis': {'title': y_label, 'side': 'left'},
            'template': theme,
            'height': height,
            'width': width,
            'showlegend': True,
            'legend': dict(x=0.01, y=0.99, bgcolor="rgba(0,0,0,0)")
        }

        if show_data_points:
            layout_update['yaxis2'] = {
                'title': 'Data Points',
                'overlaying': 'y',
                'side': 'right'
            }

        fig.update_layout(**layout_update)

        return fig

    except Exception as e:
        st.error(f"Error creating metrics bar plot: {str(e)}")
        fig = go.Figure()
        fig.update_layout(title=f"Error creating {metric_type} plot")
        return fig


def plot_feature_distribution_by_prediction_range(
    df: pd.DataFrame,
    feature: str,
    grouping_method: str = "median_split",
    theme: str = "plotly_white"
) -> go.Figure:
    """
    Creates histogram overlays showing feature distributions by prediction groups.
    
    Parameters:
    -----------
    df : pd.DataFrame
        DataFrame with prediction data.
    feature : str
        Name of the feature column to analyze.
    grouping_method : str, optional
        Grouping method used. Defaults to "median_split".
    theme : str, optional
        Plotly theme template. Defaults to "plotly_white".
        
    Returns:
    --------
    go.Figure
        Plotly figure with histogram overlays.
    """
    if df is None or df.empty:
        fig = go.Figure()
        fig.update_layout(title="No Data Available")
        return fig
    
    if feature not in df.columns:
        fig = go.Figure()
        fig.update_layout(title=f"Feature '{feature}' not found")
        return fig
    
    try:
        # Use centralized statistics calculation
        analysis_results = calculate_feature_group_statistics(df, feature, grouping_method)
        
        if 'error' in analysis_results:
            fig = go.Figure()
            fig.update_layout(title=f"Error: {analysis_results['error']}")
            return fig
        
        grouped_df = analysis_results['grouped_df']
        statistical_tests = analysis_results.get('statistical_tests', {})
        
        # Define colors for different groups
        color_map = {
            0: 'rgba(31, 119, 180, 0.6)',  # Blue
            1: 'rgba(255, 127, 14, 0.6)',  # Orange
            2: 'rgba(44, 160, 44, 0.6)',   # Green
            3: 'rgba(214, 39, 40, 0.6)'    # Red
        }
        
        border_color_map = {
            0: 'rgba(31, 119, 180, 1.0)',  # Solid Blue
            1: 'rgba(255, 127, 14, 1.0)',  # Solid Orange
            2: 'rgba(44, 160, 44, 1.0)',   # Solid Green
            3: 'rgba(214, 39, 40, 1.0)'    # Solid Red
        }
        
        fig = go.Figure()
        
        # Calculate optimal number of bins
        total_samples = len(grouped_df[feature].dropna())
        optimal_bins = min(50, max(20, int(np.sqrt(total_samples))))
        
        # Create histograms for each group
        groups = sorted(grouped_df['group'].unique())
        for group_id in groups:
            group_data = grouped_df[grouped_df['group'] == group_id]
            group_label = group_data['group_label'].iloc[0] if len(group_data) > 0 else f"Group {group_id}"
            feature_values = group_data[feature].dropna()
            
            if len(feature_values) > 0:
                fig.add_trace(go.Histogram(
                    x=feature_values,
                    name=group_label,
                    opacity=0.7,
                    nbinsx=optimal_bins,
                    marker=dict(
                        color=color_map.get(group_id, 'rgba(128,128,128,0.6)'),
                        line=dict(
                            color=border_color_map.get(group_id, 'rgba(128,128,128,1.0)'),
                            width=1
                        )
                    ),
                    histnorm='probability density' if len(groups) > 1 else 'count'
                ))
        
        # Add statistical significance annotation
        significance_text = ""
        if 'two_group' in statistical_tests:
            test = statistical_tests['two_group']
            p_val = test.get('p_value', 1.0)
            significance_text = f"Statistical Test: {test.get('test_name', 'Unknown')}<br>"
            significance_text += f"p-value: {format_p_value(p_val)}<br>"
            significance_text += f"Significant: {'Yes' if test.get('significant', False) else 'No'}"
        
        # Update layout
        fig.update_layout(
            title=f'Distribution of {feature} by Prediction Groups ({grouping_method.replace("_", " ").title()})',
            xaxis_title=feature,
            yaxis_title='Density' if len(groups) > 1 else 'Count',
            template=theme,
            barmode='overlay',
            showlegend=True,
            legend=dict(x=0.7, y=0.95),
            annotations=[
                dict(
                    x=0.02, y=0.98,
                    xref="paper", yref="paper",
                    text=significance_text,
                    showarrow=False,
                    font=dict(size=10),
                    bgcolor="rgba(0,0,0,0)",
                    bordercolor="rgba(0,0,0,0)",
                    borderwidth=0,
                    xanchor="left", yanchor="top"
                )
            ] if significance_text else None
        )
        
        return fig
        
    except Exception as e:
        fig = go.Figure()
        fig.update_layout(title=f"Error creating distribution plot: {str(e)}")
        return fig


def plot_feature_comparison_by_prediction_range(
    df: pd.DataFrame,
    features: List[str],
    grouping_method: str = "median_split",
    theme: str = "plotly_white"
) -> go.Figure:
    """
    Creates box plots comparing feature values between prediction groups.
    
    Parameters:
    -----------
    df : pd.DataFrame
        DataFrame with prediction data.
    features : List[str]
        List of feature column names to analyze.
    grouping_method : str, optional
        Grouping method used. Defaults to "median_split".
    theme : str, optional
        Plotly theme template. Defaults to "plotly_white".
        
    Returns:
    --------
    go.Figure
        Plotly figure with box plot comparisons.
    """
    if df is None or df.empty:
        fig = go.Figure()
        fig.update_layout(title="No Data Available")
        return fig
    
    if not features or not all(f in df.columns for f in features):
        missing_features = [f for f in features if f not in df.columns]
        fig = go.Figure()
        fig.update_layout(title=f"Features not found: {missing_features}")
        return fig
    
    try:
        # Use the first feature for grouping
        primary_feature = features[0]
        analysis_results = calculate_feature_group_statistics(df, primary_feature, grouping_method)
        
        if 'error' in analysis_results:
            fig = go.Figure()
            fig.update_layout(title=f"Error: {analysis_results['error']}")
            return fig
        
        grouped_df = analysis_results['grouped_df']
        
        # Create box plots
        fig = go.Figure()
        
        groups = sorted(grouped_df['group'].unique())
        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728']
        
        for feature in features:
            for i, group_id in enumerate(groups):
                group_data = grouped_df[grouped_df['group'] == group_id]
                group_label = group_data['group_label'].iloc[0] if len(group_data) > 0 else f"Group {group_id}"
                
                fig.add_trace(go.Box(
                    y=group_data[feature],
                    name=f"{feature} - {group_label}",
                    marker_color=colors[i % len(colors)],
                    boxpoints='outliers'
                ))
        
        fig.update_layout(
            title=f'Feature Comparison by Prediction Groups ({grouping_method.replace("_", " ").title()})',
            yaxis_title='Feature Values',
            template=theme,
            showlegend=True
        )
        
        return fig
        
    except Exception as e:
        fig = go.Figure()
        fig.update_layout(title=f"Error creating comparison plot: {str(e)}")
        return fig 