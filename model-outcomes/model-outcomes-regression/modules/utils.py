"""
Utility functions for the Model Prediction Visualizer application.
This module contains general purpose helper functions that can be used across the application.
"""
import os
import base64
import streamlit as st
from typing import Any, Dict

def get_base64_encoded_image(image_path: str) -> str:
    """
    Get base64 encoded image for embedding in HTML.
    
    Parameters:
    -----------
    image_path : str
        Path to the image file
        
    Returns:
    --------
    str
        Base64 encoded string of the image, or an empty string if the file
        doesn't exist or an error occurs during reading/encoding.
    """
    if not isinstance(image_path, str) or not image_path:
        return ""
    try:
        if os.path.exists(image_path):
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode()
        else:
            return ""
    except Exception:
        return ""


def extend_session_state(**kwargs: Any) -> None:
    """
    Extend session state with default values if keys don't exist.
    
    Parameters:
    -----------
    **kwargs : Any
        Key-value pairs to add to session state if they don't already exist.
    """
    for key, default_value in kwargs.items():
        if key not in st.session_state:
            st.session_state[key] = default_value

# Example of how extend_session_state might be called in app.py (after auth init):
# extend_session_state(
#     current_file=None,
#     uploaded_data=None,
#     active_tab="single"
# ) 