"""
Authentication module for the Model Outcomes | Classification Model Visualizer.
This module handles user authentication using CSV-based user credentials.
"""
import os
import pandas as pd
import streamlit as st
import base64
from typing import Dict, Optional, Tuple, List

def load_users(users_file: str = "users.csv") -> pd.DataFrame:
    """
    Load user credentials from a CSV file.
    
    Parameters:
    -----------
    users_file : str
        Path to the CSV file containing user credentials
        
    Returns:
    --------
    pd.DataFrame
        DataFrame containing user credentials
    """
    # Get the directory of the current file
    current_dir = os.path.dirname(os.path.abspath(__file__))
    users_path = os.path.join(current_dir, users_file)
    
    if not os.path.exists(users_path):
        # Create a default admin user if file doesn't exist
        users_df = pd.DataFrame({
            'username': ['admin'],
            'password': ['admin123'],
            'name': ['Administrator'],
            'role': ['admin']
        })
        try:
            users_df.to_csv(users_path, index=False)
        except Exception as e:
            st.error(f"Error creating users file: {str(e)}")
            # Return the default DataFrame even if file creation fails
            return users_df
        return users_df
    
    try:
        return pd.read_csv(users_path)
    except Exception as e:
        st.error(f"Error reading users file: {str(e)}")
        # Return default DataFrame if file read fails
        return pd.DataFrame({
            'username': ['admin'],
            'password': ['admin123'],
            'name': ['Administrator'],
            'role': ['admin']
        })

def verify_credentials(username: str, password: str, users_df: pd.DataFrame) -> Tuple[bool, Dict]:
    """
    Verify user credentials against the user database.
    
    Parameters:
    -----------
    username : str
        Username to verify
    password : str
        Password to verify
    users_df : pd.DataFrame
        DataFrame containing user credentials
        
    Returns:
    --------
    Tuple[bool, Dict]
        (is_authenticated, user_info)
    """
    # Check if username exists
    user_row = users_df[users_df['username'] == username]
    if user_row.empty:
        return False, {}
    
    # Check password
    if user_row.iloc[0]['password'] == password:
        # Create user info dictionary
        user_info = {
            'username': username,
            'name': user_row.iloc[0]['name'],
            'role': user_row.iloc[0]['role'] if 'role' in user_row.columns else 'user'
        }
        return True, user_info
    
    return False, {}

def initialize_session_state():
    """Initialize session state variables for authentication"""
    # Authentication state
    if 'is_authenticated' not in st.session_state:
        st.session_state.is_authenticated = False
    
    # User info
    if 'user_info' not in st.session_state:
        st.session_state.user_info = {}
    
    # Current page/view
    if 'current_page' not in st.session_state:
        st.session_state.current_page = 'login'

def login_user(username: str, password: str, users_file: str = "users.csv") -> bool:
    """
    Attempt to log in a user with the provided credentials.
    Updates session state if successful.
    
    Parameters:
    -----------
    username : str
        Username to verify
    password : str
        Password to verify
    users_file : str
        Path to the CSV file containing user credentials
        
    Returns:
    --------
    bool
        True if login successful, False otherwise
    """
    # Load user database
    users_df = load_users(users_file)
    
    # Verify credentials
    is_authenticated, user_info = verify_credentials(username, password, users_df)
    
    # Update session state
    if is_authenticated:
        st.session_state.is_authenticated = True
        st.session_state.user_info = user_info
        st.session_state.current_page = 'main'
        return True
    
    return False

def logout_user():
    """Log out the current user by resetting session state"""
    st.session_state.is_authenticated = False
    st.session_state.user_info = {}
    st.session_state.current_page = 'login'

def display_login_page():
    """Display the login page and handle login form submission"""
    # Get the directory of the current file
    current_dir = os.path.dirname(os.path.abspath(__file__))
    static_dir = os.path.join(os.path.dirname(current_dir), 'static')
    logo_path = os.path.join(static_dir, 'AIO-logo.png')
    
    # Add simplified styling - focus on functionality over fancy effects
    st.markdown("""
        <style>
        /* Theme-adaptive styling */
        :root {
            --primary-color: #1E3A8A;
            --primary-color-light: #4F8DF5;
            --text-color: #333333;
            --form-bg: rgba(255, 255, 255, 0.15);
            --form-border: rgba(255, 255, 255, 0.2);
        }

        [data-theme="dark"] {
            --primary-color: #4F8DF5;
            --primary-color-light: #60A5FA;
            --text-color: #E2E8F0;
            --form-bg: rgba(30, 41, 59, 0.25);
            --form-border: rgba(255, 255, 255, 0.1);
        }

        /* Transparent form styling with backdrop filter */
        .stForm {
            background-color: var(--form-bg) !important;
            border: 1px solid var(--form-border) !important;
            border-radius: 10px !important;
            padding: 2rem !important;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15) !important;
            backdrop-filter: blur(10px) !important;
            -webkit-backdrop-filter: blur(10px) !important;
        }

        /* Button styling */
        .stButton > button {
            background-color: var(--primary-color) !important;
            color: white !important;
            border: none !important;
            border-radius: 4px !important;
            padding: 0.5rem 1rem !important;
            font-weight: 600 !important;
            transition: all 0.2s ease !important;
        }

        .stButton > button:hover {
            background-color: var(--primary-color-light) !important;
            transform: translateY(-1px) !important;
        }

        /* Logo container */
        .logo-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 auto 2rem auto;
            width: 100%;
        }
        
        /* Title styling */
        .login-title {
            text-align: center;
            margin-bottom: 1.5rem;
            color: var(--primary-color);
        }

        .login-subtitle {
            text-align: center;
            color: var(--text-color);
            margin-bottom: 2rem;
            font-size: 1.1rem;
        }

        /* Code styling */
        .login-code {
            background: rgba(0,0,0,0.05);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
        }
        </style>
    """, unsafe_allow_html=True)
    
    # Create a centered layout using Streamlit columns
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        # Display logo with HTML to ensure proper centering
        if os.path.exists(logo_path):
            # Convert image to base64 for direct embedding
            with open(logo_path, "rb") as image_file:
                encoded_image = base64.b64encode(image_file.read()).decode()
            
            st.markdown(f"""
                <div class="logo-container">
                    <img src="data:image/png;base64,{encoded_image}" alt="AIO Logo" style="max-width: 300px; width: 100%; margin: 0 auto;">
                </div>
            """, unsafe_allow_html=True)
        
        # Display title with more direct styling
        st.markdown(
            """
            <h1 class="login-title">Model Outcomes | Classification</h1>
            <p class="login-subtitle">Log in to access the application</p>
            """, 
            unsafe_allow_html=True
        )
        
        # Create a simple login form
        with st.form("login_form"):
            username = st.text_input("Username", placeholder="Enter your username")
            password = st.text_input("Password", type="password", placeholder="Enter your password")
            
            # Add some spacing
            st.markdown("<br>", unsafe_allow_html=True)
            
            # Submit button
            submit_button = st.form_submit_button(
                "Log In",
                use_container_width=True
            )
            
            if submit_button:
                if login_user(username, password):
                    st.success("Login successful!")
                    st.rerun()  # Refresh the app to show the main interface
                else:
                    st.error("Invalid username or password. Please try again.")
        
        # Add a note about default credentials
        st.markdown("""
            <div style='text-align: center; margin-top: 1rem;'>
                <p style='color: var(--text-color); font-size: 0.9rem; opacity: 0.9;'>
                    Default login: <code class="login-code">username = 'admin'</code>, 
                    <code class="login-code">password = 'admin123'</code>
                </p>
            </div>
        """, unsafe_allow_html=True)


def display_welcome_screen():
    """
    Display welcome information based on user authentication status and data loading state.
    Provides personalized experience and clear path to next actions.
    """
    # Get user info and check if there's a loaded file
    user_name = st.session_state.user_info.get("name", "User") if hasattr(st.session_state, "user_info") else "User"
    has_data = "uploaded_data" in st.session_state and st.session_state.uploaded_data is not None
    
    # Section 1: Personalized Welcome with Status
    st.markdown(f"## Welcome, {user_name}!")
    
    # Show appropriate system status message
    if has_data:
        st.success("✅ Classification data loaded successfully. You can now explore the visualizations.")
    else:
        st.info("🔍 No data loaded yet. Please upload a classification dataset file to begin analysis.")
    
    # Section 2: Quick Start Panel
    st.markdown("### 🚀 Quick Start")
    
    quick_start_col1, quick_start_col2 = st.columns([1, 1])
    
    with quick_start_col1:
        st.markdown("""
        **Actions:**
        1. Upload your classification data file using the sidebar
        2. Upload feature importance data (optional)
        3. Adjust the classification threshold
        4. Select models and patients to analyze
        5. Explore the different visualizations and metrics
        """)
    
    with quick_start_col2:
        # Show different options based on data state
        if has_data:
            st.markdown("**Current Options:**")
            st.button("Clear Current Data", key="clear_data", 
                      help="Remove the currently loaded data to start fresh")
        else:
            st.markdown("**Need Sample Data?**")
            sample_data = pd.DataFrame({
                'patient_id': [1, 1, 2, 2, 3, 3],
                'model': ['model_a', 'model_b', 'model_a', 'model_b', 'model_a', 'model_b'],
                'real': [1, 1, 0, 0, 1, 1],
                'pred': [0.92, 0.85, 0.35, 0.12, 0.78, 0.67],
                'feature_A': [22.1, 22.1, 18.5, 18.5, 25.3, 25.3],
                'feature_B': [0, 0, 1, 1, 0, 0],
                'feature_C': [2.3, 2.3, 1.8, 1.8, 3.1, 3.1]
            })
            
            # Template download option
            csv = sample_data.to_csv(index=False).encode('utf-8')
            st.download_button(
                "Download Data Template",
                csv,
                "classification_template.csv",
                "text/csv",
                key='download-template',
                help="Download a template CSV file with the required structure"
            )
            
            # Add a feature importance template
            importance_data = pd.DataFrame({
                'feature_name': ['feature_A', 'feature_B', 'feature_C'],
                'importance_score': [0.65, 0.25, 0.10]
            })
            
            imp_csv = importance_data.to_csv(index=False).encode('utf-8')
            st.download_button(
                "Download Importance Template",
                imp_csv,
                "importance_template.csv",
                "text/csv",
                key='download-importance-template',
                help="Download a template for feature importance scores"
            )
    
    # Section 3: Data Requirements (collapsible)
    with st.expander("📋 Data Format Requirements", expanded=not has_data):
        req_col1, req_col2 = st.columns([3, 2])
        
        with req_col1:
            st.markdown("""
            Your CSV file must contain these mandatory columns:
            
            | Column | Description | Type |
            | ------ | ----------- | ---- |
            | **patient_id** | Unique identifier for patients | integer |
            | **real** | True class labels (0 or 1) | integer |
            | **pred** | Predicted probabilities | float (0.0 to 1.0) |
            | **model** | Model identifier | string |
            
            Additional columns will be treated as features used by the model.
            
            **Note:** For feature importance visualization, upload a separate CSV with two columns:
            - `feature_name`: The name of the feature (matching columns in the main dataset)
            - `importance_score`: Numeric importance value for the feature
            """)
        
        with req_col2:
            # Show compact example
            st.markdown("**Example Data:**")
            st.dataframe(
                sample_data.head(3),
                hide_index=True,
                column_config={
                    "patient_id": st.column_config.NumberColumn("Patient ID"),
                    "model": st.column_config.TextColumn("Model"),
                    "real": st.column_config.NumberColumn("Real (0/1)"),
                    "pred": st.column_config.NumberColumn("Pred Prob", format="%.2f"),
                    "feature_A": st.column_config.NumberColumn("Feature A", format="%.1f"),
                    "feature_B": st.column_config.NumberColumn("Feature B"),
                    "feature_C": st.column_config.NumberColumn("Feature C", format="%.1f"),
                },
                height=150
            )
    
    # Section 4: Visualization Options (collapsible)
    with st.expander("🔍 Available Visualizations", expanded=False):
        st.markdown("""
        This tool offers multiple visualizations for binary classification analysis:
        
        - **Confusion Matrix**: Interactive visualization of TP, TN, FP, FN based on threshold
        - **Metrics Table**: Accuracy, Precision, Recall, F1, MCC, ROC-AUC, PR-AUC
        - **Statistical Tests**: Chi-square or Fisher's exact test on confusion matrix
        - **ROC Curve**: True Positive Rate vs. False Positive Rate with AUC
        - **Precision-Recall Curve**: Precision vs. Recall with PR-AUC
        - **Prediction Distribution**: Histogram of prediction probabilities by actual class
        - **Feature Importance**: Visualization of feature importance scores (if provided)
        
        Each visualization provides different insights into your model's classification performance.
        """)
        
        # Add visual cues for different visualization aspects
        modes_col1, modes_col2 = st.columns([1, 1])
        
        with modes_col1:
            st.markdown("**Prediction Threshold:**")
            st.markdown("- Adjustable slider (0.0 to 1.0)")
            st.markdown("- Affects confusion matrix and metrics")
            st.markdown("- Helps find optimal classification point")
        
        with modes_col2:
            st.markdown("**Filtering Options:**")
            st.markdown("- Filter by specific model")
            st.markdown("- Filter by multiple patients")
            st.markdown("- Compare performance across filters")
    
    # Section 5: Next Steps Guide
    st.markdown("### ⏭️ Next Steps")
    
    if not has_data:
        st.markdown("""
        1. **Upload a classification CSV file** using the file uploader in the sidebar
        2. **Optional:** Upload feature importance scores
        3. Adjust the classification threshold
        4. Select models and patients to analyze
        5. Explore the different tabs to analyze model performance
        
        Need help? Expand the sections above for detailed information.
        """)
    else:
        st.markdown("""
        1. Use the **classification threshold** slider to find the optimal decision boundary
        2. Select specific **models** and **patients** to focus your analysis
        3. Compare key metrics across different threshold values
        4. Examine the ROC and PR curves to understand model performance
        5. Analyze feature importance to identify key predictors
        
        💡 **Tip**: Toggle between dark and light themes using the menu in the top right corner.
        """)
        
    # Update session state for tracking user's journey
    if "welcome_screen_viewed" not in st.session_state:
        st.session_state.welcome_screen_viewed = True 