"""
Module to define the content and structure of the main application tabs.
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
from typing import Dict, Any, Optional

# Import necessary functions from other modules
from .utils import display_insight_message
from .ui_components import create_tooltip  # Assuming we will use this
from .classification_metrics import display_metrics_table, display_statistical_test, categorize_by_performance, get_feature_columns
from .classification_visualization import (
    plot_class_distribution,
    plot_confusion_matrix,
    plot_roc_curve,
    plot_pr_curve,
    plot_probability_distribution,
    plot_feature_importance,
    plot_feature_distribution_by_performance,
    plot_feature_comparison_by_correctness,
    plot_feature_distribution_by_prediction,
    plot_feature_comparison_by_prediction,
    plot_feature_scatterplots_by_prediction
)

# --- Helper Functions for Tabs ---

def check_data_availability(filtered_df: pd.DataFrame, custom_message: str = None) -> bool:
    """
    Centralized function to check data availability and display a warning if empty.

    Parameters:
    -----------
    filtered_df : pd.DataFrame
        The DataFrame to check.
    custom_message : str, optional
        A custom message to display if the DataFrame is empty.

    Returns:
    --------
    bool
        True if data is available, False otherwise.
    """
    if filtered_df is None or filtered_df.empty:
        st.warning(custom_message or "No data available with current filters. Please adjust your selection.")
        return False
    return True

def display_active_filters(settings: Dict[str, Any]) -> None:
    """
    Display information about active filters consistently.

    Parameters:
    -----------
    settings : Dict[str, Any]
        Dictionary containing the current settings from the sidebar.
    """
    active_filters = []
    if settings.get('selected_model'):
        active_filters.append(f"Model: **{settings['selected_model']}**")
    selected_patients = settings.get('selected_patients')
    if selected_patients:
        if len(selected_patients) <= 3:
            patient_text = ", ".join([str(p) for p in selected_patients])
            active_filters.append(f"Patients: **{patient_text}**")
        else:
            active_filters.append(f"Patients: **{len(selected_patients)} selected**")

    if active_filters:
        filter_desc = " | ".join(active_filters)
        st.info(f"🔍 Active Filters: {filter_desc}")
    else:
        st.info("🔍 No filters active - showing all data.")

def calculate_dataset_stats(filtered_df: pd.DataFrame) -> Dict[str, Any]:
    """
    Calculate common dataset statistics to avoid repetition.

    Parameters:
    -----------
    filtered_df : pd.DataFrame
        The filtered DataFrame.

    Returns:
    --------
    Dict[str, Any]
        Dictionary containing key statistics.
    """
    stats = {}
    if filtered_df is not None and not filtered_df.empty:
        stats['total_samples'] = len(filtered_df)
        stats['positive_count'] = (filtered_df['real'] == 1).sum()
        stats['negative_count'] = (filtered_df['real'] == 0).sum()
        stats['class_ratio'] = stats['positive_count'] / stats['total_samples'] if stats['total_samples'] > 0 else 0
        stats['models_count'] = filtered_df['model'].nunique()
        stats['patients_count'] = filtered_df['patient_id'].nunique()
        stats['avg_pred'] = filtered_df['pred'].mean()
        stats['median_pred'] = filtered_df['pred'].median()
        # Add more stats as needed
    else:
        # Return default values if no data
        stats = {
            'total_samples': 0, 'positive_count': 0, 'negative_count': 0,
            'class_ratio': 0, 'models_count': 0, 'patients_count': 0,
            'avg_pred': float('nan'), 'median_pred': float('nan')
        }
    return stats

def get_metric_indicator(value: float, thresholds: tuple = (0.7, 0.9)) -> str:
    """
    Get a visual indicator for metric values based on biomedical context thresholds.

    Parameters:
    -----------
    value : float
        The metric value.
    thresholds : tuple
        Tuple containing (lower_bound, upper_bound) for categorization.
        Defaults are adjusted for potential biomedical relevance.

    Returns:
    --------
    str
        Emoji indicator (e.g., "🟢", "🟡", "🔴", "❓").
    """
    if np.isnan(value):
        return "❓"
    elif value >= thresholds[1]:
        return "🟢"  # Generally desired range
    elif value >= thresholds[0]:
        return "🟡"  # Intermediate range
    else:
        return "🔴"  # Potentially concerning range

# --- Tab Implementation Functions ---

def display_overview_tab(filtered_df: pd.DataFrame, settings: Dict[str, Any]) -> None:
    """
    Display the content for the 'Overview & Data' tab.

    Parameters:
    -----------
    filtered_df : pd.DataFrame
        The filtered data to display.
    settings : Dict[str, Any]
        Current application settings from the sidebar.
    """
    st.markdown("---")
    
    # Crear layout con título a la izquierda y contenido a la derecha
    left_col, right_col = st.columns([1.5, 1])
    
    with left_col:
        st.markdown("# Overview & Data")
    
    with right_col:
        display_active_filters(settings)
        
        if not check_data_availability(filtered_df):
            return
    
    # Calculate stats once
    stats = calculate_dataset_stats(filtered_df)

    # --- Layout --- 
    col1, col2 = st.columns([1.5, 1]) # Use equal columns for a cleaner look

    with col1:
        #### Class Distribution
        fig_class_dist = plot_class_distribution(filtered_df, theme=settings.get('theme'))
        st.plotly_chart(fig_class_dist, use_container_width=True)
        
        # Data Sample moved below summary for better flow
        
    with col2:
        st.markdown("#### Dataset Summary")
        st.markdown(f"""
        | Statistic         | Value |
        |-------------------|-------|
        | Total Samples     | {stats['total_samples']:,} |
        | Positive Class    | {stats['positive_count']:,} ({stats['class_ratio']:.1%}) |
        | Negative Class    | {stats['negative_count']:,} ({(1-stats['class_ratio']):.1%}) |
        | Models            | {stats['models_count']} |
        | Unique Patients   | {stats['patients_count']:,} |
        | Avg. Prediction   | {stats['avg_pred']:.4f} |
        | Median Prediction | {stats['median_pred']:.4f} |
        """)
        
        # Class Balance Insight with tooltip usando display_insight_message
        balance_explanation = """
        **Class Balance Context:**
        
        - **Highly Imbalanced (<20% or >80% minority):** May require specific modeling techniques (e.g., resampling, cost-sensitive learning) and metrics like PR-AUC or F1-score become more crucial than Accuracy.
        
        - **Moderately Imbalanced (20-40% minority):** Standard metrics might still be informative, but performance should be evaluated carefully, considering both ROC-AUC and PR-AUC.
        
        - **Balanced (40-60% minority):** Standard metrics like Accuracy and ROC-AUC are generally reliable indicators of performance.
        
        *Reference: He, H., & Garcia, E. A. (2009). Learning from imbalanced data. IEEE Transactions on knowledge and data engineering, 21(9), 1263-1284.*
        """
        
        class_ratio = stats['class_ratio']
        minority_perc = min(class_ratio, 1-class_ratio)
        
        if minority_perc < 0.2:
            display_insight_message(
                f"📊 Highly Imbalanced ({minority_perc:.1%} minority class)",
                balance_explanation,
                "warning",
                "balance"
            )
        elif 0.2 <= minority_perc < 0.4:
            display_insight_message(
                f"📊 Moderately Imbalanced ({minority_perc:.1%} minority class)",
                balance_explanation,
                "info",
                "balance"
            )
        else:
            display_insight_message(
                f"📊 Relatively Balanced ({minority_perc:.1%} minority class)",
                balance_explanation,
                "success",
                "balance"
            )
             
    st.markdown("---")
    st.markdown("#### Data Sample & Export")
        
    # Create a row with two columns to display the slider and the download button side by side.
    col_slider, col_download = st.columns([8, 1])
    with col_slider:
        # Data Sample Slider: Allows the user to select the number of rows to view.
        num_rows = st.slider(
            "Number of sample rows to display",
            min_value=5,
            max_value=min(50, len(filtered_df)),
            value=5,
            step=5,
            key="overview_sample_rows"
        )
    with col_download:
        # Download Button: Converts the filtered DataFrame to CSV and provides a button to download it.
        csv = filtered_df.to_csv(index=False).encode('utf-8')
        st.download_button(
            "Download Filtered Data",
            csv,
            "filtered_classification_data.csv",
            "text/csv",
            use_container_width=True,
            key="download_filtered_overview"
        )
    # Display the sample data table below the slider and download button row.
    st.dataframe(
        filtered_df.head(num_rows),
        use_container_width=True,
        column_config={
            "patient_id": st.column_config.TextColumn("Patient ID"),
            "model": st.column_config.TextColumn("Model"),
            "real": st.column_config.NumberColumn("Actual (0/1)"),
            "pred": st.column_config.NumberColumn("Predicted Prob.", format="%.4f")
        }
    )

def display_threshold_tab(filtered_df: pd.DataFrame, metrics: Dict[str, Any], settings: Dict[str, Any]) -> None:
    """
    Display the content for the 'Threshold Performance' tab.
    
    This tab focuses exclusively on threshold-related analysis including
    confusion matrix and statistical tests for the current threshold setting.

    Parameters:
    -----------
    filtered_df : pd.DataFrame
        The filtered data.
    metrics : Dict[str, Any]
        Calculated metrics based on the current threshold.
    settings : Dict[str, Any]
        Current application settings.
    """
    
    st.markdown("---")

    # Create layout with title on the left and content on the right
    left_col, right_col = st.columns([1.5, 1])
    
    with left_col:
        st.markdown(f"# Threshold Performance")

        # Show key performance metrics prominently 
        precision = metrics.get('precision', float('nan'))
        recall = metrics.get('recall', float('nan'))
        f1_score = metrics.get('f1', float('nan'))
        roc_auc = metrics.get('roc_auc', float('nan'))
        
        # Format metrics for display - use "N/A" for undefined values
        def format_metric(value, decimals=3):
            return f"{value:.{decimals}f}" if not np.isnan(value) else "N/A"
        
        st.markdown(f"""
        #### 📊 **Precision**: {format_metric(precision)} | **Recall**: {format_metric(recall)} | **F1-Score**: {format_metric(f1_score)} | **ROC-AUC**: {format_metric(roc_auc)}
        """)
    
    with right_col:
        # Mostrar filtros activos
        display_active_filters(settings)
        
        if not check_data_availability(filtered_df, "Data needed to analyze threshold performance."):
            return
            
        if not metrics or metrics.get('total', 0) == 0:
            st.info("Metrics calculation requires valid data and threshold settings, resulting in non-zero counts.")
            return
        current_threshold = settings.get('threshold', 0.5)
        st.markdown(f"#### 🎯 Current Threshold: **{current_threshold:.2f}**")
    
    
    # Layout for confusion matrix and statistical test
    col1, col2 = st.columns([1.5, 1])

    with col1:
        # CONFUSION MATRIX
        fig_cm = plot_confusion_matrix(metrics, theme=settings.get('theme'))
        st.plotly_chart(fig_cm, use_container_width=True)

    with col2:
        # STATISTICAL TEST
        display_statistical_test(metrics)
    
    # Threshold optimization guidance
    st.markdown("---")
    with st.expander("🔄 Understanding Threshold Tradeoffs", expanded=False):
        st.markdown("""
        Adjusting the threshold value in the sidebar changes how the model's probability scores are converted to binary predictions:
        - **Higher threshold** → More conservative predictions (fewer positives)
        - **Lower threshold** → More aggressive predictions (more positives)
        """)
        
        st.markdown("**Key tradeoffs to consider:**")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**Increasing Threshold**")
            st.markdown("- ✅ Higher Precision (fewer false alarms)")
            st.markdown("- ✅ Higher Specificity (better at identifying true negatives)")
            st.markdown("- ❌ Lower Recall/Sensitivity (more false negatives)")
            st.markdown("- 🔍 Good for: Confirmatory tests, reducing false positives")
            
        with col2:
            st.markdown("**Decreasing Threshold**")
            st.markdown("- ✅ Higher Recall/Sensitivity (fewer missed positives)")
            st.markdown("- ✅ Better for rare but critical conditions")
            st.markdown("- ❌ Lower Precision (more false alarms)")
            st.markdown("- 🔍 Good for: Screening, ensuring no cases are missed")


        

def display_global_performance_tab(filtered_df: pd.DataFrame, metrics: Dict[str, Any], settings: Dict[str, Any]) -> None:
    """
    Display the content for the 'Global Performance' tab.

    Parameters:
    -----------
    filtered_df : pd.DataFrame
        The filtered data.
    metrics : Dict[str, Any]
        Calculated metrics (contains ROC-AUC, PR-AUC).
    settings : Dict[str, Any]
        Current application settings.
    """
    st.markdown("---")
    
    # Create layout with title on the left and content on the right
    left_col, right_col = st.columns([1.5, 1])
    
    with left_col:
        st.markdown("# Global Performance")
    
    with right_col:
        # Mostrar filtros activos
        display_active_filters(settings)
        
        if not check_data_availability(filtered_df, "Data needed to analyze global performance."):
            return
            
        # Check if AUC metrics are available (need both classes present)
        auc_available = not np.isnan(metrics.get('roc_auc', float('nan')))
        
        # Mostrar métricas AUC clave
        if auc_available:
            # ROC AUC y PR AUC values from pre-calculated metrics
            roc_auc = metrics.get('roc_auc', float('nan'))
            pr_auc = metrics.get('pr_auc', float('nan'))
            
            # Calculate baseline from the filtered data
            stats = calculate_dataset_stats(filtered_df)
            baseline = stats['class_ratio']
            
        else:
            st.info("AUC metrics require both positive and negative classes in the filtered data.")
    
    # Create detailed descriptions for the expanders
    roc_auc_explanation = """
    **Area Under the ROC Curve (AUC):**
    
    Measures the model's ability to discriminate between positive and negative classes across all possible thresholds.
    
    - **1.0:** Perfect discrimination
    - **0.5:** No better than random guessing
    
    ROC AUC is relatively insensitive to class imbalance, but may provide an overly optimistic view when classes are highly imbalanced.
    """
    
    pr_auc_explanation = """
    **Area Under the Precision-Recall Curve (PR AUC):**
    
    Represents average precision across all recall values. Particularly useful for imbalanced datasets.
    
    - **1.0:** Perfect model (100% precision at all recall levels)
    - **Baseline:** Equal to the proportion of positive samples (random classifier)
    - Higher values relative to the baseline indicate better performance
    
    PR AUC focuses on the positive class and is more informative than ROC AUC when dealing with imbalanced datasets or when false positives are more costly than false negatives.
    """
    
    # Display ROC and PR curves side by side in columns
    if auc_available:
        # Create two columns for ROC and PR curves
        curve_cols = st.columns([1.5, 1])
        
        with curve_cols[0]:
            ### Precision-Recall Curve
            # Pass pre-calculated AUC value to ensure consistency
            fig_pr = plot_pr_curve(
                filtered_df['real'].values, 
                filtered_df['pred'].values,
                precalculated_auc=pr_auc,  # Pass the pre-calculated AUC 
                theme=settings.get('theme')
            )
            st.plotly_chart(fig_pr, use_container_width=True)
            
            # # Add explanation with display_insight_message
            # display_insight_message(
            #     "What does PR AUC mean?",
            #     pr_auc_explanation,
            #     "info",
            #     "pr_auc_explanation"
            # )
        
        with curve_cols[1]:
            ### ROC Curve
            # Pass pre-calculated AUC value to ensure consistency
            fig_roc = plot_roc_curve(
                filtered_df['real'].values, 
                filtered_df['pred'].values,
                precalculated_auc=roc_auc,  # Pass the pre-calculated AUC
                theme=settings.get('theme')
            )
            st.plotly_chart(fig_roc, use_container_width=True)
            
            # # Add explanation with display_insight_message
            # display_insight_message(
            #     "What does ROC AUC mean?",
            #     roc_auc_explanation,
            #     "info",
            #     "roc_auc_explanation"
            # )
    else:
        st.info("ROC and PR curves require both positive and negative classes to be present in the filtered data.")

    # Prediction Probability Distribution Analysis
    st.markdown("---")
    fig_prob = plot_probability_distribution(filtered_df, theme=settings.get('theme'))
    st.plotly_chart(fig_prob, use_container_width=True)
    
    # Analysis of distribution separation in an expander
    with st.expander("🔍 Distribution Separation Analysis", expanded=False):
        class0 = filtered_df[filtered_df['real'] == 0]['pred']
        class1 = filtered_df[filtered_df['real'] == 1]['pred']
        
        if not class0.empty and not class1.empty:
            # Calculate key statistics
            separation = abs(class1.mean() - class0.mean())
            class0_mean = class0.mean()
            class1_mean = class1.mean()
            class0_std = class0.std()
            class1_std = class1.std()
            overlap = max(0, min(class0.max(), class1.max()) - max(class0.min(), class1.min()))
            
            # Create visual metrics
            col1, col2 = st.columns(2)
            
            with col1:
                st.metric("Mean Probability Separation", f"{separation:.4f}")
                st.markdown("""
                **What this means:**
                - Higher values indicate better separation between classes
                - Lower values suggest more distributional overlap
                """)
            
            with col2:
                st.markdown("### Class Probability Statistics")
                st.markdown(f"""
                | Class | Mean Probability | Std. Deviation |
                |-------|------------------|----------------|
                | Negative (0) | {class0_mean:.4f} | {class0_std:.4f} |
                | Positive (1) | {class1_mean:.4f} | {class1_std:.4f} |
                """)
            
            # Explanation of what this means for threshold selection
            st.markdown("""
            **Implications for Threshold Selection:**
            
            - **High Separation:** Classes are well-distinguished, allowing for more flexibility in threshold choice
            - **Low Separation:** Threshold selection is more critical, as small changes can significantly impact classification metrics
            - **Areas of Overlap:** Represent cases where misclassifications are more likely to occur, regardless of threshold
            """)
        else:
            st.warning("Distribution separation analysis requires both positive and negative classes to be present.")

def display_feature_performance_tab(filtered_df: pd.DataFrame, settings: Dict[str, Any]) -> None:
    """
    Display the content for the 'Feature Performance' tab.
    
    This tab focuses exclusively on feature analysis and behavior across
    different prediction categories, providing detailed insights into
    how features relate to model performance.

    Parameters:
    -----------
    filtered_df : pd.DataFrame
        The filtered data.
    settings : Dict[str, Any]
        Current application settings.
    """
    
    st.markdown("---")

    # Create layout with title on the left and content on the right
    left_col, right_col = st.columns([1.5, 1])
    
    with left_col:
        st.markdown("# Feature Performance Analysis")
    
    with right_col:
        # Mostrar filtros activos
        display_active_filters(settings)
        
        if not check_data_availability(filtered_df, "Data needed to analyze feature performance."):
            return
    
    # Get feature information
    feature_info = get_feature_columns(filtered_df)
    
    if not feature_info['all_features']:
        st.info("No feature columns detected for analysis.")
        return
    
    # Get analysis settings
    threshold = settings.get('threshold', 0.5)
    theme = settings.get('theme', 'plotly_white')
    
    # Analysis type selection
    st.markdown("#### Feature Analysis")
    
    analysis_type = st.radio(
        "Analysis perspective:",
        options=[
            "By Predicted Class",
            "By Performance Category",
            "By Prediction Correctness"
        ],
        index=0,  # Default to "By Predicted Class" as requested by team
        horizontal=True
    )
    
    # Prepare data based on analysis type
    if analysis_type == "By Predicted Class":
        df_analysis = filtered_df.copy()
        df_analysis['predicted_class'] = (df_analysis['pred'] >= threshold).astype(int)
        pred_counts = df_analysis['predicted_class'].value_counts()
    elif analysis_type == "By Performance Category":
        df_with_performance = categorize_by_performance(filtered_df, threshold)
        performance_counts = df_with_performance['performance_category'].value_counts()
        correct_pct = (df_with_performance['is_correct'].sum() / len(df_with_performance)) * 100
    else:  # By Prediction Correctness
        df_with_performance = categorize_by_performance(filtered_df, threshold)
        correct_pct = (df_with_performance['is_correct'].sum() / len(df_with_performance)) * 100
    
    # Individual Feature Analysis
    st.markdown("##### Individual Feature Analysis")
    
    # Layout: metrics on the left, analysis controls on the right
    metrics_row_col1, metrics_row_col2 = st.columns([2, 1])
    
    with metrics_row_col1:
        # Summary metrics based on analysis type
        metric_col1, metric_col2, metric_col3 = st.columns(3)
        
        if analysis_type == "By Predicted Class":
            with metric_col1:
                st.metric("Predicted Class 0", f"{pred_counts.get(0, 0):,}")
            with metric_col2:
                st.metric("Predicted Class 1", f"{pred_counts.get(1, 0):,}")
            with metric_col3:
                st.metric("Total Features", len(feature_info['all_features']))
        elif analysis_type == "By Performance Category":
            with metric_col1:
                st.metric("Prediction Accuracy", f"{correct_pct:.1f}%")
            with metric_col2:
                st.metric("Total Features", len(feature_info['all_features']))
            with metric_col3:
                st.metric("Error Cases", performance_counts.get('False Positive', 0) + performance_counts.get('False Negative', 0))
        else:  # By Prediction Correctness
            with metric_col1:
                st.metric("Prediction Accuracy", f"{correct_pct:.1f}%")
            with metric_col2:
                st.metric("Total Features", len(feature_info['all_features']))
            with metric_col3:
                incorrect_count = len(df_with_performance[df_with_performance['is_correct'] == False])
                st.metric("Incorrect Cases", incorrect_count)
    
    with metrics_row_col2:
        selected_feature = st.selectbox(
            "Select feature to analyze:",
            options=feature_info['all_features']
        )
        show_feature_stats = st.checkbox(
            "Show statistical summary",
            value=True
        )
    
    if selected_feature:
        # Feature distribution plot based on analysis type
        if analysis_type == "By Predicted Class":
            fig_dist = plot_feature_distribution_by_prediction(
                filtered_df, selected_feature, threshold, theme
            )
        elif analysis_type == "By Performance Category":
            fig_dist = plot_feature_distribution_by_performance(
                filtered_df, selected_feature, threshold, theme
            )
        else:  # By Prediction Correctness
            fig_dist = plot_feature_comparison_by_correctness(
                filtered_df, [selected_feature], threshold, theme
            )
        
        st.plotly_chart(fig_dist, use_container_width=True)
        
        # Statistical summary for numerical features
        if show_feature_stats and selected_feature in feature_info['numerical']:
            if analysis_type == "By Predicted Class":
                feature_stats = df_analysis.groupby('predicted_class')[selected_feature].agg([
                    'count', 'mean', 'std', 'median'
                ]).round(3)
                feature_stats.index = [f'Predicted {idx}' for idx in feature_stats.index]
            elif analysis_type == "By Performance Category":
                feature_stats = df_with_performance.groupby('performance_category')[selected_feature].agg([
                    'count', 'mean', 'std', 'median'
                ]).round(3)
            else:  # By Prediction Correctness
                feature_stats = df_with_performance.groupby('is_correct')[selected_feature].agg([
                    'count', 'mean', 'std', 'median'
                ]).round(3)
                feature_stats.index = ['Incorrect', 'Correct']
            
            st.dataframe(feature_stats, use_container_width=True)
    
    # Multi-feature comparison
    st.markdown("---")
    st.markdown("#### Multi-Feature Comparison")
    
    # Controls for multi-feature comparison
    multi_control_col1, multi_control_col2, multi_control_col3 = st.columns([2, 1, 1])
    
    with multi_control_col1:
        selected_features = st.multiselect(
            "Compare multiple features:",
            options=feature_info['all_features'],
            default=feature_info['all_features'][:min(3, len(feature_info['all_features']))]
        )
    
    with multi_control_col2:
        multi_viz_type = st.radio(
            "Chart type:",
            options=["Box Plots", "Scatter Plots"],
            index=0,
            horizontal=True
        )
    
    # Comparison variable selector (only show for scatter plots)
    comparison_column = None
    if multi_viz_type == "Scatter Plots":
        with multi_control_col3:
            # Get all numerical columns except required ones for comparison
            required_cols = {'patient_id', 'model', 'real', 'pred'}
            available_cols = [col for col in filtered_df.columns 
                            if filtered_df[col].dtype in ['int64', 'float64'] and col not in required_cols]
            
            if available_cols:
                comparison_column = st.selectbox(
                    "X-axis variable:",
                    options=available_cols
                )
            else:
                st.warning("No numerical columns available for comparison")
                multi_viz_type = "Box Plots"  # Fallback to box plots
    
    if selected_features:
        if len(selected_features) > 4:
            selected_features = selected_features[:4]
        
        # Create comparison plot based on analysis type and visualization type
        if multi_viz_type == "Box Plots":
            # Original box plot functionality
            if analysis_type == "By Predicted Class":
                fig_comparison = plot_feature_comparison_by_prediction(
                    filtered_df, selected_features, threshold, theme
                )
            elif analysis_type == "By Performance Category":
                # For performance category, we'll use the correctness comparison as it's cleaner
                fig_comparison = plot_feature_comparison_by_correctness(
                    filtered_df, selected_features, threshold, theme
                )
            else:  # By Prediction Correctness
                fig_comparison = plot_feature_comparison_by_correctness(
                    filtered_df, selected_features, threshold, theme
                )
        else:  # Scatter Plots
            # New scatter plot functionality with selectable comparison column
            if comparison_column:
                fig_comparison = plot_feature_scatterplots_by_prediction(
                    filtered_df, selected_features, comparison_column, threshold, theme
                )
            else:
                st.error("Please select a comparison variable for scatter plots")
                return
        
        st.plotly_chart(fig_comparison, use_container_width=True)
        


def display_feature_importance_tab(importance_df: Optional[pd.DataFrame], settings: Dict[str, Any]) -> None:
    """
    Display the content for the 'Feature Importance' tab.

    Parameters:
    -----------
    importance_df : pd.DataFrame or None
        DataFrame containing feature importance scores.
    settings : Dict[str, Any]
        Current application settings.
    """
    st.markdown("---")
    
    # Crear layout con título a la izquierda y contenido a la derecha
    left_col, right_col = st.columns([1.5, 1])
    
    with left_col:
        st.markdown("# Feature Importance")
    
    with right_col:
        # Active filters are usually not applied to importance data, but display for context if needed
        # display_active_filters(settings) 
        
        if importance_df is None or importance_df.empty:
            st.info("Upload a feature importance file using the sidebar uploader to view this analysis.")
            st.markdown("""
            **Required format:** A CSV file with columns named `feature_name` and `importance_score`.
            
            *Importance scores can originate from various methods, such as model coefficients (e.g., Logistic Regression), 
            feature importance attributes (e.g., Random Forest, XGBoost), permutation importance, or SHAP values.*
            """)
            return

    #st.markdown("### Feature Importance Plot")
    
    # Ensure the plot function handles potential errors gracefully
    try:
        fig_imp = plot_feature_importance(importance_df, theme=settings.get('theme'))
        st.plotly_chart(fig_imp, use_container_width=True)
    except Exception as e:
        st.error(f"Could not generate feature importance plot: {e}")
        return # Stop processing this tab if plot fails

    # Simplified Summary Table and Download
    st.markdown("#### Top Features by Absolute Importance")
    
    # Create a row with two columns to display the slider and the download button side by side.
    col_slider, col_download = st.columns([8, 1])
    
    with col_slider:
        # Determine number of features to show, ensuring it doesn't exceed available features
        max_features = len(importance_df)
        default_n = min(10, max_features)
        top_n = st.slider(
            "Number of top features to display", 
            min_value=min(5, max_features), 
            max_value=max_features, 
            value=default_n, 
            step=1, 
            key="feature_importance_top_n"
        )
    
    with col_download:
        # Add download button for the full importance data
        try:
            csv = importance_df.to_csv(index=False).encode('utf-8')
            st.download_button(
                "Download Importance Data",
                csv,
                "feature_importance_data.csv",
                "text/csv",
                use_container_width=True,
                key="download_feature_importance"
            )
        except Exception as e:
            st.error(f"Failed to prepare download: {e}")
    
    # Sort by absolute importance and display top N
    imp_df_sorted = importance_df.iloc[importance_df['importance_score'].abs().argsort()[::-1]]
    st.dataframe(
        imp_df_sorted.head(top_n),
        use_container_width=True,
        column_config={
            "feature_name": "Feature Name",
            "importance_score": st.column_config.NumberColumn("Importance Score", format="%.4f")
        }
    )

    st.caption("Importance scores indicate the relative influence of each feature. Positive/negative scores may represent direction depending on the importance method used.")



