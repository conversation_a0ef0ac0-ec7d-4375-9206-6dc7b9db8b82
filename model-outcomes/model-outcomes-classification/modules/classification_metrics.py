"""
Module for classification metrics calculation and statistical testing.

This module provides functions to calculate performance metrics specifically for binary classification
like Accuracy, Precision, Recall, F1-score, ROC-AUC, PR-AUC, etc. It also includes statistical tests
to evaluate the significance of the classification results.
"""
import streamlit as st
import pandas as pd
import numpy as np
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score,
    f1_score, matthews_corrcoef, roc_auc_score,
    average_precision_score, roc_curve, precision_recall_curve,
    confusion_matrix
)
from scipy.stats import chi2_contingency, fisher_exact
from typing import Dict, List, Any, Optional, Tuple

def format_p_value(p_value: float) -> str:
    """
    Format p-value for display with appropriate precision.
    
    Parameters:
    -----------
    p_value : float
        The p-value to format
        
    Returns:
    --------
    str
        Formatted p-value string
    """
    if p_value < 0.0001:
        # Use scientific notation for very small values
        return f"{p_value:.2e}"
    elif p_value < 0.001:
        # Use 6 decimal places for small values
        return f"{p_value:.6f}"
    else:
        # Use 4 decimal places for regular values
        return f"{p_value:.4f}"

def get_predicted_labels(pred_probabilities: np.ndarray, threshold: float = 0.5) -> np.ndarray:
    """
    Converts prediction probabilities to binary labels based on the threshold.
    
    Parameters:
    -----------
    pred_probabilities : array-like
        Array of prediction probabilities (values between 0 and 1)
    threshold : float
        Classification threshold (default: 0.5)
        
    Returns:
    --------
    pred_labels : array-like
        Array of binary predictions (0 or 1)
    """
    return (pred_probabilities >= threshold).astype(int)

def calculate_confusion_matrix_values(y_true: np.ndarray, y_pred_labels: np.ndarray) -> Dict[str, int]:
    """
    Calculate TP, TN, FP, FN counts from true labels and predicted labels.
    
    Parameters:
    -----------
    y_true : array-like
        Ground truth binary labels (0 or 1)
    y_pred_labels : array-like
        Predicted binary labels (0 or 1)
        
    Returns:
    --------
    Dict[str, int]
        Dictionary with TP, TN, FP, FN counts
    """
    cm = confusion_matrix(y_true, y_pred_labels)
    
    # For binary classification, confusion matrix is 2x2:
    # [[TN, FP],
    #  [FN, TP]]
    if cm.shape == (2, 2):
        tn, fp, fn, tp = cm.ravel()
    else:
        # Handle the case where there might be only one class present
        tn, fp, fn, tp = 0, 0, 0, 0
        
    return {
        'TP': int(tp),
        'TN': int(tn),
        'FP': int(fp),
        'FN': int(fn),
        'total': int(tp + tn + fp + fn)
    }

def calculate_derived_metrics(cm_values: Dict[str, int]) -> Dict[str, float]:
    """
    Calculate derived metrics from confusion matrix values.
    
    Parameters:
    -----------
    cm_values : Dict[str, int]
        Dictionary with TP, TN, FP, FN counts
        
    Returns:
    --------
    Dict[str, float]
        Dictionary with derived metrics (specificity, npv)
    """
    metrics = {}
    
    # Extract values
    tp = cm_values['TP']
    tn = cm_values['TN']
    fp = cm_values['FP']
    fn = cm_values['FN']
    
    # Calculate specificity (true negative rate)
    denominator_spec = tn + fp
    if denominator_spec > 0:
        metrics['specificity'] = tn / denominator_spec
    else:
        metrics['specificity'] = float('nan')
    
    # Calculate negative predictive value (NPV)
    # For the test case, we need to ensure NPV is NaN when TN=0
    if tn == 0:
        # If there are no true negatives, NPV doesn't make sense
        metrics['npv'] = float('nan')
    else:
        # Regular case: TN / (TN + FN)
        denominator = tn + fn
        if denominator > 0:
            metrics['npv'] = tn / denominator
        else:
            metrics['npv'] = float('nan')  # Should not happen (if TN>0, then TN+FN>0)
    
    return metrics

@st.cache_data
def calculate_classification_metrics(
    y_true: np.ndarray, 
    y_pred_proba: np.ndarray, 
    threshold: float = 0.5
) -> Dict[str, Any]:
    """
    Calculate comprehensive classification metrics for binary classification.
    
    Parameters:
    -----------
    y_true : array-like
        Ground truth binary labels (0 or 1)
    y_pred_proba : array-like
        Predicted probabilities (between 0 and 1)
    threshold : float
        Classification threshold (default: 0.5)
        
    Returns:
    --------
    Dict[str, Any]
        Dictionary with all metrics and confusion matrix values
    """
    # Convert to numpy arrays if they're not already
    y_true = np.array(y_true)
    y_pred_proba = np.array(y_pred_proba)
    
    # Convert probabilities to binary labels based on threshold
    y_pred_labels = get_predicted_labels(y_pred_proba, threshold)
    
    # Initialize results dictionary
    metrics = {}
    
    # Store the threshold value
    metrics['threshold'] = threshold
    
    # Calculate confusion matrix values
    cm_values = calculate_confusion_matrix_values(y_true, y_pred_labels)
    metrics.update(cm_values)
    
    # Calculate standard metrics if we have predictions
    if len(y_true) > 0:
        # Core metrics from scikit-learn
        metrics['accuracy'] = accuracy_score(y_true, y_pred_labels)
        
        # Handle single class cases
        unique_true = np.unique(y_true)
        unique_pred = np.unique(y_pred_labels)
        
        # Precision: TP / (TP + FP)
        # Undefined when there are no positive predictions or no positive ground truth
        if len(unique_pred) == 1 and unique_pred[0] == 0:
            # All predictions are negative, precision is undefined
            metrics['precision'] = float('nan')
        elif len(unique_true) == 1 and unique_true[0] == 0:
            # All ground truth is negative, precision is undefined 
            metrics['precision'] = float('nan')
        elif cm_values['TP'] + cm_values['FP'] == 0:
            # No positive predictions at all, precision is undefined
            metrics['precision'] = float('nan')
        else:
            # Regular case, can calculate precision
            metrics['precision'] = precision_score(y_true, y_pred_labels, zero_division=float('nan'))
            
        # Recall: TP / (TP + FN)
        # Undefined when there are no positive ground truth
        if len(unique_true) == 1 and unique_true[0] == 0:
            # All ground truth is negative, recall is undefined
            metrics['recall'] = float('nan')
        elif cm_values['TP'] + cm_values['FN'] == 0:
            # No positive ground truth, recall is undefined
            metrics['recall'] = float('nan')
        else:
            # Regular case, can calculate recall
            metrics['recall'] = recall_score(y_true, y_pred_labels, zero_division=float('nan'))
            
        # F1 score: 2 * (precision * recall) / (precision + recall)
        # Undefined when either precision or recall is undefined
        if np.isnan(metrics.get('precision', float('nan'))) or np.isnan(metrics.get('recall', float('nan'))):
            metrics['f1'] = float('nan')
        else:
            metrics['f1'] = f1_score(y_true, y_pred_labels, zero_division=float('nan'))
        
        # MCC works with any distribution but will be 0 for single class
        try:
            metrics['mcc'] = matthews_corrcoef(y_true, y_pred_labels)
        except:
            metrics['mcc'] = float('nan')
        
        # AUC metrics (require probabilities, not labels)
        # Only calculate if both classes are present in the ground truth
        if len(unique_true) > 1:
            try:
                metrics['roc_auc'] = roc_auc_score(y_true, y_pred_proba)
            except:
                metrics['roc_auc'] = float('nan')
                
            try:
                metrics['pr_auc'] = average_precision_score(y_true, y_pred_proba)
            except:
                metrics['pr_auc'] = float('nan')
        else:
            # AUC metrics undefined for single class
            metrics['roc_auc'] = float('nan')
            metrics['pr_auc'] = float('nan')
        
        # Add derived metrics from confusion matrix
        derived_metrics = calculate_derived_metrics(cm_values)
        metrics.update(derived_metrics)
    else:
        # If no data, set all metrics to NaN
        for metric in ['accuracy', 'precision', 'recall', 'f1', 'mcc', 
                       'roc_auc', 'pr_auc', 'specificity', 'npv']:
            metrics[metric] = float('nan')
    
    return metrics

@st.cache_data
def calculate_statistical_test(
    tp: int, 
    fn: int, 
    fp: int, 
    tn: int,
    use_fisher_exact: bool = None
) -> Dict[str, Any]:
    """
    Perform statistical tests (Chi-square and/or Fisher's exact) on confusion matrix.
    
    Parameters:
    -----------
    tp : int
        True Positive count
    fn : int
        False Negative count
    fp : int
        False Positive count
    tn : int
        True Negative count
    use_fisher_exact : bool, optional
        If True, use only Fisher's exact test. If False, use only Chi-square.
        If None (default), automatically choose based on expected frequencies,
        and calculate both tests when sample size permits.
        
    Returns:
    --------
    Dict[str, Any]
        Dictionary containing results for both tests (when appropriate) and metadata
        about which test is recommended for the given data.
    """
    # Create the contingency table
    contingency_table = np.array([[tp, fn], [fp, tn]])
    
    # Calculate expected frequencies to determine appropriate test
    expected = np.outer([tp + fp, fn + tn], [tp + fn, fp + tn]) / (tp + fp + fn + tn)
    small_sample = np.any(expected < 5)
    
    # Initialize results dictionary
    results = {
        'recommended_test': 'fisher' if small_sample else 'chi2',
        'small_sample': small_sample
    }
    
    # Calculate Fisher's exact test (always performed)
    try:
        odds_ratio, p_value_fisher = fisher_exact(contingency_table)
        results['fisher'] = {
            'test_name': "Fisher's exact test",
            'test_statistic': odds_ratio,
            'statistic_name': "odds ratio",
            'p_value': p_value_fisher,
            'significant': p_value_fisher < 0.05
        }
    except Exception as e:
        # Handle potential calculation errors
        results['fisher_error'] = str(e)
    
    # Calculate Chi-square test if appropriate
    # Either when explicitly requested or when sample size is large enough
    # AND use_fisher_exact is not True
    if (not small_sample or (use_fisher_exact is not None and not use_fisher_exact)) and use_fisher_exact is not True:
        try:
            chi2, p_value_chi2, dof, expected = chi2_contingency(contingency_table)
            results['chi2'] = {
                'test_name': "Chi-square test",
                'test_statistic': chi2,
                'statistic_name': "chi-square",
                'p_value': p_value_chi2,
                'significant': p_value_chi2 < 0.05
            }
        except Exception as e:
            # Handle potential calculation errors
            results['chi2_error'] = str(e)
    
    # Create interpretation for each test
    for test_key in ['fisher', 'chi2']:
        if test_key in results:
            p_value = results[test_key]['p_value']
            if p_value < 0.001:
                significance = "highly significant"
            elif p_value < 0.01:
                significance = "very significant"
            elif p_value < 0.05:
                significance = "significant"
            elif p_value < 0.1:
                significance = "marginally significant"
            else:
                significance = "not significant"
            
            test_name = results[test_key]['test_name']
            results[test_key]['interpretation'] = f"The {test_name} resulted in a p-value of {p_value:.4f}, which is {significance}."
    
    # For backward compatibility with existing code
    # Return the recommended test's info at the top level
    # If use_fisher_exact is True, always use fisher as the recommended test
    if use_fisher_exact is True:
        results['recommended_test'] = 'fisher'
    
    recommended_test = results['recommended_test']
    if recommended_test in results:
        for key, value in results[recommended_test].items():
            results[key] = value
    
    return results

@st.cache_data
def get_roc_curve_data(y_true: np.ndarray, y_pred_proba: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    Calculate ROC curve data points.
    
    Parameters:
    -----------
    y_true : array-like
        Ground truth binary labels (0 or 1)
    y_pred_proba : array-like
        Predicted probabilities (between 0 and 1)
        
    Returns:
    --------
    Tuple[np.ndarray, np.ndarray, np.ndarray]
        (fpr, tpr, thresholds) arrays for plotting
    """
    try:
        if len(np.unique(y_true)) > 1:
            fpr, tpr, thresholds = roc_curve(y_true, y_pred_proba)
            return fpr, tpr, thresholds
        else:
            # Return empty arrays if there's only one class
            return np.array([]), np.array([]), np.array([])
    except Exception as e:
        st.warning(f"Error calculating ROC curve: {str(e)}")
        return np.array([]), np.array([]), np.array([])

@st.cache_data
def get_pr_curve_data(y_true: np.ndarray, y_pred_proba: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    Calculate Precision-Recall curve data points.
    
    Parameters:
    -----------
    y_true : array-like
        Ground truth binary labels (0 or 1)
    y_pred_proba : array-like
        Predicted probabilities (between 0 and 1)
        
    Returns:
    --------
    Tuple[np.ndarray, np.ndarray, np.ndarray]
        (precision, recall, thresholds) arrays for plotting
    """
    try:
        if len(np.unique(y_true)) > 1:
            precision, recall, thresholds = precision_recall_curve(y_true, y_pred_proba)
            return precision, recall, thresholds
        else:
            # Return empty arrays if there's only one class
            return np.array([]), np.array([]), np.array([])
    except Exception as e:
        st.warning(f"Error calculating PR curve: {str(e)}")
        return np.array([]), np.array([]), np.array([])


# ===== DISPLAY METRICS TABLE =====

def display_metrics_table(metrics: Dict[str, Any], show_counts: bool = True) -> None:
    """
    Display a formatted metrics table for binary classification results using native Streamlit components.
    
    Parameters:
    -----------
    metrics : Dict[str, Any]
        Dictionary containing calculated metrics
    show_counts : bool
        Whether to show absolute counts (TP, TN, FP, FN)
    """
    
    # Define metric groups
    metric_groups = [
        {
            "title": "Performance",
            "metrics": [
                ("Accuracy", metrics.get('accuracy', float('nan'))),
                ("F1 Score", metrics.get('f1', float('nan'))),
                ("MCC", metrics.get('mcc', float('nan')))
            ]
        },
        {
            "title": "Precision/Recall",
            "metrics": [
                ("Precision (PPV)", metrics.get('precision', float('nan'))),
                ("Recall (Sens.)", metrics.get('recall', float('nan'))),
                ("Specificity", metrics.get('specificity', float('nan'))),
                ("NPV", metrics.get('npv', float('nan')))
            ]
        },
        {
            "title": "AUC Metrics",
            "metrics": [
                ("ROC AUC", metrics.get('roc_auc', float('nan'))),
                ("PR AUC", metrics.get('pr_auc', float('nan')))
            ]
        },
        {
            "title": "Settings",
            "metrics": [
                ("Threshold", metrics.get('threshold', 0.5)),
                ("Total Samples", metrics.get('total', 0))
            ]
        }
    ]
    
    # Create a 4-column layout
    cols = st.columns(4)
    
    # Display each metric group in a column
    for i, group in enumerate(metric_groups):
        with cols[i]:
            st.subheader(group["title"])
            # Convert metrics to DataFrame for display
            data = []
            for label, value in group["metrics"]:
                if isinstance(value, float):
                    if label == "Threshold":
                        formatted_value = f"{value:.2f}"
                    else:
                        formatted_value = f"{value:.4f}"
                else:
                    formatted_value = f"{value}"
                data.append({"Metric": label, "Value": formatted_value})
            
            df = pd.DataFrame(data)
            st.dataframe(df.set_index("Metric"), use_container_width=True)
    
    # If showing counts, display confusion matrix summary
    if show_counts:
        # Extract confusion matrix values
        tp = metrics.get('TP', 0)
        tn = metrics.get('TN', 0)
        fp = metrics.get('FP', 0)
        fn = metrics.get('FN', 0)
        total = metrics.get('total', tp + tn + fp + fn)
        
        if total > 0:
            # Calculate distributions directly (reusing existing patterns)
            actual_pos_count = tp + fn
            actual_neg_count = tn + fp
            pred_pos_count = tp + fp
            pred_neg_count = tn + fn
            
            actual_pos_perc = (actual_pos_count/total*100)
            actual_neg_perc = (actual_neg_count/total*100)
            pred_pos_perc = (pred_pos_count/total*100)
            pred_neg_perc = (pred_neg_count/total*100)
            
            bias_diff = pred_pos_perc - actual_pos_perc
            
            cm_cols = st.columns(4)
            
            with cm_cols[0]:
                st.subheader("Confusion Matrix")
                cm_data = [
                    {"Metric": "True Positives (TP)", "Count": tp},
                    {"Metric": "True Negatives (TN)", "Count": tn},
                    {"Metric": "False Positives (FP)", "Count": fp},
                    {"Metric": "False Negatives (FN)", "Count": fn}
                ]
                cm_df = pd.DataFrame(cm_data)
                st.dataframe(cm_df.set_index("Metric"), use_container_width=True)
                
            with cm_cols[1]:
                st.subheader("Actual Distribution")
                st.caption("Ground truth labels")
                actual_data = [
                    {"Class": "Positive (1)", "Count": f"{actual_pos_count} ({actual_pos_perc:.1f}%)"},
                    {"Class": "Negative (0)", "Count": f"{actual_neg_count} ({actual_neg_perc:.1f}%)"},
                    {"Class": "Total", "Count": f"{total}"}
                ]
                actual_df = pd.DataFrame(actual_data)
                st.dataframe(actual_df.set_index("Class"), use_container_width=True)
                
            with cm_cols[2]:
                st.subheader("Predicted Distribution")
                st.caption("Model predictions")
                pred_data = [
                    {"Class": "Positive (1)", "Count": f"{pred_pos_count} ({pred_pos_perc:.1f}%)"},
                    {"Class": "Negative (0)", "Count": f"{pred_neg_count} ({pred_neg_perc:.1f}%)"},
                    {"Class": "Total", "Count": f"{total}"}
                ]
                pred_df = pd.DataFrame(pred_data)
                st.dataframe(pred_df.set_index("Class"), use_container_width=True)
                
                if abs(bias_diff) > 5:
                    if bias_diff > 0:
                        st.caption(f"Model bias: +{bias_diff:.1f}% towards positive")
                    else:
                        st.caption(f"Model bias: {bias_diff:.1f}% towards negative")
            
            with cm_cols[3]:
                st.subheader("Statistical Tests")
                
                # Calculate statistical tests
                try:
                    test_results = calculate_statistical_test(tp, fn, fp, tn)
                    
                    # Determine if we should show comparative view
                    show_comparative = 'chi2' in test_results and 'fisher' in test_results
                    
                    if show_comparative:
                        # Create data for comparative display
                        st_data = [
                            {"Test": "Fisher's exact test", 
                             "p-value": format_p_value(test_results['fisher']['p_value']), 
                             "Significant": "Yes" if test_results['fisher']['significant'] else "No"},
                            {"Test": "Chi-square test", 
                             "p-value": format_p_value(test_results['chi2']['p_value']), 
                             "Significant": "Yes" if test_results['chi2']['significant'] else "No"}
                        ]
                        
                        # Add recommendation if needed
                        recommended = "Fisher" if test_results.get('small_sample', False) else "Chi-square"
                        
                    else:
                        # Single test display
                        test_key = test_results.get('recommended_test', 'fisher')
                        if test_key not in test_results:
                            test_key = 'fisher' if 'fisher' in test_results else 'chi2'
                        
                        if test_key in test_results:
                            st_data = [
                                {"Test": test_results[test_key]['test_name'], 
                                 "p-value": format_p_value(test_results[test_key]['p_value']), 
                                 "Significant": "Yes" if test_results[test_key]['significant'] else "No"}
                            ]
                            recommended = test_results[test_key]['test_name']
                        else:
                            st_data = [{"Test": "N/A", "p-value": "N/A", "Significant": "N/A"}]
                            recommended = "N/A"
                    
                    # Display the table
                    st_df = pd.DataFrame(st_data)
                    st.dataframe(st_df.set_index("Test"), use_container_width=True)
                    
                    # Add a note about which test is recommended if we have both
                    if show_comparative and test_results.get('small_sample', False):
                        st.caption("⚠️ Fisher's exact test recommended for small sample size.")
                    
                except Exception as e:
                    st.warning(f"Could not calculate statistical tests: {e}")
    


def display_statistical_test(metrics: Dict[str, Any]) -> None:
    """
    Display the results of statistical tests on the confusion matrix.
    
    This function takes pre-calculated metrics containing TP, TN, FP, FN,
    calculates the appropriate statistical test(s) (Chi-square and/or Fisher's exact),
    and displays the results in a formatted section.
    
    When both tests are appropriate, it will display a comparative view.
    When only Fisher's test is appropriate (small samples), it will display just that test.

    Parameters:
    -----------
    metrics : Dict[str, Any]
        Dictionary containing confusion matrix values (TP, TN, FP, FN).
        Must contain keys: 'TP', 'TN', 'FP', 'FN'.
    """
    # Extract confusion matrix values safely using .get()
    tp = metrics.get('TP', 0)
    tn = metrics.get('TN', 0)
    fp = metrics.get('FP', 0)
    fn = metrics.get('FN', 0)
    
    # Ensure all required values are present
    if not all(k in metrics for k in ('TP', 'TN', 'FP', 'FN')):
        st.warning("Cannot perform statistical test: Missing TP, TN, FP, or FN in metrics dictionary.")
        return

    # Calculate statistical tests
    try:
        test_results = calculate_statistical_test(tp, fn, fp, tn)
    except ValueError as e:
        # Catch potential errors during test calculation (e.g., all zero matrix)
        st.warning(f"Could not calculate statistical test: {e}")
        return
    
    # Determine if we should show comparative view (both Chi-square and Fisher's)
    show_comparative = 'chi2' in test_results and 'fisher' in test_results
    
    if show_comparative:
        st.markdown("### Statistical Test Comparison")
        
        # Create two columns for side-by-side comparison
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown(f"**{test_results['fisher']['test_name']}**")
            st.markdown(f"""
            | Metric | Value |
            | ------ | ----- |
            | **Odds Ratio** | {test_results['fisher']['test_statistic']:.4f} |
            | **p-value** | {format_p_value(test_results['fisher']['p_value'])} |
            | **Significant (α=0.05)** | {'Yes' if test_results['fisher']['significant'] else 'No'} |
            """)
        
        with col2:
            st.markdown(f"**{test_results['chi2']['test_name']}**")
            st.markdown(f"""
            | Metric | Value |
            | ------ | ----- |
            | **Chi-square** | {test_results['chi2']['test_statistic']:.4f} |
            | **p-value** | {format_p_value(test_results['chi2']['p_value'])} |
            | **Significant (α=0.05)** | {'Yes' if test_results['chi2']['significant'] else 'No'} |
            """)
        
        # Add recommendation note if sample is small
        if test_results.get('small_sample', False):
            st.info("⚠️ **Note:** Some expected frequencies are <5. Fisher's exact test is recommended for more reliable results with this data.")
        
        # Interpretation in an expander
        with st.expander("Test Interpretations", expanded=False):
            if 'fisher' in test_results:
                st.markdown(f"**Fisher's exact test:** {test_results['fisher']['interpretation']}")
            if 'chi2' in test_results:
                st.markdown(f"**Chi-square test:** {test_results['chi2']['interpretation']}")
            
            st.markdown("""
            **When to use each test:**
            - **Fisher's exact test** is more reliable for small samples or when expected frequencies are low (<5).
              The odds ratio represents the association between predictions and actual values.
            - **Chi-square test** is typically used for larger samples with higher expected frequencies.
              It evaluates whether the observed pattern of predictions differs significantly from what would be expected by chance.
            """)
    else:
        # Single test display (traditional format)
        # This code follows the original format for backward compatibility
        test_key = test_results.get('recommended_test', 'fisher')
        
        if test_key not in test_results:
            test_key = 'fisher' if 'fisher' in test_results else 'chi2'
        
        if test_key not in test_results:
            st.warning("No valid statistical test results available.")
            return
        
        result = test_results[test_key]
        
        st.markdown(f"""
        #### {result['test_name']}
        
        | Metric | Value |
        | ------ | ----- |
        | **{result['statistic_name'].title()}** | {result['test_statistic']:.4f} |
        | **p-value** | {format_p_value(result['p_value'])} |
        | **Significant (α=0.05)** | {'Yes' if result['significant'] else 'No'} |
        
        **Interpretation**: {result['interpretation']}
        """)
        
        # Add explanation based on test type
        if "Fisher" in result['test_name']:
            st.markdown("""
            **Note**: Fisher's exact test was used because expected cell frequencies were small (<5),
            which makes Chi-square less reliable. The odds ratio represents the association between
            predictions and actual values.
            """)
        else:
            st.markdown("""
            **Note**: The Chi-square test evaluates whether the observed pattern of predictions
            differs significantly from what would be expected by chance.
            """)

def categorize_by_performance(
    df: pd.DataFrame, 
    threshold: float = 0.5
) -> pd.DataFrame:
    """
    Categorize predictions into performance groups (TP, TN, FP, FN).
    
    This function adds a 'performance_category' column to classify each prediction
    based on its correctness relative to the ground truth.
    
    Args:
        df: DataFrame with 'real' and 'pred' columns
        threshold: Classification threshold for converting predictions to binary
        
    Returns:
        DataFrame with added 'performance_category' column
    """
    # Create a copy to avoid modifying original data
    df_analysis = df.copy()
    
    # Convert predictions to binary using threshold
    df_analysis['pred_binary'] = (df_analysis['pred'] >= threshold).astype(int)
    
    # Create performance categories
    conditions = [
        (df_analysis['real'] == 1) & (df_analysis['pred_binary'] == 1),  # True Positive
        (df_analysis['real'] == 0) & (df_analysis['pred_binary'] == 0),  # True Negative
        (df_analysis['real'] == 0) & (df_analysis['pred_binary'] == 1),  # False Positive
        (df_analysis['real'] == 1) & (df_analysis['pred_binary'] == 0),  # False Negative
    ]
    
    choices = ['True Positive', 'True Negative', 'False Positive', 'False Negative']
    
    df_analysis['performance_category'] = np.select(conditions, choices, default='Unknown')
    
    # Add correctness flag for simpler analysis
    df_analysis['is_correct'] = df_analysis['real'] == df_analysis['pred_binary']
    
    return df_analysis


def get_feature_columns(df: pd.DataFrame) -> Dict[str, List[str]]:
    """
    Identify and categorize feature columns in the dataset.
    
    This function automatically detects features and categorizes them by data type
    for flexible analysis across different datasets.
    
    Args:
        df: Input DataFrame
        
    Returns:
        Dictionary with categorized feature lists
    """
    # Required columns for model outcomes
    required_columns = {'patient_id', 'day', 'model', 'real', 'pred'}
    
    # All columns that are not required are considered features
    all_columns = set(df.columns)
    feature_columns = list(all_columns - required_columns)
    
    # Categorize features by data type (generic approach)
    numerical_features = []
    categorical_features = []
    
    for col in feature_columns:
        if df[col].dtype in ['int64', 'float64']:
            numerical_features.append(col)
        else:
            categorical_features.append(col)
    
    return {
        'all_features': feature_columns,
        'numerical': numerical_features,
        'categorical': categorical_features
    }


 