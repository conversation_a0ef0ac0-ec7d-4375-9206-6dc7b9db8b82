import streamlit as st
import os
import pandas as pd
from typing import Dict, Any

# Relative imports for modules within the same package
from .auth import logout_user
from .utils import get_base64_encoded_image
from .data_handling import validate_theme


def display_sidebar_header():
    """
    Display the sidebar header with user profile and file upload section.
    
    Returns:
    --------
    uploaded_file : st.UploadedFile or None
        The uploaded file object if a file was uploaded, None otherwise
    importance_file : st.UploadedFile or None
        The feature importance file if uploaded, None otherwise
    """
    # Initialize return variables
    uploaded_file = None
    importance_file = None

    # Get logo path for sidebar
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # Adjust path assuming 'static' is relative to the main app directory, not modules
    app_dir = os.path.dirname(current_dir) 
    logo_path = os.path.join(app_dir, 'static', 'AIO-logo.png')
    
    # Add custom CSS for sidebar header styling
    st.markdown("""
    <style>
        /* Logo styling */
        .sidebar-logo {
            width: 100%;
            text-align: center;
            padding: 0.7rem 0;
            margin-bottom: 0.5rem;
            border-bottom: 1px solid rgba(250, 250, 250, 0.1);
        }
        .sidebar-logo img {
            max-width: 120px;
            height: auto;
        }
        
        /* User profile card */
        .user-profile {
            display: flex;
            align-items: center;
            padding: 0.7rem 0;
            border-bottom: 1px solid rgba(250, 250, 250, 0.1);
            margin-bottom: 1rem;
        }
        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: #4F8DF5;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        .user-info {
            flex: 1;
        }
        .user-name {
            font-weight: 600;
            margin: 0;
            font-size: 1rem;
        }
        .user-role {
            color: #94A3B8;
            font-size: 0.85rem;
            margin: 0;
        }
        
        /* Upload section */
        .upload-section {
            margin-top: 1rem;
            margin-bottom: 1rem;
        }
        
        /* Help text styling */
        .help-text {
            font-size: 0.75rem;
            color: #94A3B8;
            margin-top: 0.3rem;
        }

        /* Custom expander styling */
        .streamlit-expanderHeader {
            font-size: 0.85rem !important;
            padding: 0.3rem 0.5rem !important;
        }
        div.streamlit-expanderContent {
            padding: 0.5rem 0 !important;
        }
    </style>
    """, unsafe_allow_html=True)
    
    # Display company logo at the top of sidebar
    if os.path.exists(logo_path):
        st.markdown(f"""
        <div class="sidebar-logo">
            <img src="data:image/png;base64,{get_base64_encoded_image(logo_path)}" alt="AIO Logo">
        </div>
        """, unsafe_allow_html=True)
    else:
        st.warning(f"Logo not found at: {logo_path}") # Add warning if logo not found

    # Get user info from session state
    user_info = st.session_state.get('user_info', {}) # Use get for safety
    name = user_info.get('name', 'Unknown')
    role = user_info.get('role', 'user')
    
    # Display user profile with logout in the same row
    initial = name[0].upper() if name and len(name) > 0 else "?"
    
    # Create a 3-column layout: Avatar | Name+Role | Logout
    cols = st.columns([0.5, 2.3, 1.2])
    
    # Column 1: Avatar
    with cols[0]:
        st.markdown(f"""
        <div style="display: flex; justify-content: center; align-items: center; 
                    width: 36px; height: 36px; background-color: #4F8DF5; 
                    color: white; border-radius: 50%; font-weight: bold;">
            {initial}
        </div>
        """, unsafe_allow_html=True)
    
    # Column 2: Name and Role
    with cols[1]:
        st.markdown(f"""
        <div style="margin: 0; padding: 0;">
            <p style="font-weight: 600; margin: 0; font-size: 1rem;">{name}</p>
            <p style="color: #94A3B8; font-size: 0.85rem; margin: 0;">{role}</p>
        </div>
        """, unsafe_allow_html=True)
    
    # Column 3: Logout Button
    with cols[2]:
        if st.button("Log Out", type="secondary", key="logout_btn", use_container_width=True):
            logout_user()
            st.rerun()
    
    # Add a separator
    st.markdown("---")
    
    # Check if there's already a file loaded from a previous interaction
    current_file_name = st.session_state.get('current_file', None)
    
    # Header for main data upload
    # 📊 Classification Data")
    
    # If a file is already loaded, show a compact file indicator with expand option
    if current_file_name:
        st.success(f"📄 Loaded: {current_file_name}")
        with st.expander(" 📊 Classification Dataset", expanded=False):
            new_uploaded_file = st.file_uploader(
                "Upload New Classification Data CSV",
                type=["csv"],
                key="file_uploader_change", # Different key
                help="Upload CSV with columns: patient_id, model, real (0/1), pred (0.0-1.0)"
            )
            if new_uploaded_file: # If a new file is uploaded in the expander
                 uploaded_file = new_uploaded_file 
    else:
        # When no file is loaded, show the standard uploader prominently
        uploaded_file = st.file_uploader(
            "Upload Classification Data CSV",
            type=["csv"],
            key="file_uploader_initial", # Different key
            help="Upload CSV with columns: patient_id, model, real (0/1), pred (0.0-1.0)"
        )
    
    # Header for feature importance upload (separate section)
    # 🔍 Feature Importance")
    
    # Check if there's already a feature importance file loaded
    importance_file_name = st.session_state.get('importance_file', None) # Use correct session state variable
    
    if importance_file_name:
        st.success(f"📄 Loaded: {importance_file_name}")
        with st.expander("🔍 Feature importance data", expanded=False):
            new_importance_file = st.file_uploader(
                "Upload New Feature Importance CSV",
                type=["csv"],
                key="importance_uploader_change", # Different key
                help="Upload CSV with columns: feature_name, importance_score"
            )
            if new_importance_file:
                importance_file = new_importance_file
    else:
        # When no importance file is loaded, show the uploader
        importance_file = st.file_uploader(
            "Upload Feature Importance CSV (Optional)",
            type=["csv"],
            key="importance_uploader_initial", # Different key
            help="Upload CSV with columns: feature_name, importance_score"
        )
    
    # Return the file objects only if they were newly uploaded in this run
    return uploaded_file, importance_file

def setup_sidebar_controls(df, importance_df=None, default_model=None, default_patients=None):
    """
    Set up interactive controls in the sidebar.
    
    Parameters
    ----------
    df : pandas.DataFrame
        The dataframe with classification data
    importance_df : pandas.DataFrame, optional
        Feature importance data, if available
    default_model : str, optional
        Default model to select
    default_patients : list, optional
        Default list of patients to select
    
    Returns
    -------
    dict
        Dictionary of settings from the controls
    """
    # Initialize settings dictionary
    settings = {}
    
    # Add a separator
    st.markdown("---")
    
    # Filtering options
    st.sidebar.markdown("### Data Filters")
    
    # 1. MODEL FILTER
    # Create list of models for the selector
    models = sorted(df['model'].unique().tolist())
    model_options = ["All models"] + models
    
    # Determine default model index
    if default_model is None:
        default_model_index = 0  # "All models"
    else:
        # Find the index of the default model in the list, with fallback to 0
        try:
            default_model_index = model_options.index(default_model)
        except ValueError:
            default_model_index = 0  # Default to "All models" if not found
    
    # Use st.session_state for selectbox consistency
    if 'selected_model_index' not in st.session_state:
        st.session_state['selected_model_index'] = default_model_index
    
    # Create model selector
    selected_model_index = st.sidebar.selectbox(
        "Model",
        options=range(len(model_options)),
        format_func=lambda x: model_options[x],
        index=st.session_state['selected_model_index'],
        key='model_select'
    )
    
    # Update session state
    st.session_state['selected_model_index'] = selected_model_index
    
    # Get the selected model name (None if "All models")
    selected_model = None if selected_model_index == 0 else model_options[selected_model_index]
    settings['selected_model'] = selected_model
    
    # 2. PATIENT FILTER
    # Get unique patient IDs
    patient_ids = sorted(df['patient_id'].unique().tolist())
    
    # Setup default patients
    if default_patients is None:
        default_patients = []
    
    # Multi-select for patients
    selected_patients = st.sidebar.multiselect(
        "Patients",
        options=patient_ids,
        default=default_patients
    )
    
    # Store the selected patients in settings
    # An empty list will be treated as "all patients" in the filtering logic
    settings['selected_patients'] = selected_patients
    
    # Classification threshold
    
    # Initialize threshold in session state if not present
    if 'threshold' not in st.session_state:
        st.session_state['threshold'] = 0.5
    
    threshold = st.sidebar.slider(
        "Classification Threshold",
        min_value=0.0,
        max_value=1.0,
        value=st.session_state['threshold'],  # Use value from session state with dictionary access
        step=0.01,
        format="%0.2f",
        key="threshold_slider"  # Add a key for the slider
        )
    
    # Update session state with the new threshold value (use dictionary access)
    st.session_state['threshold'] = threshold
    settings['threshold'] = threshold

    
    # Theme selection
    # st.sidebar.markdown("### Visualization Settings")
    
    # Load theme from session state if available
    current_theme = st.session_state.get('theme', "plotly_white")
    
    # Use a different key for the theme selector to force reactivity
    theme_key = f"theme_selector_{st.session_state.get('theme_counter', 0)}"
    
    # Lista completa de temas disponibles
    available_themes = [
        'plotly', 'plotly_white', 'plotly_dark', 
        'ggplot2', 'seaborn', 'simple_white',
        'presentation', 'none'
    ]
    
    # Obtener el índice del tema actual en la lista
    current_theme_index = 0
    if current_theme in available_themes:
        current_theme_index = available_themes.index(current_theme)
    
    theme = st.sidebar.selectbox(
        "Chart Theme",
        options=available_themes,
        index=current_theme_index,
        key=theme_key
    )
    
    # Validate theme and store in state
    settings['theme'] = validate_theme(theme)
    st.session_state['theme'] = settings['theme']
    
    # If theme changed, show information about future visualizations
    if theme != current_theme:
        # Increment theme counter to force widget recreation on next run
        st.session_state['theme_counter'] = st.session_state.get('theme_counter', 0) + 1

    # Show counts is now directly set to True without a checkbox
    settings['show_counts'] = True
    st.session_state['show_counts'] = True
    
    return settings 