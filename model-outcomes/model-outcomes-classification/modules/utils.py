"""
Utility functions for the Model Outcomes | Classification application.
This module contains general purpose helper functions that can be used across the application.
"""
import os
import base64
import streamlit as st

def get_base64_encoded_image(image_path):
    """
    Get base64 encoded image for embedding in HTML.
    
    Parameters:
    -----------
    image_path : str
        Path to the image file
        
    Returns:
    --------
    str
        Base64 encoded string of the image or empty string if file doesn't exist
    """
    if os.path.exists(image_path):
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode()
    return ""

def display_insight_message(message: str, explanation: str, message_type: str = "info", key_prefix: str = None):
    """
    Display an insight message with a help button that shows an explanation.
    
    Parameters:
    -----------
    message : str
        The main message to display
    explanation : str
        The explanation to show when the help button is clicked
    message_type : str
        Type of message: "info", "success", or "warning"
    key_prefix : str
        Optional prefix for the unique key to avoid conflicts when using multiple messages
    """
    # Create unique key for the expander based on message and prefix
    message_key = f"{key_prefix}_{hash(message)}" if key_prefix else f"insight_{hash(message)}"
    help_key = f"{message_key}_help"
    
    # Create columns for the message and the help button
    col1, col2 = st.columns([0.9, 0.1])
    
    # Display the message in the first column
    with col1:
        if message_type == "success":
            st.success(message)
        elif message_type == "warning":
            st.warning(message)
        else:  # Default is info
            st.info(message)
    
    # Display the help button in the second column with improved styling
    with col2:
        # Style the help button to look more like a tooltip icon
        st.markdown("""
        <style>
        .help-button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: #E0E0E0;
            color: #505050;
            font-weight: bold;
            font-size: 14px;
            cursor: pointer;
            border: none;
            margin-top: 10px;
        }
        .help-button:hover {
            background-color: #BDBDBD;
        }
        </style>
        """, unsafe_allow_html=True)
        
        help_clicked = st.button("?", key=help_key, help="Click for more information about this rating")
    
    # Show explanation if help button was clicked
    if help_clicked:
        with st.expander("Criteria Used for This Rating", expanded=True):
            st.markdown(explanation)

def extend_session_state():
    """
    Extend session state variables beyond authentication.
    Used to maintain state across reruns of the Streamlit app.
    
    Note: This function should be called after auth.initialize_session_state()
    to ensure all state variables are properly initialized.
    """
    # For storing the current file name
    if 'current_file' not in st.session_state:
        st.session_state['current_file'] = None
        
    # For storing the uploaded data
    if 'uploaded_data' not in st.session_state:
        st.session_state['uploaded_data'] = None
        
    # For storing the feature importance data
    if 'importance_data' not in st.session_state:
        st.session_state['importance_data'] = None
        
    # For storing the importance file name
    if 'importance_file' not in st.session_state:
        st.session_state['importance_file'] = None
        
    # For theme selection
    if 'theme' not in st.session_state:
        st.session_state['theme'] = 'plotly_white'  # Default theme
        
    # For tracking theme changes
    if 'theme_counter' not in st.session_state:
        st.session_state['theme_counter'] = 0
        
    # For tracking metrics expander state
    if 'metrics_expander_state' not in st.session_state:
        st.session_state['metrics_expander_state'] = True 