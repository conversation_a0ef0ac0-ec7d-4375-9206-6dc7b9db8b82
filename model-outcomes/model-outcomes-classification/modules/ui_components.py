import streamlit as st
import pandas as pd

def create_tooltip(content, icon="ℹ️", position="top"):
    """
    Creates an HTML tooltip that can be embedded in streamlit markdown.
    
    Parameters:
    -----------
    content : str
        HTML content to display in the tooltip
    icon : str
        Icon to show for the tooltip trigger (default: ℹ️)
    position : str
        Position of tooltip ('top', 'bottom', 'left', 'right')
        
    Returns:
    --------
    str
        HTML string with tooltip that can be included in st.markdown()
    """
    # Add the CSS only once per session
    if "tooltip_css_added" not in st.session_state:
        st.markdown("""
        <style>
        .tooltip-container {
          position: relative;
          display: inline-block;
          cursor: help;
          margin-left: 4px;
          color: #888;
          font-size: 14px;
        }
        
        .tooltip-content {
          visibility: hidden;
          width: 320px;
          background-color: #333;
          color: #fff;
          text-align: left;
          border-radius: 6px;
          padding: 10px;
          position: absolute;
          z-index: 1;
          opacity: 0;
          transition: opacity 0.3s;
          font-size: 12px;
          line-height: 1.4;
          box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        
        /* Positions */
        .tooltip-top { bottom: 125%; left: 50%; margin-left: -160px; }
        .tooltip-bottom { top: 125%; left: 50%; margin-left: -160px; }
        .tooltip-left { top: -5px; right: 125%; }
        .tooltip-right { top: -5px; left: 125%; }
        
        /* Arrows */
        .tooltip-top::after {
          content: "";
          position: absolute;
          top: 100%;
          left: 50%;
          margin-left: -5px;
          border-width: 5px;
          border-style: solid;
          border-color: #333 transparent transparent transparent;
        }
        
        .tooltip-bottom::after {
          content: "";
          position: absolute;
          bottom: 100%;
          left: 50%;
          margin-left: -5px;
          border-width: 5px;
          border-style: solid;
          border-color: transparent transparent #333 transparent;
        }
        
        .tooltip-left::after {
          content: "";
          position: absolute;
          top: 15px;
          left: 100%;
          margin-top: -5px;
          border-width: 5px;
          border-style: solid;
          border-color: transparent transparent transparent #333;
        }
        
        .tooltip-right::after {
          content: "";
          position: absolute;
          top: 15px;
          right: 100%;
          margin-top: -5px;
          border-width: 5px;
          border-style: solid;
          border-color: transparent #333 transparent transparent;
        }
        
        .tooltip-container:hover .tooltip-content {
          visibility: visible;
          opacity: 1;
        }
        </style>
        """, unsafe_allow_html=True)
        st.session_state.tooltip_css_added = True
    
    # Generate tooltip HTML
    position_class = f"tooltip-{position}"
    return f"""<span class="tooltip-container">{icon}
                <span class="tooltip-content {position_class}">{content}</span>
              </span>"""

def display_welcome_screen():
    """Displays a comprehensive welcome screen with guidance, data requirements, and examples."""
    # Get user info and check if there's a loaded file
    user_name = st.session_state.user_info.get("name", "there") if hasattr(st.session_state, "user_info") else "there"
    has_data = "uploaded_data" in st.session_state and st.session_state.uploaded_data is not None
    
    # Section 1: Personalized Welcome with Status
    st.markdown(f"## Welcome to the Model Outcomes | Classification, {user_name}!")
    
    # Show appropriate system status message
    if has_data:
        st.success("✅ Data successfully loaded. Explore the tabs below to analyze your classification model's performance.")
    else:
        st.info("🔍 No data loaded yet. Please upload a file using the sidebar to begin analysis.")
    
    # Section 2: Quick Start Panel
    st.markdown("### 🚀 Quick Start")
    
    quick_start_col1, quick_start_col2 = st.columns([1, 1])
    
    with quick_start_col1:
        st.markdown("""
        **Key Actions:**
        1. Upload your classification data file using the sidebar
        2. (Optional) Upload a feature importance file
        3. Adjust threshold and filters in the sidebar
        4. Explore performance metrics and visualizations in the tabs
        """)
    
    with quick_start_col2:
        # Create sample data for the template
        sample_data = pd.DataFrame({
            'patient_id': ['PT001', 'PT002', 'PT001', 'PT002'],
            'model': ['model_a', 'model_a', 'model_b', 'model_b'],
            'real': [0, 1, 0, 1],
            'pred': [0.25, 0.85, 0.15, 0.75]
        })
        
        # Create sample importance data
        sample_importance = pd.DataFrame({
            'feature_name': ['age', 'glucose', 'bmi', 'blood_pressure'],
            'importance_score': [0.35, 0.28, 0.22, 0.15]
        })
        
        # Template download options
        st.markdown("**Need Sample Templates?**")
        col1, col2 = st.columns(2)
        
        with col1:
            csv = sample_data.to_csv(index=False).encode('utf-8')
            st.download_button(
                "Classification Data Template",
                csv,
                "classification_template.csv",
                "text/csv",
                key='download-classification-template',
                help="Download a template CSV with the required structure"
            )
            
        with col2:
            importance_csv = sample_importance.to_csv(index=False).encode('utf-8')
            st.download_button(
                "Importance Data Template",
                importance_csv,
                "importance_template.csv", 
                "text/csv",
                key='download-importance-template',
                help="Download a template for feature importance data"
            )
    
    # Section 3: Data Requirements (collapsible)
    with st.expander("📋 Classification Data Format", expanded=not has_data):
        req_col1, req_col2 = st.columns([3, 2])
        
        with req_col1:
            st.markdown("""
            Your classification data CSV must contain these columns:
            
            | Column | Description | Type |
            | ------ | ----------- | ---- |
            | **patient_id** | Patient identifier | string/number |
            | **model** | Model identifier | string |
            | **real** | Actual class label | binary (0 or 1) |
            | **pred** | Predicted probability | float (0.0 to 1.0) |
            
            **Note:** You can include additional columns that will be treated as features.
            """)
        
        with req_col2:
            # Show compact example
            st.markdown("**Example Data:**")
            st.dataframe(
                sample_data,
                hide_index=True,
                column_config={
                    "patient_id": st.column_config.TextColumn("Patient ID"),
                    "model": st.column_config.TextColumn("Model"),
                    "real": st.column_config.NumberColumn("Real (0/1)"),
                    "pred": st.column_config.NumberColumn("Pred Prob", format="%.2f"),
                },
                height=160
            )
    
    # Section 4: Feature Importance Requirements
    with st.expander("📊 Feature Importance Format (Optional)", expanded=False):
        imp_col1, imp_col2 = st.columns([3, 2])
        
        with imp_col1:
            st.markdown("""
            Your feature importance CSV should contain:
            
            | Column | Description | Type |
            | ------ | ----------- | ---- |
            | **feature_name** | Name of the feature | string |
            | **importance_score** | Importance value | float |
            
            **Note:** Importance scores can come from various sources:
            - Model coefficients (e.g., logistic regression)
            - Feature importance from tree-based models
            - SHAP values
            - Permutation importance
            """)
        
        with imp_col2:
            # Show compact example
            st.markdown("**Example Importance Data:**")
            st.dataframe(
                sample_importance,
                hide_index=True,
                column_config={
                    "feature_name": st.column_config.TextColumn("Feature"),
                    "importance_score": st.column_config.NumberColumn("Importance", format="%.2f"),
                },
                height=160
            )
    
    # Section 5: Available Metrics and Visualizations
    with st.expander("🔍 Available Metrics & Visualizations", expanded=False):
        viz_col1, viz_col2 = st.columns(2)
        
        with viz_col1:
            st.markdown("**Performance Metrics:**")
            st.markdown("""
            - **Threshold-based:** Accuracy, Precision, Recall, F1
            - **Specialized:** Specificity, NPV, MCC
            - **Threshold-independent:** ROC AUC, PR AUC
            - **Statistical Tests:** Chi-square, Fisher's exact test
            """)
        
        with viz_col2:
            st.markdown("**Visualizations:**")
            st.markdown("""
            - **Confusion Matrix:** Interactive heatmap
            - **ROC Curve:** With AUC score
            - **Precision-Recall Curve:** With average precision
            - **Probability Distribution:** Separated by class
            - **Feature Importance:** Bar chart of feature contributions
            """)
    
    # Section 6: Tour of the Interface
    with st.expander("🧭 Navigation Guide", expanded=False):
        st.markdown("""
        This application is organized into four main tabs:
        
        1. **Overview & Data:** Data summary, class distribution, and sample view
        2. **Threshold Performance:** Confusion matrix and metrics at current threshold
        3. **Global Performance:** ROC/PR curves and probability distributions
        4. **Feature Importance:** Feature ranking and contribution analysis
        
        **📊 Global Metrics Summary:**
        
        At the top of the application, you'll find an expandable "Classification Metrics Summary" panel that:
        - Provides a comprehensive view of all key metrics in one place
        - Updates automatically with threshold changes
        - Remains accessible as you navigate between tabs
        - Contains both performance metrics and confusion matrix details
        
        This central dashboard is designed to be your reference point as you explore different visualizations.
        
        Use the sidebar controls to:
        - Set the classification threshold (default: 0.5)
        - Filter by specific model or patient
        - Select visualization theme
        """)
    
    # Footer with tip
    st.markdown("---")
    st.markdown("""
    💡 **Tip:** For best experience, start by uploading data, then explore the tabs from left to right for a logical analysis flow.
    """)
        
    # Update session state for tracking user's journey
    if "welcome_screen_viewed" not in st.session_state:
        st.session_state.welcome_screen_viewed = True 