"""
Flexible data loader module for classification model visualization.

This module provides a more resilient and flexible way to load classification data
from various file formats (CSV, Excel, Pickle, Parquet) and with flexible column mapping.
It can intelligently detect and map columns, and provides an interactive UI for manual mapping when needed.
"""
import streamlit as st
import pandas as pd
import numpy as np
import os
from typing import Dict, List, Optional, Tuple, Any, Union
import io


def detect_file_format(uploaded_file) -> str:
    """
    Detect the format of the uploaded file based on extension and content.
    
    Parameters:
    -----------
    uploaded_file : UploadedFile
        The file object from Streamlit's file_uploader
        
    Returns:
    --------
    str
        Detected format: 'csv', 'excel', 'pickle', 'parquet' or 'unknown'
    """
    if uploaded_file is None:
        return 'unknown'
    
    # Get file extension
    filename = uploaded_file.name.lower()
    
    if filename.endswith('.csv'):
        return 'csv'
    elif filename.endswith(('.xls', '.xlsx')):
        return 'excel'
    elif filename.endswith('.pkl') or filename.endswith('.pickle'):
        return 'pickle'
    elif filename.endswith('.parquet'):
        return 'parquet'
    
    # Try to detect by content if extension is not conclusive
    try:
        # Check first few bytes for CSV-like content
        content_start = uploaded_file.read(1024).decode('utf-8')
        uploaded_file.seek(0)  # Reset file pointer
        
        if ',' in content_start or ';' in content_start or '\t' in content_start:
            return 'csv'
    except:
        pass
    
    return 'unknown'


def load_dataframe(uploaded_file, file_format: str = None) -> Optional[pd.DataFrame]:
    """
    Load a dataframe from the uploaded file in the detected or specified format.
    
    Parameters:
    -----------
    uploaded_file : UploadedFile
        The file object from Streamlit's file_uploader
    file_format : str, optional
        Format to use for loading. If None, will be detected automatically.
        
    Returns:
    --------
    pd.DataFrame or None
        Loaded dataframe or None if loading fails
    """
    if uploaded_file is None:
        return None
    
    # Detect format if not specified
    if file_format is None:
        file_format = detect_file_format(uploaded_file)
    
    try:
        if file_format == 'csv':
            # Try different separators
            return pd.read_csv(uploaded_file, sep=None, engine='python')
        
        elif file_format == 'excel':
            return pd.read_excel(uploaded_file)
        
        elif file_format == 'pickle':
            return pd.read_pickle(uploaded_file)
        
        elif file_format == 'parquet':
            return pd.read_parquet(uploaded_file)
        
        else:
            st.error(f"Unsupported file format: {file_format}")
            return None
            
    except Exception as e:
        st.error(f"Error loading file: {str(e)}")
        return None


def get_candidate_columns(df: pd.DataFrame, target_type: str) -> List[str]:
    """
    Get candidate columns that might match the required column type based on heuristics.
    
    Parameters:
    -----------
    df : pd.DataFrame
        Dataframe to analyze
    target_type : str
        Type of column to find ('patient_id', 'model', 'real', 'pred')
        
    Returns:
    --------
    List[str]
        List of column names that are candidates for the target type
    """
    if df is None or df.empty:
        return []
    
    candidates = []
    
    # Normalize column names for comparison
    col_map = {col.lower().replace(' ', '_').replace('-', '_'): col for col in df.columns}
    
    if target_type == 'patient_id':
        # Look for ID-like columns
        id_patterns = ['id', 'patient', 'subject', 'participant', 'case', 'person', 'number', 'identifier']
        for pattern in id_patterns:
            for col in col_map:
                if pattern in col and col_map[col] not in candidates:
                    candidates.append(col_map[col])
        
        # Prioritize columns that are unique identifiers
        for col in df.columns:
            if col not in candidates and df[col].nunique() == len(df):
                candidates.append(col)
    
    elif target_type == 'model':
        # Look for model-like columns
        model_patterns = ['model', 'algorithm', 'method', 'technique', 'class', 'classifier', 'type']
        for pattern in model_patterns:
            for col in col_map:
                if pattern in col and col_map[col] not in candidates:
                    candidates.append(col_map[col])
        
        # Look for categorical columns with few unique values
        for col in df.columns:
            if col not in candidates and df[col].dtype == 'object' and 1 < df[col].nunique() < 10:
                candidates.append(col)
    
    elif target_type == 'real':
        # Look for columns with binary values or ground truth indicators
        real_patterns = ['real', 'true', 'actual', 'ground_truth', 'target', 'label', 'y', 'class', 'outcome']
        for pattern in real_patterns:
            for col in col_map:
                if pattern in col and col_map[col] not in candidates:
                    candidates.append(col_map[col])
        
        # Look for binary columns
        for col in df.columns:
            if col not in candidates:
                unique_vals = df[col].dropna().unique()
                if len(unique_vals) == 2 and set(unique_vals).issubset({0, 1, True, False, "0", "1", "True", "False"}):
                    candidates.append(col)
    
    elif target_type == 'pred':
        # Look for prediction-like columns
        pred_patterns = ['pred', 'prediction', 'score', 'probability', 'confidence', 'output', 'y_pred', 'p_value', 'prob']
        for pattern in pred_patterns:
            for col in col_map:
                if pattern in col and col_map[col] not in candidates:
                    candidates.append(col_map[col])
        
        # Look for numeric columns with values between 0 and 1
        for col in df.columns:
            if col not in candidates and pd.api.types.is_numeric_dtype(df[col]):
                min_val = df[col].min()
                max_val = df[col].max()
                if 0 <= min_val and max_val <= 1:
                    candidates.append(col)
    
    return candidates


def display_column_mapping_ui(df: pd.DataFrame) -> Dict[str, str]:
    """
    Display UI for mapping columns in the dataframe to required columns.
    
    Parameters:
    -----------
    df : pd.DataFrame
        Dataframe to map columns from
        
    Returns:
    --------
    Dict[str, str]
        Mapping from required column names to actual column names in the dataframe
    """
    st.markdown("### Column Mapping")
    st.info("Please map the columns in your dataset to the required columns for classification visualization.")
    
    required_cols = ['patient_id', 'model', 'real', 'pred']
    column_mapping = {}
    
    # Create two columns for a more compact UI
    col1, col2 = st.columns(2)
    
    # Column descriptions for user guidance
    descriptions = {
        'patient_id': "Unique identifier for each patient/subject",
        'model': "Identifier for the prediction model (can be auto-generated)",
        'real': "Actual ground truth values (binary: 0 or 1)",
        'pred': "Predicted probabilities (values between 0 and 1)"
    }
    
    with col1:
        # Map patient_id and model columns
        for req_col in ['patient_id', 'model']:
            # Get candidate columns
            candidates = get_candidate_columns(df, req_col)
            
            # Add None option for model column (can be auto-generated)
            options = list(df.columns)
            if req_col == 'model':
                options = ["[Auto-generate]"] + options
            
            # Determine default selection
            default_idx = 0
            if candidates and candidates[0] in options:
                default_idx = options.index(candidates[0])
            
            # Create selectbox with highlighted candidates
            st.markdown(f"**{req_col}**")
            st.caption(descriptions[req_col])
            
            # Show candidate recommendations if available
            if candidates:
                st.caption(f"🔍 Suggestions: {', '.join(candidates[:3])}")
            
            selected = st.selectbox(
                f"Select column for '{req_col}'",
                options=options,
                index=default_idx,
                key=f"column_map_{req_col}"
            )
            
            # Store the mapping
            if req_col == 'model' and selected == "[Auto-generate]":
                column_mapping[req_col] = None  # Signal to auto-generate
            else:
                column_mapping[req_col] = selected
    
    with col2:
        # Map real and pred columns
        for req_col in ['real', 'pred']:
            # Get candidate columns
            candidates = get_candidate_columns(df, req_col)
            
            # Determine default selection
            default_idx = 0
            if candidates and candidates[0] in df.columns:
                default_idx = list(df.columns).index(candidates[0])
            
            # Create selectbox with highlighted candidates
            st.markdown(f"**{req_col}**")
            st.caption(descriptions[req_col])
            
            # Show candidate recommendations if available
            if candidates:
                st.caption(f"🔍 Suggestions: {', '.join(candidates[:3])}")
            
            selected = st.selectbox(
                f"Select column for '{req_col}'",
                options=df.columns,
                index=default_idx,
                key=f"column_map_{req_col}"
            )
            
            # Store the mapping
            column_mapping[req_col] = selected
    
    return column_mapping


def validate_and_transform_data(df: pd.DataFrame, column_mapping: Dict[str, str]) -> Tuple[pd.DataFrame, bool, str]:
    """
    Validate and transform the dataframe based on the column mapping.
    
    Parameters:
    -----------
    df : pd.DataFrame
        Original dataframe
    column_mapping : Dict[str, str]
        Mapping from required column names to actual column names
        
    Returns:
    --------
    Tuple[pd.DataFrame, bool, str]
        Transformed dataframe, success flag, and error message if any
    """
    if df is None or df.empty:
        return None, False, "Empty dataframe"
    
    try:
        # Create a copy to avoid modifying the original
        result_df = df.copy()
        
        # Handle missing model column (auto-generate)
        if column_mapping['model'] is None:
            result_df['model'] = "No model defined"
            column_mapping['model'] = 'model'  # Update mapping to use the new column
        
        # Rename columns according to mapping
        rename_dict = {v: k for k, v in column_mapping.items() if v is not None}
        result_df = result_df.rename(columns=rename_dict)
        
        # Ensure 'real' column contains only 0 and 1
        if 'real' in result_df.columns:
            # Convert boolean or string values to integers
            if result_df['real'].dtype == bool:
                result_df['real'] = result_df['real'].astype(int)
            elif result_df['real'].dtype == object:
                # Try to convert strings like 'true'/'false' or '1'/'0'
                try:
                    result_df['real'] = result_df['real'].map(
                        lambda x: 1 if str(x).lower() in ('1', 'true', 't', 'yes', 'y') else 0
                    )
                except:
                    pass
            
            # Check if values are valid now
            if not result_df['real'].isin([0, 1]).all():
                return result_df, False, "The 'real' column must contain only binary values (0 or 1)."
        
        # Ensure 'pred' column contains values between 0 and 1
        if 'pred' in result_df.columns:
            # Convert to numeric if possible
            if result_df['pred'].dtype == object:
                result_df['pred'] = pd.to_numeric(result_df['pred'], errors='coerce')
            
            # If values are outside [0,1], try to normalize them
            min_val = result_df['pred'].min()
            max_val = result_df['pred'].max()
            
            if min_val < 0 or max_val > 1:
                # Check if normalization makes sense (e.g., not for class labels like 0/1/2)
                if pd.api.types.is_numeric_dtype(result_df['pred']) and max_val > min_val:
                    # Only normalize if range indicates probabilities (e.g., 0-100 for percentages)
                    if (min_val >= 0 and max_val <= 100) or (min_val >= -1 and max_val <= 1):
                        # Scale to [0,1]
                        result_df['pred'] = (result_df['pred'] - min_val) / (max_val - min_val)
                        st.warning(f"'pred' column was automatically scaled from [{min_val:.4f}, {max_val:.4f}] to [0, 1]")
            
            # Check if values are valid now
            if not ((result_df['pred'] >= 0) & (result_df['pred'] <= 1)).all():
                return result_df, False, "The 'pred' column must contain probabilities between 0 and 1."
        
        # Make sure all required columns are in the result
        required_cols = ['patient_id', 'model', 'real', 'pred']
        missing_cols = [col for col in required_cols if col not in result_df.columns]
        
        if missing_cols:
            return result_df, False, f"Missing required columns: {', '.join(missing_cols)}"
        
        # Success!
        return result_df, True, ""
        
    except Exception as e:
        return df, False, f"Error transforming data: {str(e)}"


def flexible_process_uploaded_file(uploaded_file) -> Optional[pd.DataFrame]:
    """
    Process an uploaded file with flexible format detection and column mapping.
    
    Parameters:
    -----------
    uploaded_file : UploadedFile
        The file object from Streamlit's file_uploader
        
    Returns:
    --------
    pd.DataFrame or None
        Processed dataframe or None if processing fails
    """
    if uploaded_file is None:
        # If no file is uploaded but we have previous data, use that
        if 'uploaded_data' in st.session_state:
            return st.session_state['uploaded_data']
        return None
    
    try:
        # Check if it's a new file or if there's no previous data
        current_file_name = uploaded_file.name
        
        # Update the data if:
        # 1. No previous file exists, or
        # 2. The file name is different from the previous one
        if ('current_file' not in st.session_state or 
            st.session_state['current_file'] != current_file_name):
            
            # Detect file format
            file_format = detect_file_format(uploaded_file)
            if file_format == 'unknown':
                st.error("Could not detect file format. Please use CSV, Excel, Pickle, or Parquet files.")
                return None
            
            # Load the dataframe
            df = load_dataframe(uploaded_file, file_format)
            if df is None:
                return None
            
            # Check if required columns exist
            required_cols = ['patient_id', 'model', 'real', 'pred']
            if all(col in df.columns for col in required_cols):
                # Direct match - no need for mapping
                transformed_df, success, error_msg = validate_and_transform_data(
                    df, {col: col for col in required_cols}
                )
                
                if not success:
                    st.error(error_msg)
                    return None
                
                # Store validated data in session state
                st.session_state['uploaded_data'] = transformed_df
                st.session_state['current_file'] = current_file_name
                
                # Show success message
                st.success(f"Classification data file '{current_file_name}' loaded successfully.")
                return transformed_df
            else:
                # Columns don't match - show mapping UI
                st.info(f"File loaded, but columns need to be mapped to the required format.")
                
                # Use a form to group the UI elements
                with st.form("column_mapping_form"):
                    column_mapping = display_column_mapping_ui(df)
                    
                    # Add preview section
                    st.markdown("### Data Preview")
                    st.dataframe(df.head(5), use_container_width=True)
                    
                    # Submit button
                    submit = st.form_submit_button("Apply Column Mapping")
                
                # Process form submission (must be outside the form)
                if submit:
                    transformed_df, success, error_msg = validate_and_transform_data(df, column_mapping)
                    
                    if not success:
                        st.error(error_msg)
                        return None
                    
                    # Store validated data in session state
                    st.session_state['uploaded_data'] = transformed_df
                    st.session_state['current_file'] = current_file_name
                    
                    # Show success message
                    st.success(f"Classification data file '{current_file_name}' loaded and mapped successfully.")
                    return transformed_df
                
                # If we reach here, the form hasn't been submitted yet
                return None
        else:
            # Use the stored data
            return st.session_state['uploaded_data']
            
    except Exception as e:
        st.error(f"Error processing file: {str(e)}")
        st.info("Please check your file format and try again.")
        return None 