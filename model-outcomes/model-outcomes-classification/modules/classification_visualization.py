"""
Classification Visualization Module

This module provides comprehensive visualization functions for binary classification analysis.
It creates interactive plots using Plotly to help data scientists understand model performance
and data patterns.

Main functionalities:
- Confusion matrix heatmaps with performance metrics
- ROC curves with AUC calculations
- Precision-Recall curves with average precision
- Probability distribution histograms by class
- Feature importance bar charts
- Feature comparison box plots by prediction outcomes

Dependencies:
- streamlit: For web app framework
- pandas: For data manipulation  
- numpy: For numerical operations
- plotly: For interactive visualizations
- typing: For type hints

Author: Data Science Team
Created for: Model Outcomes Classification Project
"""

# Standard library imports
import time
from typing import Dict, List, Any, Optional, Tuple

# Third-party imports
import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Local module imports
from modules.classification_metrics import get_roc_curve_data, get_pr_curve_data, get_predicted_labels

def plot_confusion_matrix(
    cm_values: Dict[str, int],
    theme: str = "plotly_white"
    ) -> go.Figure:
    """
    Create an interactive heatmap visualization of the confusion matrix.
    
    Parameters:
    -----------
    cm_values : Dict[str, int]
        Dictionary with TP, TN, FP, FN counts
    theme : str
        Plotly theme template to apply
        
    Returns:
    --------
    fig : plotly.graph_objects.Figure
        Plotly figure with confusion matrix visualization
    """
    # Extract values from the dictionary
    tp = cm_values.get('TP', 0)
    tn = cm_values.get('TN', 0)
    fp = cm_values.get('FP', 0)
    fn = cm_values.get('FN', 0)
    total = cm_values.get('total', tp + tn + fp + fn)
    
    # Generate timestamp for unique id to force Streamlit to recreate the graph
    unique_id = int(time.time() * 1000)
    
    # Calculate percentages for annotations
    tp_pct = (tp / total * 100) if total > 0 else 0
    tn_pct = (tn / total * 100) if total > 0 else 0
    fp_pct = (fp / total * 100) if total > 0 else 0
    fn_pct = (fn / total * 100) if total > 0 else 0
    
    # Create matrix data - arrange as [[TN, FP], [FN, TP]]
    z = [[tn, fp], [fn, tp]]
    
    # Create annotations with performance-aware colors
    annotations = [
        # TN (top-left) - Good performance
        dict(
            x=0, y=0,
            text=f"TN: {tn}<br>({tn_pct:.1f}%)",
            showarrow=False,
            font=dict(color="white", size=18, weight="bold")
        ),
        # FP (top-right) - Bad performance
        dict(
            x=1, y=0,
            text=f"FP: {fp}<br>({fp_pct:.1f}%)",
            showarrow=False,
            font=dict(color="white", size=18, weight="bold")
        ),
        # FN (bottom-left) - Bad performance
        dict(
            x=0, y=1,
            text=f"FN: {fn}<br>({fn_pct:.1f}%)",
            showarrow=False,
            font=dict(color="black", size=18, weight="bold")
        ),
        # TP (bottom-right) - Good performance
        dict(
            x=1, y=1,
            text=f"TP: {tp}<br>({tp_pct:.1f}%)",
            showarrow=False,
            font=dict(color="white", size=18, weight="bold")
        )
    ]
    
    # Performance-semantic colorscale using Plotly's RdYlGn (Red-Yellow-Green) reversed
    # This provides semantic meaning: Red=bad performance, Green=good performance
    max_val = max(tp, tn, fp, fn) if any([tp, tn, fp, fn]) else 1
    
    # Create the heatmap using plotly graph objects
    fig = go.Figure(data=go.Heatmap(
        z=z,
        x=['Predicted 0', 'Predicted 1'],
        y=['Actual 0', 'Actual 1'],
        colorscale='RdYlGn',  # Built-in Plotly scale: Red (bad) to Green (good)
        showscale=True,
        colorbar=dict(
            tickmode="array",
            ticks="outside",
            thickness=15,
            len=1,
            x=1.02,
            ),
        zmin=0,
        zmax=max_val
    ))
    
    # Add annotations to the heatmap
    fig.update_layout(
        annotations=annotations,
        title=dict(
            text="Confusion Matrix",
            font=dict(size=18)
        ),
        xaxis_title=dict(
            # text="Predicted Class",
            font=dict(size=14)
        ),
        yaxis_title=dict(
            # text="Actual Class",
            font=dict(size=14)
        ),
        margin=dict(l=60, r=80, t=80, b=60),  # Increased right margin for colorbar
        height=400,
        width=500,  # Increased width for colorbar with title
        template=theme  # Apply theme here, don't override with explicit colors
    )
    
    # Add grid lines with theme-aware styling
    fig.update_xaxes(showgrid=True, gridwidth=1, gridcolor='rgba(128, 128, 128, 0.2)')
    fig.update_yaxes(showgrid=True, gridwidth=1, gridcolor='rgba(128, 128, 128, 0.2)')
    
    return fig

def plot_roc_curve(
    y_true: np.ndarray,
    y_pred_proba: np.ndarray,
    precalculated_auc: float = None,
    theme: str = "plotly_white"
    ) -> go.Figure:
    """
    Create an interactive ROC curve visualization.
    
    Parameters:
    -----------
    y_true : array-like
        Ground truth binary labels (0 or 1)
    y_pred_proba : array-like
        Predicted probabilities (between 0 and 1)
    precalculated_auc : float, optional
        Pre-calculated AUC value to ensure consistency
    theme : str
        Plotly theme template
        
    Returns:
    --------
    fig : plotly.graph_objects.Figure
        Plotly figure with ROC curve
    """
    # Get ROC curve data
    fpr, tpr, thresholds = get_roc_curve_data(y_true, y_pred_proba)
    
    # Create figure
    fig = go.Figure()
    
    # Check if we have valid curve data
    if len(fpr) > 0 and len(tpr) > 0:
        # Use pre-calculated AUC if provided, otherwise calculate it here
        if precalculated_auc is not None and not np.isnan(precalculated_auc):
            auc = precalculated_auc
        else:
            from sklearn.metrics import roc_auc_score
            try:
                auc = roc_auc_score(y_true, y_pred_proba)
            except:
                auc = float('nan')
            
        # Create hover text with thresholds
        hover_text = []
        for i, threshold in enumerate(thresholds):
            if i < len(fpr):  # Ensure we don't go out of bounds
                hover_text.append(f"Threshold: {threshold:.2f}<br>FPR: {fpr[i]:.3f}<br>TPR: {tpr[i]:.3f}")
        
        # Add ROC curve
        fig.add_trace(go.Scatter(
            x=fpr, y=tpr,
            mode='lines',
            name=f'ROC Curve (AUC = {auc:.4f})',
            line=dict(width=2),
            hovertext=hover_text if len(hover_text) == len(fpr) else None,
            hoverinfo='text'
        ))
        
        # Add diagonal reference line (random classifier)
        fig.add_trace(go.Scatter(
            x=[0, 1], y=[0, 1],
            mode='lines',
            name='Random Classifier (AUC = 0.5)',
            line=dict(width=2, dash='dash'),
            hoverinfo='skip'
        ))
        
        # Add points for specific thresholds if needed
        # (You could highlight common thresholds like 0.5, 0.7, etc.)
        
        # Update layout with increased size
        fig.update_layout(
            title=f"ROC Curve (AUC = {auc:.4f})",
            xaxis_title="False Positive Rate (1 - Specificity)",
            yaxis_title="True Positive Rate (Sensitivity)",
            legend=dict(
                yanchor="bottom",
                y=0.01,
                xanchor="right",
                x=0.99
            ),
            xaxis=dict(
                range=[-0.01, 1.01],
                scaleanchor="y",
                scaleratio=1
            ),
            yaxis=dict(
                range=[-0.01, 1.01]
            ),
            width=800,  # Increased from 600
            height=600,  # Increased from 500
            template=theme,
            hovermode='closest'
        )
    else:
        # If we don't have valid data, show an empty plot with a message
        fig.add_annotation(
            text="Insufficient data for ROC curve<br>(Need both classes present)",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=14)
        )
        
        # Update layout for empty plot with increased size
        fig.update_layout(
            title="ROC Curve",
            xaxis_title="False Positive Rate (1 - Specificity)",
            yaxis_title="True Positive Rate (Sensitivity)",
            xaxis=dict(range=[0, 1]),
            yaxis=dict(range=[0, 1]),
            width=800,  # Increased from 600
            height=600,  # Increased from 500
            template=theme
        )
    
    return fig

def plot_pr_curve(
    y_true: np.ndarray,
    y_pred_proba: np.ndarray,
    precalculated_auc: float = None,
    theme: str = "plotly_white"
) -> go.Figure:
    """
    Create an interactive Precision-Recall curve visualization.
    
    Parameters:
    -----------
    y_true : array-like
        Ground truth binary labels (0 or 1)
    y_pred_proba : array-like
        Predicted probabilities (between 0 and 1)
    precalculated_auc : float, optional
        Pre-calculated PR AUC value to ensure consistency
    theme : str
        Plotly theme template
        
    Returns:
    --------
    fig : plotly.graph_objects.Figure
        Plotly figure with Precision-Recall curve
    """
    # Get PR curve data
    precision, recall, thresholds = get_pr_curve_data(y_true, y_pred_proba)
    
    # Create figure
    fig = go.Figure()
    
    # Check if we have valid curve data
    if len(precision) > 0 and len(recall) > 0:
        # Use pre-calculated AUC if provided, otherwise calculate it here
        if precalculated_auc is not None and not np.isnan(precalculated_auc):
            average_precision = precalculated_auc
        else:
            # Calculate PR AUC (average precision)
            from sklearn.metrics import average_precision_score
            try:
                average_precision = average_precision_score(y_true, y_pred_proba)
            except:
                average_precision = float('nan')
        
        # Calculate the baseline (proportion of positive samples)
        baseline = np.mean(y_true)
        
        # Create hover text with thresholds
        hover_text = []
        if len(thresholds) > 0:
            # precision and recall have 1 more point than thresholds
            for i in range(len(thresholds)):
                hover_text.append(f"Threshold: {thresholds[i]:.2f}<br>Precision: {precision[i]:.3f}<br>Recall: {recall[i]:.3f}")
            # Add the last point separately since it doesn't have a threshold
            hover_text.append(f"Precision: {precision[-1]:.3f}<br>Recall: {recall[-1]:.3f}")
        
        # Add PR curve
        fig.add_trace(go.Scatter(
            x=recall, y=precision,
            mode='lines',
            name=f'PR Curve (AUC = {average_precision:.4f})',
            line=dict(width=2),
            hovertext=hover_text if len(hover_text) == len(recall) else None,
            hoverinfo='text'
        ))
        
        # Add baseline
        fig.add_trace(go.Scatter(
            x=[0, 1], y=[baseline, baseline],
            mode='lines',
            name=f'Baseline ({baseline:.4f})',
            line=dict(width=2, dash='dash'),
            hoverinfo='skip'
        ))
        
        # Update layout with increased size
        fig.update_layout(
            title=f"Precision-Recall Curve (AP = {average_precision:.4f})",
            xaxis_title="Recall",
            yaxis_title="Precision",
            legend=dict(
                yanchor="bottom",
                y=0.01,
                xanchor="right",
                x=0.99
            ),
            xaxis=dict(range=[-0.01, 1.01]),
            yaxis=dict(range=[-0.01, 1.01]),
            width=800,  # Increased from 600
            height=600,  # Increased from 500
            template=theme,
            hovermode='closest'
        )
    else:
        # If we don't have valid data, show an empty plot with a message
        fig.add_annotation(
            text="Insufficient data for Precision-Recall curve<br>(Need both classes present)",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=14)
        )
        
        # Update layout for empty plot with increased size
        fig.update_layout(
            title="Precision-Recall Curve",
            xaxis_title="Recall",
            yaxis_title="Precision",
            xaxis=dict(range=[0, 1]),
            yaxis=dict(range=[0, 1]),
            width=800,  # Increased from 600
            height=600,  # Increased from 500
            template=theme
        )
    
    return fig

def plot_probability_distribution(
    df_filtered: pd.DataFrame,
    theme: str = "plotly_white",
    n_bins: int = 20
) -> go.Figure:
    """
    Create a histogram showing distribution of predicted probabilities grouped by actual class.
    
    Parameters:
    -----------
    df_filtered : DataFrame
        Filtered DataFrame with 'real' and 'pred' columns
    theme : str
        Plotly theme template
    n_bins : int
        Number of bins for the histogram
        
    Returns:
    --------
    fig : plotly.graph_objects.Figure
        Plotly figure with probability distribution histogram
    """
    if df_filtered.empty or 'real' not in df_filtered.columns or 'pred' not in df_filtered.columns:
        # Create empty plot with message if no valid data
        fig = go.Figure()
        fig.add_annotation(
            text="No valid data for probability distribution",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=14)
        )
        fig.update_layout(
            title="Prediction Probability Distribution",
            xaxis_title="Predicted Probability",
            yaxis_title="Count",
            width=700,
            height=400,
            template=theme
        )
        return fig
    
    # Create figure using Plotly Express for easy grouping and coloring
    fig = px.histogram(
        df_filtered,
        x='pred',
        color='real',
        barmode='overlay',
        nbins=n_bins,
        opacity=0.7,
        labels={'pred': 'Predicted Probability', 'real': 'Actual Class'},
        template=theme,
        title='Prediction Probability Distribution by Actual Class',
        width=700,
        height=400
    )
    
    # Add a vertical line for default threshold (0.5)
    fig.add_vline(
        x=0.5,
        line_dash="dash",
        annotation_text="Default Threshold (0.5)",
        annotation_position="top right"
    )
    
    # Update layout
    fig.update_layout(
        xaxis_title="Predicted Probability",
        yaxis_title="Count",
        legend_title="Actual Class",
        bargap=0.1,
        xaxis=dict(range=[0, 1], dtick=0.1)
    )
    
    return fig

def plot_feature_importance(
    importance_df: Optional[pd.DataFrame] = None,
    theme: str = "plotly_white",
    top_n: int = 20
) -> go.Figure:
    """
    Create a horizontal bar chart for feature importance scores.
    
    Parameters:
    -----------
    importance_df : DataFrame or None
        DataFrame with 'feature_name' and 'importance_score' columns
    theme : str
        Plotly theme template
    top_n : int
        Number of top features to display
        
    Returns:
    --------
    fig : plotly.graph_objects.Figure
        Plotly figure with feature importance visualization
    """
    # Create figure
    fig = go.Figure()
    
    # Check if we have valid importance data
    if importance_df is not None and not importance_df.empty and 'feature_name' in importance_df.columns and 'importance_score' in importance_df.columns:
        # Sort by absolute importance (for both positive and negative scores)
        importance_df = importance_df.copy()
        importance_df['abs_importance'] = importance_df['importance_score'].abs()
        sorted_df = importance_df.sort_values(by='abs_importance', ascending=False).head(top_n)
        
        # Reverse order for horizontal bars (top to bottom)
        sorted_df = sorted_df.iloc[::-1]
        
        # Determine if we have negative values
        has_negative = (sorted_df['importance_score'] < 0).any()
        
        # Create bar chart
        fig.add_trace(go.Bar(
            y=sorted_df['feature_name'],
            x=sorted_df['importance_score'],
            orientation='h',
            hovertemplate='<b>%{y}</b><br>Importance: %{x:.4f}<extra></extra>'
        ))
        
        # Update layout
        fig.update_layout(
            title=f"Top {len(sorted_df)} Feature Importance Scores",
            xaxis_title="Importance Score",
            yaxis_title="Feature",
            width=700,
            height=max(400, len(sorted_df) * 20),  # Dynamic height based on number of features
            template=theme,
            margin=dict(l=200, r=40, t=80, b=60)  # Extra left margin for feature names
        )
    else:
        # If we don't have valid data, show an empty plot with a message
        fig.add_annotation(
            text="No feature importance data available<br>Please upload an importance file",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=14)
        )
        
        # Update layout for empty plot
        fig.update_layout(
            title="Feature Importance",
            xaxis_title="Importance Score",
            yaxis_title="Feature",
            width=700,
            height=400,
            template=theme
        )
    
    return fig

def plot_class_distribution(
    df: pd.DataFrame,
    theme: str = "plotly_white"
) -> go.Figure:
    """
    Create a pie chart showing the distribution of positive and negative classes.
    
    Parameters:
    -----------
    df : DataFrame
        DataFrame containing the classification data with 'real' column
    theme : str
        Plotly theme template
        
    Returns:
    --------
    fig : plotly.graph_objects.Figure
        Plotly figure with class distribution pie chart
    """
    # Create figure
    fig = go.Figure()
    
    # Check if we have valid data
    if df is not None and not df.empty and 'real' in df.columns:
        # Compute class counts
        class_counts = df['real'].value_counts().reset_index()
        class_counts.columns = ['Class', 'Count']
        class_counts['Class'] = class_counts['Class'].map({0: 'Negative (0)', 1: 'Positive (1)'})
        
        # Create class distribution pie chart
        fig = px.pie(
            class_counts,
            names='Class',
            values='Count',
            color='Class',
            title='Distribution of Positive and Negative Classes'
        )
        
        # Customize layout and labels
        fig.update_layout(
            height=400,
            width=400,
            margin=dict(l=40, r=40, t=40, b=40),
            title_x=0.05,  # Move title to the left
            title_xanchor='left', # Anchor title text to the left
            title_font_size=14,
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="top",  # Anchor to the top
                y=0.99,         # Position near the top
                xanchor="center", # Anchor to the left
                x=0.01          # Position near the left
            ),
            template=theme
        )
        
        # Add value annotations and percentages inside the pie chart
        fig.update_traces(
            textinfo='percent+value',  # Show percentage and value
            textposition='inside',
            insidetextorientation='radial',
            hovertemplate='<b>%{label}</b><br>Count: %{value}<br>Percentage: %{percent:.1%}<extra></extra>'
        )
    else:
        # If we don't have valid data, show an empty plot with a message
        fig.add_annotation(
            text="No data available for class distribution",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=14)
        )
        
        # Update layout for empty plot
        fig.update_layout(
            title="Class Distribution",
            height=300,
            width=400,
            template=theme
        )
    
    return fig

def plot_prediction_distribution_by_class(
    df: pd.DataFrame,
    threshold: float = 0.5,
    theme: str = "plotly_white"
) -> go.Figure:
    """
    Create a histogram showing prediction probability distribution split by actual class.
    
    Parameters:
    -----------
    df : DataFrame
        DataFrame containing the classification data with 'real' and 'pred' columns
    threshold : float
        Current classification threshold to show as vertical line
    theme : str
        Plotly theme template
        
    Returns:
    --------
    fig : plotly.graph_objects.Figure
        Plotly figure with prediction distribution histogram
    """
    # Create figure
    fig = go.Figure()
    
    # Check if we have valid data
    if df is not None and not df.empty and 'real' in df.columns and 'pred' in df.columns:
        # Create prediction probability histogram
        fig = px.histogram(
            df,
            x='pred',
            color='real',
            nbins=20,
            opacity=0.8,
            barmode='overlay',
            labels={'pred': 'Prediction Probability', 'real': 'Actual Class'},
            title='Distribution of Prediction Probabilities by Class'
        )
        
        # Customize layout
        fig.update_layout(
            height=300,
            width=400,
            margin=dict(l=40, r=40, t=40, b=40),
            xaxis_title="Prediction Probability",
            yaxis_title="Frequency",
            legend_title="Actual Class",
            hovermode='closest',
            title_x=0.5,
            title_font_size=14,
            legend=dict(
                orientation="h",
                yanchor="top",
                y=1.02,
                xanchor="right",
                x=1,
                title_text='',
                itemsizing='constant',
            ),
            yaxis=dict(
            ),
            xaxis=dict(
                tickformat='.2f',
                range=[0, 1],
                dtick=0.1,
            ),
            template=theme
        )
        
        # Add vertical line for threshold
        fig.add_shape(
            type="line",
            x0=threshold, y0=0,
            x1=threshold, y1=1,
            yref="paper",
            line=dict(dash="dash"),
        )
        
        # Add threshold annotation
        fig.add_annotation(
            x=threshold,
            y=1,
            yref="paper",
            text=f"Threshold: {threshold:.2f}",
            showarrow=True,
            arrowhead=1,
            ax=0,
            ay=-30
        )
    else:
        # If we don't have valid data, show an empty plot with a message
        fig.add_annotation(
            text="No data available for prediction distribution",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=14)
        )
        
        # Update layout for empty plot
        fig.update_layout(
            title="Prediction Distribution by Class",
            xaxis_title="Prediction Probability",
            yaxis_title="Frequency",
            height=300,
            width=400,
            template=theme
        )
    
    return fig

def plot_feature_distribution_by_performance(
    df: pd.DataFrame,
    feature: str,
    threshold: float = 0.5,
    theme: str = "plotly_white"
) -> go.Figure:
    """
    Create histogram showing feature distribution by performance category.
    
    This visualization helps identify how feature values differ across
    True Positives, True Negatives, False Positives, and False Negatives.
    
    Args:
        df: DataFrame with model outcomes and features
        feature: Name of the feature to analyze
        threshold: Classification threshold
        theme: Plotly theme for the chart
        
    Returns:
        Plotly figure object
    """
    from modules.classification_metrics import categorize_by_performance
    
    # Get data with performance categories
    df_perf = categorize_by_performance(df, threshold)
    
    # Performance semantic color mapping using intuitive colors
    color_map = {
        'True Positive': '#2E8B57',    # Sea Green - correct positive prediction
        'True Negative': '#4169E1',    # Royal Blue - correct negative prediction  
        'False Positive': '#FF6347',   # Tomato Red - incorrect positive prediction
        'False Negative': '#FFD700'    # Gold - incorrect negative prediction
    }
    
    # Create figure
    fig = go.Figure()
    
    # Add histogram for each performance category
    for category in ['True Positive', 'True Negative', 'False Positive', 'False Negative']:
        category_data = df_perf[df_perf['performance_category'] == category]
        
        if len(category_data) > 0:
            fig.add_trace(go.Histogram(
                x=category_data[feature],
                name=f"{category} (n={len(category_data)})",
                opacity=0.7,
                marker_color=color_map[category],
                nbinsx=20
            ))
    
    # Update layout
    fig.update_layout(
        title=f"Distribution of {feature.title()} by Model Performance",
        xaxis_title=feature.title(),
        yaxis_title="Frequency",
        barmode='overlay',
        template=theme,
        height=500,
        showlegend=True,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )
    
    return fig

def plot_feature_comparison_by_correctness(
    df: pd.DataFrame,
    features: List[str],
    threshold: float = 0.5,
    theme: str = "plotly_white"
) -> go.Figure:
    """
    Create box plot comparing feature distributions between correct and incorrect predictions.
    
    This visualization provides a simplified view of how features behave
    when predictions are correct vs incorrect.
    
    Args:
        df: DataFrame with model outcomes and features
        features: List of features to compare
        threshold: Classification threshold
        theme: Plotly theme for the chart
        
    Returns:
        Plotly figure object
    """
    from modules.classification_metrics import categorize_by_performance
    
    # Get data with performance categories
    df_perf = categorize_by_performance(df, threshold)
    
    # Create subplots
    fig = make_subplots(
        rows=1, cols=len(features),
        subplot_titles=[f.title() for f in features],
        horizontal_spacing=0.05
    )
    
    # Correctness semantic colors: Green for correct, Red for incorrect
    colors = ['#2E8B57', '#FF6347']  # Sea Green for correct, Tomato Red for incorrect
    
    for idx, feature in enumerate(features):
        col_num = idx + 1
        
        # Add box plot for correct predictions
        correct_data = df_perf[df_perf['is_correct'] == True]
        fig.add_trace(
            go.Box(
                y=correct_data[feature],
                name='Correct',
                marker_color=colors[0],
                showlegend=(idx == 0)  # Only show legend for first subplot
            ),
            row=1, col=col_num
        )
        
        # Add box plot for incorrect predictions
        incorrect_data = df_perf[df_perf['is_correct'] == False]
        fig.add_trace(
            go.Box(
                y=incorrect_data[feature],
                name='Incorrect',
                marker_color=colors[1],
                showlegend=(idx == 0)  # Only show legend for first subplot
            ),
            row=1, col=col_num
        )
    
    # Update layout
    fig.update_layout(
        title="Feature Distributions: Correct vs Incorrect Predictions",
        template=theme,
        height=500,
        showlegend=True,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )
    
    return fig

def plot_feature_distribution_by_prediction(
    df: pd.DataFrame,
    feature: str,
    threshold: float = 0.5,
    theme: str = "plotly_white"
) -> go.Figure:
    """
    Create histogram showing feature distribution by predicted class.
    
    This visualization shows how feature values differ between cases
    where the model predicts class 0 vs class 1, as requested by the team.
    
    Args:
        df: DataFrame with model outcomes and features
        feature: Name of the feature to analyze
        threshold: Classification threshold for converting predictions to binary
        theme: Plotly theme for the chart
        
    Returns:
        Plotly figure object
    """
    # Create a copy to avoid modifying original data
    df_analysis = df.copy()
    
    # Convert predictions to binary using threshold
    df_analysis['predicted_class'] = (df_analysis['pred'] >= threshold).astype(int)
    
    # Simple color mapping for predicted classes
    color_map = {
        0: '#4169E1',    # Royal Blue for predicted class 0
        1: '#FF6347'     # Tomato Red for predicted class 1
    }
    
    # Create figure
    fig = go.Figure()
    
    # Add histogram for each predicted class
    for pred_class in [0, 1]:
        class_data = df_analysis[df_analysis['predicted_class'] == pred_class]
        
        if len(class_data) > 0:
            fig.add_trace(go.Histogram(
                x=class_data[feature],
                name=f"Predicted {pred_class} (n={len(class_data)})",
                opacity=0.7,
                marker_color=color_map[pred_class],
                nbinsx=20
            ))
    
    # Update layout
    fig.update_layout(
        title=f"Distribution of {feature.title()} by Predicted Class",
        xaxis_title=feature.title(),
        yaxis_title="Frequency",
        barmode='overlay',
        template=theme,
        height=500,
        showlegend=True,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )
    
    return fig

def plot_feature_comparison_by_prediction(
    df: pd.DataFrame,
    features: List[str],
    threshold: float = 0.5,
    theme: str = "plotly_white"
) -> go.Figure:
    """
    Create box plot comparing feature distributions between predicted classes.
    
    This visualization shows how multiple features behave differently
    when the model predicts class 0 vs class 1.
    
    Args:
        df: DataFrame with model outcomes and features
        features: List of features to compare
        threshold: Classification threshold
        theme: Plotly theme for the chart
        
    Returns:
        Plotly figure object
    """
    # Create a copy and add predicted class
    df_analysis = df.copy()
    df_analysis['predicted_class'] = (df_analysis['pred'] >= threshold).astype(int)
    
    # Create subplots
    fig = make_subplots(
        rows=1, cols=len(features),
        subplot_titles=[f.title() for f in features],
        horizontal_spacing=0.05
    )
    
    # Colors for predicted classes
    colors = ['#4169E1', '#FF6347']  # Royal Blue for 0, Tomato Red for 1
    
    for idx, feature in enumerate(features):
        col_num = idx + 1
        
        # Add box plot for predicted class 0
        class_0_data = df_analysis[df_analysis['predicted_class'] == 0]
        fig.add_trace(
            go.Box(
                y=class_0_data[feature],
                name='Predicted 0',
                marker_color=colors[0],
                showlegend=(idx == 0)  # Only show legend for first subplot
            ),
            row=1, col=col_num
        )
        
        # Add box plot for predicted class 1
        class_1_data = df_analysis[df_analysis['predicted_class'] == 1]
        fig.add_trace(
            go.Box(
                y=class_1_data[feature],
                name='Predicted 1',
                marker_color=colors[1],
                showlegend=(idx == 0)  # Only show legend for first subplot
            ),
            row=1, col=col_num
        )
    
    # Update layout
    fig.update_layout(
        title="Feature Distributions: Predicted Class 0 vs 1",
        template=theme,
        height=500,
        showlegend=True,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )
    
    return fig



def plot_feature_scatterplots_by_prediction(
    df: pd.DataFrame,
    features: List[str],
    comparison_column: str,
    threshold: float = 0.5,
    theme: str = "plotly_white"
) -> go.Figure:
    """
    Create scatter plots grid showing feature values vs comparison variable.
    
    This visualization shows how feature values relate to a continuous comparison 
    variable (e.g., actual SBP values), with points colored by prediction
    correctness. It provides clinical insight into model errors and feature patterns.
    
    Args:
        df: DataFrame with model outcomes and features
        features: List of features to display in grid
        comparison_column: Name of the column to use for X-axis comparison
        threshold: Classification threshold for determining correctness
        theme: Plotly theme for the chart
        
    Returns:
        Plotly figure object with subplots grid
    """
    from modules.classification_metrics import categorize_by_performance
    
    # Get data with performance categories
    df_analysis = categorize_by_performance(df, threshold)
    
    # Colors for correctness - semantic green/red
    color_map = {
        True: '#2E8B57',   # Sea Green for correct predictions
        False: '#FF6347'   # Tomato Red for incorrect predictions
    }
    
    # Create subplots
    n_features = len(features)
    cols = min(3, n_features)  # Max 3 columns
    rows = max(1, (n_features + cols - 1) // cols)  # Calculate rows needed
    
    fig = make_subplots(
        rows=rows, cols=cols,
        subplot_titles=[f.title() for f in features],
        vertical_spacing=0.08,
        horizontal_spacing=0.08
    )
    
    # Add scatter plots for each feature
    for idx, feature in enumerate(features):
        row = idx // cols + 1
        col = idx % cols + 1
        
        # Add scatter plot for each correctness category
        for is_correct in [True, False]:
            correctness_data = df_analysis[df_analysis['is_correct'] == is_correct]
            label = 'Correct' if is_correct else 'Incorrect'
            
            if len(correctness_data) > 0:
                fig.add_trace(
                    go.Scatter(
                        x=correctness_data[comparison_column],  # Comparison variable on X-axis
                        y=correctness_data[feature],  # Feature values on Y-axis
                        mode='markers',
                        name=f'{label} Predictions',
                        marker=dict(
                            color=color_map[is_correct],
                            size=6,
                            opacity=0.6,
                            line=dict(width=0.5, color='white')
                        ),
                        showlegend=(idx == 0),  # Only show legend for first subplot
                        legendgroup=f'correct_{is_correct}',  # Group legends together
                        hovertemplate=(
                            f"<b>{label} Prediction</b><br>"
                            f"{comparison_column.title()}: %{{x}}<br>"
                            f"{feature.title()}: %{{y}}<br>"
                            f"Real Class: %{{customdata[0]}}<br>"
                            f"Predicted Prob: %{{customdata[1]:.3f}}<br>"
                            f"Performance: %{{customdata[2]}}<br>"
                            f"Patient: %{{customdata[3]}}<extra></extra>"
                        ),
                        customdata=correctness_data[['real', 'pred', 'performance_category', 'patient_id']].values
                    ),
                    row=row, col=col
                )
    
    # Update layout
    fig.update_layout(
        title=f"Feature Values vs {comparison_column.title()} by Prediction Correctness",
        template=theme,
        height=max(400, rows * 250),  # Dynamic height based on rows
        showlegend=True,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )
    
    # Update axes labels dynamically
    for idx, feature in enumerate(features):
        row = idx // cols + 1
        col = idx % cols + 1
        fig.update_xaxes(title_text=comparison_column.title(), row=row, col=col)
        fig.update_yaxes(title_text=feature.title(), row=row, col=col)
    
    return fig
 
