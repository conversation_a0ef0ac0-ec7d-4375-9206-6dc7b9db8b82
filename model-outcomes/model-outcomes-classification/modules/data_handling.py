"""
    Utility functions for data processing and visualization in the Model Outcomes | Classification application.
"""

import pandas as pd
import numpy as np
import streamlit as st
from typing import Optional, Dict, List, Any, Tuple
import plotly.io as pio

def validate_theme(theme: str = "plotly_white") -> str:
    """
    Validates the specified Plotly theme.
    
    Parameters:
    -----------
    theme : str
        Plotly theme template name
        
    Returns:
    --------
    theme : str
        Validated theme (default if invalid)
    """
    # Validate theme
    available_templates = list(pio.templates)
    if theme not in available_templates:
        st.warning(f"Theme '{theme}' not available. Using default theme.")
        theme = "plotly_white"  # Default theme
    
    return theme

@st.cache_data
def get_filtered_data(
    df: pd.DataFrame,
    selected_model: Optional[str] = None,
    selected_patients: Optional[List[Any]] = None,
    settings: Optional[Dict[str, Any]] = None
) -> pd.DataFrame:
    """
    Central function to get filtered data based on settings.
    This function is used throughout the app to maintain consistency.
    
    Parameters:
    -----------
    df : DataFrame
        Original DataFrame containing all data
    selected_model : str, optional
        Selected model identifier
        If None, derived from settings
    selected_patients : List[Any], optional
        List of selected patient identifiers
        If None, derived from settings
    settings : dict, optional
        Dictionary containing all settings (used if model/patients not provided)
    
    Returns:
    --------
    filtered_df : DataFrame
        Filtered DataFrame based on specified parameters
    """
    # If settings dictionary provided, extract parameters from it
    if settings is not None:
        if selected_model is None and 'selected_model' in settings:
            selected_model = settings['selected_model']
        if selected_patients is None and 'selected_patients' in settings:
            selected_patients = settings['selected_patients']
    
    # Start with a copy of the full dataset
    filtered_df = df.copy()
    
    # Filter by model if specified
    if selected_model is not None:
        filtered_df = filtered_df[filtered_df['model'] == selected_model]
    
    # Filter by patients only if specified AND non-empty
    # An empty list means "all patients" - no filtering
    if selected_patients and len(selected_patients) > 0:
        # Verificar si patient_id contiene valores alfanuméricos o numéricos
        if filtered_df['patient_id'].dtype == 'object':  # Es string/alfanumérico
            filtered_df = filtered_df[filtered_df['patient_id'].isin(selected_patients)]
        else:  # Es numérico (int o float)
            # Si selected_patients contiene strings pero el DataFrame tiene IDs numéricos,
            # intentar convertir los selected_patients a números
            try:
                numeric_patients = [int(p) if isinstance(p, str) and p.isdigit() else p 
                                   for p in selected_patients]
                filtered_df = filtered_df[filtered_df['patient_id'].isin(numeric_patients)]
            except (ValueError, TypeError):
                # Si la conversión falla, usar la comparación original
                filtered_df = filtered_df[filtered_df['patient_id'].isin(selected_patients)]
    
    # Add index as column for reference if not already present
    if 'point_index' not in filtered_df.columns:
        filtered_df['point_index'] = filtered_df.index
    
    return filtered_df

def process_uploaded_file(uploaded_file) -> Optional[pd.DataFrame]:
    """
    Process an uploaded classification dataset file and return the DataFrame.
    Handles loading, basic validation, and session state management.
    
    Parameters:
    -----------
    uploaded_file : UploadedFile
        The file object from Streamlit's file_uploader
        
    Returns:
    --------
    df : DataFrame or None
        DataFrame containing the data, or None if validation fails
    """
    if uploaded_file is None:
        # If no file is uploaded but we have previous data, use that
        if 'uploaded_data' in st.session_state:
            return st.session_state['uploaded_data']
        return None
        
    try:
        # Check if it's a new file or if there's no previous data
        current_file_name = uploaded_file.name
        
        # Update the data if:
        # 1. No previous file exists, or
        # 2. The file name is different from the previous one
        if ('current_file' not in st.session_state or 
            st.session_state['current_file'] != current_file_name):
            
            # Try to load the new file
            df = pd.read_csv(uploaded_file, sep=None, engine='python')
            
            # Check required columns
            required_cols = ['patient_id', 'model', 'real', 'pred']
            if not all(col in df.columns for col in required_cols):
                st.error(f"CSV file must contain these columns: {', '.join(required_cols)}")
                return None
                
            # Validate real column contains only 0 and 1
            if not df['real'].isin([0, 1]).all():
                st.error("The 'real' column must contain only binary values (0 or 1).")
                return None
                
            # Validate pred column contains values between 0 and 1
            if not ((df['pred'] >= 0) & (df['pred'] <= 1)).all():
                st.error("The 'pred' column must contain probabilities between 0 and 1.")
                return None
                
            # Store in session state
            st.session_state['uploaded_data'] = df
            st.session_state['current_file'] = current_file_name
            
            # Show success message
            st.success(f"Classification data file '{current_file_name}' loaded successfully.")
            return df
        else:
            # Use the stored data
            return st.session_state['uploaded_data']
    except Exception as e:
        st.error(f"Error processing file: {str(e)}")
        st.info("Please check your file format and try again. The required columns are: patient_id, model, real, pred")
        return None

def process_importance_file(uploaded_file) -> Optional[pd.DataFrame]:
    """
    Process an uploaded feature importance file and return the DataFrame.
    
    Parameters:
    -----------
    uploaded_file : UploadedFile
        The file object from Streamlit's file_uploader
        
    Returns:
    --------
    df : DataFrame or None
        DataFrame containing the importance data, or None if validation fails
    """
    if uploaded_file is None:
        # If no file is uploaded but we have previous data, use that
        if 'importance_data' in st.session_state:
            return st.session_state['importance_data']
        return None
        
    try:
        # Check if it's a new file or if there's no previous data
        importance_file_name = uploaded_file.name
        
        # Update the data if:
        # 1. No previous importance data exists, or
        # 2. The file name is different from the previous one
        if ('importance_file' not in st.session_state or 
            st.session_state['importance_file'] != importance_file_name):
            
            # Try to load the new file
            importance_df = pd.read_csv(uploaded_file, sep=None, engine='python')
            
            # Check required columns
            required_cols = ['feature_name', 'importance_score']
            if not all(col in importance_df.columns for col in required_cols):
                st.error(f"Importance CSV file must contain these columns: {', '.join(required_cols)}")
                return None
                
            # Store in session state
            st.session_state['importance_data'] = importance_df
            st.session_state['importance_file'] = importance_file_name
            
            # Show success message
            st.success(f"Feature importance file '{importance_file_name}' loaded successfully.")
            return importance_df
        else:
            # Use the stored importance data
            return st.session_state['importance_data']
    except Exception as e:
        st.error(f"Error processing importance file: {str(e)}")
        st.info("Please check your file format. Required columns: feature_name, importance_score")
        return None 