# Standard library imports
from typing import List, Dict, Any, Optional

# Third-party imports
import numpy as np
import pandas as pd
import streamlit as st

# Local application imports
# Authentication and session management
from modules.auth import initialize_session_state, display_login_page, logout_user
from modules.utils import extend_session_state

# Data processing and metrics
from modules.data_handling import process_uploaded_file, process_importance_file, get_filtered_data
from modules.classification_metrics import calculate_classification_metrics, display_metrics_table
from modules.flexible_loader import flexible_process_uploaded_file

# UI components
from modules.ui_sidebar import display_sidebar_header, setup_sidebar_controls
from modules.ui_components import display_welcome_screen, create_tooltip
from modules.ui_tabs import (
    display_overview_tab,
    display_threshold_tab,
    display_feature_performance_tab,
    display_global_performance_tab,
    display_feature_importance_tab
)

# Set page configuration
st.set_page_config(
    page_title="Model Outcomes | Classification 📊",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Add custom styling for the header and tabs
st.markdown("""
<style>
    /* Header styling */
    .header-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
        padding: 1rem 0;
    }
    .header-title {
        font-size: 2rem;
        font-weight: 600;
    }
    
    /* Custom Radio Button Tabs */
    div.row-widget.stRadio > div {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        background-color: rgba(240, 242, 246, 0.4);
        border-radius: 0.8rem;
        padding: 0.7rem;
        margin-bottom: 2rem;
    }
    
    div.row-widget.stRadio > div > label {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        padding: 1rem 2rem;
        margin: 0 0.5rem;
        font-size: 16px;
        font-weight: 600;
        text-align: center;
        border-radius: 0.8rem;
        transition: all 0.25s ease;
    }
    
    /* Hide the radio button circle */
    div.row-widget.stRadio > div > label > div:first-child {
        display: none;
    }
    
    /* Active and hover states */
    div.row-widget.stRadio > div > label:hover {
        background-color: rgba(220, 222, 226, 0.7);
        transform: translateY(-2px);
    }
    
    div.row-widget.stRadio > div [data-baseweb="radio"] {
        padding: 0.75rem 1.2rem;
    }
    
    /* Selected tab style */
    div.row-widget.stRadio > div [data-testid="stRadioLabel"]:has(input:checked) {
        background-color: #FFFFFF;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        transform: translateY(-2px);
    }
    
    .stPlotlyChart { margin-bottom: 20px; }
</style>
""", unsafe_allow_html=True)

def main():
    """Main function that runs the refactored Streamlit application."""
    # Initialize session state for authentication and app state
    initialize_session_state()
    extend_session_state() # Initializes 'current_file', 'uploaded_data', etc.
    
    # Verify if the user is authenticated
    if not st.session_state.get('is_authenticated', False):
        display_login_page()
        return

    # Page Header
    st.markdown("""
        <div class="header-container">
            <h1 class="header-title">Model Outcomes | Classification 📊 </h1>
        </div>
    """, unsafe_allow_html=True)

    # ===== SIDEBAR AREA =====
    with st.sidebar:
        # Display sidebar header with file uploaders
        # These functions now manage state internally or via session_state
        uploaded_file, importance_file = display_sidebar_header()

    # Check if data file needs processing or if session state holds data
    # Use the flexible loader instead of the original process_uploaded_file
    df = flexible_process_uploaded_file(uploaded_file)
    importance_df = process_importance_file(importance_file)

    # Display welcome screen if no data is loaded (either newly uploaded or from session)
    if df is None:
        display_welcome_screen()
        return

    # ===== MAIN CONTENT AREA =====
    try:
        # Setup sidebar controls (threshold, filters, theme) using the loaded dataframe
        with st.sidebar:
            settings = setup_sidebar_controls(df)

        # Get filtered data based on current settings from sidebar
        filtered_df = get_filtered_data(
            df,
            selected_model=settings.get('selected_model'),
            selected_patients=settings.get('selected_patients')
            # The function `get_filtered_data` can internally access settings if needed,
            # but explicit passing is clearer.
        )

        # Calculate metrics based on filtered data and threshold
        # Handles empty filtered_df internally
        metrics = calculate_classification_metrics(
            filtered_df['real'].values if not filtered_df.empty else np.array([]),
            filtered_df['pred'].values if not filtered_df.empty else np.array([]),
            threshold=settings.get('threshold', 0.5)
        )

        # --- Create Tabs ---
        tab_titles = [
            "📊 Overview & Data",
            "🎯 Threshold Performance",
            "🔬 Feature Performance",
            "📈 Global Performance",
            "🔍 Feature Importance"
        ]
        
        # Initialize active_tab in session_state if it doesn't exist
        if 'active_tab' not in st.session_state:
            st.session_state['active_tab'] = 0
            
        # Use radio buttons styled as tabs
        # Visualization Sections
        
        # Create the tabs as radio buttons (keep their state)
        active_tab = st.radio(
            "",
            tab_titles,
            index=st.session_state['active_tab'],
            horizontal=True
        )
        
        # Update the session_state with the active tab
        st.session_state['active_tab'] = tab_titles.index(active_tab)
        
        # Determine the index of the selected tab to display the corresponding content
        active_index = tab_titles.index(active_tab)
        
        # Display content based on the selected tab
        if active_index == 0:
            display_overview_tab(filtered_df, settings)
        elif active_index == 1:
            display_threshold_tab(filtered_df, metrics, settings)
        elif active_index == 2:
            display_feature_performance_tab(filtered_df, settings)
        elif active_index == 3:
            display_global_performance_tab(filtered_df, metrics, settings)
        elif active_index == 4:
            display_feature_importance_tab(importance_df, settings)

        # --- Display Global Metrics Expander (As per proposal) ---
        with st.expander("📊 Classification Metrics Summary", expanded=True):
            if not metrics:
                 st.info("Metrics require data to be loaded and filtered.")
            else:
                # Display the centralized metrics table
                display_metrics_table(metrics, settings.get('show_counts', True))

    except Exception as e:
        st.error(f"An error occurred: {str(e)}")
        st.exception(e) # Provides traceback for debugging
        st.info("Please check your data files or app configuration.")


# Run the application
if __name__ == "__main__":
    main() 