# Model Outcomes - Classification

Interactive application for evaluating and visualizing binary classification model performance with comprehensive metrics, statistical tests, and feature importance analysis.

## Features

- **Flexible Data Loading**: Supports CSV, Excel, Pickle, Parquet formats with intelligent column mapping and validation.
- **Enhanced Metrics Dashboard**: Centralized metrics summary with grouped layout showing all key performance indicators, including actual vs predicted class distributions with automatic bias detection
- **Interactive Confusion Matrix**: Color-coded matrix showing TP, TN, FP, FN with percentage breakdown
- **Performance Metrics**: Accuracy, Precision, Recall, Specificity, NPV, F1-score, MCC, ROC-AUC, PR-AUC
- **Statistical Testing**: Automatic Chi-square/Fisher's Exact Test with comparative view and interpretation
- **Optimized Visualization Plots**: 
  - Enhanced ROC and Precision-Recall curves with consistent AUC values
  - Interactive tooltips showing threshold values
  - Probability Distribution Analysis with class separation statistics
- **Feature Importance Visualization**: Horizontal bar charts showing feature contribution to predictions
- **Adjustable Threshold**: Interactive slider to find optimal classification threshold with persistent tab navigation
- **Filtering Capabilities**: Filter by model and/or multiple patients
- **Interactive Welcome Screen**: Comprehensive guidance, downloadable data templates, and visual examples
- **Dark/Light Mode**: Theme compatibility for comfortable viewing
- **User Authentication**: Secure login system with personalized welcome

## Screenshots

_Coming soon_

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd model-outcomes
```

2. Create and activate the Conda environment:
```bash
# Create the environment from environment.yml
conda env create -f environment.yml

# Activate the environment
conda activate modeloutcomes
```

3. Navigate to the classification app directory:
```bash
cd model-outcomes-classification
```

## Usage

1. Ensure you're in the correct directory and the environment is activated:
```bash
conda activate modeloutcomes
cd model-outcomes-classification
```

2. Run the application with Streamlit:
```bash
streamlit run classification_app.py
```

3. Access the application in your browser (typically at http://localhost:8501)

4. Use the welcome screen to:
   - Download data templates
   - View format requirements
   - Understand application features

5. Upload your data and explore:
   - Overview & Data tab: Data summary and class distribution
   - Threshold Performance tab: Confusion matrix and metrics at current threshold
   - Global Performance tab: ROC/PR curves and probability distributions
   - Feature Importance tab: Feature ranking and contribution analysis

### Default Login Credentials

- **Username**: admin
- **Password**: admin123

## Data Format

The application accepts various file formats (CSV, Excel, Pickle, Parquet) and provides an interface for column mapping if needed.

### 1. Classification Data (Required)

The application looks for the following data types:
- `patient_id`: Unique identifier for patients/subjects
- `real`: Binary true class labels (0 or 1)
- `pred`: Predicted probabilities (values from 0.0 to 1.0)
- `model`: Name or identifier of the predictive model (can be auto-generated if missing)
- Additional columns are treated as features used by the model

Example (CSV):
```
Patient Identifier,Prediction Model,Actual Outcome,Probability Score,Feature A,Feature B
PT001,model_a,1,0.92,22.1,0
PT001,model_b,1,0.85,22.1,0
PT002,model_a,0,0.35,18.5,1
PT002,model_b,0,0.12,18.5,1
```
*Note: The column names in your file can differ. The application will prompt you to map them.* 

### 2. Feature Importance File (Optional)

CSV with the following columns:
- `feature_name`: The name of the feature (matching column names in your main dataset)
- `importance_score`: Numeric importance value for the feature

Example:
```
feature_name,importance_score
Feature A,0.65
Feature B,0.25
```

## Project Structure

```
model-outcomes-classification/
├── classification_app.py    # Main application file
├── modules/                 # Modular components
│   ├── __init__.py
│   ├── auth.py
│   ├── data_handling.py
│   ├── flexible_loader.py   # Flexible data loading and mapping
│   ├── classification_metrics.py
│   ├── classification_visualization.py
│   ├── ui_tabs.py
│   ├── ui_sidebar.py
│   ├── ui_components.py
│   ├── utils.py
│   └── users.csv
├── tests/                   # Unit tests
│   ├── __init__.py
│   ├── test_modules/
│   └── test_statistical_tests.py
├── static/                  # Static assets (e.g., logo)
│   └── AIO-logo.png
├── data/                    # Sample data files
├── requirements.txt         # Project dependencies
├── CHANGELOG.md             # Version history and changes
└── README.md                # This file
```

## Modular Architecture

The application follows a modular design pattern:

- **Flexible Data Loading**: `flexible_loader.py` handles loading various formats and column mapping.
- **Centralized Calculations**: All metrics and curve data calculations are centralized in `classification_metrics.py` to ensure consistency across the application
- **Visualization Layer**: `classification_visualization.py` handles all plotting functionality
- **UI Organization**: Content is organized into reusable components (`ui_tabs.py`, `ui_sidebar.py`, `ui_components.py`)
- **Data Filtering**: `data_handling.py` manages data filtering after loading.
- **Orchestration**: `classification_app.py` coordinates these modules together

## Metrics Explanation

The application calculates the following performance metrics:

### Basic Metrics
- **Accuracy**: (TP + TN) / (TP + TN + FP + FN) - Overall correctness of all predictions
- **Precision (PPV)**: TP / (TP + FP) - Percentage of positive predictions that are correct
- **Recall (Sensitivity)**: TP / (TP + FN) - Percentage of actual positives that were identified
- **Specificity**: TN / (TN + FP) - Percentage of actual negatives that were identified
- **NPV**: TN / (TN + FN) - Percentage of negative predictions that are correct

### Advanced Metrics
- **F1 Score**: 2 * (Precision * Recall) / (Precision + Recall) - Harmonic mean of precision and recall
- **MCC**: Correlation coefficient between predicted and actual values (-1 to +1)
- **ROC-AUC**: Area under the ROC curve, measuring discrimination ability across thresholds
- **PR-AUC**: Area under the Precision-Recall curve, particularly useful for imbalanced datasets

## Statistical Tests

The application automatically selects between:

- **Chi-square Test**: Used when expected cell frequencies are ≥ 5
- **Fisher's Exact Test**: Used for small sample sizes where expected frequencies < 5

## Feature Importance

Feature importance scores can be obtained from various sources:
- Coefficients from logistic regression
- Feature importance from tree-based models (Random Forest, XGBoost)
- SHAP values
- Permutation importance

## Version History

See [CHANGELOG.md](CHANGELOG.md) for a detailed history of changes and version information.

## License

This project is currently under development for the Artificial Intelligence Orchestrator (AIO) and is not yet licensed for public use.

## Acknowledgments

- This tool was developed as part of the AIO visualization tools project.
- Thanks to the Streamlit team for their amazing framework.
- Special thanks to the scikit-learn project for their comprehensive metrics implementations. 

## Feature Analysis

The **Threshold Analysis** tab includes feature distribution analysis with three perspectives:

1. **By Predicted Class** (default): Compare features between predictions 0 vs 1
2. **By Prediction Correctness**: Analyze features for correct vs incorrect predictions  
3. **By Performance Category**: Detailed analysis across TP, TN, FP, FN categories

Use the radio buttons to switch between analysis types and explore how features behave differently across these groupings. 