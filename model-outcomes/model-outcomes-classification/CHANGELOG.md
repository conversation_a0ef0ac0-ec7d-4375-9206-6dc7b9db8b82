# Changelog

All notable changes to the Model Outcomes | Classification Model Visualizer app will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]
### Added

### Changed

### Fixed

## [classification-v1.2.1] - 2025-01-27
### Changed
- **Threshold Performance Display:** Replaced redundant threshold display with key performance metrics in threshold tab header. Shows Precision, Recall, F1-Score, and ROC-AUC in compact format with 3 decimal precision and "N/A" handling for undefined metrics, improving information density since threshold is already visible in sidebar.

## [classification-v1.2.0] - 2025-01-20
### Added
- **Predicted Distribution Analysis:** Added predicted class distribution display alongside actual distribution in metrics table, providing comprehensive view of model behavior
- **Model Bias Detection:** Implemented automatic bias detection showing percentage difference between predicted and actual class distributions with threshold-based warnings

### Changed
- **Metrics Table Layout:** Expanded from 3-column to 4-column layout to accommodate new predicted distribution section
- **Code Optimization:** Eliminated redundant helper function to avoid code duplication, using existing calculation patterns directly
- **UI Cleanup:** Removed unnecessary tooltips and emojis from bias indicators for cleaner interface

### Fixed

## [1.1.0] - 2025-01-14
### Added
- **Feature Performance Tab:** Created dedicated tab for comprehensive feature analysis capabilities:
  - **Dynamic Scatter Plots:** Added scatter plot functionality with selectable comparison variables for clinical insights
  - **Variable Selector:** Implemented dropdown selector allowing users to choose any numerical column for X-axis comparison (e.g., actual SBP values vs binary classification)
  - **Multi-Feature Analysis:** Enhanced multi-feature comparison with both box plots and scatter plots options
  - **Performance Color Coding:** Semantic green (correct) and red (incorrect) color mapping for prediction accuracy visualization

### Changed
- **Modular Tab Architecture:** Refactored "Threshold & Features" tab into two focused tabs:
  - **"🎯 Threshold Performance":** Simplified to focus exclusively on confusion matrix, statistical tests, and threshold guidance
  - **"🔬 Feature Performance":** Dedicated space for all feature analysis (individual and multi-feature comparisons)
- **Clean Interface Design:** Removed unnecessary tooltips and help text across the application:
  - Eliminated redundant help messages for obvious controls (model selector, patients, chart theme)
  - Removed verbose explanations that added visual clutter
  - Streamlined warning messages to automatic handling where possible
- **Enhanced Scatter Plot Functionality:** Updated `plot_feature_scatterplots_by_prediction()` to accept dynamic comparison columns instead of hardcoded 'real' values
- **Tab Navigation:** Updated application structure to accommodate new 5-tab layout with proper imports and routing

### Fixed
- **Code Cleanup:** Removed deprecated scatter plot functions (`plot_feature_scatterplot_by_prediction`, `plot_feature_scatterplot_by_correctness`, `plot_feature_scatterplot_by_performance`) and associated imports

## [1.0.0] - 2025-04-08

### Added
- **Flexible Data Loader:** Implemented a new intelligent data loader system that significantly improves resilience and user experience:
  - Supports multiple file formats (CSV, Excel, Pickle, Parquet)
  - Intelligently detects and suggests column mappings
  - Provides interactive UI for manual column mapping when needed
  - Auto-corrects common data issues (non-binary values, scaling probabilities)
  - Handles cases where model column is missing by auto-generating it
  - Complete with comprehensive unit tests
- **Statistical Test Comparison:** Enhanced the statistical test framework to provide a comparative view of both Fisher's exact test and Chi-square test when appropriate:
  - Automatically calculates both tests when sample size permits
  - Displays side-by-side comparison with clear recommendations
  - Added detailed interpretation in an expandable section
  - Implemented in both Threshold and Global Performance tabs
  - Integrated into the Classification Metrics Summary for quick reference alongside confusion matrix data
- **Unit Tests for Statistical Tests:** Added comprehensive tests to verify the behavior of the enhanced statistical test functions

### Changed
- **Application File Renaming:** Renamed primary application files to follow standard naming conventions:
  - Renamed main application file to `classification_app.py` in classification-module-visualizator
  - Renamed main application file to `app.py` in model-visualizator
- **Internationalization:** Translated Spanish docstrings to English in `classification_visualization.py` for better code maintainability and international collaboration
- **Metrics Summary Placement:** Moved Classification Metrics Summary expander from above the tabs to the bottom of the main content area to prioritize visualization content.
- **Tab Navigation Upgrade:** Replaced radio buttons with native Streamlit tabs for improved usability and cleaner UI.
- **Download Buttons Standardization:** Unified download button layout across all tabs, placing them consistently at the top of relevant data displays.
- **Navigation Buttons Enhancement:** Improved visibility of navigation controls with larger buttons, increased font size, and subtle hover effects.
- **UI Standardization Across Tabs:** Implemented consistent layout across all tabs with prominent titles (h1) in the left column and contextual information in the right column, creating a coherent visual hierarchy throughout the application.
- **Threshold Tab Layout Redesign:** Restructured "Threshold Performance Analysis" section with a more prominent title (h1 instead of h3) in the left column and contextual information (filters and threshold) in the right column, creating a better visual hierarchy and balance.
- **Performance Curves Prioritization:** Reordered and resized visualization curves in the Global Performance tab to prioritize Precision-Recall curve (left, 60% width) over ROC curve (right, 40% width), reflecting the importance of PR curves for imbalanced classification problems. Removed tooltips for cleaner interface and added visual separation before probability distribution section.
- **Theme-Responsive Visualization:** Removed explicit color definitions across all plots (except confusion matrix) to allow Plotly themes to properly control the appearance, ensuring consistent visual styling when the theme is changed in the sidebar. Confusion matrix retains semantic colors for better interpretability.
- **Confusion Matrix Enhancements:** 
  - Improved color scheme with intuitive progression from red (errors) to blue (correct predictions)
  - Changed from dark colors at extremes and middle to a more logical gradient (dark red → orange → yellow → light blue → dark blue)
  - Added visible color scale bar to assist in matrix interpretation
  - Adjusted figure width to accommodate the color bar
  - Improved color scheme interpretability with clear visual differentiation between prediction categories
- **Metrics Summary Visibility:** Set the Classification Metrics Summary to remain expanded by default, ensuring important metrics are always visible without requiring user interaction to expand the section after each filter change.
- **Extended Theme Selection:** Expanded the chart theme options to include all available Plotly templates: 'ggplot2', 'seaborn', 'simple_white', 'plotly', 'plotly_white', 'plotly_dark', 'presentation', 'xgridoff', 'ygridoff', 'gridon', and 'none', providing users with comprehensive styling options for visualizations.
- **Metrics Summary Enhancement:** Expanded the Classification Metrics Summary with statistical test results in a three-column layout, providing a more comprehensive overview of model performance

### Fixed
- **P-value Display Format:** Fixed an issue where small p-values (< 0.0001) were displayed as "0.0000" in the statistical test tables. Implemented adaptive formatting that uses scientific notation for very small values.
- **Statistical Test Selection Logic:** Fixed an issue in the `calculate_statistical_test` function where Chi-square test would still be calculated even when Fisher's exact test was explicitly requested
- **Session State Access:** Fixed session state access pattern across the application to use dictionary syntax (`st.session_state['key']` instead of `st.session_state.key`) for better compatibility with testing environments.
- **Classification Metrics Edge Cases:** Improved classification metrics calculations for edge cases:
  - Fixed NPV calculation to correctly return NaN when no true negatives are present
  - Enhanced precision, recall and F1 calculation to properly handle single-class datasets (all positives or all negatives)
  - Improved handling of special cases in ROC AUC and PR AUC calculations
- **Metrics Display Consistency:** Ensured consistent handling of undefined metrics (using NaN) for mathematically undefined cases like precision with no positive predictions.
- **Tab Position Preservation:** Fixed issue where adjusting threshold slider would reset to the first tab by using styled radio buttons that maintain state while providing a tab-like interface.
- **Theme Responsiveness:** Fixed issue where visualizations didn't update when changing themes in the sidebar by removing explicit color definitions that were overriding theme settings, ensuring theme changes now apply to all plots consistently.

## [0.5.0-beta] - 2025-04-01

### Added
- **Unit Testing Framework:** Added comprehensive unit test framework with test cases for:
  - Classification metrics calculation functions
  - Data filtering and processing
  - Session state management utilities
  - Created test data fixtures for various test scenarios

### Changed
- **Application Rebranding:** Renamed the application from "Classification Model Visualizer" to "AIO.BI - Classification" to align with corporate branding and better reflect its role within the AIO Business Intelligence ecosystem.

## [0.4.0-beta] - 2025-03-25

### Changed
- **Redesigned Metrics Summary:** Completely redesigned the Classification Metrics Summary display to be more complementary to the tabs. Introduced a card-based layout with metrics grouped by category for easier scanning, and simplified the confusion matrix display to avoid duplication with the Threshold tab.
- **Simplified Metrics Table Implementation:** Replaced complex HTML/CSS-based metrics table with pure Streamlit/pandas implementation for improved code readability and maintainability.
- **Improved Table Display:** Changed from `st.table()` to `st.dataframe()` for automatic theme adaptation, providing better visual consistency with Streamlit's dark/light modes without custom styling.
- **Removed Subjective Threshold Indicators:** Eliminated colored indicators and subjective value interpretations for AUC metrics to avoid presenting arbitrary thresholds as standardized benchmarks.
- **Enhanced Visualization Plots:**
  - Increased ROC and PR curve plot sizes (800×600 pixels, up from 600×500) for better visibility and detail
  - Improved AUC value consistency by passing pre-calculated metrics from `classification_metrics.py` to visualization functions
  - Enhanced hover information on curves to show more precise threshold values
  - Optimized plot margins and sizing to show curves more prominently while maintaining side-by-side layout
  - Standardized plot styling for better visual coherence
- **UI/UX Enhancements:** 
  - Replaced redundant expandable "Class Balance Context" in Overview tab with a cleaner button-triggered expandable explanation
  - Eliminated metrics duplication by removing redundant metrics section from Threshold tab and referring users to the global metrics summary
  - Improved threshold tradeoffs explanation with a more visual, two-column layout highlighting key benefits and drawbacks
  - Moved threshold tradeoffs explanation to a collapsible expander to reduce visual clutter
  - Enhanced Global Performance tab with key metrics at the top, cleaner explanations, and detailed distribution analysis in an expander
  - Optimized AUC metrics layout in Global Performance tab by displaying ROC AUC and PR AUC side by side
  - Simplified metric displays to focus on values and indicators, moving detailed descriptions behind interactive help buttons
  - Removed redundant AUC metrics from Global Performance tab as these values are already displayed in plot titles and metrics summary
- **Enhanced Welcome Screen:**
  - Completely redesigned welcome screen with interactive elements and comprehensive guidance
  - Added downloadable templates for both classification and feature importance data
  - Included visual examples of data formats and detailed explanations of application features
  - Moved `display_welcome_screen()` function to `ui_components.py` module for better code organization and modularity

### Fixed
- **Metrics Display HTML Rendering:** Fixed an issue where raw HTML was being displayed instead of properly rendered content in the metrics summary table. Improved HTML generation with simpler string concatenation and proper CSS classes.
- **ROC Curve AUC Inconsistency:** Fixed a discrepancy where the AUC value displayed in the ROC curve plot differed from the value calculated in the metrics summary by ensuring consistent calculation and display of AUC values throughout the application.
- **Tab Reset on Threshold Change:** Resolved an issue where adjusting the threshold slider would reset the active tab selection. Implemented session state persistence to maintain the user's position in the application during interactive adjustments.

## [0.3.0-beta] - 2025-03-20

### Changed
- **Simplified Patient Filter Logic:** Modified the patient filter to treat an empty selection as "all patients," making the interface more intuitive and reducing code complexity.

## [0.2.1-beta] - 2025-03-15

### Fixed
- **Feature Importance File UI:** Fixed a session state variable mismatch in the sidebar that prevented the feature importance file uploader from collapsing into an expander after a file was loaded.

## [0.2.0-beta] - 2025-03-10

### Added
- **Refactoring & Optimization (v2 Prototype - `classification_app_2.py`)**:
  - Created `classification_app_2.py` as a refactored version of the main application.
  - Introduced `modules/ui_tabs.py` to modularize UI content for each tab (`display_overview_tab`, `display_threshold_tab`, `display_global_performance_tab`, `display_feature_importance_tab`).
  - Centralized helper functions within `modules/ui_tabs.py`:
    - `check_data_availability()`: Standard check for empty DataFrames.
    - `display_active_filters()`: Consistent display of selected model/patients.
    - `calculate_dataset_stats()`: Unified calculation of common dataset statistics.
    - `get_metric_indicator()`: Visual metric indicator with thresholds adjusted for biomedical context (0.7, 0.9).
  - Simplified `main()` function in `classification_app_2.py` by delegating tab content generation to `modules/ui_tabs.py`.
  - Implemented a global "Classification Metrics Summary" expander above the tabs using `st.expander` and `classification_metrics.display_metrics_table()`.
  - Refined UI layouts within tabs for better clarity and reduced nesting (max 2 columns typically).
  - Integrated `ui_components.create_tooltip()` to provide contextual information for metrics (Threshold, Global Performance tabs) without cluttering the main view.
  - Enhanced explanations for metrics and plots, focusing on biomedical relevance and removing subjective categorization (e.g., good/bad).
  - Added specific references/context for class imbalance interpretation.
  - Simplified data sample display in the Overview tab with a row count slider.
  - Streamlined Feature Importance tab layout.
  - Fixed `ImportError` by defining `display_welcome_screen()` directly within `classification_app_2.py`.

### Changed
- Default thresholds for metric indicators (`get_metric_indicator`) adjusted to (0.7, 0.9) for better alignment with potential biomedical use cases.
- Enhanced theme selector in sidebar to display changes in real-time without requiring a page refresh, using reactive widget keys for better user experience.

### Removed
- Removed complex nested column layouts and extensive inline explanations from the main application script (`classification_app.py` logic moved to `classification_app_2.py` and `modules/ui_tabs.py`).
- Removed unused `modules/ui_components.py` module (though `create_tooltip` function logic was integrated into `ui_tabs.py`). *(Self-correction: `create_tooltip` is still in `ui_components.py` and imported)*
- Removed "Show Data Point Counts" checkbox from sidebar as it was redundant - counts are now always displayed by default in the metrics table.

### Fixed
- **Model Selector Logic:** Corrected `TypeError` in `modules/ui_sidebar.py` (`setup_sidebar_controls`) caused by attempting to concatenate a list and an integer when determining the default index for the model selectbox. Ensured index calculation correctly uses the full options list and handles the "All models" case (`None`) appropriately.
- **Patient ID Handling:** (Carried over from previous version)
  - Enhanced `process_uploaded_file` in `modules/data_handling.py` to intelligently detect and handle both numeric and alphanumeric `patient_id` formats (e.g., "PT001"), ensuring correct display and filtering in the sidebar controls.

## [0.1.0-beta] - 2025-03-01

### Added
- **Initial Application Structure:**
  - Created main application file `classification_app.py`.
  - Established `modules/`, `static/`, and `data/` directories.
- **Core Modules:**
  - Adapted `modules/auth.py` for user authentication and classification-specific welcome screen.
  - Adapted `modules/utils.py` for utility functions.
  - Adapted `modules/data_handling.py` for classification data loading, validation (patient_id, real 0/1, pred 0-1), filtering (model, multiple patients), and feature importance file processing.
  - Created `modules/classification_metrics.py` for calculating binary classification metrics (Accuracy, Precision, Recall, Specificity, F1, MCC, ROC-AUC, PR-AUC) and statistical tests (Chi-square/Fisher's).
  - Created `modules/classification_visualization.py` for generating interactive classification plots (Confusion Matrix, ROC Curve, PR Curve, Probability Distribution, Feature Importance).
- **User Interface:**
  - Implemented login page and session management using `auth.py`.
  - Designed main application layout with sidebar and tabbed main area.
  - Created sidebar header with user profile, logo, and data/importance file uploaders.
  - Implemented sidebar controls: classification threshold slider, model selector, multi-patient selector, theme selector, data counts toggle.
  - Created tabs for: Data Overview, Confusion Matrix, Metrics Table, Statistical Test, ROC Curve, PR Curve, Probability Distribution, Feature Importance.
- **Visualizations & Analysis:**
  - Implemented `plot_confusion_matrix` with annotations and threshold dependency.
  - Implemented `plot_roc_curve` and `plot_pr_curve` with AUC calculation and hover details.
  - Implemented `plot_probability_distribution` histogram.
  - Implemented `plot_feature_importance` horizontal bar chart.
  - Integrated metric calculations (`calculate_classification_metrics`) based on filtered data and threshold.
  - Integrated statistical tests (`calculate_statistical_test`).
  - Added detailed explanations and interpretations alongside plots and tables within tabs.
- **Supporting Files:**
  - Created `requirements.txt` with necessary dependencies (streamlit, pandas, numpy, plotly, scikit-learn, scipy).
  - Created `README.md` with project description, features, installation, usage, data format, and structure.
  - Added sample `sample_classification_data.csv` and `sample_importance.csv` files in the `data/` directory.