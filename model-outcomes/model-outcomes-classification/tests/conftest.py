import os
import pytest
import pandas as pd
import numpy as np

@pytest.fixture
def sample_classification_data():
    """
    Fixture providing a small sample of classification data for testing.
    
    Returns:
        pd.DataFrame: Sample classification dataset with patient_id, model, real, pred columns
    """
    return pd.DataFrame({
        'patient_id': ['PT001', 'PT002', 'PT003', 'PT004', 'PT005'],
        'model': ['model_a', 'model_a', 'model_b', 'model_b', 'model_a'],
        'real': [0, 1, 0, 1, 0],
        'pred': [0.1, 0.9, 0.3, 0.6, 0.2],
        'feature_A': [10, 15, 12, 18, 9],
        'feature_B': [1.2, 3.5, 2.1, 4.0, 1.8]
    })

# === ADDITIONAL CLASSIFICATION DATA FIXTURES ===

@pytest.fixture
def classification_data_perfect():
    """Perfect prediction arrays for classification tests."""
    return {
        'y_true': np.array([1, 0, 1, 0]),
        'y_pred_proba': np.array([0.9, 0.1, 0.8, 0.2]),
        'y_pred': np.array([1, 0, 1, 0])
    }

@pytest.fixture
def classification_data_wrong():
    """Wrong prediction arrays for classification tests."""
    return {
        'y_true': np.array([1, 0, 1, 0]),
        'y_pred_proba': np.array([0.1, 0.9, 0.2, 0.8]),
        'y_pred': np.array([0, 1, 0, 1])
    }

@pytest.fixture
def classification_data_mixed():
    """Mixed performance prediction arrays for classification tests."""
    return {
        'y_true': np.array([1, 0, 1, 0, 1, 0, 1, 0]),
        'y_pred_proba': np.array([0.9, 0.1, 0.3, 0.8, 0.7, 0.4, 0.6, 0.2]),
        'y_pred': np.array([1, 0, 0, 1, 1, 0, 1, 0])
    }

@pytest.fixture
def classification_data_single_class_positive():
    """Single class test data (all positives)."""
    return {
        'y_true': np.array([1, 1, 1, 1]),
        'y_pred_proba': np.array([0.9, 0.8, 0.7, 0.6]),
        'y_pred': np.array([1, 1, 1, 1])
    }

@pytest.fixture
def classification_data_single_class_negative():
    """Single class test data (all negatives)."""
    return {
        'y_true': np.array([0, 0, 0, 0]),
        'y_pred_proba': np.array([0.3, 0.2, 0.1, 0.4]),
        'y_pred': np.array([0, 0, 0, 0])
    }

@pytest.fixture
def classification_data_imbalanced():
    """Imbalanced test data (mostly negatives)."""
    return {
        'y_true': np.array([1, 0, 0, 0, 0, 0, 0, 0]),
        'y_pred_proba': np.array([0.9, 0.1, 0.2, 0.3, 0.4, 0.3, 0.2, 0.1]),
        'y_pred': np.array([1, 0, 0, 0, 0, 0, 0, 0])
    }

@pytest.fixture
def test_dataframe_for_filtering():
    """DataFrame for testing data filtering functions."""
    return pd.DataFrame({
        'patient_id': ['PT001', 'PT001', 'PT002', 'PT002', 'PT003', 'PT003'],
        'model': ['model_a', 'model_b', 'model_a', 'model_b', 'model_a', 'model_b'],
        'real': [1, 1, 0, 0, 1, 1],
        'pred': [0.9, 0.8, 0.3, 0.2, 0.7, 0.6],
        'feature_A': [22.1, 22.1, 18.5, 18.5, 20.3, 20.3]
    })

@pytest.fixture
def test_importance_data_valid():
    """Valid feature importance DataFrame for testing."""
    return pd.DataFrame({
        'feature_name': ['feature_A', 'feature_B', 'feature_C'],
        'importance_score': [0.5, 0.3, 0.2]
    })

@pytest.fixture
def test_importance_data_invalid():
    """Invalid feature importance DataFrame (missing importance_score column)."""
    return pd.DataFrame({
        'feature_name': ['feature_A', 'feature_B', 'feature_C'],
        'score': [0.5, 0.3, 0.2]  # Wrong column name
    })

@pytest.fixture
def sample_importance_data():
    """
    Fixture providing a sample of feature importance data for testing.
    
    Returns:
        pd.DataFrame: Sample feature importance dataset
    """
    return pd.DataFrame({
        'feature': ['feature_A', 'feature_B', 'feature_C'],
        'importance': [0.65, 0.25, 0.10]
    })

@pytest.fixture
def perfect_classification_data():
    """
    Fixture providing classification data with perfect predictions.
    
    Returns:
        pd.DataFrame: Dataset where predictions perfectly match actual values
    """
    return pd.DataFrame({
        'patient_id': ['PT001', 'PT002', 'PT003', 'PT004'],
        'model': ['perfect_model'] * 4,
        'real': [0, 1, 0, 1],
        'pred': [0.0, 1.0, 0.0, 1.0]
    })

@pytest.fixture
def single_class_data():
    """
    Fixture providing classification data with only one class (all negatives).
    
    Returns:
        pd.DataFrame: Dataset with only negative samples
    """
    return pd.DataFrame({
        'patient_id': ['PT001', 'PT002', 'PT003'],
        'model': ['single_class_model'] * 3,
        'real': [0, 0, 0],
        'pred': [0.1, 0.2, 0.3]
    })

@pytest.fixture
def asset_path():
    """
    Fixture providing the path to test assets.
    
    Returns:
        str: Path to the test assets directory
    """
    return os.path.join(os.path.dirname(__file__), 'assets')

@pytest.fixture
def binary_arrays_perfect():
    """Perfect prediction arrays for binary classification tests."""
    y_true = np.array([0, 1, 0, 1, 0])
    y_pred_proba = np.array([0.1, 0.9, 0.1, 0.9, 0.1])
    y_pred = np.array([0, 1, 0, 1, 0])
    return y_true, y_pred_proba, y_pred

@pytest.fixture
def binary_arrays_wrong():
    """Wrong prediction arrays for binary classification tests."""
    y_true = np.array([0, 1, 0, 1, 0])
    y_pred_proba = np.array([0.9, 0.1, 0.9, 0.1, 0.9])
    y_pred = np.array([1, 0, 1, 0, 1])
    return y_true, y_pred_proba, y_pred

@pytest.fixture
def binary_arrays_mixed():
    """Mixed performance prediction arrays for binary classification tests."""
    y_true = np.array([0, 1, 0, 1, 0, 1, 0, 1])
    y_pred_proba = np.array([0.1, 0.9, 0.9, 0.1, 0.1, 0.9, 0.6, 0.7])
    y_pred = np.array([0, 1, 1, 0, 0, 1, 1, 1])
    return y_true, y_pred_proba, y_pred

@pytest.fixture
def binary_arrays_imbalanced():
    """Imbalanced class prediction arrays for binary classification tests."""
    y_true = np.array([0, 0, 0, 0, 0, 0, 0, 0, 0, 1])
    y_pred_proba = np.array([0.1, 0.1, 0.2, 0.1, 0.3, 0.1, 0.1, 0.2, 0.1, 0.8])
    y_pred = np.array([0, 0, 0, 0, 0, 0, 0, 0, 0, 1])
    return y_true, y_pred_proba, y_pred 