# Comprehensive Guide to Unit Testing Streamlit Applications

## Overview

This guide provides a structured approach to building effective unit tests for Streamlit applications, with a focus on isolating business logic from UI components. Based on practical experience with the Classification Model Visualizer project, it offers patterns, examples, and best practices tailored to the unique challenges of testing Streamlit apps.

## Project Structure for Testable Streamlit Applications

```
streamlit-project/
├── modules/                  # Functional modules with pure logic
│   ├── __init__.py
│   ├── data_handling.py      # Data processing and validation
│   ├── business_logic.py     # Core calculation and processing logic
│   ├── visualization.py      # Visualization functions
│   └── ui_components.py      # UI rendering functions
├── static/                   # Static assets (images, CSS)
├── app.py                    # Main application entry point
├── tests/                    # Test directory
│   ├── __init__.py
│   ├── conftest.py           # Shared fixtures and configuration
│   ├── pytest.ini            # Pytest configuration
│   ├── assets/               # Test data files
│   │   ├── README.md         # Documentation of test datasets
│   │   └── [test data files] # CSV, JSON, etc.
│   ├── test_modules/         # Tests organized by module
│   │   ├── __init__.py
│   │   ├── test_data_handling.py
│   │   ├── test_business_logic.py
│   │   └── test_visualization.py
│   └── requirements-test.txt # Test-specific dependencies
└── requirements.txt          # Project dependencies
```

## Key Testing Components

### 1. pytest.ini Configuration

```ini
[pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
markers =
    unit: mark a unit test
    integration: mark an integration test
    slow: mark a slow test
```

### 2. conftest.py with Essential Fixtures

```python
import pytest
import pandas as pd
import numpy as np
import os

@pytest.fixture
def sample_data():
    """Fixture providing sample data for testing."""
    return pd.DataFrame({
        'id': [1, 2, 3, 4, 5],
        'feature_1': [10.5, 12.3, 8.7, 15.2, 9.9],
        'target': [0, 1, 0, 1, 0]
    })

@pytest.fixture
def mock_session_state():
    """Fixture providing an empty mock session state."""
    return {}

@pytest.fixture
def asset_path():
    """Path to test asset files."""
    return os.path.join(os.path.dirname(__file__), 'assets')
```

## Testing Patterns for Streamlit Applications

### 1. Pure Logic Testing (No Streamlit Dependency) - pytest Style

```python
import pandas as pd
import numpy as np
import pytest

from modules.classification_metrics import calculate_classification_metrics

def test_calculate_classification_metrics_perfect():
    """Test metrics calculation with perfect predictions using pytest."""
    # Arrange
    y_true = np.array([0, 1, 0, 1, 0])
    y_pred_proba = np.array([0.1, 0.9, 0.3, 0.8, 0.2])
    
    # Act
    metrics = calculate_classification_metrics(y_true, y_pred_proba, threshold=0.5)
    
    # Assert
    assert metrics['accuracy'] == 1.0
    assert metrics['precision'] == 1.0
    assert metrics['recall'] == 1.0
    assert metrics['f1_score'] == 1.0
```

### 2. Testing Functions with Streamlit Session State - pytest Style

```python
from unittest.mock import patch
import streamlit as st
import pandas as pd

from modules.utils import extend_session_state

def test_extend_session_state():
    """Test session state extension using pytest."""
    # Arrange
    mock_state = {}
    
    # Act
    with patch.object(st, 'session_state', mock_state):
        extend_session_state()
    
    # Assert
    assert 'current_file' in mock_state
    assert 'uploaded_data' in mock_state
    assert mock_state['uploaded_data'] is None
    assert mock_state['current_file'] is None
```

### 3. Testing Streamlit UI Components - pytest Style

```python
from unittest.mock import patch, MagicMock
import pytest

from modules.ui_components import display_classification_metrics

@patch('streamlit.columns')
@patch('streamlit.metric')
def test_display_classification_metrics(mock_metric, mock_columns, classification_data_perfect):
    """Test UI metrics display using pytest."""
    # Create mock columns
    mock_cols = [MagicMock(), MagicMock(), MagicMock()]
    mock_columns.return_value = mock_cols
    
    # Test data
    y_true = classification_data_perfect['y_true']
    y_pred_proba = classification_data_perfect['y_pred_proba']
    
    # Execute function
    display_classification_metrics(y_true, y_pred_proba, threshold=0.5)
    
    # Verify columns were created
    mock_columns.assert_called_once_with(3)
    
    # Verify metrics were displayed (should be called for each metric)
    assert mock_metric.call_count >= 3  # At least accuracy, precision, recall
```

### 4. Testing File Upload Processing - pytest Style

```python
from unittest.mock import patch, MagicMock
import pandas as pd
import streamlit as st
import pytest

from modules.data_handling import process_uploaded_file

@patch('pandas.read_csv')
def test_process_uploaded_file_valid(mock_read_csv, test_dataframe_for_filtering):
    """Test valid file upload processing using pytest."""
    # Arrange
    mock_file = MagicMock()
    mock_file.name = "valid_data.csv"
    
    # Use existing test fixture data
    mock_read_csv.return_value = test_dataframe_for_filtering
    
    # Mock session state
    mock_session = {}
    
    # Act
    with patch.object(st, 'session_state', mock_session):
        result = process_uploaded_file(mock_file)
    
    # Assert
    assert result is not None
    assert 'uploaded_data' in mock_session
    assert 'current_file' in mock_session
    pd.testing.assert_frame_equal(test_dataframe_for_filtering, result)
```

### 5. Testing Edge Cases and Error Handling - pytest Style

```python
from unittest.mock import patch
import numpy as np
import pytest

from modules.classification_metrics import calculate_classification_metrics

def test_calculate_metrics_single_class(classification_data_single_class_positive):
    """Test handling of single-class data using pytest."""
    # Arrange - Single class data from fixture
    y_true = classification_data_single_class_positive['y_true']
    y_pred_proba = classification_data_single_class_positive['y_pred_proba']
    
    # Act
    metrics = calculate_classification_metrics(y_true, y_pred_proba, threshold=0.5)
    
    # Assert - Some metrics undefined for single class
    assert np.isnan(metrics['precision'])  # Precision undefined
    assert np.isnan(metrics['roc_auc'])   # ROC-AUC undefined for single class

@patch('streamlit.error')
def test_data_handling_error(mock_error, test_dataframe_for_filtering):
    """Test that errors are properly displayed to the user."""
    # Create invalid data (missing required column)
    invalid_df = test_dataframe_for_filtering.drop(columns=['real'])
    mock_file = MagicMock()
    mock_file.name = "invalid_data.csv"
    
    with patch('pandas.read_csv', return_value=invalid_df):
        with patch.object(st, 'session_state', {}):
            result = process_uploaded_file(mock_file)
    
    # Verify st.error was called and function returned None
    mock_error.assert_called_once()
    assert result is None
```

## Advanced Testing Techniques

### 1. Parameterized Tests for Multiple Scenarios

```python
import pytest
from modules.business_logic import get_predicted_labels

@pytest.mark.parametrize("threshold,probabilities,expected", [
    (0.5, [0.3, 0.7, 0.4, 0.8], [0, 1, 0, 1]),
    (0.7, [0.3, 0.7, 0.4, 0.8], [0, 1, 0, 1]),
    (0.8, [0.3, 0.7, 0.4, 0.8], [0, 0, 0, 1]),
])
def test_predicted_labels_with_different_thresholds(threshold, probabilities, expected):
    """Test label prediction with different thresholds."""
    result = get_predicted_labels(np.array(probabilities), threshold)
    np.testing.assert_array_equal(result, np.array(expected))
```

### 2. Testing Data Filtering and Processing - pytest Style

```python
def test_filter_data_by_model_and_metric(test_dataframe_for_filtering):
    """Test data filtering functionality using pytest."""
    # Act - Filter by model
    filtered_model = filter_data_by_model_and_metric(
        test_dataframe_for_filtering, 
        model='model_a'
    )
    
    # Assert
    assert len(filtered_model) == 3  # Expected count for model_a
    assert (filtered_model['model'] == 'model_a').all()
    
    # Act - Filter by model and metric
    filtered_specific = filter_data_by_model_and_metric(
        test_dataframe_for_filtering, 
        model='model_a',
        metric='accuracy'
    )
    
    # Assert - Should have fewer rows when both filters applied
    assert len(filtered_specific) <= len(filtered_model)
    assert (filtered_specific['model'] == 'model_a').all()
```

### 3. Testing Visualization Functions - pytest Style

```python
@patch('modules.visualization.plot_confusion_matrix')
def test_visualization_function(mock_plot, test_dataframe_for_filtering):
    """Test that visualization function calls plotting library correctly using pytest."""
    # Arrange - Use fixture data
    df = test_dataframe_for_filtering
    
    # Act - Call a real visualization function from our modules
    display_confusion_matrix_visualization(df, model='model_a')
    
    # Assert
    mock_plot.assert_called_once()
    call_args = mock_plot.call_args[0]
    
    # Verify correct data was passed (y_true and y_pred arrays)
    assert len(call_args) >= 2  # Should have at least y_true and y_pred
    assert isinstance(call_args[0], np.ndarray)  # y_true should be numpy array
    assert isinstance(call_args[1], np.ndarray)  # y_pred should be numpy array
```

## Best Practices for Streamlit Application Testing

1. **Separation of Concerns**
   - Extract pure business logic from Streamlit UI code
   - Create separate modules for data processing, analysis, and visualization
   - Minimize direct Streamlit dependencies in core functions

2. **Session State Management**
   - Use dictionary access syntax (`session_state['key']`) instead of attribute access (`session_state.key`)
   - Mock session_state when testing functions that use it
   - Test both empty and pre-populated session states

3. **Test Data Strategy**
   - Create dedicated test datasets for different scenarios
   - Document test data characteristics and purpose
   - Consider both normal cases and edge cases (single class, empty data, etc.)

4. **Mocking Strategy**
   - Mock `st.` functions that render UI components
   - Use `patch.object(st, 'session_state', mock_dict)` for session state
   - Mock file uploads with `MagicMock()` objects

5. **Edge Case Testing**
   - Test with empty datasets
   - Test with single class datasets
   - Test with invalid or out-of-range values
   - Test with missing required columns

6. **Streamlit-Specific Considerations**
   - Handle `ScriptRunContext` warnings during testing (can be ignored)
   - Use pytest's warning filter to suppress Streamlit warnings
   - Remember that some Streamlit functions may not work outside of a Streamlit context

## Example Test Suite Structure for a Streamlit Data Visualization App

```
tests/
├── test_modules/
│   ├── test_data_handling.py
│   │   ├── test_process_uploaded_file()
│   │   ├── test_get_filtered_data()
│   │   └── test_validate_data_format()
│   ├── test_analytics.py
│   │   ├── test_calculate_metrics()
│   │   ├── test_handle_edge_cases()
│   │   └── test_statistical_calculations()
│   └── test_visualization.py
│       ├── test_generate_plots()
│       ├── test_format_plot_data()
│       └── test_theme_application()
├── assets/
│   ├── valid_data.csv
│   ├── invalid_data.csv
│   └── edge_case_data.csv
├── conftest.py
└── pytest.ini
```

## Debugging Streamlit Test Issues

1. **Missing ScriptRunContext Warnings**
   - These can be safely ignored in test environment
   - Add to pytest.ini: `ignore::RuntimeWarning:streamlit.*`

2. **Session State Issues**
   - Use dictionary access syntax consistently
   - Ensure mocks are properly initialized
   - Verify patch is applied at correct level

3. **UI Component Testing**
   - Use return_value patterns for context managers
   - Verify only the call arguments that matter
   - Don't try to test visual appearance, only function calls

4. **Mocking External Libraries**
   - Use patch at the import location, not usage location
   - Consider side effects for complex behaviors
   - Set up multi-call responses with `side_effect = [result1, result2]`

## Real World Example: Testing a Data Filter Function

```python
# In modules/data_handling.py
def get_filtered_data(df, selected_model=None, selected_patients=None):
    """Filter DataFrame by model and/or patients."""
    filtered_df = df.copy()
    
    if selected_model:
        filtered_df = filtered_df[filtered_df['model'] == selected_model]
        
    if selected_patients and len(selected_patients) > 0:
        filtered_df = filtered_df[filtered_df['patient_id'].isin(selected_patients)]
    
    return filtered_df

# In tests/test_modules/test_data_handling.py (pytest style)
def test_filter_data_combined(test_dataframe_for_filtering):
    """Test filtering by multiple criteria using pytest."""
    # Arrange - Use fixture data
    df = test_dataframe_for_filtering
    
    # Act - Combined filtering
    filtered_df = filter_data_by_model_and_metric(
        df, 
        model='model_a'
    )
    
    # Assert with pytest assertions
    assert len(filtered_df) >= 0  # Should have some results
    assert (filtered_df['model'] == 'model_a').all()
    
    # Verify columns are preserved
    expected_columns = ['patient_id', 'model', 'real', 'pred']
    for col in expected_columns:
        assert col in filtered_df.columns
```

## Common Testing Patterns and Design Principles

1. **Arrange-Act-Assert (AAA)**
   - Clearly separate the setup, execution, and verification phases
   - Improves test readability and maintenance

2. **Test Isolation with pytest**
   - Each test should be independent of other tests
   - Use pytest fixtures to create a clean environment

3. **Dependency Injection via pytest Fixtures**
   - Use @pytest.fixture to provide test data and dependencies
   - Makes tests more maintainable and reduces duplication
   - Centralize test data in conftest.py for reuse across modules

4. **Single Responsibility Principle**
   - Each test should verify one specific behavior
   - If a test is checking multiple things, split it into separate tests

5. **Test Data Transparency**
   - Make test data explicit and visible
   - Document the purpose and characteristics of test datasets

6. **Regression Test Coverage**
   - Write tests for bugs once they're fixed
   - Prevents the same issues from recurring

## Lessons from the Classification Visualizer Project

1. **Session State Dictionary Access**
   - Always use `session_state['key']` instead of `session_state.key`
   - Makes code more compatible with testing environments

2. **Edge Case Handling**
   - Identify and test critical edge cases like:
     - Single class datasets where metrics like AUC are undefined
     - Empty datasets or empty filtered results
     - Invalid data values (out of range, wrong type)

3. **Metrics Calculation Consistency**
   - Centralize calculations in a single place
   - Pass pre-calculated values rather than recalculating

4. **Data Validation**
   - Test validation logic thoroughly
   - Check handling of missing columns, invalid types, out-of-range values

5. **Error Handling and User Feedback**
   - Verify that errors are properly displayed to users
   - Test error conditions and recovery mechanisms

## Framework Migration Complete

**This guide has been fully updated to reflect the pytest migration** that was implemented across the classification app. All examples now use:
- **pytest function structure** instead of unittest classes
- **@pytest.fixture** instead of setUp() methods
- **assert statements** instead of unittest assertion methods
- **Centralized fixtures** in conftest.py for shared test data

## Conclusion

Effective testing of Streamlit applications requires a systematic approach that isolates business logic from UI components. By following the patterns and practices in this guide, you can create maintainable, robust test suites that enhance the reliability of your Streamlit applications while facilitating continued development and refactoring.

**Updated Best Practices with pytest**:
- Separate core logic from UI code
- Use pytest fixtures for test data management
- Use appropriate mocking techniques with @patch decorators
- Test edge cases thoroughly with descriptive function names
- Structure tests logically with conftest.py for shared fixtures
- Document test data and fixtures with clear docstrings
- Use assert statements for clear, readable test assertions

With these **standardized pytest practices** in place across both classification and regression apps, your Streamlit applications will be more robust, maintainable, and ready for future enhancements. 