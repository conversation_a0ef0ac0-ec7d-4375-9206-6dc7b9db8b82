# Testing Documentation for Model Outcomes | Classification

## Overview

This directory contains unit tests for the Model Outcomes | Classification application. The testing structure follows a modular approach to match the architecture of the application, with a focus on testing core logic independently from Streamlit UI components.

## Testing Architecture

The test structure is organized following these principles:

- **Separation of Concerns**: Tests are separated by module functionality
- **Test Data Isolation**: Test data is kept separate from test logic
- **Configuration Centralization**: Common configurations and fixtures are centralized
- **Hierarchical Organization**: Tests are organized in a logical directory structure

```
tests/
├── __init__.py              # Makes the directory a proper Python package
├── assets/                  # Test data files
│   ├── README.md            # Documentation of test datasets
│   ├── valid_classification_data.csv
│   ├── imbalanced_classification_data.csv
│   ├── invalid_classification_data.csv
│   ├── outofrange_classification_data.csv
│   └── valid_feature_importance.csv
├── conftest.py              # Shared fixtures across all tests
├── pytest.ini               # Pytest configuration
├── requirements-test.txt    # Test-specific dependencies
├── test_modules/            # Tests organized by application module
│   ├── __init__.py
│   ├── test_classification_metrics.py
│   ├── test_data_handling.py
│   └── test_utils.py
└── README.md                # This documentation file
```

## Key Components

### 1. pytest.ini

The `pytest.ini` file is a configuration file that controls pytest behavior globally:

```ini
[pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::FutureWarning
markers =
    unit: mark a test as a unit test
    integration: mark a test as an integration test
    slow: mark a test as slow
```

This configuration:
- Defines where pytest should look for tests (`testpaths`)
- Sets naming conventions for test files, classes, and functions
- Configures warning suppression to reduce noise in test output
- Defines custom markers for categorizing tests

### 2. conftest.py

The `conftest.py` file contains shared fixtures that can be used across multiple test files:

```python
@pytest.fixture
def sample_classification_data():
    """
    Fixture providing a small sample of classification data for testing.
    """
    return pd.DataFrame({
        'patient_id': ['PT001', 'PT002', 'PT003', 'PT004', 'PT005'],
        'model': ['model_a', 'model_a', 'model_b', 'model_b', 'model_a'],
        'real': [0, 1, 0, 1, 0],
        'pred': [0.1, 0.9, 0.3, 0.6, 0.2],
        'feature_A': [10, 15, 12, 18, 9],
        'feature_B': [1.2, 3.5, 2.1, 4.0, 1.8]
    })
```

Fixtures provide:
- **Reusable test data** (e.g., DataFrames, classification arrays)
- **Test case scenarios** (perfect predictions, single class, imbalanced data)
- **Utility functions** for testing (paths to test assets, etc.)

### 3. Test Modules

Each test module corresponds to an application module and contains related test functions using **pytest**:

```python
def test_calculate_confusion_matrix_values_perfect(classification_data_perfect):
    """Test confusion matrix calculation with perfect predictions."""
    # Convert probabilities to labels
    y_pred_labels = get_predicted_labels(classification_data_perfect['y_pred_proba'])
    
    # Calculate confusion matrix values
    cm_values = calculate_confusion_matrix_values(classification_data_perfect['y_true'], y_pred_labels)
    
    # Check expected values for perfect prediction
    assert cm_values['TP'] == 2  # True positives
    assert cm_values['TN'] == 2  # True negatives
    assert cm_values['FP'] == 0  # False positives
    assert cm_values['FN'] == 0  # False negatives
```

## Design Patterns

### 1. Arrange-Act-Assert (AAA) with pytest

Tests follow the AAA pattern using **pytest** functions and fixtures:

```python
def test_get_predicted_labels(classification_data_mixed):
    """Test conversion of probabilities to binary labels based on threshold."""
    # Arrange (via fixture)
    y_pred_proba = classification_data_mixed['y_pred_proba']
    
    # Act
    pred_labels = get_predicted_labels(y_pred_proba)
    expected_labels = np.array([1, 0, 0, 1, 1, 0, 1, 0])
    
    # Assert
    np.testing.assert_array_equal(pred_labels, expected_labels)
```

### 2. Mocking with pytest

The tests extensively use mocking to isolate components and simulate external dependencies:

```python
@patch('streamlit.error')
@patch('pandas.read_csv')
def test_process_uploaded_file_missing_columns(mock_read_csv, mock_error, test_dataframe_for_filtering):
    """Test processing a file with missing required columns."""
    # Create a DataFrame missing the 'real' column
    invalid_df = test_dataframe_for_filtering.drop(columns=['real'])
    
    # Mock the file and read_csv to return the invalid DataFrame
    mock_file = MagicMock()
    mock_file.name = "invalid_data.csv"
    mock_read_csv.return_value = invalid_df
    
    # Mock session_state
    mock_session_state = {}
    with patch.object(st, 'session_state', mock_session_state):
        result_df = process_uploaded_file(mock_file)
        
        # Should show error and return None
        mock_error.assert_called_once()
        assert result_df is None
```

Mocking is particularly important for Streamlit applications to:
- Isolate from Streamlit's UI components
- Simulate user interactions
- Test error handling

### 3. Edge Case Testing with pytest

Tests specifically target edge cases to ensure robust handling of exceptional situations:

```python
def test_calculate_classification_metrics_single_class(
    classification_data_single_class_positive, 
    classification_data_single_class_negative
):
    """Test metrics calculation with only one class present."""
    # Test with all positives
    metrics_pos = calculate_classification_metrics(
        classification_data_single_class_positive['y_true'], 
        classification_data_single_class_positive['y_pred_proba'],
        threshold=0.5
    )
    
    # Some metrics should be NaN when only one class is present
    assert np.isnan(metrics_pos['precision'])  # All predictions are positive
    assert np.isnan(metrics_pos['recall'])  # Recall undefined with single class
    assert np.isnan(metrics_pos['roc_auc'])  # ROC-AUC undefined for single class
```

## Testing Strategies for Streamlit Applications

### 1. Session State Handling with pytest

Streamlit applications heavily rely on `session_state` for persistence. Our tests mock this object:

```python
def test_extend_session_state():
    """Test session state extension with new keys."""
    # Mock an empty session_state
    mock_session_state = {}
    with patch.object(st, 'session_state', mock_session_state):
        extend_session_state()
        
        # Verify session state was updated correctly
        assert 'current_file' in mock_session_state
        assert mock_session_state['current_file'] is None
        assert 'uploaded_data' in mock_session_state
        assert mock_session_state['uploaded_data'] is None
```

### 2. UI Component Separation

The application separates business logic from UI rendering, making it easier to test:

- **Pure Functions**: Most calculation logic is in pure functions that don't depend on Streamlit
- **UI Functions**: UI functions primarily call pure functions and render results

### 3. Streamlit Component Mocking with pytest

When testing functions that directly use Streamlit components, we mock those components:

```python
@patch('streamlit.error')
def test_process_uploaded_file_none(mock_error, test_dataframe_for_filtering):
    """Test processing when no file is uploaded but session has data."""
    # Set up session state with existing data
    mock_session_state = {
        'uploaded_data': test_dataframe_for_filtering,
        'current_file': 'existing_file.csv'
    }
    
    # Mock session_state to have existing data
    with patch.object(st, 'session_state', mock_session_state):
        result_df = process_uploaded_file(None)
        
        # Should return the existing data
        assert result_df is test_dataframe_for_filtering
        mock_error.assert_not_called()
```

## Test Data Strategy

The test data strategy uses a combination of:

1. **In-memory test data**: Created within test functions or fixtures
2. **Sample CSV files**: Stored in the `assets/` directory for file processing tests
3. **Edge case generators**: Functions that create specific edge case datasets

Each test dataset serves a specific purpose:
- `valid_classification_data.csv`: Balanced dataset with correct structure
- `imbalanced_classification_data.csv`: Highly skewed class distribution 
- `invalid_classification_data.csv`: Missing required columns
- `outofrange_classification_data.csv`: Contains values outside valid ranges

## Running Tests

To run the tests:

```bash
# Run all tests
pytest

# Run tests with output
pytest -v

# Run tests for a specific module
pytest tests/test_modules/test_classification_metrics.py

# Run tests and generate a coverage report
pytest --cov=modules tests/
```

## Key Testing Principles Applied

1. **Single Responsibility**: Each test verifies one specific behavior
2. **Independence**: Tests do not depend on each other
3. **Repeatability**: Tests produce consistent results each time they run
4. **Self-validation**: Tests automatically determine if they pass or fail
5. **Isolation**: Tests run independently of external systems

## Best Practices for Streamlit Application Testing

1. **Separate Core Logic**: Extract core logic into pure functions that don't depend on Streamlit
2. **Mock Streamlit Functions**: Use `unittest.mock` to simulate Streamlit components
3. **Test Session State Manipulation**: Verify correct updates to `session_state`
4. **Parameterize Tests**: Use pytest's parameterization for testing multiple scenarios
5. **Handle Context-Dependent Functions**: Use context managers for functions that depend on Streamlit's context

## Framework Migration Complete

This documentation reflects the **complete migration from unittest to pytest** that was implemented across the entire classification app test suite. All 45 tests now use:

- **pytest function structure** instead of unittest classes
- **@pytest.fixture** instead of setUp() methods  
- **assert statements** instead of unittest assertion methods
- **Centralized fixtures** in conftest.py for shared test data
- **Consistent patterns** aligned with the regression app testing approach

## Common Testing Patterns

### Function with Streamlit Dependency (pytest style)

```python
def test_function_with_streamlit_dependency():
    """Test function that depends on Streamlit session state."""
    # Mock session_state with pytest fixture or patch
    mock_session_state = {'theme': 'dark'}
    with patch.object(st, 'session_state', mock_session_state):
        result = function_that_uses_streamlit()
        # Assert expected behavior
        assert result == expected_value
```

### Data Processing Tests (pytest with fixtures)

```python
def test_data_processing(test_dataframe_for_filtering):
    """Test data processing with pytest fixture."""
    # 1. Prepare test data (via fixture)
    test_data = test_dataframe_for_filtering
    
    # 2. Process the data
    result = filter_data_by_model_and_metric(test_data, model='model_a')
    
    # 3. Verify the result
    expected_shape = (3, 6)  # Expected filtered data shape
    assert result.shape == expected_shape
    assert all(result['model'] == 'model_a')
```

### Visualization Function Tests (pytest with mocking)

```python
@patch('modules.visualization.plot_confusion_matrix')
def test_display_confusion_matrix_visualization(mock_plot, test_dataframe_for_filtering):
    """Test visualization function with pytest and mocking."""
    # Prepare test data
    data = test_dataframe_for_filtering
    
    # Call the visualization function
    display_confusion_matrix_visualization(data, model='model_a')
    
    # Verify plot_function was called with correct arguments
    mock_plot.assert_called_once()
    call_args = mock_plot.call_args[0]
    assert len(call_args) >= 2  # Should have y_true and y_pred
```

## Extending the Test Suite

When adding new functionality to the application:

1. Create test functions for new core logic in the appropriate test file
2. Create new fixtures in `conftest.py` if needed for common test data
3. Use the existing test patterns and naming conventions
4. Ensure proper mocking of Streamlit UI elements
5. Test edge cases and normal usage patterns

## References

- [Pytest Documentation](https://docs.pytest.org/)
- [Streamlit Testing Guide](https://docs.streamlit.io/knowledge-base/tutorials/testing)
- [unittest.mock Documentation](https://docs.python.org/3/library/unittest.mock.html)
- [Software Testing Patterns](https://martinfowler.com/articles/practical-test-pyramid.html) 