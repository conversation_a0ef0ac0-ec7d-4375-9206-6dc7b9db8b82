"""
Unit tests for the classification_metrics module.
"""

import pytest
import numpy as np
import pandas as pd
from unittest.mock import patch, MagicMock

from modules.classification_metrics import (
    get_predicted_labels,
    calculate_confusion_matrix_values,
    calculate_derived_metrics,
    calculate_classification_metrics
)


def test_get_predicted_labels(classification_data_mixed):
    """Test conversion of probabilities to binary labels based on threshold."""
    # Test with default threshold (0.5)
    pred_labels = get_predicted_labels(classification_data_mixed['y_pred_proba'])
    expected_labels = np.array([1, 0, 0, 1, 1, 0, 1, 0])
    np.testing.assert_array_equal(pred_labels, expected_labels)
    
    # Test with custom threshold (0.7)
    pred_labels = get_predicted_labels(classification_data_mixed['y_pred_proba'], threshold=0.7)
    expected_labels = np.array([1, 0, 0, 1, 1, 0, 0, 0])
    np.testing.assert_array_equal(pred_labels, expected_labels)


def test_calculate_confusion_matrix_values_perfect(classification_data_perfect):
    """Test confusion matrix calculation with perfect predictions."""
    # Convert probabilities to labels
    y_pred_labels = get_predicted_labels(classification_data_perfect['y_pred_proba'])
    
    # Calculate confusion matrix values
    cm_values = calculate_confusion_matrix_values(classification_data_perfect['y_true'], y_pred_labels)
    
    # Check expected values for perfect prediction
    assert cm_values['TP'] == 2  # True positives
    assert cm_values['TN'] == 2  # True negatives
    assert cm_values['FP'] == 0  # False positives
    assert cm_values['FN'] == 0  # False negatives
    assert cm_values['total'] == 4  # Total samples


def test_calculate_confusion_matrix_values_wrong(classification_data_wrong):
    """Test confusion matrix calculation with completely wrong predictions."""
    # Convert probabilities to labels
    y_pred_labels = get_predicted_labels(classification_data_wrong['y_pred_proba'])
    
    # Calculate confusion matrix values
    cm_values = calculate_confusion_matrix_values(classification_data_wrong['y_true'], y_pred_labels)
    
    # Check expected values for wrong prediction
    assert cm_values['TP'] == 0  # True positives
    assert cm_values['TN'] == 0  # True negatives
    assert cm_values['FP'] == 2  # False positives
    assert cm_values['FN'] == 2  # False negatives
    assert cm_values['total'] == 4  # Total samples


def test_calculate_derived_metrics():
    """Test derived metrics calculation from confusion matrix values."""
    # Create test confusion matrix values
    cm_values = {'TP': 8, 'TN': 12, 'FP': 3, 'FN': 2, 'total': 25}
    
    # Calculate derived metrics
    metrics = calculate_derived_metrics(cm_values)
    
    # Check expected values
    assert abs(metrics['specificity'] - 12/15) < 1e-6  # TN / (TN + FP)
    assert abs(metrics['npv'] - 12/14) < 1e-6  # TN / (TN + FN)


def test_calculate_derived_metrics_edge_cases():
    """Test derived metrics calculation with edge cases (zero denominators)."""
    # Edge case 1: No negatives (specificity undefined)
    cm_values = {'TP': 5, 'TN': 0, 'FP': 0, 'FN': 2, 'total': 7}
    metrics = calculate_derived_metrics(cm_values)
    assert np.isnan(metrics['specificity'])
    assert np.isnan(metrics['npv'])
    
    # Edge case 2: No false negatives (NPV = 1.0)
    cm_values = {'TP': 5, 'TN': 3, 'FP': 2, 'FN': 0, 'total': 10}
    metrics = calculate_derived_metrics(cm_values)
    assert abs(metrics['specificity'] - 3/5) < 1e-6
    assert abs(metrics['npv'] - 1.0) < 1e-6


def test_calculate_classification_metrics_perfect(classification_data_perfect):
    """Test comprehensive metrics calculation with perfect predictions."""
    metrics = calculate_classification_metrics(
        classification_data_perfect['y_true'], 
        classification_data_perfect['y_pred_proba'],
        threshold=0.5
    )
    
    # Check core metrics
    assert abs(metrics['accuracy'] - 1.0) < 1e-6
    assert abs(metrics['precision'] - 1.0) < 1e-6
    assert abs(metrics['recall'] - 1.0) < 1e-6
    assert abs(metrics['f1'] - 1.0) < 1e-6
    assert abs(metrics['specificity'] - 1.0) < 1e-6
    assert abs(metrics['npv'] - 1.0) < 1e-6
    
    # Check advanced metrics
    assert abs(metrics['mcc'] - 1.0) < 1e-6
    assert abs(metrics['roc_auc'] - 1.0) < 1e-6
    
    # Check confusion matrix values
    assert metrics['TP'] == 2
    assert metrics['TN'] == 2
    assert metrics['FP'] == 0
    assert metrics['FN'] == 0
    assert metrics['total'] == 4


def test_calculate_classification_metrics_single_class(
    classification_data_single_class_positive, 
    classification_data_single_class_negative
):
    """Test metrics calculation with only one class present."""
    # Test with all positives
    metrics_pos = calculate_classification_metrics(
        classification_data_single_class_positive['y_true'], 
        classification_data_single_class_positive['y_pred_proba'],
        threshold=0.5
    )
    
    # Some metrics should be NaN when only one class is present
    assert np.isnan(metrics_pos['precision'])  # All predictions are positive
    assert np.isnan(metrics_pos['recall'])  # Recall undefined with single class
    assert np.isnan(metrics_pos['roc_auc'])  # ROC-AUC undefined for single class
    
    # Test with all negatives
    metrics_neg = calculate_classification_metrics(
        classification_data_single_class_negative['y_true'], 
        classification_data_single_class_negative['y_pred_proba'],
        threshold=0.5
    )
    
    # Some metrics should be NaN when only one class is present
    assert np.isnan(metrics_neg['precision'])  # No positive predictions
    assert np.isnan(metrics_neg['recall'])  # No positive ground truth
    assert np.isnan(metrics_neg['roc_auc'])  # ROC-AUC undefined for single class 