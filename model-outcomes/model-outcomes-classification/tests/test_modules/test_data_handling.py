"""
Unit tests for the data_handling module.
"""

import pytest
import pandas as pd
import numpy as np
import os
import streamlit as st
from unittest.mock import patch, MagicMock, mock_open
from io import StringIO

from modules.data_handling import (
    get_filtered_data,
    validate_theme,
    process_uploaded_file,
    process_importance_file
)


@pytest.fixture
def assets_dir():
    """Path to test assets for file testing."""
    return os.path.join(os.path.dirname(os.path.dirname(__file__)), 'assets')


def test_get_filtered_data_all(test_dataframe_for_filtering):
    """Test filtering with no specific filters (all data)."""
    filtered_df = get_filtered_data(test_dataframe_for_filtering)
    
    # Should return all rows
    assert len(filtered_df) == 6


def test_get_filtered_data_by_model(test_dataframe_for_filtering):
    """Test filtering by specific model."""
    filtered_df = get_filtered_data(test_dataframe_for_filtering, selected_model='model_a')
    
    # Should return only rows with model_a
    assert len(filtered_df) == 3
    assert (filtered_df['model'] == 'model_a').all()


def test_get_filtered_data_by_patients(test_dataframe_for_filtering):
    """Test filtering by specific patients."""
    filtered_df = get_filtered_data(test_dataframe_for_filtering, selected_patients=['PT001', 'PT003'])
    
    # Should return only rows for PT001 and PT003
    assert len(filtered_df) == 4
    assert filtered_df['patient_id'].isin(['PT001', 'PT003']).all()


def test_get_filtered_data_combined(test_dataframe_for_filtering):
    """Test filtering by both model and patients."""
    filtered_df = get_filtered_data(
        test_dataframe_for_filtering, 
        selected_model='model_a',
        selected_patients=['PT001', 'PT003']
    )
    
    # Should return only model_a rows for PT001 and PT003
    assert len(filtered_df) == 2
    assert (filtered_df['model'] == 'model_a').all()
    assert filtered_df['patient_id'].isin(['PT001', 'PT003']).all()


def test_get_filtered_data_empty_patients(test_dataframe_for_filtering):
    """Test filtering with empty patient list (should return all)."""
    filtered_df = get_filtered_data(
        test_dataframe_for_filtering, 
        selected_model='model_a',
        selected_patients=[]
    )
    
    # Empty patients list should be treated as "all patients"
    assert len(filtered_df) == 3
    assert (filtered_df['model'] == 'model_a').all()


def test_validate_theme():
    """Test theme validation function."""
    # Valid themes should return unchanged
    assert validate_theme('plotly') == 'plotly'
    assert validate_theme('plotly_white') == 'plotly_white'
    assert validate_theme('ggplot2') == 'ggplot2'
    
    # Invalid themes should return default
    assert validate_theme('invalid_theme') == 'plotly_white'
    assert validate_theme(None) == 'plotly_white'


@patch('streamlit.error')
@patch('pandas.read_csv')
def test_process_uploaded_file_valid(mock_read_csv, mock_error, test_dataframe_for_filtering):
    """Test processing a valid uploaded file."""
    # Mock the file and read_csv to return our test DataFrame
    mock_file = MagicMock()
    mock_file.name = "valid_data.csv"
    mock_read_csv.return_value = test_dataframe_for_filtering
    
    # Mock session_state to store the file info
    mock_session_state = {}
    with patch.object(st, 'session_state', mock_session_state):
        result_df = process_uploaded_file(mock_file)
        
        # Should update session_state and return the DataFrame
        assert 'uploaded_data' in mock_session_state
        assert 'current_file' in mock_session_state
        assert mock_session_state['current_file'] == "valid_data.csv"
        assert result_df is test_dataframe_for_filtering
        
        # Error should not be called for valid data
        mock_error.assert_not_called()


@patch('streamlit.error')
@patch('pandas.read_csv')
def test_process_uploaded_file_missing_columns(mock_read_csv, mock_error, test_dataframe_for_filtering):
    """Test processing a file with missing required columns."""
    # Create a DataFrame missing the 'real' column
    invalid_df = test_dataframe_for_filtering.drop(columns=['real'])
    
    # Mock the file and read_csv to return the invalid DataFrame
    mock_file = MagicMock()
    mock_file.name = "invalid_data.csv"
    mock_read_csv.return_value = invalid_df
    
    # Mock session_state
    mock_session_state = {}
    with patch.object(st, 'session_state', mock_session_state):
        result_df = process_uploaded_file(mock_file)
        
        # Should show error and return None
        mock_error.assert_called_once()
        assert result_df is None


@patch('streamlit.error')
def test_process_uploaded_file_none(mock_error, test_dataframe_for_filtering):
    """Test processing when no file is uploaded but session has data."""
    # Set up session state with existing data
    mock_session_state = {
        'uploaded_data': test_dataframe_for_filtering,
        'current_file': 'existing_file.csv'
    }
    
    # Mock session_state to have existing data
    with patch.object(st, 'session_state', mock_session_state):
        result_df = process_uploaded_file(None)
        
        # Should return the existing data
        assert result_df is test_dataframe_for_filtering
        mock_error.assert_not_called()


@patch('streamlit.error')
@patch('pandas.read_csv')
def test_process_importance_file_valid(mock_read_csv, mock_error, test_importance_data_valid):
    """Test processing a valid feature importance file."""
    # Mock the file and read_csv to return our importance DataFrame
    mock_file = MagicMock()
    mock_file.name = "valid_importance.csv"
    mock_read_csv.return_value = test_importance_data_valid
    
    # Mock session_state to store the file info
    mock_session_state = {}
    with patch.object(st, 'session_state', mock_session_state):
        result_df = process_importance_file(mock_file)
        
        # Should update session_state and return the DataFrame
        assert 'importance_data' in mock_session_state
        assert 'importance_file' in mock_session_state
        assert mock_session_state['importance_file'] == "valid_importance.csv"
        assert result_df is test_importance_data_valid
        
        # Error should not be called for valid data
        mock_error.assert_not_called()


@patch('streamlit.error')
@patch('pandas.read_csv')
def test_process_importance_file_missing_columns(mock_read_csv, mock_error, test_importance_data_invalid):
    """Test processing an importance file with missing required columns."""
    # Mock the file and read_csv to return the invalid DataFrame
    mock_file = MagicMock()
    mock_file.name = "invalid_importance.csv"
    mock_read_csv.return_value = test_importance_data_invalid
    
    # Mock session_state
    mock_session_state = {}
    with patch.object(st, 'session_state', mock_session_state):
        result_df = process_importance_file(mock_file)
        
        # Should show error and return None
        mock_error.assert_called_once()
        assert result_df is None 