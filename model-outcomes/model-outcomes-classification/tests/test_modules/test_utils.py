"""
Unit tests for the utils module.
"""

import pytest
import streamlit as st
import os
from unittest.mock import patch, MagicMock, mock_open
from modules.utils import extend_session_state, get_base64_encoded_image


def test_extend_session_state():
    """Test session state extension with new keys."""
    # Mock an empty session_state
    mock_session_state = {}
    with patch.object(st, 'session_state', mock_session_state):
        extend_session_state()
        
        # Verify session state was updated correctly
        assert 'current_file' in mock_session_state
        assert mock_session_state['current_file'] is None
        assert 'uploaded_data' in mock_session_state
        assert mock_session_state['uploaded_data'] is None
        assert 'importance_file' in mock_session_state
        assert mock_session_state['importance_file'] is None
        assert 'importance_data' in mock_session_state
        assert mock_session_state['importance_data'] is None


def test_extend_session_state_existing():
    """Test session state extension when some keys already exist."""
    # Mock a session_state with some existing data
    mock_session_state = {
        'current_file': 'existing_file.csv',
        'uploaded_data': {'existing': 'data'}
    }
    
    with patch.object(st, 'session_state', mock_session_state):
        extend_session_state()
        
        # Existing data should be preserved
        assert mock_session_state['current_file'] == 'existing_file.csv'
        assert mock_session_state['uploaded_data'] == {'existing': 'data'}
        
        # New keys should be added with None values
        assert 'importance_file' in mock_session_state
        assert mock_session_state['importance_file'] is None
        assert 'importance_data' in mock_session_state
        assert mock_session_state['importance_data'] is None


@patch('builtins.open', new_callable=mock_open, read_data=b'test image data')
@patch('os.path.exists')
def test_get_base64_encoded_image_valid(mock_exists, mock_file):
    """Test base64 encoding of a valid image file."""
    mock_exists.return_value = True
    
    result = get_base64_encoded_image('test_image.png')
    
    # Should return a base64 encoded string
    assert result is not None
    assert isinstance(result, str)
    assert len(result) > 0
    
    # Verify file operations
    mock_exists.assert_called_once_with('test_image.png')
    mock_file.assert_called_once_with('test_image.png', 'rb')


@patch('builtins.open')
def test_get_base64_encoded_image_file_not_found(mock_file):
    """Test handling of file not found error."""
    # Mock open to raise FileNotFoundError
    mock_file.side_effect = FileNotFoundError()
    
    # Call the function with a non-existent file
    result = get_base64_encoded_image('nonexistent_file.png')
    
    # Should return empty string on error
    assert result == ''


@patch('builtins.open')
def test_get_base64_encoded_image_permission_error(mock_file):
    """Test handling of permission error."""
    # Mock open to raise PermissionError
    mock_file.side_effect = PermissionError()
    
    # Call the function
    result = get_base64_encoded_image('protected_file.png')
    
    # Should return empty string on error
    assert result == '' 