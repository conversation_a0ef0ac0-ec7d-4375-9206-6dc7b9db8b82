"""
Unit tests for the flexible_loader module.
"""

import pytest
import pandas as pd
import numpy as np
import os
import io
import streamlit as st
from unittest.mock import patch, MagicMock, mock_open
from io import StringIO, BytesIO

from modules.flexible_loader import (
    detect_file_format,
    load_dataframe,
    get_candidate_columns,
    validate_and_transform_data,
    flexible_process_uploaded_file
)


@pytest.fixture
def test_df_standard():
    """Create a sample DataFrame with standard column names."""
    return pd.DataFrame({
        'patient_id': ['PT001', 'PT001', 'PT002', 'PT002', 'PT003', 'PT003'],
        'model': ['model_a', 'model_b', 'model_a', 'model_b', 'model_a', 'model_b'],
        'real': [1, 1, 0, 0, 1, 1],
        'pred': [0.9, 0.8, 0.3, 0.2, 0.7, 0.6]
    })


@pytest.fixture
def test_df_nonstandard():
    """Create a sample DataFrame with non-standard column names."""
    return pd.DataFrame({
        'subject_id': ['PT001', 'PT001', 'PT002', 'PT002', 'PT003', 'PT003'],
        'algorithm': ['model_a', 'model_b', 'model_a', 'model_b', 'model_a', 'model_b'],
        'ground_truth': [1, 1, 0, 0, 1, 1],
        'probability': [0.9, 0.8, 0.3, 0.2, 0.7, 0.6]
    })


@pytest.fixture
def test_df_conversion():
    """Create a sample DataFrame with real values needing conversion."""
    return pd.DataFrame({
        'id': ['PT001', 'PT001', 'PT002', 'PT002', 'PT003', 'PT003'],
        'model': ['model_a', 'model_b', 'model_a', 'model_b', 'model_a', 'model_b'],
        'actual': ['Yes', 'True', '1', 'No', 'False', '0'],
        'score': [90, 80, 30, 20, 70, 60]  # Percentages (0-100) instead of probabilities (0-1)
    })


@pytest.fixture
def assets_dir():
    """Path to test assets for file testing."""
    return os.path.join(os.path.dirname(os.path.dirname(__file__)), 'assets')


def test_detect_file_format_by_extension():
    """Test file format detection based on file extension."""
    # Create mock files with different extensions
    csv_file = MagicMock()
    csv_file.name = "data.csv"
    
    excel_file = MagicMock()
    excel_file.name = "data.xlsx"
    
    pickle_file = MagicMock()
    pickle_file.name = "data.pkl"
    
    parquet_file = MagicMock()
    parquet_file.name = "data.parquet"
    
    # Test format detection
    assert detect_file_format(csv_file) == 'csv'
    assert detect_file_format(excel_file) == 'excel'
    assert detect_file_format(pickle_file) == 'pickle'
    assert detect_file_format(parquet_file) == 'parquet'


def test_detect_file_format_no_extension():
    """Test file format detection based on content when extension is missing."""
    # Create a mock file with no extension but CSV content
    csv_content = StringIO("col1,col2,col3\n1,2,3\n4,5,6")
    
    # Mock file with read method
    mock_file = MagicMock()
    mock_file.name = "data_file"  # No extension
    mock_file.read.return_value = csv_content.getvalue().encode('utf-8')
    mock_file.seek = MagicMock()  # Mock seek method
    
    # Test content-based detection
    assert detect_file_format(mock_file) == 'csv'


def test_detect_file_format_unknown():
    """Test detection of unknown file format."""
    # Create a mock file with unknown extension and binary content
    unknown_file = MagicMock()
    unknown_file.name = "data.xyz"
    unknown_file.read.return_value = b'\x00\x01\x02\x03'  # Binary data
    unknown_file.seek = MagicMock()
    
    # Test unknown format detection
    assert detect_file_format(unknown_file) == 'unknown'


@patch('pandas.read_csv')
def test_load_dataframe_csv(mock_read_csv, test_df_standard):
    """Test loading a CSV file into a DataFrame."""
    # Set up mock to return our test DataFrame
    mock_read_csv.return_value = test_df_standard
    
    # Create mock CSV file
    mock_file = MagicMock()
    mock_file.name = "data.csv"
    
    # Test loading
    df = load_dataframe(mock_file, 'csv')
    
    # Verify read_csv was called and returned our DataFrame
    mock_read_csv.assert_called_once()
    assert df is test_df_standard


@patch('pandas.read_excel')
def test_load_dataframe_excel(mock_read_excel, test_df_standard):
    """Test loading an Excel file into a DataFrame."""
    # Set up mock to return our test DataFrame
    mock_read_excel.return_value = test_df_standard
    
    # Create mock Excel file
    mock_file = MagicMock()
    mock_file.name = "data.xlsx"
    
    # Test loading
    df = load_dataframe(mock_file, 'excel')
    
    # Verify read_excel was called and returned our DataFrame
    mock_read_excel.assert_called_once()
    assert df is test_df_standard


@patch('streamlit.error')
def test_load_dataframe_error(mock_error):
    """Test error handling when loading a DataFrame fails."""
    # Create mock file that will cause an error
    mock_file = MagicMock()
    mock_file.name = "data.csv"
    
    # Patch read_csv to raise an exception
    with patch('pandas.read_csv', side_effect=Exception("Test error")):
        df = load_dataframe(mock_file, 'csv')
        
        # Should show error and return None
        mock_error.assert_called_once()
        assert df is None


def test_get_candidate_columns_patient_id(test_df_nonstandard):
    """Test candidate column detection for patient_id."""
    # Test with non-standard column names
    candidates = get_candidate_columns(test_df_nonstandard, 'patient_id')
    
    # Should identify 'subject_id' as a candidate for 'patient_id'
    assert 'subject_id' in candidates


def test_get_candidate_columns_real(test_df_nonstandard):
    """Test candidate column detection for real (ground truth)."""
    # Test with non-standard column names
    candidates = get_candidate_columns(test_df_nonstandard, 'real')
    
    # Should identify 'ground_truth' as a candidate for 'real'
    assert 'ground_truth' in candidates


def test_get_candidate_columns_pred(test_df_nonstandard):
    """Test candidate column detection for pred (probabilities)."""
    # Test with non-standard column names
    candidates = get_candidate_columns(test_df_nonstandard, 'pred')
    
    # Should identify 'probability' as a candidate for 'pred'
    assert 'probability' in candidates


def test_validate_and_transform_data_standard(test_df_standard):
    """Test validation and transformation with standard data."""
    # Define column mapping for standard data (columns already match)
    column_mapping = {
        'patient_id': 'patient_id',
        'model': 'model',
        'real': 'real',
        'pred': 'pred'
    }
    
    # Validate and transform
    result_df, success, error_msg = validate_and_transform_data(
        test_df_standard, column_mapping
    )
    
    # Should succeed with no changes needed
    assert success
    assert error_msg == ""
    assert len(result_df) == len(test_df_standard)


def test_validate_and_transform_data_nonstandard(test_df_nonstandard):
    """Test validation and transformation with non-standard column names."""
    # Define column mapping for non-standard data
    column_mapping = {
        'patient_id': 'subject_id',
        'model': 'algorithm',
        'real': 'ground_truth',
        'pred': 'probability'
    }
    
    # Validate and transform
    result_df, success, error_msg = validate_and_transform_data(
        test_df_nonstandard, column_mapping
    )
    
    # Should succeed after column renaming
    assert success
    assert error_msg == ""
    assert len(result_df) == len(test_df_nonstandard)
    
    # Check that columns were renamed correctly
    expected_columns = ['patient_id', 'model', 'real', 'pred']
    assert all(col in result_df.columns for col in expected_columns)


def test_validate_and_transform_data_conversion(test_df_conversion):
    """Test validation and transformation with data type conversions."""
    # Define column mapping
    column_mapping = {
        'patient_id': 'id',
        'model': 'model',
        'real': 'actual',
        'pred': 'score'
    }
    
    # Validate and transform
    result_df, success, error_msg = validate_and_transform_data(
        test_df_conversion, column_mapping
    )
    
    # Should succeed after data conversions
    assert success
    assert error_msg == ""
    
    # Check that real values were converted to binary (0/1)
    assert all(val in [0, 1] for val in result_df['real'])
    
    # Check that pred values were converted to probabilities (0-1)
    assert all(0 <= val <= 1 for val in result_df['pred'])


def test_validate_and_transform_data_auto_model():
    """Test automatic model column generation when missing."""
    # Create DataFrame without model column
    df_no_model = pd.DataFrame({
        'patient_id': ['PT001', 'PT002', 'PT003'],
        'real': [1, 0, 1],
        'pred': [0.9, 0.3, 0.7]
    })
    
    # Column mapping with None for model (auto-generate)
    column_mapping = {
        'patient_id': 'patient_id',
        'model': None,  # Signal to auto-generate
        'real': 'real',
        'pred': 'pred'
    }
    
    # Validate and transform
    result_df, success, error_msg = validate_and_transform_data(
        df_no_model, column_mapping
    )
    
    # Should succeed with auto-generated model column
    assert success
    assert error_msg == ""
    assert 'model' in result_df.columns
    assert all(val == 'No model defined' for val in result_df['model'])


def test_validate_and_transform_data_invalid_real():
    """Test validation failure with invalid real values."""
    # Create DataFrame with invalid real values (not binary)
    df_invalid_real = pd.DataFrame({
        'patient_id': ['PT001', 'PT002', 'PT003'],
        'model': ['model_a', 'model_b', 'model_a'],
        'real': [1, 2, 3],  # Not binary
        'pred': [0.9, 0.3, 0.7]
    })
    
    # Define column mapping
    column_mapping = {
        'patient_id': 'patient_id',
        'model': 'model',
        'real': 'real',
        'pred': 'pred'
    }
    
    # Validate and transform
    result_df, success, error_msg = validate_and_transform_data(
        df_invalid_real, column_mapping
    )
    
    # Should fail validation
    assert not success
    assert "real" in error_msg.lower()


@patch('streamlit.success')
@patch('modules.flexible_loader.load_dataframe')
def test_flexible_process_uploaded_file_standard(mock_load_dataframe, mock_success, test_df_standard):
    """Test processing a file with standard column names."""
    # Mock load_dataframe to return standard DataFrame
    mock_load_dataframe.return_value = test_df_standard
    
    # Create mock file
    mock_file = MagicMock()
    mock_file.name = "standard_data.csv"
    
    # Mock session_state
    mock_session_state = {}
    with patch.object(st, 'session_state', mock_session_state):
        result_df = flexible_process_uploaded_file(mock_file)
        
        # Should succeed and update session_state
        assert result_df is not None
        assert 'uploaded_data' in mock_session_state
        mock_success.assert_called_once()


@patch('modules.flexible_loader.load_dataframe')
@patch('modules.flexible_loader.display_column_mapping_ui')
def test_flexible_process_uploaded_file_nonstandard(mock_display_mapping, mock_load_dataframe, test_df_nonstandard):
    """Test processing a file with non-standard column names."""
    # Mock load_dataframe to return non-standard DataFrame
    mock_load_dataframe.return_value = test_df_nonstandard
    
    # Mock display_column_mapping_ui to return mapping
    column_mapping = {
        'patient_id': 'subject_id',
        'model': 'algorithm',
        'real': 'ground_truth',
        'pred': 'probability'
    }
    mock_display_mapping.return_value = column_mapping
    
    # Create mock file
    mock_file = MagicMock()
    mock_file.name = "nonstandard_data.csv"
    
    # Mock streamlit form and form_submit_button
    with patch('streamlit.form') as mock_form, \
         patch('streamlit.form_submit_button', return_value=True) as mock_submit, \
         patch('streamlit.info') as mock_info, \
         patch('streamlit.success') as mock_success, \
         patch('streamlit.dataframe') as mock_dataframe:
        
        # Create a context manager for the form
        mock_form_ctx = MagicMock()
        mock_form.return_value.__enter__.return_value = mock_form_ctx
        
        # Mock detect_file_format to return 'csv'
        with patch('modules.flexible_loader.detect_file_format', return_value='csv'):
            # Mock session_state
            mock_session_state = {}
            with patch.object(st, 'session_state', mock_session_state):
                # Process the file
                result_df = flexible_process_uploaded_file(mock_file)
                
                # Should succeed and store mapped data
                assert result_df is not None
                assert 'uploaded_data' in mock_session_state
                mock_success.assert_called_once()


@patch('streamlit.error')
def test_flexible_process_uploaded_file_unknown_format(mock_error):
    """Test processing a file with unknown format."""
    # Create mock file with unknown format
    mock_file = MagicMock()
    mock_file.name = "data.xyz"
    mock_file.read.return_value = b'\x00\x01\x02\x03'  # Binary data
    mock_file.seek = MagicMock()
    
    # Mock session_state
    mock_session_state = {}
    with patch.object(st, 'session_state', mock_session_state):
        result_df = flexible_process_uploaded_file(mock_file)
        
        # Should show error and return None
        mock_error.assert_called_once()
        assert result_df is None


def test_flexible_process_uploaded_file_none():
    """Test processing when no file is uploaded but session has data."""
    # Mock session_state with existing data
    mock_session_state = {
        'uploaded_data': pd.DataFrame({'test': [1, 2, 3]}),
        'current_file': 'existing_file.csv'
    }
    
    with patch.object(st, 'session_state', mock_session_state):
        result_df = flexible_process_uploaded_file(None)
        
        # Should return existing data
        assert result_df is not None
        assert 'test' in result_df.columns 