"""
Unit tests for statistical test functionality.
"""

import pytest
import pandas as pd
from modules.classification_metrics import calculate_statistical_test


def test_small_sample_calculation():
    """Test if <PERSON>'s exact test is used for small samples."""
    # Small sample that should trigger <PERSON>'s exact test
    result = calculate_statistical_test(tp=2, fn=1, fp=1, tn=3)
    
    # Check if the function correctly identified this as a small sample
    assert result['small_sample']
    assert result['recommended_test'] == 'fisher'
    
    # Check if <PERSON>'s test was calculated
    assert 'fisher' in result
    
    # Check if the original API is preserved
    assert 'test_name' in result
    assert 'statistic_name' in result
    assert 'p_value' in result


def test_large_sample_calculation():
    """Test if both tests are calculated for large samples."""
    # Larger sample that should allow Chi-square
    result = calculate_statistical_test(tp=15, fn=10, fp=8, tn=20)
    
    # Check if the function correctly identified this as a larger sample
    assert not result['small_sample']
    assert result['recommended_test'] == 'chi2'
    
    # Check if both tests were calculated
    assert 'fisher' in result
    assert 'chi2' in result


def test_force_fisher_test():
    """Test if we can force <PERSON>'s test even for large samples."""
    # Explicitly request <PERSON>'s test
    result = calculate_statistical_test(tp=15, fn=10, fp=8, tn=20, use_fisher_exact=True)
    
    # Check if <PERSON>'s test is available
    assert 'fisher' in result
    
    # Chi-square should not be calculated
    assert 'chi2' not in result


def test_force_chi_square_test():
    """Test if we can force Chi-square test even for small samples."""
    # Explicitly request Chi-square test
    result = calculate_statistical_test(tp=2, fn=1, fp=1, tn=3, use_fisher_exact=False)
    
    # Both tests should be available since Fisher's is always calculated
    assert 'fisher' in result
    assert 'chi2' in result 