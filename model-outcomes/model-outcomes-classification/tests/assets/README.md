# Test Data Files

This directory contains test data files used for unit testing of the "Model Outcomes | Classification" application. Each file is designed to test specific aspects of the application, from ideal cases to edge cases and errors.

## Classification Files

| File Name | Description | Key Characteristics | Validity/Test Criteria |
|-----------|-------------|---------------------|------------------------|
| `valid_classification_data.csv` | Valid and balanced dataset for binary classification testing | • 12 records (6 patients with 2 models each)<br>• Balanced class distribution: 6 positive (1) and 6 negative (0)<br>• Prediction values (`pred`) in correct range [0-1]<br>• Contains all required columns<br>• Includes features: `feature_A`, `feature_B`, `feature_C` | • Contains all mandatory columns<br>• `real` column with binary values (0 and 1)<br>• `pred` column with probabilities between 0 and 1<br>• Equitable class distribution (positive:negative ratio = 1:1) |
| `imbalanced_classification_data.csv` | Dataset with imbalanced classes to test metric behavior with bias toward negative class | • 16 records (8 patients with 2 models each)<br>• Imbalanced distribution: 2 positive (1) and 14 negative (0)<br>• Highly skewed class ratio (positive:negative = 1:7) | • Class imbalance ratio greater than 1:3<br>• Only approximately 12.5% of records belong to positive class<br>• Critical for testing metrics sensitive to class distribution (e.g., PR-AUC) |
| `invalid_classification_data.csv` | Invalid dataset due to missing required columns | • 3 records<br>• Missing the `real` column (mandatory)<br>• Has `prediction` instead of `pred` (incorrect name) | • Absence of at least one mandatory column (`real`)<br>• Incorrect nomenclature (uses `prediction` instead of `pred`)<br>• Should cause validation failure in `process_uploaded_file()` function |
| `outofrange_classification_data.csv` | Dataset with values outside the allowed range to test constraint validation | • 5 records<br>• Range errors:<br>  - PT003: `real` value = 2 (outside binary range)<br>  - PT004: `pred` value = 1.25 (above max 1.0)<br>  - PT005: `pred` value = -0.15 (below min 0.0) | • `real` values must be binary (0 or 1)<br>• `pred` values must be in range [0, 1]<br>• Each error tests a specific validation in data processing |

## Feature Importance File

| File Name | Description | Key Characteristics | Validity/Test Criteria |
|-----------|-------------|---------------------|------------------------|
| `valid_feature_importance.csv` | Valid feature importance file for importance visualization testing | • 3 features (`feature_A`, `feature_B`, `feature_C`)<br>• Normalized importance values (sum to 1.0)<br>• Contains all required columns | • Contains mandatory columns for importance analysis<br>• Feature names match those in classification datasets<br>• Importance values are numeric and suitable for visualization |

## Usage in Tests

These datasets are designed for specific tests:

1. **Format validation tests:** Verify that the application correctly validates the required structure and data types.
2. **Metric calculation tests:** Check that metrics are calculated correctly in balanced and imbalanced scenarios.
3. **Edge case tests:** Evaluate the application's behavior with extreme or invalid data.
4. **Integration tests:** Verify that the different functionalities of the application work together correctly.

The files are organized to systematically cover different use cases and error situations that might be encountered in a real environment.