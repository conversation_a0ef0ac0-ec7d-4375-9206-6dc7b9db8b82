"""
OMOP Database State Checker

Simple utility to check if OMOP database and user already exist.
Uses main project .env configuration.
"""
import psycopg2
import logging
from typing import Tuple, Dict, Optional

try:
    from .config import (
        OMOP_DB_HOST, OMOP_DB_PORT, OMOP_DB_NAME, OMOP_DB_USERNAME, OMOP_DB_PASSWORD,
        POSTGRES_ADMIN_USER, POSTGRES_ADMIN_DB, OMOP_CDM_EXPECTED_TABLES
    )
except ImportError:
    # When running as script directly, import from same directory
    from config import (
        OMOP_DB_HOST, OMOP_DB_PORT, OMOP_DB_NAME, OMOP_DB_USERNAME, OMOP_DB_PASSWORD,
        POSTGRES_ADMIN_USER, POSTGRES_ADMIN_DB, OMOP_CDM_EXPECTED_TABLES
    )

logger = logging.getLogger(__name__)

def check_database_exists() -> bool:
    """
    Check if OMOP database exists.
    
    Returns
    -------
    bool
        True if database exists, False otherwise
    """
    try:
        conn = psycopg2.connect(
            host=OMOP_DB_HOST,
            port=OMOP_DB_PORT,
            database=POSTGRES_ADMIN_DB,
            user=POSTGRES_ADMIN_USER
        )
        cur = conn.cursor()
        cur.execute(
            "SELECT 1 FROM pg_database WHERE datname = %s",
            (OMOP_DB_NAME,)
        )
        exists = cur.fetchone() is not None
        cur.close()
        conn.close()
        return exists
    except Exception as e:
        logger.error(f"Error checking database existence: {e}")
        return False

def check_user_exists() -> bool:
    """
    Check if OMOP user exists.
    
    Returns
    -------
    bool
        True if user exists, False otherwise
    """
    try:
        conn = psycopg2.connect(
            host=OMOP_DB_HOST,
            port=OMOP_DB_PORT,
            database=POSTGRES_ADMIN_DB,
            user=POSTGRES_ADMIN_USER
        )
        cur = conn.cursor()
        cur.execute(
            "SELECT 1 FROM pg_user WHERE usename = %s",
            (OMOP_DB_USERNAME,)
        )
        exists = cur.fetchone() is not None
        cur.close()
        conn.close()
        return exists
    except Exception as e:
        logger.error(f"Error checking user existence: {e}")
        return False

def check_tables_exist() -> Tuple[bool, int]:
    """
    Check if OMOP tables exist and return count.
    
    Returns
    -------
    Tuple[bool, int]
        (True if tables exist, number of tables found)
    """
    try:
        conn = psycopg2.connect(
            host=OMOP_DB_HOST,
            port=OMOP_DB_PORT,
            database=OMOP_DB_NAME,
            user=OMOP_DB_USERNAME,
            password=OMOP_DB_PASSWORD
        )
        cur = conn.cursor()
        cur.execute(
            "SELECT COUNT(*) FROM information_schema.tables "
            "WHERE table_schema = 'public' AND table_type = 'BASE TABLE'"
        )
        table_count = cur.fetchone()[0]
        cur.close()
        conn.close()
        return table_count > 0, table_count
    except Exception as e:
        logger.error(f"Error checking tables: {e}")
        return False, 0

def check_vocabularies_loaded() -> Tuple[bool, int]:
    """
    Check if vocabularies are loaded and return concept count.
    
    Returns
    -------
    Tuple[bool, int]
        (True if vocabularies loaded, number of concepts found)
    """
    try:
        conn = psycopg2.connect(
            host=OMOP_DB_HOST,
            port=OMOP_DB_PORT,
            database=OMOP_DB_NAME,
            user=OMOP_DB_USERNAME,
            password=OMOP_DB_PASSWORD
        )
        cur = conn.cursor()
        cur.execute("SELECT COUNT(*) FROM concept")
        concept_count = cur.fetchone()[0]
        cur.close()
        conn.close()
        return concept_count > 0, concept_count
    except Exception as e:
        logger.error(f"Error checking vocabularies: {e}")
        return False, 0

def get_database_status() -> Dict[str, any]:
    """
    Get complete database status.
    
    Returns
    -------
    Dict[str, any]
        Dictionary with database status information
    """
    user_exists = check_user_exists()
    db_exists = check_database_exists()
    tables_exist, table_count = check_tables_exist()
    vocabularies_loaded, concept_count = check_vocabularies_loaded()
    
    return {
        "user_exists": user_exists,
        "database_exists": db_exists,
        "tables_exist": tables_exist,
        "table_count": table_count,
        "expected_tables": OMOP_CDM_EXPECTED_TABLES,
        "vocabularies_loaded": vocabularies_loaded,
        "concept_count": concept_count,
        "setup_complete": (
            user_exists and 
            db_exists and 
            table_count == OMOP_CDM_EXPECTED_TABLES and 
            concept_count > 0
        )
    }

if __name__ == "__main__":
    """Test database checker when run directly."""
    
    print("🔍 OMOP Database Status Checker")
    print("=" * 50)
    
    status = get_database_status()
    
    print(f"👤 User '{OMOP_DB_USERNAME}' exists: {'✅' if status['user_exists'] else '❌'}")
    print(f"🗄️  Database '{OMOP_DB_NAME}' exists: {'✅' if status['database_exists'] else '❌'}")
    print(f"📋 Tables exist: {'✅' if status['tables_exist'] else '❌'} ({status['table_count']}/{status['expected_tables']})")
    print(f"📚 Vocabularies loaded: {'✅' if status['vocabularies_loaded'] else '❌'} ({status['concept_count']:,} concepts)")
    print(f"🎯 Setup complete: {'✅' if status['setup_complete'] else '❌'}")
    
    print("\n📊 Detailed Status:")
    for key, value in status.items():
        print(f"  {key}: {value}")
