"""
OMOP Database Creator

Automates Steps 2-6 from docs/guides/omop/database/postgresql_setup.md
Simple, focused script that handles only database creation.
"""
import argparse
import logging
import subprocess
import sys
import urllib.request
from pathlib import Path
from typing import Optional, Tuple

import psycopg2

try:
    from .config import (
        OMOP_DB_HOST, OMOP_DB_PORT, OMOP_DB_NAME, OMOP_DB_USERNAME, OMOP_DB_PASSWORD,
        POSTGRES_ADMIN_USER, POSTGRES_ADMIN_DB, DDL_BASE_URL, DDL_SCRIPTS_DIR,
        DDL_EXECUTION_ORDER, OMOP_CDM_EXPECTED_TABLES
    )
    from .database_checker import get_database_status
except ImportError:
    # When running as script directly, import from same directory
    from config import (
        OMOP_DB_HOST, OMOP_DB_PORT, OMOP_DB_NAME, OMOP_DB_USERNAME, OMOP_DB_PASSWORD,
        POST<PERSON><PERSON>_ADMIN_USER, POSTGRES_ADMIN_DB, DDL_BASE_URL, DDL_SCRIPTS_DIR,
        DDL_EXECUTION_ORDER, OMOP_CDM_EXPECTED_TABLES
    )
    from database_checker import get_database_status

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OMOPDatabaseCreator:
    """Creates OMOP CDM database following official OHDSI guidelines."""
    
    def __init__(self, force: bool = False):
        """
        Initialize database creator.
        
        Parameters
        ----------
        force : bool
            Force recreation even if database exists
        """
        self.force = force
        self.ddl_scripts_dir = Path(DDL_SCRIPTS_DIR)
    


    def _validate_configuration(self) -> Tuple[bool, str]:
        """
        Validate configuration values for basic correctness.
        
        Simple validation for environment variables used in database creation.
        
        Returns
        -------
        Tuple[bool, str]
            (is_valid, error_message)
        """
        # Basic validation: check for empty/None values
        if not OMOP_DB_NAME or not OMOP_DB_NAME.strip():
            return False, "Database name cannot be empty"
        
        if not OMOP_DB_USERNAME or not OMOP_DB_USERNAME.strip():
            return False, "Username cannot be empty"
        
        return True, ""
    #TODO: Solve this path issue to find psql command by default
    def _find_psql_command(self) -> str:
        """
        Find psql command following documented troubleshooting guide.

        Based on docs/guides/development/shell-path-troubleshooting.md
        and docs/guides/omop/database/postgresql_setup.md

        Returns
        -------
        str
            Path to psql command
            
        Raises
        ------
        RuntimeError
            If psql command is not found in PATH or common locations
        """
        import shutil

        # Try to find psql in PATH first
        psql_path = shutil.which('psql')
        if psql_path:
            logger.info(f"✅ Found psql in PATH: {psql_path}")
            return psql_path

        # Common locations for psql on macOS (from documentation)
        common_paths = [
            '/opt/homebrew/bin/psql',  # Homebrew ARM64 (Apple Silicon)
            '/usr/local/bin/psql',     # Homebrew Intel
            '/usr/bin/psql',           # System installation
            '/Applications/Postgres.app/Contents/Versions/latest/bin/psql'  # Postgres.app
        ]

        for path in common_paths:
            if Path(path).exists():
                logger.info(f"✅ Found psql at: {path}")
                return path

        # If not found, provide helpful error message and raise exception
        error_msg = (
            "❌ psql command not found. Please ensure PostgreSQL is installed.\n"
            "💡 For macOS with Homebrew, try: brew install postgresql\n"
            "💡 Or add to PATH: export PATH=\"/opt/homebrew/bin:$PATH\"\n"
            "📖 See docs/guides/development/shell-path-troubleshooting.md"
        )
        logger.error(error_msg)
        raise RuntimeError(error_msg)
        
    def create_user_and_database(self) -> bool:
        """
        Create OMOP user and database if they don't exist.
        Automates Step 2 from postgresql_setup.md
        
        Returns
        -------
        bool
            True if successful, False otherwise
        """
        try:
            logger.info("🔧 Step 2: Creating OMOP user and database...")
            
            # Strict security validation for SQL injection prevention
            config_valid, config_error = self._validate_configuration()
            if not config_valid:
                logger.error(f"❌ Security validation failed: {config_error}")
                return False
            
            # Check if already exists and not forcing
            status = get_database_status()
            if status['user_exists'] and status['database_exists'] and not self.force:
                logger.info("✅ User and database already exist (use --force to recreate)")
                return True
                
            # Connect as admin user
            conn = psycopg2.connect(
                host=OMOP_DB_HOST,
                port=OMOP_DB_PORT,
                database=POSTGRES_ADMIN_DB,
                user=POSTGRES_ADMIN_USER
            )
            conn.autocommit = True
            cur = conn.cursor()
            
            # Create user if not exists
            if not status['user_exists'] or self.force:
                if status['user_exists'] and self.force:
                    logger.info(f"🗑️  Dropping existing user '{OMOP_DB_USERNAME}'...")
                    cur.execute("DROP USER IF EXISTS %s", (OMOP_DB_USERNAME,))
                
                logger.info(f"👤 Creating user '{OMOP_DB_USERNAME}'...")
                cur.execute("CREATE USER %s WITH PASSWORD %s", (OMOP_DB_USERNAME, OMOP_DB_PASSWORD))
            
            # Create database if not exists
            if not status['database_exists'] or self.force:
                if status['database_exists'] and self.force:
                    logger.info(f"🗑️  Dropping existing database '{OMOP_DB_NAME}'...")
                    # Terminate connections to database
                    cur.execute(
                        "SELECT pg_terminate_backend(pid) FROM pg_stat_activity "
                        "WHERE datname = %s AND pid <> pg_backend_pid()",
                        (OMOP_DB_NAME,)
                    )
                    cur.execute("DROP DATABASE IF EXISTS %s", (OMOP_DB_NAME,))
                    
                logger.info(f"🗄️  Creating database '{OMOP_DB_NAME}'...")
                # Note: Database and user names cannot be parameterized in PostgreSQL
                # These values come from controlled configuration, not user input
                cur.execute(f"CREATE DATABASE {OMOP_DB_NAME} OWNER {OMOP_DB_USERNAME}")
                cur.execute(f"GRANT ALL PRIVILEGES ON DATABASE {OMOP_DB_NAME} TO {OMOP_DB_USERNAME}")
                
            cur.close()
            conn.close()
            
            logger.info("✅ User and database creation completed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error creating user and database: {e}")
            return False
    
    def download_ddl_scripts(self) -> bool:
        """
        Download official OHDSI DDL scripts.
        Automates Step 3 from postgresql_setup.md
        
        Returns
        -------
        bool
            True if successful, False otherwise
        """
        try:
            logger.info("📥 Step 3: Downloading DDL scripts...")
            
            # Create DDL directory if it doesn't exist
            self.ddl_scripts_dir.mkdir(parents=True, exist_ok=True)
            
            # Download each DDL script
            for script_name in DDL_EXECUTION_ORDER:
                script_path = self.ddl_scripts_dir / script_name
                script_url = f"{DDL_BASE_URL}/{script_name}"
                
                if script_path.exists() and not self.force:
                    logger.info(f"✅ {script_name} already exists")
                    continue
                    
                logger.info(f"📥 Downloading {script_name}...")
                urllib.request.urlretrieve(script_url, script_path)
                logger.info(f"✅ Downloaded {script_name}")
                
            logger.info("✅ DDL scripts download completed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error downloading DDL scripts: {e}")
            return False
    
    def modify_schema_placeholders(self) -> bool:
        """
        Replace @cdmDatabaseSchema with actual schema name.
        Automates Step 4 from postgresql_setup.md
        
        Returns
        -------
        bool
            True if successful, False otherwise
        """
        try:
            logger.info("🔧 Step 4: Modifying schema placeholders...")
            
            for script_name in DDL_EXECUTION_ORDER:
                script_path = self.ddl_scripts_dir / script_name
                
                if not script_path.exists():
                    logger.error(f"❌ Script not found: {script_path}")
                    return False
                    
                # Read file content
                with open(script_path, 'r') as f:
                    content = f.read()
                
                # Replace placeholders
                original_content = content
                content = content.replace('@cdmDatabaseSchema', 'public')
                
                # Write back if changed
                if content != original_content:
                    with open(script_path, 'w') as f:
                        f.write(content)
                    logger.info(f"✅ Modified {script_name}")
                else:
                    logger.info(f"✅ {script_name} already modified")
                    
            logger.info("✅ Schema placeholder modification completed")
            return True

        except Exception as e:
            logger.error(f"❌ Error modifying schema placeholders: {e}")
            return False

    def execute_ddl_scripts(self) -> bool:
        """
        Execute DDL scripts in correct order.
        Automates Step 5 from postgresql_setup.md

        Returns
        -------
        bool
            True if successful, False otherwise
        """
        try:
            logger.info("🚀 Step 5: Executing DDL scripts...")

            for i, script_name in enumerate(DDL_EXECUTION_ORDER, 1):
                script_path = self.ddl_scripts_dir / script_name

                if not script_path.exists():
                    logger.error(f"❌ Script not found: {script_path}")
                    return False

                logger.info(f"📋 {i}/{len(DDL_EXECUTION_ORDER)}: Executing {script_name}...")

                # Execute script using psql (use full path for macOS compatibility)
                try:
                    psql_cmd = self._find_psql_command()
                except RuntimeError as e:
                    logger.error(f"❌ Cannot execute scripts: {e}")
                    return False
                cmd = [
                    psql_cmd,
                    '-h', OMOP_DB_HOST,
                    '-p', str(OMOP_DB_PORT),
                    '-U', OMOP_DB_USERNAME,
                    '-d', OMOP_DB_NAME,
                    '-f', str(script_path),
                    '-q'  # Quiet mode
                ]

                env = {'PGPASSWORD': OMOP_DB_PASSWORD}
                result = subprocess.run(cmd, env=env, capture_output=True, text=True)

                if result.returncode != 0:
                    logger.error(f"❌ Error executing {script_name}: {result.stderr}")
                    return False

                logger.info(f"✅ {script_name} executed successfully")

            logger.info("✅ DDL scripts execution completed")
            return True

        except Exception as e:
            logger.error(f"❌ Error executing DDL scripts: {e}")
            return False

    def verify_installation(self) -> bool:
        """
        Verify OMOP CDM tables were created successfully.
        Automates Step 6 from postgresql_setup.md

        Returns
        -------
        bool
            True if installation is valid, False otherwise
        """
        try:
            logger.info("🔍 Step 6: Verifying installation...")

            status = get_database_status()

            if status['table_count'] == status['expected_tables']:
                logger.info(f"✅ Installation verified: {status['table_count']}/{status['expected_tables']} tables created")
                return True
            else:
                logger.error(f"❌ Installation failed: {status['table_count']}/{status['expected_tables']} tables created")
                return False

        except Exception as e:
            logger.error(f"❌ Error verifying installation: {e}")
            return False

    def create_database(self) -> bool:
        """
        Complete database creation process.

        Returns
        -------
        bool
            True if successful, False otherwise
        """
        logger.info("🚀 Starting OMOP CDM Database Creation")
        logger.info("=" * 50)

        # Check if already complete and not forcing
        if not self.force:
            status = get_database_status()
            if status['setup_complete']:
                logger.info("✅ OMOP database already fully set up (use --force to recreate)")
                return True

        # Execute all steps
        steps = [
            self.create_user_and_database,
            self.download_ddl_scripts,
            self.modify_schema_placeholders,
            self.execute_ddl_scripts,
            self.verify_installation
        ]

        for step in steps:
            if not step():
                logger.error("❌ Database creation failed")
                return False

        logger.info("🎉 OMOP CDM Database creation completed successfully!")
        return True

def main():
    """Main entry point with argument parsing."""
    parser = argparse.ArgumentParser(description="Create OMOP CDM database")
    parser.add_argument(
        "--force",
        action="store_true",
        help="Force recreation even if database exists"
    )
    args = parser.parse_args()

    creator = OMOPDatabaseCreator(force=args.force)
    success = creator.create_database()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
