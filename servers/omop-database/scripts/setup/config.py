"""
OMOP Database Setup Configuration

Uses the main project .env file located at project root.
Centralized configuration for OMOP database module.
"""
import os
import logging
from pathlib import Path
from dotenv import load_dotenv

# Get the project root directory (5 levels up from this file)
# servers/omop-database/scripts/setup/config.py -> project root
PROJECT_ROOT = Path(__file__).parent.parent.parent.parent.parent
ENV_FILE = PROJECT_ROOT / ".env"

# Load environment variables from main project .env file
load_dotenv(ENV_FILE)

# Database Configuration (using existing variables from .env)
OMOP_DB_HOST = os.getenv("OMOP_DB_HOST", "localhost")
OMOP_DB_PORT = int(os.getenv("OMOP_DB_PORT", "5432"))
OMOP_DB_NAME = os.getenv("OMOP_DB_NAME", "omop_cdm")
OMOP_DB_USERNAME = os.getenv("OMOP_DB_USERNAME", "omop")
OMOP_DB_PASSWORD = os.getenv("OMOP_DB_PASSWORD", "omop_secure_2024")
OMOP_DB_SCHEMA = os.getenv("OMOP_DB_SCHEMA", "public")

# Admin Database Configuration (for creating user/database)
POSTGRES_ADMIN_USER = os.getenv("POSTGRES_ADMIN_USER", os.getenv("USER"))
POSTGRES_ADMIN_DB = os.getenv("POSTGRES_ADMIN_DB", "postgres")

# DDL Configuration
DDL_BASE_URL = "https://raw.githubusercontent.com/OHDSI/CommonDataModel/v5.4.2/inst/ddl/5.4/postgresql"
DDL_SCRIPTS_DIR = PROJECT_ROOT / "servers" / "omop-database" / "scripts" / "ddl" / "postgresql"

# Vocabulary Configuration (using existing VOCABULARY_PATH from .env)
VOCABULARY_PATH = Path(os.getenv("VOCABULARY_PATH", "./data/vocabulary/omop_v5_20250630"))
# Convert to absolute path relative to project root
if not VOCABULARY_PATH.is_absolute():
    VOCABULARY_PATH = PROJECT_ROOT / VOCABULARY_PATH

# UMLS Configuration (using existing UMLS_API_KEY from .env)
UMLS_API_KEY = os.getenv("UMLS_API_KEY")

# Processing Configuration (using existing values from .env)
BATCH_SIZE = int(os.getenv("BATCH_SIZE", "100"))
MAX_WORKERS = int(os.getenv("MAX_WORKERS", "4"))

# Logging Configuration
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
LOG_FILE = PROJECT_ROOT / os.getenv("LOG_FILE", "./etl.log")

# DDL Scripts in execution order (CRITICAL ORDER)
DDL_EXECUTION_ORDER = [
    "OMOPCDM_postgresql_5.4_ddl.sql",        # 1. Create tables
    "OMOPCDM_postgresql_5.4_primary_keys.sql", # 2. Add primary keys
    "OMOPCDM_postgresql_5.4_indices.sql",     # 3. Create indices
    "OMOPCDM_postgresql_5.4_constraints.sql"  # 4. Add foreign key constraints
]

# OMOP CDM Database Schema Constants
# Based on official OMOP CDM v5.4.2 specification
OMOP_CDM_EXPECTED_TABLES = 39  # Official OMOP CDM v5.4.2 table count

# Vocabulary files in official OHDSI loading order
VOCABULARY_FILES = [
    'CONCEPT.csv',           # 1. Central metadata table
    'VOCABULARY.csv',        # 2. Vocabulary definitions
    'CONCEPT_ANCESTOR.csv',  # 3. Hierarchical relationships
    'CONCEPT_RELATIONSHIP.csv', # 4. Concept relationships
    'RELATIONSHIP.csv',      # 5. Relationship types
    'CONCEPT_SYNONYM.csv',   # 6. Alternative names
    'DOMAIN.csv',           # 7. Domain definitions
    'CONCEPT_CLASS.csv',    # 8. Concept classes
    'DRUG_STRENGTH.csv'     # 9. Drug strength data
]

def get_env_file_path() -> Path:
    """
    Get the path to the .env file.

    Returns
    -------
    Path
        Path to the .env file
    """
    return ENV_FILE

def get_vocabulary_path() -> Path:
    """
    Get vocabulary directory path from environment or auto-detect.

    Returns
    -------
    Path
        Path to vocabulary directory

    Raises
    ------
    FileNotFoundError
        If vocabulary directory is not found
    """
    if VOCABULARY_PATH.exists():
        return VOCABULARY_PATH
    else:
        raise FileNotFoundError(f"Vocabulary directory not found: {VOCABULARY_PATH}")

def get_db_config() -> dict:
    """
    Get database configuration dictionary.

    Returns
    -------
    dict
        Database configuration for psycopg2
    """
    return {
        'host': OMOP_DB_HOST,
        'port': OMOP_DB_PORT,
        'database': OMOP_DB_NAME,
        'user': OMOP_DB_USERNAME,
        'password': OMOP_DB_PASSWORD
    }

def setup_logging() -> logging.Logger:
    """
    Setup logging configuration.

    Returns
    -------
    logging.Logger
        Configured logger instance
    """

    # Create logger
    logger = logging.getLogger('omop_vocabulary_loader')
    logger.setLevel(getattr(logging, LOG_LEVEL.upper()))

    # Clear any existing handlers
    logger.handlers.clear()

    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(getattr(logging, LOG_LEVEL.upper()))

    # Create file handler
    file_handler = logging.FileHandler(LOG_FILE)
    file_handler.setLevel(getattr(logging, LOG_LEVEL.upper()))

    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    file_handler.setFormatter(formatter)

    # Add handlers to logger
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)

    return logger

def get_config_summary() -> dict:
    """
    Get configuration summary for debugging.
    
    Returns
    -------
    dict
        Dictionary with configuration values
    """
    return {
        "project_root": str(PROJECT_ROOT),
        "env_file": str(ENV_FILE),
        "env_file_exists": ENV_FILE.exists(),
        "omop_db_host": OMOP_DB_HOST,
        "omop_db_name": OMOP_DB_NAME,
        "omop_db_user": OMOP_DB_USERNAME,
        "ddl_scripts_dir": str(DDL_SCRIPTS_DIR),
        "ddl_scripts_exist": DDL_SCRIPTS_DIR.exists(),
        "vocabulary_path": str(VOCABULARY_PATH),
        "vocabulary_path_exists": VOCABULARY_PATH.exists(),
        "umls_api_key_set": bool(UMLS_API_KEY),
        "log_file": str(LOG_FILE)
    }

if __name__ == "__main__":
    """Test configuration when run directly."""
    
    print("🔧 OMOP Database Setup Configuration")
    print("=" * 50)
    
    config = get_config_summary()
    for key, value in config.items():
        print(f"{key}: {value}")
    
    print("\n📋 DDL Execution Order:")
    for i, script in enumerate(DDL_EXECUTION_ORDER, 1):
        script_path = DDL_SCRIPTS_DIR / script
        exists = "✅" if script_path.exists() else "❌"
        print(f"{i}. {script} {exists}")
