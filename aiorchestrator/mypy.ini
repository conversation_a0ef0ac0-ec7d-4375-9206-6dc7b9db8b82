[mypy]
# Target Python version
python_version = 3.10

# Suppress errors for imports that mypy can't resolve
ignore_missing_imports = True

# Enable a few common strictness flags
strict_optional = True      # no implicit None → T
warn_unused_configs = True  # catch unused mypy settings

# You can turn on more checks as you go:
# disallow_untyped_defs = True   # force annotations on all defs
# disallow_incomplete_defs = True
# warn_unreachable = True