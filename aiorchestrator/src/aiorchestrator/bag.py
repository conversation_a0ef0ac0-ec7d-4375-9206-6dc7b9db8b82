from __future__ import annotations
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
from sklearn.utils import resample

from .base import MultiEnsembler  # Changed from BaseEnsembler to MultiEnsembler


class BaggingEnsembler(MultiEnsembler):  # Now inherits from MultiEnsembler
    """
    Ensemble model that trains the same base estimator on different bootstrap samples.

    This implementation creates multiple versions of the same model trained on
    different random subsets of the training data, then combines their predictions
    using statistical measures (mean, median, mode) and provides uncertainty
    metrics (standard deviation, entropy).

    Parameters:
    -----------
    base_estimator : Any
        The base estimator to fit on random subsets of the dataset.
    n_estimators : int, default=10
        The number of base estimators in the ensemble.
    max_samples : int or float, default=1.0
        The number of samples to draw from X to train each base estimator.
        - If int, then draw max_samples samples.
        - If float, then draw max_samples * X.shape[0] samples.
    bootstrap : bool, default=True
        Whether samples are drawn with replacement (True) or not (False).
    random_state : Optional[int], default=None
        Controls the random resampling of the original dataset.
    column_names : Optional[Dict[str, str]], default=None
        Custom names for the columns in the output dictionary.
        Possible keys: "mean", "median", "mode", "std", "entropy", "predictions"
        For "predictions", the value should contain "{nn}" which will be replaced
        with the estimator number (e.g., "prediction_{nn}" -> "prediction_01").
    """

    base_estimator: Any
    n_estimators: int
    max_samples: Union[int, float]
    bootstrap: bool
    random_state: Optional[int]

    def __init__(
            self,
            base_estimator: Any,
            n_estimators: int = 10,
            max_samples: Union[int, float] = 1.0,
            bootstrap: bool = True,
            random_state: Optional[int] = None,
            column_names: Optional[Dict[str, str]] = None,
    ) -> None:
        super().__init__(column_names)
        self.base_estimator = base_estimator
        self.n_estimators = n_estimators
        self.max_samples = max_samples
        self.bootstrap = bootstrap
        self.random_state = random_state
        self.models = []  # Initialize empty models list

    def fit(
            self,
            X: np.ndarray,
            y: np.ndarray,
            estimator_fit_kwargs: Optional[Dict[str, Dict[str, Any]]] = None,
            **fit_kwargs: Any
    ) -> BaggingEnsembler:
        """
        Fit base estimators on random subsets of the dataset.

        Parameters:
        -----------
        X : np.ndarray
            Training data.
        y : np.ndarray
            Target values.
        estimator_fit_kwargs : Optional[Dict[str, Dict[str, Any]]], default=None
            Dictionary mapping estimator names to their specific fit arguments.
            Special parameters:
            - 'skip_fit': If True, the estimator will not be fitted (for pre-trained estimators)
            Example: {'estimator_1': {'skip_fit': True}}
        **fit_kwargs : Any
            Additional arguments to pass to the base estimator's fit method.

        Returns:
        --------
        self : BaggingEnsembler
            The fitted ensemble.
        """

        # If models are already created (e.g., pre-trained), check if we should skip training them
        if self.models and estimator_fit_kwargs:
            for name, model in self.models:
                if name in estimator_fit_kwargs and estimator_fit_kwargs[name].get('skip_fit', False):
                    continue

                # Prepare kwargs for this model
                kwargs = fit_kwargs.copy()
                if name in estimator_fit_kwargs:
                    est_kwargs = estimator_fit_kwargs[name].copy()
                    est_kwargs.pop('skip_fit', None)
                    kwargs.update(est_kwargs)

                # Determine bootstrap sample
                n_samples: int = (
                    int(self.max_samples * X.shape[0])
                    if isinstance(self.max_samples, float)
                    else self.max_samples
                )
                rng = np.random.RandomState(self.random_state)
                X_sample, y_sample = resample(
                    X, y,
                    replace=self.bootstrap,
                    n_samples=n_samples,
                    random_state=rng.randint(np.iinfo(np.int32).max)
                )

                # Fit the model
                model.fit(X_sample, y_sample, **kwargs)

            return self

        # If no models exist yet, create and train them from scratch
        estimator_fit_kwargs = estimator_fit_kwargs or {}
        self.models = []
        rng: np.random.RandomState = np.random.RandomState(self.random_state)

        for i in range(self.n_estimators):
            estimator_name = f"estimator_{i + 1}"

            # Check if we should skip creating this estimator (unlikely but possible)
            if estimator_name in estimator_fit_kwargs and estimator_fit_kwargs[estimator_name].get('skip_fit', False):
                continue

            estimator = clone_estimator(self.base_estimator)

            n_samples: int = (
                int(self.max_samples * X.shape[0])
                if isinstance(self.max_samples, float)
                else self.max_samples
            )
            X_sample: np.ndarray
            y_sample: np.ndarray
            X_sample, y_sample = resample(
                X, y,
                replace=self.bootstrap,
                n_samples=n_samples,
                random_state=rng.randint(np.iinfo(np.int32).max)
            )

            # Prepare kwargs for this estimator
            kwargs = fit_kwargs.copy()
            if estimator_name in estimator_fit_kwargs:
                est_kwargs = estimator_fit_kwargs[estimator_name].copy()
                est_kwargs.pop('skip_fit', None)
                kwargs.update(est_kwargs)

            estimator.fit(X_sample, y_sample, **kwargs)
            self.models.append((estimator_name, estimator))

        return self


def clone_estimator(estimator: Any) -> Any:
    """
    Create a clone of an estimator.

    Parameters:
    -----------
    estimator : Any
        The estimator to clone.

    Returns:
    --------
    Any
        A clone of the input estimator.
    """
    try:
        from sklearn.base import clone
        return clone(estimator)
    except ImportError:
        import copy
        return copy.deepcopy(estimator)