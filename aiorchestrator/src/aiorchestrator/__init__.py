"""
AIOchestrator - A library for creating ensembled predictive models.

This library allows you to create "ensembled" predictive models that combine
different machine learning models to provide better predictions and confidence metrics.

Main components:
- VotingEnsembler: Combines predictions from multiple models using voting
- BaggingEnsembler: Trains the same model on different subsets of the data
- ChainEnsembler: Creates a pipeline where a router model directs inputs to specialist models
- EnsembleConfig: Configuration system for creating ensemble models
- BaggingAdapter: Adapter for using BaggingEnsembler with components that expect a standard prediction interface

Example:
    >>> from aiorchestrator import VotingEnsembler
    >>> from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
    >>> estimators = [
    ...     ('rf', RandomForestRegressor(n_estimators=100)),
    ...     ('gb', GradientBoostingRegressor(n_estimators=100))
    ... ]
    >>> ensemble = VotingEnsembler(estimators, aggregation='mean', flag_std_above=0.5)
    >>> ensemble.fit(X_train, y_train)
    >>> predictions, reliability = ensemble.predict(X_test)
"""

from .base import BaseEnsembler
from .vote import VotingEnsembler
from .bag import BaggingEnsembler
from .chain import ChainEnsembler
from .config import EnsembleConfig
from .adapters import BaggingAdapter

__all__ = [
    'BaseEnsembler',
    'VotingEnsembler',
    'BaggingEnsembler',
    'ChainEnsembler',
    'EnsembleConfig',
    'BaggingAdapter',
]

__version__ = '0.1.0'