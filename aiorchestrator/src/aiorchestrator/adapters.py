import numpy as np

class BaggingAdapter:
    """
    Adapter for BaggingEnsembler that allows it to work with components 
    that expect a standard prediction interface.
    
    This adapter converts the dictionary output of BaggingEnsembler.predict()
    to a numpy array using a specified key from the predictions dictionary.
    
    Parameters:
    -----------
    bagging_ensembler : BaggingEnsembler
        The BaggingEnsembler to adapt.
    prediction_key : str, default="mean"
        Which prediction to use from the BaggingEnsembler's output dictionary.
        Options typically include: "mean", "median", "mode".
    """
    def __init__(self, bagging_ensembler, prediction_key="mean"):
        self.ensembler = bagging_ensembler
        self.prediction_key = prediction_key
        
    def fit(self, X, y, **fit_kwargs):
        """Delegate fitting to the underlying BaggingEnsembler."""
        self.ensembler.fit(X, y, **fit_kwargs)
        return self
        
    def predict(self, X):
        """
        Get predictions from BaggingEnsembler and extract the desired metric.
        
        Returns:
        --------
        numpy.ndarray
            The predictions using the specified prediction_key.
        """
        predictions_dict = self.ensembler.predict_detailed(X)
        return predictions_dict[self.prediction_key]