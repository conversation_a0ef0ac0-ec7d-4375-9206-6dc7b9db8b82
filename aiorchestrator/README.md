# AIOchestrator

A Python library for creating ensembled predictive models that combine different machine learning models to provide better predictions and confidence metrics.

## Features

- **Multiple Ensemble Strategies**:
  - **Voting**: Combine predictions from different models
  - **Bagging**: Train the same model on different subsets of data
  - **Chained Pipelines**: Use a router model to direct inputs to specialized models

- **Confidence Metrics**:
  - Flag predictions with high uncertainty
  - Get reliability scores for predictions
  - Different metrics for classification (entropy) and regression (standard deviation)

- **Flexible Configuration**:
  - Create ensembles from dictionaries, JSON, or YAML
  - Dynamically load models and configure parameters

## Installation

```bash
pip install aiorchestrator
```

## Quick Start

```python
from aiorchestrator import VotingEnsembler
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor

# Create individual models
rf = RandomForestRegressor(n_estimators=100)
gb = GradientBoostingRegressor(n_estimators=100)

# Create a voting ensemble
estimators = [
    ('rf', rf),
    ('gb', gb)
]
ensemble = VotingEnsembler(estimators, aggregation='mean', flag_std_above=0.5)

# Train the ensemble
ensemble.fit(X_train, y_train)

# Make predictions with reliability flags
predictions, reliability = ensemble.predict(X_test)
```

## Ensemble Types

### VotingEnsembler

Combines predictions from multiple models using voting (mean/median for regression, majority vote for classification).

```python
from aiorchestrator import VotingEnsembler

ensemble = VotingEnsembler(
    estimators=[('model1', model1), ('model2', model2)],
    aggregation='mean',  # or 'median' for regression, 'vote' for classification
    flag_std_above=0.5   # threshold for flagging unreliable predictions
)
```

### BaggingEnsembler

Trains the same model on different subsets of the data.

```python
from aiorchestrator import BaggingEnsembler

ensemble = BaggingEnsembler(
    base_estimator=model,
    n_estimators=10,
    max_samples=0.8,
    bootstrap=True,
    random_state=42,
    aggregation='mean',
    flag_std_above=0.5
)
```

### ChainEnsembler

Creates a pipeline where a router model directs inputs to specialist models.

```python
from aiorchestrator import ChainEnsembler

ensemble = ChainEnsembler(
    router=router_model,
    specialists={
        0: model_for_category_0,
        1: model_for_category_1
    },
    threshold=threshold_value,  # for regression routers
    fallback=fallback_model,    # for categories without specialists
    flag_std_above=0.5
)
```

## Configuration System

Create ensembles from configuration dictionaries, JSON, or YAML.

```python
from aiorchestrator import EnsembleConfig

# From dictionary
config = {
    "type": "voting",
    "estimators": [
        {
            "name": "rf",
            "class": "sklearn.ensemble.RandomForestRegressor",
            "params": {"n_estimators": 100}
        },
        {
            "name": "gb",
            "class": "sklearn.ensemble.GradientBoostingRegressor",
            "params": {"n_estimators": 100}
        }
    ],
    "aggregation": "mean",
    "flag_std_above": 0.5
}
ensemble = EnsembleConfig.from_dict(config)

# From JSON file
ensemble = EnsembleConfig.from_file("config.json")

# From YAML file
ensemble = EnsembleConfig.from_file("config.yaml")
```

## Examples

See the `examples` directory for more detailed examples:

- `simple_ensemble_example.py`: Demonstrates basic usage of all ensemble types
- `configuration_example.py`: Shows how to use the configuration system

