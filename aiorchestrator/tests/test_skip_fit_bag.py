import unittest
import os
import pickle
import numpy as np
from sklearn.datasets import load_breast_cancer
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score

# Import the BaggingEnsembler class
from aiorchestrator import BaggingEnsembler


class TestBaggingEnsemblerSkipFit(unittest.TestCase):
    """Test the skip_fit functionality in BaggingEnsembler."""

    @classmethod
    def setUpClass(cls):
        """Set up test fixtures once for all test methods."""
        # Load and prepare data
        X, y = load_breast_cancer(return_X_y=True)
        cls.X_train, cls.X_test, cls.y_train, cls.y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )

        # Standardize the features
        scaler = StandardScaler()
        cls.X_train_scaled = scaler.fit_transform(cls.X_train)
        cls.X_test_scaled = scaler.transform(cls.X_test)

        # Create and save multiple pre-trained forest models
        cls.pretrained_preds = []
        for i in range(5):
            rf_model = RandomForestClassifier(
                n_estimators=50,
                max_depth=5,
                random_state=42 + i
            )
            rf_model.fit(cls.X_train_scaled, cls.y_train)

            # Save the model
            model_path = f'pretrained_rf_{i}.pkl'
            with open(model_path, 'wb') as f:
                pickle.dump(rf_model, f)

            # Store predictions for later comparison
            cls.pretrained_preds.append(rf_model.predict(cls.X_test_scaled))

    @classmethod
    def tearDownClass(cls):
        """Clean up after all tests."""
        # Remove pickle files
        for i in range(5):
            model_path = f'pretrained_rf_{i}.pkl'
            if os.path.exists(model_path):
                os.remove(model_path)

    def test_skip_fit_preserves_all_pretrained_models(self):
        """Test that skip_fit preserves all pre-trained models' predictions."""
        # Load the pre-trained models and create a BaggingEnsembler manually
        models = []
        for i in range(5):
            with open(f'pretrained_rf_{i}.pkl', 'rb') as f:
                loaded_rf = pickle.load(f)
                models.append((f"estimator_{i + 1}", loaded_rf))

        # Create a custom BaggingEnsembler with pre-loaded models
        ensemble = BaggingEnsembler(
            base_estimator=RandomForestClassifier(),  # This won't be used
            n_estimators=5  # Should match the number of loaded models
        )
        # Manually assign the models
        ensemble.models = models

        # Fit with skip_fit for all models
        estimator_fit_kwargs = {
            f"estimator_{i + 1}": {'skip_fit': True} for i in range(5)
        }

        ensemble.fit(
            self.X_train_scaled,
            self.y_train,
            estimator_fit_kwargs=estimator_fit_kwargs
        )

        # Make predictions
        predictions = ensemble.predict_detailed(self.X_test_scaled)

        # Verify each model's predictions match the original
        for i in range(5):
            model_preds = predictions[f'prediction_{i + 1:02d}']  # e.g., prediction_01

            # Compare with stored predictions
            np.testing.assert_array_equal(
                model_preds,
                self.pretrained_preds[i],
                f"Model {i + 1}'s predictions should be preserved when using skip_fit=True"
            )

    def test_selective_skip_fit(self):
        """Test that we can selectively skip fitting some models but not others."""
        # Load the pre-trained models for positions 0, 2, and 4
        models = []
        for i in [0, 2, 3]:
            with open(f'pretrained_rf_{i}.pkl', 'rb') as f:
                loaded_rf = pickle.load(f)
                model_idx = i # // 2 + 1  # Map to positions 1, 2, 3
                models.append((f"estimator_{model_idx}", loaded_rf))

        # Create new models for positions 1 and 3
        new_rf_1 = RandomForestClassifier(n_estimators=10, random_state=100)
        new_rf_4 = RandomForestClassifier(n_estimators=10, random_state=111)

        models.insert(1, ("estimator_1", new_rf_1))

        # Create a custom BaggingEnsembler with the mix of models
        ensemble = BaggingEnsembler(
            base_estimator=RandomForestClassifier(),  # Won't be used
            n_estimators=5
        )
        # Manually assign the models
        ensemble.models = models

        # Fit with skip_fit for pre-trained models only
        ensemble.fit(
            self.X_train_scaled,
            self.y_train,
            estimator_fit_kwargs={
                'estimator_0': {'skip_fit': True},  # Skip pre-trained models
                'estimator_2': {'skip_fit': True},  # Skip pre-trained models
                'estimator_3': {'skip_fit': True},
                # New models should be trained
            }
        )

        # Make predictions
        predictions = ensemble.predict_detailed(self.X_test_scaled)

        # Verify pre-trained models' predictions match the original
        model_preds_0 = predictions['prediction_01']
        model_preds_2 = predictions['prediction_03']
        model_preds_3 = predictions['prediction_04']

        np.testing.assert_array_equal(
            model_preds_0,
            self.pretrained_preds[0],
            "First model's predictions should be preserved"
        )

        np.testing.assert_array_equal(
            model_preds_2,
            self.pretrained_preds[2],
            "Third model's predictions should be preserved"
        )

        np.testing.assert_array_equal(
            model_preds_3,
            self.pretrained_preds[3],
            "Fourth model's predictions should be preserved"
        )

        # New models should have been trained with different predictions
        model_preds_1 = predictions['prediction_01']


        # These should NOT match the pre-trained models
        self.assertTrue(
            np.any(model_preds_1 != self.pretrained_preds[1]),
            "Second model should have been trained with new parameters"
        )


    def test_without_skip_fit_retrains_all(self):
        """Test that without skip_fit, all models are retrained."""
        # Load the pre-trained models
        models = []
        for i in range(5):
            with open(f'pretrained_rf_{i}.pkl', 'rb') as f:
                loaded_rf = pickle.load(f)
                models.append((f"estimator_{i + 1}", loaded_rf))

        # Create a custom BaggingEnsembler with pre-loaded models
        ensemble = BaggingEnsembler(
            base_estimator=RandomForestClassifier(),
            n_estimators=5
        )
        ensemble.models = models

        # Fit WITHOUT skip_fit on a subset of data to ensure different results
        X_subset = self.X_train_scaled[:100]
        y_subset = self.y_train[:100]

        ensemble.fit(X_subset, y_subset)

        # Make predictions
        predictions = ensemble.predict_detailed(self.X_test_scaled)

        # At least some models should have different predictions now
        different_preds_count = 0
        for i in range(5):
            model_preds = predictions[f'prediction_{i + 1:02d}']

            if np.any(model_preds != self.pretrained_preds[i]):
                different_preds_count += 1

        # Assert that all models have changed
        self.assertEqual(
            different_preds_count,
            5,
            "All models should produce different predictions when retrained"
        )

    def test_create_new_ensemble_with_skip_fit(self):
        """Test creating a new BaggingEnsembler with skip_fit for specific models."""
        # Create a new BaggingEnsembler from scratch
        base_rf = RandomForestClassifier(n_estimators=20, random_state=42)
        ensemble = BaggingEnsembler(
            base_estimator=base_rf,
            n_estimators=5,
            random_state=42
        )

        # Fit once to create the models
        ensemble.fit(self.X_train_scaled, self.y_train)

        # Store the original predictions
        orig_predictions = ensemble.predict_detailed(self.X_test_scaled)
        orig_model_preds = [
            orig_predictions[f'prediction_{i + 1:02d}'] for i in range(5)
        ]

        # Now refit with skip_fit for models 1, 3, and 5
        # but use a different data subset to retrain models 2 and 4
        X_subset = self.X_train_scaled[:150]
        y_subset = self.y_train[:150]

        ensemble.fit(
            X_subset,
            y_subset,
            estimator_fit_kwargs={
                'estimator_1': {'skip_fit': True},
                'estimator_3': {'skip_fit': True},
                'estimator_5': {'skip_fit': True}
            }
        )

        # Make new predictions
        new_predictions = ensemble.predict_detailed(self.X_test_scaled)

        # Verify skipped models kept their predictions
        for i in [0, 2, 4]:  # models 1, 3, 5
            model_idx = i + 1
            orig_preds = orig_model_preds[i]
            new_preds = new_predictions[f'prediction_{model_idx:02d}']

            np.testing.assert_array_equal(
                new_preds,
                orig_preds,
                f"Model {model_idx}'s predictions should be preserved with skip_fit=True"
            )

        # Verify retrained models changed
        for i in [1, 3]:  # models 2, 4
            model_idx = i + 1
            orig_preds = orig_model_preds[i]
            new_preds = new_predictions[f'prediction_{model_idx:02d}']

            self.assertTrue(
                np.any(new_preds != orig_preds),
                f"Model {model_idx} should have been retrained and have different predictions"
            )

    def test_ensemble_performance_with_skip_fit(self):
        """Test the overall performance of an ensemble using skip_fit."""
        # Create a mix of pre-trained and new models
        models = []

        # Load 3 pre-trained models
        for i in range(3):
            with open(f'pretrained_rf_{i}.pkl', 'rb') as f:
                loaded_rf = pickle.load(f)
                models.append((f"estimator_{i + 1}", loaded_rf))

        # Create a custom BaggingEnsembler with pre-loaded models
        ensemble = BaggingEnsembler(
            base_estimator=RandomForestClassifier(n_estimators=50, random_state=999),
            n_estimators=5
        )

        # Set the first 3 models
        ensemble.models = models

        # Fit with skip_fit for pre-trained models
        # The rest of the models will be created and trained
        ensemble.fit(
            self.X_train_scaled,
            self.y_train,
            estimator_fit_kwargs={
                'estimator_1': {'skip_fit': True},
                'estimator_2': {'skip_fit': True},
                'estimator_3': {'skip_fit': True}
            }
        )

        # Make predictions
        predictions = ensemble.predict_detailed(self.X_test_scaled)
        ensemble_preds = predictions['mean']

        # Convert to binary predictions (assuming mean is a probability)
        if ensemble_preds.max() <= 1.0 and ensemble_preds.min() >= 0.0:
            ensemble_preds_binary = (ensemble_preds > 0.5).astype(int)
        else:
            ensemble_preds_binary = ensemble_preds

        # Get accuracy of the ensemble
        ensemble_accuracy = accuracy_score(self.y_test, ensemble_preds_binary)

        # Get accuracies of individual models
        individual_accuracies = []
        for i in range(3):
            model_preds = predictions[f'prediction_{i + 1:02d}']
            acc = accuracy_score(self.y_test, model_preds)
            individual_accuracies.append(acc)

        # Verify that the ensemble performs at least as well as the average model
        avg_individual_accuracy = np.mean(individual_accuracies)
        self.assertGreaterEqual(
            ensemble_accuracy,
            avg_individual_accuracy,
            "The ensemble should perform at least as well as the average individual model"
        )

        # Output performance metrics for inspection
        print(f"Ensemble accuracy: {ensemble_accuracy:.4f}")
        print("Individual model accuracies:")
        for i, acc in enumerate(individual_accuracies):
            status = "pre-trained" if i < 3 else "newly trained"
            print(f"  - Model {i + 1} ({status}): {acc:.4f}")


if __name__ == '__main__':
    unittest.main()
