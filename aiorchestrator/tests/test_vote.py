import unittest
from unittest.mock import Mock, patch
import numpy as np
from scipy import stats

# Import the modules to test
from aiorchestrator.vote import VotingEnsembler
from aiorchestrator.base import BaseEnsembler
from aiorchestrator.bag import BaggingEnsembler
# No need to actually import BaggingAdapter since we'll be patching it


class TestVotingEnsembler(unittest.TestCase):

    def setUp(self):
        """Set up test fixtures before each test."""
        # Create mock estimators
        self.estimator1 = Mock()
        self.estimator1.predict = Mock(return_value=np.array([1, 2]))  # Two samples to match X.shape[0]

        self.estimator2 = Mock()
        self.estimator2.predict = Mock(return_value=np.array([2, 3]))  # Two samples

        self.estimator3 = Mock()
        self.estimator3.predict = Mock(return_value=np.array([1, 3]))  # Two samples

        # Create a list of estimators with names
        self.estimators = [
            ('model1', self.estimator1),
            ('model2', self.estimator2),
            ('model3', self.estimator3)
        ]

        # We'll need to mock BaggingAdapter with the correct import path
        self.adapter_patcher = patch('aiorchestrator.vote.BaggingAdapter')
        self.mock_adapter_class = self.adapter_patcher.start()

        # Create a mock instance that will be returned when BaggingAdapter is instantiated
        self.mock_adapter_instance = Mock()
        self.mock_adapter_instance.predict = Mock(return_value=np.array([1.5, 2.5]))  # Two samples
        self.mock_adapter_instance.fit = Mock()
        self.mock_adapter_class.return_value = self.mock_adapter_instance

    def tearDown(self):
        """Tear down test fixtures after each test."""
        # Stop the patches
        self.adapter_patcher.stop()

    def test_init_normal_estimators(self):
        """Test initialization with normal estimators."""
        # Create the ensembler
        ensembler = VotingEnsembler(estimators=self.estimators)

        # Check if models are stored correctly
        self.assertEqual(len(ensembler.models), 3)
        self.assertEqual(ensembler.models[0][0], 'model1')
        self.assertEqual(ensembler.models[0][1], self.estimator1)
        self.assertEqual(ensembler.models[1][0], 'model2')
        self.assertEqual(ensembler.models[1][1], self.estimator2)
        self.assertEqual(ensembler.models[2][0], 'model3')
        self.assertEqual(ensembler.models[2][1], self.estimator3)

    def test_init_with_bagging_ensembler(self):
        """Test initialization with a BaggingEnsembler."""
        # Create a real BaggingEnsembler instance
        base_estimator = Mock()
        bagging_ensembler = BaggingEnsembler(
            base_estimator=base_estimator,
            n_estimators=5,
            max_samples=0.8,
            bootstrap=True,
            random_state=42
        )

        # Create a list of estimators with a BaggingEnsembler
        estimators = [
            ('model1', self.estimator1),
            ('bagging', bagging_ensembler)
        ]

        # Create the ensembler
        ensembler = VotingEnsembler(estimators=estimators)

        # Check if models are stored correctly
        self.assertEqual(len(ensembler.models), 2)
        self.assertEqual(ensembler.models[0][0], 'model1')
        self.assertEqual(ensembler.models[0][1], self.estimator1)
        self.assertEqual(ensembler.models[1][0], 'bagging')

        # Check if BaggingEnsembler is wrapped with a BaggingAdapter
        self.mock_adapter_class.assert_called_once_with(bagging_ensembler, prediction_key="mean")
        self.assertEqual(ensembler.models[1][1], self.mock_adapter_instance)

    def test_init_with_column_names(self):
        """Test initialization with custom column names."""
        # Define custom column names
        column_names = {
            "mean": "mean_prediction",
            "median": "median_prediction",
            "predictions": "est_{nn}_pred"
        }

        # Create the ensembler with custom column names
        ensembler = VotingEnsembler(estimators=self.estimators, column_names=column_names)

        # Check if column names are stored correctly in BaseEnsembler
        # Since we're inheriting from BaseEnsembler correctly, we can check directly
        self.assertEqual(ensembler.column_names, column_names)

    def test_fit_with_common_kwargs(self):
        """Test fit method with common kwargs."""
        # Create the ensembler
        ensembler = VotingEnsembler(estimators=self.estimators)

        # Create dummy data
        X = np.array([[1, 2], [3, 4]])
        y = np.array([0, 1])

        # Common kwargs for all estimators
        common_kwargs = {"verbose": True, "sample_weight": np.array([0.5, 0.5])}

        # Call fit
        ensembler.fit(X, y, **common_kwargs)

        # Check if fit was called for each estimator with the correct arguments
        self.estimator1.fit.assert_called_once_with(X, y, **common_kwargs)
        self.estimator2.fit.assert_called_once_with(X, y, **common_kwargs)
        self.estimator3.fit.assert_called_once_with(X, y, **common_kwargs)

    def test_fit_with_estimator_specific_kwargs(self):
        """Test fit method with estimator-specific kwargs."""
        # Create the ensembler
        ensembler = VotingEnsembler(estimators=self.estimators)

        # Create dummy data
        X = np.array([[1, 2], [3, 4]])
        y = np.array([0, 1])

        # Common kwargs for all estimators
        common_kwargs = {"verbose": True}

        # Estimator-specific kwargs
        estimator_kwargs = {
            "model1": {"alpha": 0.1},
            "model2": {"max_depth": 5}
        }

        # Call fit
        ensembler.fit(X, y, estimator_fit_kwargs=estimator_kwargs, **common_kwargs)

        # Check if fit was called for each estimator with the correct arguments
        self.estimator1.fit.assert_called_once_with(X, y, verbose=True, alpha=0.1)
        self.estimator2.fit.assert_called_once_with(X, y, verbose=True, max_depth=5)
        self.estimator3.fit.assert_called_once_with(X, y, verbose=True)

    def test_fit_returns_self(self):
        """Test that fit returns self for method chaining."""
        # Create the ensembler
        ensembler = VotingEnsembler(estimators=self.estimators)

        # Create dummy data
        X = np.array([[1, 2], [3, 4]])
        y = np.array([0, 1])

        # Call fit and check return value
        result = ensembler.fit(X, y)
        self.assertIs(result, ensembler)

    def test_predict(self):
        """Test that predict method calls the parent class implementation."""
        # Create the ensembler
        ensembler = VotingEnsembler(estimators=self.estimators)

        # Make sure models is set (normally done in fit, but we're testing predict directly)
        ensembler.models = self.estimators

        # Create dummy data
        X = np.array([[1, 2], [3, 4]])  # 2 samples

        # Call predict
        result = ensembler.predict_detailed(X)

        # Check that all estimators' predict methods were called
        self.estimator1.predict.assert_called_once_with(X)
        self.estimator2.predict.assert_called_once_with(X)
        self.estimator3.predict.assert_called_once_with(X)

        # Check that the result includes the expected keys based on BaseEnsembler's implementation
        self.assertIn('mean', result)
        self.assertIn('median', result)
        self.assertIn('mode', result)
        self.assertIn('std', result)
        self.assertIn('entropy', result)
        self.assertIn('prediction_01', result)
        self.assertIn('prediction_02', result)
        self.assertIn('prediction_03', result)

        # Test the values match what we expect from our mocked estimators
        np.testing.assert_array_equal(result['prediction_01'], np.array([1, 2]))
        np.testing.assert_array_equal(result['prediction_02'], np.array([2, 3]))
        np.testing.assert_array_equal(result['prediction_03'], np.array([1, 3]))

        # Test statistical outputs - based on our mock estimator return values
        expected_mean = np.array([(1 + 2 + 1) / 3, (2 + 3 + 3) / 3])  # [4/3, 8/3]
        np.testing.assert_array_almost_equal(result['mean'], expected_mean)

        expected_median = np.array([1, 3])  # Median of [1,2,1] and [2,3,3]
        np.testing.assert_array_equal(result['median'], expected_median)

        # Mode is trickier to test because scipy.stats.mode might change behavior
        # Just verify it has the right shape
        self.assertEqual(result['mode'].shape, (2,))

        # Check std
        expected_std = np.std(np.array([[1, 2], [2, 3], [1, 3]]), axis=0)
        np.testing.assert_array_almost_equal(result['std'], expected_std)

    def test_predict_with_custom_column_names(self):
        """Test predict method with custom column names."""
        # Define custom column names
        column_names = {
            "mean": "average",
            "median": "middle",
            "predictions": "model_{nn}"
        }

        # Create the ensembler with custom column names
        ensembler = VotingEnsembler(estimators=self.estimators, column_names=column_names)

        # Make sure models is set (normally done in fit, but we're testing predict directly)
        ensembler.models = self.estimators

        # Create dummy data - 2 samples
        X = np.array([[1, 2], [3, 4]])

        # Call predict
        result = ensembler.predict_detailed(X)

        # Check that the custom column names are used
        self.assertIn('average', result)
        self.assertIn('middle', result)
        self.assertIn('model_01', result)
        self.assertIn('model_02', result)
        self.assertIn('model_03', result)

        # Default column names should not be present
        self.assertNotIn('mean', result)
        self.assertNotIn('median', result)
        self.assertNotIn('prediction_01', result)


if __name__ == '__main__':
    unittest.main()