import unittest
import numpy as np

from aiorchestrator.bag import BaggingEnsembler


class FakeEstimator:
    """A simple estimator that returns a predictable linear combination of inputs."""
    def __init__(self, coef=1.0):
        self.coef = coef

    def fit(self, X, y, **kwargs):
        return self

    def predict(self, X):
        X = np.asarray(X)
        return np.sum(X, axis=1) * self.coef

    def get_params(self, deep=True):
        return {"coef": self.coef}

    def set_params(self, **params):
        for k, v in params.items():
            setattr(self, k, v)
        return self


class TestBaggingEnsembler(unittest.TestCase):

    def test_predict_before_fit_raises(self):
        ens = BaggingEnsembler(base_estimator=FakeEstimator(), n_estimators=3)
        X = np.zeros((5, 2))
        with self.assertRaises(ValueError):
            _ = ens.predict_detailed(X)

    def test_standard_output_keys_and_lengths(self):
        X = np.arange(10).reshape(5, 2)
        y = X[:, 0] + X[:, 1]

        n_estimators = 3
        ens = BaggingEnsembler(
            base_estimator=FakeEstimator(coef=2.0),
            n_estimators=n_estimators,
            random_state=42
        )
        ens.fit(X, y)
        preds = ens.predict_detailed(X)

        agg_keys = {"mean", "median", "mode", "std", "entropy"}
        indiv_keys = {f"prediction_{str(i).zfill(2)}" for i in range(1, n_estimators + 1)}
        expected_keys = agg_keys.union(indiv_keys)
        self.assertEqual(set(preds.keys()), expected_keys)

        for key, arr in preds.items():
            self.assertIsInstance(arr, np.ndarray)
            self.assertEqual(arr.shape, (X.shape[0],), f"Wrong shape for {key}")

    def test_custom_column_names_template(self):
        X = np.ones((4, 2))
        y = np.ones(4)

        custom_names = {
            "mean": "avg",
            "median": "med",
            "mode": "most",
            "std": "dev",
            "entropy": "entr",
            "predictions": "m_{nn}"
        }
        ens = BaggingEnsembler(
            base_estimator=FakeEstimator(),
            n_estimators=2,
            random_state=0,
            column_names=custom_names
        )
        ens.fit(X, y)
        preds = ens.predict_detailed(X)

        # Aggregated names
        for name in ["avg", "med", "most", "dev", "entr"]:
            self.assertIn(name, preds)

        # Individual prediction columns
        self.assertIn("m_01", preds)
        self.assertIn("m_02", preds)

        expected = set(custom_names.values()).union({"m_01", "m_02"})
        expected.remove("m_{nn}")
        self.assertEqual(set(preds.keys()), expected)


if __name__ == "__main__":
    unittest.main()