import unittest
import os
import pickle
import numpy as np
from sklearn.datasets import load_breast_cancer
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score

# Import the VotingEnsembler class
from aiorchestrator import VotingEnsembler


class TestVotingEnsemblerSkipFit(unittest.TestCase):
    """Test the skip_fit functionality in VotingEnsembler."""

    @classmethod
    def setUpClass(cls):
        """Set up test fixtures once for all test methods."""
        # Load and prepare data
        X, y = load_breast_cancer(return_X_y=True)
        cls.X_train, cls.X_test, cls.y_train, cls.y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )

        # Standardize the features
        scaler = StandardScaler()
        cls.X_train_scaled = scaler.fit_transform(cls.X_train)
        cls.X_test_scaled = scaler.transform(cls.X_test)

        # Create and save a pre-trained logistic regression model
        pretrained_model = LogisticRegression(random_state=42, max_iter=1000)
        pretrained_model.fit(cls.X_train_scaled, cls.y_train)

        # Save the model to a pickle file
        with open('pretrained_logistic.pkl', 'wb') as f:
            pickle.dump(pretrained_model, f)

        # Save the predictions of the pre-trained model for later comparison
        cls.pretrained_preds = pretrained_model.predict(cls.X_test_scaled)

    @classmethod
    def tearDownClass(cls):
        """Clean up after all tests."""
        # Remove the pickle file
        if os.path.exists('pretrained_logistic.pkl'):
            os.remove('pretrained_logistic.pkl')

    def test_skip_fit_preserves_pretrained_model(self):
        """Test that skip_fit preserves the pre-trained model's predictions."""
        # Load the pre-trained model
        with open('pretrained_logistic.pkl', 'rb') as f:
            loaded_logistic = pickle.load(f)

        # Create a new model to train
        rf_model = RandomForestClassifier(n_estimators=100, random_state=42)

        # Create ensemble with pre-trained and new models
        estimators = [
            ('logistic', loaded_logistic),  # Pre-trained model
            ('rf', rf_model)  # New model
        ]

        # Create and fit ensemble with skip_fit for the pre-trained model
        ensemble = VotingEnsembler(estimators)
        ensemble.fit(
            self.X_train_scaled,
            self.y_train,
            estimator_fit_kwargs={
                'logistic': {'skip_fit': True}  # Skip fitting the pre-trained model
            }
        )

        # Make predictions
        predictions = ensemble.predict_detailed(self.X_test_scaled)

        # Get the individual model predictions
        logistic_preds = predictions['prediction_01']  # First model predictions

        # Verify that the pre-trained model's predictions are preserved
        np.testing.assert_array_equal(
            logistic_preds,
            self.pretrained_preds,
            "The pre-trained model's predictions should be preserved when using skip_fit=True"
        )

    def test_without_skip_fit_changes_model(self):
        """Test that without skip_fit, the pre-trained model is refitted and possibly changes."""
        # Load the pre-trained model
        with open('pretrained_logistic.pkl', 'rb') as f:
            loaded_logistic = pickle.load(f)

        # Create a new model to train
        rf_model = RandomForestClassifier(n_estimators=100, random_state=42)

        # Create ensemble with pre-trained and new models
        estimators = [
            ('logistic', loaded_logistic),  # Pre-trained model
            ('rf', rf_model)  # New model
        ]

        # Create and fit ensemble WITHOUT skip_fit
        ensemble = VotingEnsembler(estimators)

        # Modify training data to ensure refitted model would differ
        # Use only a small subset for refitting to ensure different results
        X_subset = self.X_train_scaled[:100]
        y_subset = self.y_train[:100]

        ensemble.fit(X_subset, y_subset)

        # Make predictions
        predictions = ensemble.predict_detailed(self.X_test_scaled)

        # Get the individual model predictions
        logistic_preds = predictions['prediction_01']  # First model predictions

        # The refitted model should have different predictions than the pre-trained model
        # We check that at least some predictions are different
        self.assertTrue(
            np.any(logistic_preds != self.pretrained_preds),
            "Without skip_fit, the model should be refitted and produce different predictions"
        )

    def test_skip_fit_with_model_specific_params(self):
        """Test that skip_fit works alongside other model-specific parameters."""
        # Load the pre-trained model
        with open('pretrained_logistic.pkl', 'rb') as f:
            loaded_logistic = pickle.load(f)

        # Create a new model to train
        rf_model = RandomForestClassifier(n_estimators=100, random_state=42)

        # Create ensemble with pre-trained and new models
        estimators = [
            ('logistic', loaded_logistic),  # Pre-trained model
            ('rf', rf_model)  # New model
        ]

        # Create and fit ensemble with skip_fit and additional params for the other model
        ensemble = VotingEnsembler(estimators)
        ensemble.fit(
            self.X_train_scaled,
            self.y_train,
            estimator_fit_kwargs={
                'logistic': {'skip_fit': True},  # Skip fitting the pre-trained model
                'rf': {'sample_weight': np.ones(len(self.y_train))}  # Additional params for RF
            }
        )

        # Make predictions
        predictions = ensemble.predict_detailed(self.X_test_scaled)

        # Get the individual model predictions
        logistic_preds = predictions['prediction_01']  # Logistic model predictions
        rf_preds = predictions['prediction_02']  # RF model predictions

        # Verify that the pre-trained model's predictions are preserved
        np.testing.assert_array_equal(
            logistic_preds,
            self.pretrained_preds,
            "The pre-trained model's predictions should be preserved when using skip_fit=True"
        )

        # Verify that the RF model was successfully trained
        self.assertTrue(
            accuracy_score(self.y_test, rf_preds) > 0.5,
            "The RandomForest model should have been trained successfully"
        )

    def test_ensemble_performance_with_skip_fit(self):
        """Test that the ensemble performs well with a pre-trained model using skip_fit."""
        # Load the pre-trained model
        with open('pretrained_logistic.pkl', 'rb') as f:
            loaded_logistic = pickle.load(f)

        # Create a new model to train
        rf_model = RandomForestClassifier(n_estimators=100, random_state=42)

        # Create ensemble with pre-trained and new models
        estimators = [
            ('logistic', loaded_logistic),  # Pre-trained model
            ('rf', rf_model)  # New model
        ]

        # Create and fit ensemble with skip_fit
        ensemble = VotingEnsembler(estimators)
        ensemble.fit(
            self.X_train_scaled,
            self.y_train,
            estimator_fit_kwargs={
                'logistic': {'skip_fit': True}  # Skip fitting the pre-trained model
            }
        )

        # Make predictions
        predictions = ensemble.predict_detailed(self.X_test_scaled)
        ensemble_preds = predictions['mean']  # Mean ensemble predictions

        # Convert to binary predictions (assuming mean is a probability for binary classification)
        if ensemble_preds.max() <= 1.0 and ensemble_preds.min() >= 0.0:
            ensemble_preds_binary = (ensemble_preds > 0.5).astype(int)
        else:
            ensemble_preds_binary = ensemble_preds

        # Get accuracy of the ensemble
        ensemble_accuracy = accuracy_score(self.y_test, ensemble_preds_binary)

        # Get accuracies of individual models
        logistic_preds = predictions['prediction_01']
        rf_preds = predictions['prediction_02']
        logistic_accuracy = accuracy_score(self.y_test, logistic_preds)
        rf_accuracy = accuracy_score(self.y_test, rf_preds)

        # Verify that the ensemble performs at least as well as the best individual model
        self.assertGreaterEqual(
            ensemble_accuracy,
            min(logistic_accuracy, rf_accuracy),
            "The ensemble should perform at least as well as the worst individual model"
        )

        # Output performance metrics for manual inspection
        print(f"Ensemble accuracy: {ensemble_accuracy:.4f}")
        print(f"Logistic accuracy: {logistic_accuracy:.4f}")
        print(f"RandomForest accuracy: {rf_accuracy:.4f}")


if __name__ == '__main__':
    unittest.main()
