import os
import unittest
import pickle
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression, Ridge
from sklearn.metrics import accuracy_score, mean_squared_error
from sklearn.datasets import make_classification
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split

from aiorchestrator.chain import ChainEnsembler


class TestSkipFitChain(unittest.TestCase):
    """Test suite for skip_fit functionality in ChainEnsembler."""

    def setUp(self):
        """Set up test fixtures before each test."""
        # Create synthetic data for classification with more samples and clearer separation
        X, y = make_classification(
            n_samples=2000,
            n_features=10,
            n_informative=5,
            n_redundant=2,
            n_classes=2,
            class_sep=1.5,  # Increase class separation
            random_state=42
        )

        # Split data into train and test sets
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            X, y, test_size=0.25, random_state=42
        )

        # Scale the data
        scaler = StandardScaler()
        self.X_train_scaled = scaler.fit_transform(self.X_train)
        self.X_test_scaled = scaler.transform(self.X_test)

        # Use a regressor router to ensure better distribution of samples
        router = RandomForestClassifier(n_estimators=10, random_state=42)
        router.fit(self.X_train_scaled, self.y_train)

        # Get router predictions to split the data
        router_preds = router.predict(self.X_train_scaled)

        # Make sure both classes have enough samples for LogisticRegression
        class_0_count = np.sum(router_preds == 0)
        class_1_count = np.sum(router_preds == 1)
        print(f"Router predictions: class 0: {class_0_count}, class 1: {class_1_count}")

        # Check if there are enough samples of each class
        for cls in [0, 1]:
            mask = router_preds == cls
            unique_y_values = np.unique(self.y_train[mask])
            print(f"Class {cls} contains target values: {unique_y_values}")

        # Use Ridge regression instead of LogisticRegression for specialists
        # Ridge doesn't require multiple classes and works for both regression and classification
        specialist_0 = Ridge(alpha=1.0, random_state=42)
        specialist_1 = Ridge(alpha=2.0, random_state=43)

        # Fit specialists on their respective data subsets
        specialist_0.fit(self.X_train_scaled[router_preds == 0], self.y_train[router_preds == 0])
        specialist_1.fit(self.X_train_scaled[router_preds == 1], self.y_train[router_preds == 1])

        # Create the full ensemble
        pretrained_ensemble = ChainEnsembler(
            router=router,
            specialists={
                0: specialist_0,
                1: specialist_1
            }
        )

        # Fit with skip_fit=True for all models
        pretrained_ensemble.fit(
            self.X_train_scaled,
            self.y_train,
            estimator_fit_kwargs={
                'router': {'skip_fit': True},
                'specialist_0': {'skip_fit': True},
                'specialist_1': {'skip_fit': True}
            }
        )

        # Store predictions for later comparison
        pretrained_preds = pretrained_ensemble.predict_detailed(self.X_test_scaled)
        self.pretrained_mean = pretrained_preds['mean']

        # Store individual models for testing
        with open('pretrained_router.pkl', 'wb') as f:
            pickle.dump(router, f)

        with open('pretrained_specialist_0.pkl', 'wb') as f:
            pickle.dump(specialist_0, f)

        with open('pretrained_specialist_1.pkl', 'wb') as f:
            pickle.dump(specialist_1, f)

    def tearDown(self):
        """Clean up after all test"""
        for filename in ['pretrained_router.pkl', 'pretrained_specialist_0.pkl', 'pretrained_specialist_1.pkl']:
            if os.path.exists(filename):
                os.remove(filename)

    def test_skip_fit_all_models(self):
        """Test that skip_fit preserves all model predictions."""
        # Load the pre-trained models
        with open('pretrained_router.pkl', 'rb') as f:
            loaded_router = pickle.load(f)

        with open('pretrained_specialist_0.pkl', 'rb') as f:
            loaded_specialist_0 = pickle.load(f)

        with open('pretrained_specialist_1.pkl', 'rb') as f:
            loaded_specialist_1 = pickle.load(f)

        # Create an ensemble with pre-trained models
        ensemble = ChainEnsembler(
            router=loaded_router,
            specialists={
                0: loaded_specialist_0,
                1: loaded_specialist_1
            }
        )

        # Fit with skip_fit=True for all models
        ensemble.fit(
            self.X_train_scaled,
            self.y_train,
            estimator_fit_kwargs={
                'router': {'skip_fit': True},
                'specialist_0': {'skip_fit': True},
                'specialist_1': {'skip_fit': True}
            }
        )

        # Make predictions
        predictions = ensemble.predict_detailed(self.X_test_scaled)

        # Verify predictions match the original
        np.testing.assert_array_equal(
            predictions['mean'],
            self.pretrained_mean,
            "Predictions should be preserved when using skip_fit=True for all models"
        )

    def test_selective_skip_fit(self):
        """Test that we can selectively skip fitting some models but not others."""
        # Load the pre-trained router and specialist_0
        with open('pretrained_router.pkl', 'rb') as f:
            loaded_router = pickle.load(f)

        with open('pretrained_specialist_0.pkl', 'rb') as f:
            loaded_specialist_0 = pickle.load(f)

        # Create a new specialist_1 model to replace the pre-trained one
        new_specialist_1 = Ridge(alpha=10.0, random_state=999)  # Different parameters

        # Create an ensemble with a mix of pre-trained and new models
        ensemble = ChainEnsembler(
            router=loaded_router,
            specialists={
                0: loaded_specialist_0,
                1: new_specialist_1
            }
        )

        # Fit with skip_fit for pre-trained models only
        ensemble.fit(
            self.X_train_scaled,
            self.y_train,
            estimator_fit_kwargs={
                'router': {'skip_fit': True},
                'specialist_0': {'skip_fit': True}
                # specialist_1 should be trained
            }
        )

        # Make predictions
        predictions = ensemble.predict_detailed(self.X_test_scaled)

        # Load the original specialist_1 for comparison
        with open('pretrained_specialist_1.pkl', 'rb') as f:
            original_specialist_1 = pickle.load(f)

        # Get router predictions to identify samples for each specialist
        router_test_preds = loaded_router.predict(self.X_test_scaled)
        mask_1 = router_test_preds == 1

        if np.any(mask_1):  # Only compare if there are any samples routed to specialist_1
            X_subset = self.X_test_scaled[mask_1]
            original_preds = original_specialist_1.predict(X_subset)
            new_preds = new_specialist_1.predict(X_subset)

            # Assert original_preds are not equal to new_preds

            self.assertFalse(np.array_equal(np.asarray(original_preds), np.asarray(new_preds)))


        # Verify that router is preserved by checking the routing decisions
        original_routes = loaded_router.predict(self.X_test_scaled)
        test_routes = predictions['route']

        np.testing.assert_array_equal(
            original_routes,
            test_routes,
            "Router decisions should be preserved when using skip_fit=True"
        )

    def test_skip_fit_with_model_specific_params(self):
        """Test that skip_fit works alongside other model-specific parameters."""
        # Load the pre-trained router
        with open('pretrained_router.pkl', 'rb') as f:
            loaded_router = pickle.load(f)

        # Create new specialists
        specialist_0 = Ridge(random_state=100)
        specialist_1 = Ridge(random_state=101)

        # Create an ensemble
        ensemble = ChainEnsembler(
            router=loaded_router,
            specialists={
                0: specialist_0,
                1: specialist_1
            }
        )

        # Fit with skip_fit for router and other params for specialists
        ensemble.fit(
            self.X_train_scaled,
            self.y_train,
            estimator_fit_kwargs={
                'router': {'skip_fit': True},  # Skip fitting the pre-trained router
                'specialist_0': {'sample_weight': 0.5},  # Pass additional param to specialist_0
                'specialist_1': {'sample_weight': 2.0}  # Pass additional param to specialist_1
            }
        )

        # Verify that the router was preserved
        predictions = ensemble.predict_detailed(self.X_test_scaled)
        original_router_preds = loaded_router.predict(self.X_test_scaled)

        np.testing.assert_array_equal(
            predictions['route'],
            original_router_preds,
            "Router predictions should be preserved when using skip_fit=True"
        )


if __name__ == '__main__':
    unittest.main()
