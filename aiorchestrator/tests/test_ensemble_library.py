import unittest
import numpy as np
from sklearn.datasets import make_regression
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score

from aiorchestrator import VotingEnsembler, BaggingEnsembler, ChainEnsembler


class TestEnsembleLibrary(unittest.TestCase):
    """Test suite for the ensemble library based on simple_ensemble_example.py"""

    def setUp(self):
        """Set up test fixtures before each test."""
        # Create a synthetic dataset
        X, y = make_regression(
            n_samples=100,
            n_features=8,
            n_informative=5,
            noise=0.1,
            random_state=42
        )

        # Split the data into training and testing sets
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )

        # Standardize the features
        scaler = StandardScaler()
        self.X_train_scaled = scaler.fit_transform(self.X_train)
        self.X_test_scaled = scaler.transform(self.X_test)

    def test_voting_ensembler(self):
        """Test the VotingEnsembler functionality."""
        # Create individual models with their parameters
        rf = RandomForestRegressor(
            n_estimators=50,
            min_samples_split=5,
            max_features='sqrt',
            random_state=42
        )

        gb = GradientBoostingRegressor(
            n_estimators=50,
            learning_rate=0.1,
            max_depth=3,
            random_state=42
        )

        lr = LinearRegression()

        # Create a voting ensemble with custom column names
        estimators = [
            ('rf', rf),
            ('gb', gb),
            ('lr', lr)
        ]

        voting_ensemble = VotingEnsembler(estimators, column_names={
            "mean": "prediction",
            "std": "uncertainty"
        })

        # Train the ensemble
        voting_ensemble.fit(self.X_train_scaled, self.y_train)

        # Make predictions
        predictions = voting_ensemble.predict_detailed(self.X_test_scaled)

        # Test if expected keys are in the predictions
        self.assertIn("prediction", predictions)
        self.assertIn("uncertainty", predictions)
        self.assertIn("prediction_01", predictions)
        self.assertIn("prediction_02", predictions)
        self.assertIn("prediction_03", predictions)

        # Test output shapes
        self.assertEqual(predictions["prediction"].shape, (self.X_test.shape[0],))
        self.assertEqual(predictions["uncertainty"].shape, (self.X_test.shape[0],))

        # Test if standard predict method returns expected shape
        standard_preds = voting_ensemble.predict(self.X_test_scaled)
        self.assertEqual(standard_preds.shape, (self.X_test.shape[0],))

        # Test if standard predict matches the mean in detailed predictions
        np.testing.assert_array_almost_equal(standard_preds, predictions["prediction"])

    def test_bagging_ensembler(self):
        """Test the BaggingEnsembler functionality."""
        # Create a base estimator
        base_estimator = RandomForestRegressor(
            n_estimators=20,
            min_samples_split=4,
            max_features='sqrt',
            random_state=42
        )

        # Create a bagging ensemble
        bagging_ensemble = BaggingEnsembler(
            base_estimator=base_estimator,
            n_estimators=5,
            max_samples=0.8,
            bootstrap=True,
            random_state=42
        )

        # Train the ensemble
        bagging_ensemble.fit(self.X_train_scaled, self.y_train)

        # Make predictions
        predictions = bagging_ensemble.predict_detailed(self.X_test_scaled)

        # Test if expected keys are in the predictions
        self.assertIn("mean", predictions)
        self.assertIn("median", predictions)
        self.assertIn("mode", predictions)
        self.assertIn("std", predictions)
        self.assertIn("entropy", predictions)

        # Check all individual model predictions
        for i in range(1, 6):
            self.assertIn(f"prediction_{i:02d}", predictions)

        # Test output shapes
        self.assertEqual(predictions["mean"].shape, (self.X_test.shape[0],))
        self.assertEqual(predictions["std"].shape, (self.X_test.shape[0],))

        # Test if standard predict method returns expected shape
        standard_preds = bagging_ensemble.predict(self.X_test_scaled)
        self.assertEqual(standard_preds.shape, (self.X_test.shape[0],))

        # Test if standard predict matches the mean in detailed predictions
        np.testing.assert_array_almost_equal(standard_preds, predictions["mean"])

    def test_chain_ensembler(self):
        """Test the ChainEnsembler functionality."""
        # Create a router model
        router = RandomForestRegressor(n_estimators=30, random_state=42)

        # Create specialist models for different data regions
        specialists = {
            0: LinearRegression(),
            1: GradientBoostingRegressor(
                n_estimators=50,
                learning_rate=0.1,
                random_state=42
            )
        }

        # Create a fallback model
        fallback = RandomForestRegressor(n_estimators=10, random_state=42)

        # Create the ChainEnsembler
        chain_ensemble = ChainEnsembler(
            router=router,
            specialists=specialists,
            threshold=0.0,  # Split at zero (arbitrary for testing)
            fallback=fallback,
            column_names={"mean": "prediction", "route": "specialist_used"}
        )

        # Train the ensemble
        chain_ensemble.fit(self.X_train_scaled, self.y_train)

        # Make predictions
        predictions = chain_ensemble.predict_detailed(self.X_test_scaled)

        # Test if expected keys are in the predictions
        self.assertIn("prediction", predictions)
        self.assertIn("specialist_used", predictions)

        # Test output shapes
        self.assertEqual(predictions["prediction"].shape, (self.X_test.shape[0],))
        self.assertEqual(predictions["specialist_used"].shape, (self.X_test.shape[0],))

        # Test if standard predict method returns expected shape
        standard_preds = chain_ensemble.predict(self.X_test_scaled)
        self.assertEqual(standard_preds.shape, (self.X_test.shape[0],))

        # Test if standard predict matches the mean in detailed predictions
        np.testing.assert_array_almost_equal(standard_preds, predictions["prediction"])

        # Verify the specialists were used
        unique_routes = np.unique(predictions["specialist_used"])
        self.assertTrue(
            set(unique_routes).issubset({0, 1}),
            "Routes should only include values 0 and 1"
        )

    def test_combining_ensemble_types(self):
        """Test the ability to combine different ensemble types."""
        # Create a BaggingEnsembler
        bagging_rf = BaggingEnsembler(
            base_estimator=RandomForestRegressor(
                n_estimators=20,
                max_features='sqrt',
                random_state=43
            ),
            n_estimators=3,
            random_state=43
        )

        # Create individual models
        gb = GradientBoostingRegressor(
            n_estimators=50,
            learning_rate=0.1,
            random_state=44
        )
        lr = LinearRegression()

        # Create a VotingEnsembler that includes the BaggingEnsembler
        combined_estimators = [
            ('bagging_rf', bagging_rf),
            ('gb', gb),
            ('lr', lr)
        ]

        combined_ensemble = VotingEnsembler(combined_estimators)

        # Train the combined ensemble
        combined_ensemble.fit(self.X_train_scaled, self.y_train)

        # Make predictions
        try:
            predictions = combined_ensemble.predict_detailed(self.X_test_scaled)

            # Test if expected keys are in the predictions
            self.assertIn("mean", predictions)
            self.assertIn("median", predictions)
            self.assertIn("std", predictions)

            # Test if all three models' predictions are present
            self.assertIn("prediction_01", predictions)
            self.assertIn("prediction_02", predictions)
            self.assertIn("prediction_03", predictions)

            # Test output shapes
            self.assertEqual(predictions["mean"].shape, (self.X_test.shape[0],))
            self.assertEqual(predictions["std"].shape, (self.X_test.shape[0],))

            # Test if standard predict method returns expected shape
            standard_preds = combined_ensemble.predict(self.X_test_scaled)
            self.assertEqual(standard_preds.shape, (self.X_test.shape[0],))

            # Test if standard predict matches the mean in detailed predictions
            np.testing.assert_array_almost_equal(standard_preds, predictions["mean"])

        except Exception as e:
            self.fail(f"Combined ensemble failed with error: {e}")

    def test_prediction_metrics(self):
        """Test that predictions from ensemblers work with scikit-learn metrics."""
        # Setup a simple voting ensemble
        estimators = [
            ('rf', RandomForestRegressor(n_estimators=20, random_state=42)),
            ('lr', LinearRegression())
        ]

        ensemble = VotingEnsembler(estimators)
        ensemble.fit(self.X_train_scaled, self.y_train)

        # Get predictions
        preds = ensemble.predict(self.X_test_scaled)

        # Verify we can compute metrics with scikit-learn
        mse = mean_squared_error(self.y_test, preds)
        r2 = r2_score(self.y_test, preds)

        # Just check that we got valid metrics (no specific values to check)
        self.assertIsInstance(mse, float)
        self.assertIsInstance(r2, float)
        self.assertGreaterEqual(mse, 0.0)
        self.assertLessEqual(r2, 1.0)

    def test_custom_column_names(self):
        """Test that custom column names work correctly."""
        # Create a voting ensemble with custom column names
        estimators = [
            ('rf', RandomForestRegressor(n_estimators=20, random_state=42)),
            ('lr', LinearRegression())
        ]

        custom_names = {
            "mean": "average",
            "median": "middle",
            "mode": "most_common",
            "std": "spread",
            "entropy": "uncertainty",
            "predictions": "model_{nn}_output"
        }

        ensemble = VotingEnsembler(estimators, column_names=custom_names)
        ensemble.fit(self.X_train_scaled, self.y_train)

        # Get detailed predictions
        preds = ensemble.predict_detailed(self.X_test_scaled)

        # Check custom names were used
        self.assertIn("average", preds)
        self.assertIn("middle", preds)
        self.assertIn("most_common", preds)
        self.assertIn("spread", preds)
        self.assertIn("uncertainty", preds)
        self.assertIn("model_01_output", preds)
        self.assertIn("model_02_output", preds)

        # Check default names are not present
        self.assertNotIn("mean", preds)
        self.assertNotIn("median", preds)
        self.assertNotIn("prediction_01", preds)


if __name__ == '__main__':
    unittest.main()
