import unittest
import numpy as np
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LinearRegression

# Import classes to test
from aiorchestrator.bag import BaggingEnsembler
from aiorchestrator.vote import VotingEnsembler
from aiorchestrator.adapters import BaggingAdapter
from aiorchestrator.base import MultiEnsembler


class TestCombinedEnsemblers(unittest.TestCase):
    """Test suite for debugging the integration between BaggingEnsembler and VotingEnsembler."""

    def setUp(self):
        """Set up test fixtures before each test."""
        # Create simple test data
        self.X_train = np.array([[1, 2], [3, 4], [5, 6], [7, 8], [9, 10]])
        self.y_train = np.array([1, 2, 3, 4, 5])  # Simple linear relationship
        self.X_test = np.array([[2, 3], [4, 5], [6, 7], [8, 9]])

        # Create a BaggingEnsembler with 5 RandomForest models
        self.bagging_rf = BaggingEnsembler(
            base_estimator=RandomForestRegressor(
                n_estimators=10,
                max_features='sqrt',
                random_state=42
            ),
            n_estimators=5,
            random_state=42
        )

        # Create additional models
        self.gb = RandomForestRegressor(n_estimators=20, random_state=43)
        self.lr = LinearRegression()

    def test_bagging_adapter_shape(self):
        """Test that BaggingAdapter returns the expected shape."""
        # First fit the bagging ensemble
        self.bagging_rf.fit(self.X_train, self.y_train)

        # Create the adapter
        adapter = BaggingAdapter(self.bagging_rf, prediction_key="mean")

        # Predict with the adapter
        predictions = adapter.predict(self.X_test)

        # Check the shape of the output (should be a 1D array with length = number of samples)
        self.assertEqual(predictions.shape, (self.X_test.shape[0],),
                         f"BaggingAdapter should return predictions of shape ({self.X_test.shape[0],}), "
                         f"but got {predictions.shape}")

    def test_bagging_predict_detailed_shape(self):
        """Test the shape of the output from BaggingEnsembler.predict_detailed()."""
        # Fit the bagging ensemble
        self.bagging_rf.fit(self.X_train, self.y_train)

        # Get detailed predictions
        detailed_preds = self.bagging_rf.predict_detailed(self.X_test)

        # Print the keys to see what's available
        print(f"BaggingEnsembler prediction keys: {detailed_preds.keys()}")

        # Check the shape of the mean predictions (should be a 1D array with length = number of samples)
        self.assertEqual(detailed_preds["mean"].shape, (self.X_test.shape[0],),
                         f"BaggingEnsembler mean predictions should have shape ({self.X_test.shape[0],}), "
                         f"but got {detailed_preds['mean'].shape}")

        # Check the shape of individual model predictions
        for i in range(1, 6):  # 5 estimators
            key = f"prediction_{i:02d}"
            self.assertEqual(detailed_preds[key].shape, (self.X_test.shape[0],),
                             f"Individual prediction {key} should have shape ({self.X_test.shape[0],}), "
                             f"but got {detailed_preds[key].shape}")

    def test_voting_with_bagging_shape(self):
        """Test the shape of VotingEnsembler predictions with a BaggingEnsembler component."""
        # First fit all the individual models
        self.bagging_rf.fit(self.X_train, self.y_train)
        self.gb.fit(self.X_train, self.y_train)
        self.lr.fit(self.X_train, self.y_train)

        # Create a VotingEnsembler with these models
        combined_estimators = [
            ('bagging_rf', self.bagging_rf),  # BaggingEnsembler will be adapted automatically
            ('gb', self.gb),
            ('lr', self.lr)
        ]

        # Create the combined ensemble
        combined_ensemble = VotingEnsembler(combined_estimators)

        # Now check if the models were stored correctly
        self.assertEqual(len(combined_ensemble.models), 3,
                         "VotingEnsembler should have 3 models")

        # Check that the first model is a BaggingAdapter
        self.assertIsInstance(combined_ensemble.models[0][1], BaggingAdapter,
                              "First model should be wrapped in a BaggingAdapter")

        # Now test the adapter's predict function directly
        adapter = combined_ensemble.models[0][1]
        adapter_preds = adapter.predict(self.X_test)
        self.assertEqual(adapter_preds.shape, (self.X_test.shape[0],),
                         f"Adapter predictions should have shape ({self.X_test.shape[0],}), "
                         f"but got {adapter_preds.shape}")

        # Now test the actual error scenario
        try:
            # This should print to debug actual shape issue
            print(f"Testing MultiEnsembler._process_predictions directly...")

            # Create a test prediction array (3 estimators, 4 samples)
            n_estimators = len(combined_ensemble.models)
            n_samples = self.X_test.shape[0]
            preds = np.full((n_estimators, n_samples), np.nan)

            # Collect predictions from all models (this is what's happening in _process_predictions)
            for idx, (_, estimator) in enumerate(combined_ensemble.models):
                print(f"Getting predictions from estimator {idx}...")
                model_preds = estimator.predict(self.X_test)
                print(f"Estimator {idx} returned predictions of shape: {model_preds.shape}")
                preds[idx] = model_preds

            print(f"Final predictions array shape: {preds.shape}")

            # This will fail if there's a shape mismatch
            print("Testing MultiEnsembler._calculate_prediction_stats...")
            stats = MultiEnsembler._calculate_prediction_stats(combined_ensemble, preds)

            self.assertIsInstance(stats, dict, "Stats should be a dictionary")

        except Exception as e:
            self.fail(f"Error during prediction collection: {str(e)}")

    def test_full_integration(self):
        """Test the full integration workflow for nested ensemblers."""
        # Fit the bagging ensemble
        self.bagging_rf.fit(self.X_train, self.y_train)
        self.gb.fit(self.X_train, self.y_train)
        self.lr.fit(self.X_train, self.y_train)

        # Test bagging predictions before nesting
        bagging_preds = self.bagging_rf.predict_detailed(self.X_test)
        print(f"BaggingEnsembler shapes:")
        for k, v in bagging_preds.items():
            if k.startswith("prediction_"):
                print(f"  {k}: {v.shape}")

        # Create and fit the combined ensemble
        combined_estimators = [
            ('bagging_rf', self.bagging_rf),
            ('gb', self.gb),
            ('lr', self.lr)
        ]
        combined_ensemble = VotingEnsembler(combined_estimators)
        combined_ensemble.fit(self.X_train, self.y_train)

        try:
            # Try to get detailed predictions
            combined_predictions = combined_ensemble.predict_detailed(self.X_test)
            self.assertIsInstance(combined_predictions, dict,
                                  "Combined predictions should be a dictionary")

            # Check that the mean prediction has the expected shape
            self.assertEqual(combined_predictions["mean"].shape, (self.X_test.shape[0],),
                             f"Mean predictions should have shape ({self.X_test.shape[0],}), "
                             f"but got {combined_predictions['mean'].shape}")

            # If this passes, the error is fixed
            print("Integration test passed! Error has been fixed.")
        except Exception as e:
            # If we get here, the error still exists
            print(f"Error in full integration test: {str(e)}")

            # Print additional diagnostic info
            print("\nDIAGNOSTIC INFO:")
            print(f"BaggingEnsembler n_estimators: {self.bagging_rf.n_estimators}")
            print(f"Number of models in VotingEnsembler: {len(combined_ensemble.models)}")

            # Examine each model's predict function
            for i, (name, model) in enumerate(combined_ensemble.models):
                print(f"\nModel {i}: {name}")
                print(f"  Type: {type(model)}")
                if name == 'bagging_rf':
                    # Inspect the adapter
                    print(f"  Is adapter: {isinstance(model, BaggingAdapter)}")
                    print(
                        f"  Prediction key: {model.prediction_key if hasattr(model, 'prediction_key') else 'Unknown'}")

                    # Test the adapter directly
                    adapter_result = model.predict(self.X_test)
                    print(f"  Adapter predict result shape: {adapter_result.shape}")

                    # Test the underlying ensembler
                    if hasattr(model, 'ensembler'):
                        ensembler_result = model.ensembler.predict_detailed(self.X_test)
                        print(f"  Underlying ensembler keys: {list(ensembler_result.keys())}")
                        print(f"  Mean shape: {ensembler_result['mean'].shape}")

            # Re-raise the exception to fail the test
            raise


if __name__ == '__main__':
    unittest.main()
