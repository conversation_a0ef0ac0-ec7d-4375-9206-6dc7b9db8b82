{"cells": [{"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-05-22T07:12:36.591465Z", "start_time": "2025-05-22T07:12:32.177637Z"}}, "source": ["import pandas as pd\n", "src_csv = \"../data/claim_anonymized.csv\"\n", "df = pd.read_csv(src_csv)"], "outputs": [], "execution_count": 1}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-22T07:12:51.559704Z", "start_time": "2025-05-22T07:12:51.444517Z"}}, "cell_type": "code", "source": "df.head(5)", "id": "108fbd12d1232722", "outputs": [{"data": {"text/plain": ["  provider_id institution_name        case_type    claim_id  claim_net  \\\n", "0      MF4252             BDSC  Outpatient Case  **********      221.0   \n", "1      MF4252             BDSC  Outpatient Case  **********      221.0   \n", "2      MF4252             BDSC  Outpatient Case  **********       92.0   \n", "3      MF4252             BDSC  Outpatient Case  **********       92.0   \n", "4      MF4252             BDSC  Outpatient Case  **********      411.0   \n", "\n", "          unique_id        case  insurance_plan_id                plan_name  \\\n", "0  MF4252**********  **********             700000  COMPREHENSIVE 3 - ALDAR   \n", "1  MF4252**********  **********             700000  COMPREHENSIVE 3 - ALDAR   \n", "2  MF4252**********  **********             700000  COMPREHENSIVE 3 - ALDAR   \n", "3  MF4252**********  **********             700000  COMPREHENSIVE 3 - ALDAR   \n", "4  MF4252**********  **********             700000  COMPREHENSIVE 3 - ALDAR   \n", "\n", "   network_name  ... receiver_id receiver_id_desc prior_authorization  \\\n", "0  ALDAR-COMP 3  ...        A001  Daman Insurance                 NaN   \n", "1  ALDAR-COMP 3  ...        A001  Daman Insurance                 NaN   \n", "2  ALDAR-COMP 3  ...        A001  Daman Insurance                 NaN   \n", "3  ALDAR-COMP 3  ...        A001  Daman Insurance                 NaN   \n", "4  ALDAR-COMP 3  ...        A001  Daman Insurance            99957427   \n", "\n", "  submission_date processing_status accepted_type  accepted_type_reason_items  \\\n", "0      19/06/2023               NaN           NaN                         NaN   \n", "1      19/06/2023               NaN           NaN                         NaN   \n", "2      19/06/2023               NaN           NaN                         NaN   \n", "3      19/06/2023               NaN           NaN                         NaN   \n", "4      19/06/2023               NaN           NaN                         NaN   \n", "\n", "  reconciliation_claim_tag year_encounter_end_date  aio_patient_id  \n", "0                       No                    2023        AIO00001  \n", "1                       No                    2023        AIO00001  \n", "2                       No                    2023        AIO00002  \n", "3                       No                    2023        AIO00003  \n", "4                       No                    2023        AIO00004  \n", "\n", "[5 rows x 54 columns]"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>provider_id</th>\n", "      <th>institution_name</th>\n", "      <th>case_type</th>\n", "      <th>claim_id</th>\n", "      <th>claim_net</th>\n", "      <th>unique_id</th>\n", "      <th>case</th>\n", "      <th>insurance_plan_id</th>\n", "      <th>plan_name</th>\n", "      <th>network_name</th>\n", "      <th>...</th>\n", "      <th>receiver_id</th>\n", "      <th>receiver_id_desc</th>\n", "      <th>prior_authorization</th>\n", "      <th>submission_date</th>\n", "      <th>processing_status</th>\n", "      <th>accepted_type</th>\n", "      <th>accepted_type_reason_items</th>\n", "      <th>reconciliation_claim_tag</th>\n", "      <th>year_encounter_end_date</th>\n", "      <th>aio_patient_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MF4252</td>\n", "      <td>BDSC</td>\n", "      <td>Outpatient Case</td>\n", "      <td>**********</td>\n", "      <td>221.0</td>\n", "      <td>MF4252**********</td>\n", "      <td>**********</td>\n", "      <td>700000</td>\n", "      <td>COMPREHENSIVE 3 - ALDAR</td>\n", "      <td>ALDAR-COMP 3</td>\n", "      <td>...</td>\n", "      <td>A001</td>\n", "      <td>Daman Insurance</td>\n", "      <td>NaN</td>\n", "      <td>19/06/2023</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>2023</td>\n", "      <td>AIO00001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>MF4252</td>\n", "      <td>BDSC</td>\n", "      <td>Outpatient Case</td>\n", "      <td>**********</td>\n", "      <td>221.0</td>\n", "      <td>MF4252**********</td>\n", "      <td>**********</td>\n", "      <td>700000</td>\n", "      <td>COMPREHENSIVE 3 - ALDAR</td>\n", "      <td>ALDAR-COMP 3</td>\n", "      <td>...</td>\n", "      <td>A001</td>\n", "      <td>Daman Insurance</td>\n", "      <td>NaN</td>\n", "      <td>19/06/2023</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>2023</td>\n", "      <td>AIO00001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>MF4252</td>\n", "      <td>BDSC</td>\n", "      <td>Outpatient Case</td>\n", "      <td>**********</td>\n", "      <td>92.0</td>\n", "      <td>MF4252**********</td>\n", "      <td>**********</td>\n", "      <td>700000</td>\n", "      <td>COMPREHENSIVE 3 - ALDAR</td>\n", "      <td>ALDAR-COMP 3</td>\n", "      <td>...</td>\n", "      <td>A001</td>\n", "      <td>Daman Insurance</td>\n", "      <td>NaN</td>\n", "      <td>19/06/2023</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>2023</td>\n", "      <td>AIO00002</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>MF4252</td>\n", "      <td>BDSC</td>\n", "      <td>Outpatient Case</td>\n", "      <td>**********</td>\n", "      <td>92.0</td>\n", "      <td>MF4252**********</td>\n", "      <td>**********</td>\n", "      <td>700000</td>\n", "      <td>COMPREHENSIVE 3 - ALDAR</td>\n", "      <td>ALDAR-COMP 3</td>\n", "      <td>...</td>\n", "      <td>A001</td>\n", "      <td>Daman Insurance</td>\n", "      <td>NaN</td>\n", "      <td>19/06/2023</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>2023</td>\n", "      <td>AIO00003</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>MF4252</td>\n", "      <td>BDSC</td>\n", "      <td>Outpatient Case</td>\n", "      <td>**********</td>\n", "      <td>411.0</td>\n", "      <td>MF4252**********</td>\n", "      <td>**********</td>\n", "      <td>700000</td>\n", "      <td>COMPREHENSIVE 3 - ALDAR</td>\n", "      <td>ALDAR-COMP 3</td>\n", "      <td>...</td>\n", "      <td>A001</td>\n", "      <td>Daman Insurance</td>\n", "      <td>99957427</td>\n", "      <td>19/06/2023</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>2023</td>\n", "      <td>AIO00004</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 54 columns</p>\n", "</div>"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "execution_count": 2}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-22T09:44:05.846150Z", "start_time": "2025-05-22T09:44:05.757533Z"}}, "cell_type": "code", "source": "df.groupby(['code_activity', 'activity_desc']).size().reset_index('code_activity', name='count').head()", "id": "63662d9c23993219", "outputs": [{"data": {"text/plain": ["                                                   code_activity  count\n", "activity_desc                                                          \n", "Incision and drainage of abscess (eg, carbuncle...         10060      1\n", "Incision and drainage of abscess (eg, carbuncle...         10061      1\n", "POLISHING  One unit of time                                11101      1\n", "POLISHING  Each additional unit over two                   11109      1\n", "SCALING One unit of time                                   11111      1"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>code_activity</th>\n", "      <th>count</th>\n", "    </tr>\n", "    <tr>\n", "      <th>activity_desc</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Incision and drainage of abscess (eg, carbuncle, suppurative hidradenitis,</th>\n", "      <td>10060</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Incision and drainage of abscess (eg, carbuncle, suppurative hidradenitis,</th>\n", "      <td>10061</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>POLISHING  One unit of time</th>\n", "      <td>11101</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>POLISHING  Each additional unit over two</th>\n", "      <td>11109</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SCALING One unit of time</th>\n", "      <td>11111</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "execution_count": 19}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-22T07:24:32.598606Z", "start_time": "2025-05-22T07:24:32.572505Z"}}, "cell_type": "code", "source": "df.groupby(['type_activity', 'act_type_desc']).size().reset_index('type_activity', name='count').head(5)", "id": "fd8fe0450e973fd6", "outputs": [{"data": {"text/plain": ["               type_activity  count\n", "act_type_desc                      \n", "CPT                        3   3185\n", "HCPCS                      4    512\n", "Drug                       5   1154\n", "Dental                     6     78\n", "Service Code               8     69"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>type_activity</th>\n", "      <th>count</th>\n", "    </tr>\n", "    <tr>\n", "      <th>act_type_desc</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>CPT</th>\n", "      <td>3</td>\n", "      <td>3185</td>\n", "    </tr>\n", "    <tr>\n", "      <th>HCPCS</th>\n", "      <td>4</td>\n", "      <td>512</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Drug</th>\n", "      <td>5</td>\n", "      <td>1154</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Dental</th>\n", "      <td>6</td>\n", "      <td>78</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Service Code</th>\n", "      <td>8</td>\n", "      <td>69</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "execution_count": 13}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-22T07:30:19.522729Z", "start_time": "2025-05-22T07:30:19.464865Z"}}, "cell_type": "code", "source": "df.groupby(['plan_name']).size().reset_index('plan_name', name='count')", "id": "ec93738ef2d77fe0", "outputs": [{"data": {"text/plain": ["                            plan_name  count\n", "0                                ABM3      2\n", "1                            ADVANCED     27\n", "2                               BASIC    612\n", "3                               Basic    273\n", "4                              Bronze     13\n", "5             COMPREHENSIVE 3 - ALDAR   1276\n", "6                       Comprehensive      1\n", "7                  Comprehensive 1 WW      1\n", "8                     Comprehensive 2      3\n", "9               Comprehensive 2 Aldar     53\n", "10                 Comprehensive 2 WW      9\n", "11              Comprehensive 2 prime      3\n", "12                    Comprehensive 3    367\n", "13                 Comprehensive 3 WW     30\n", "14           Comprehensive 3 – Al Dar      1\n", "15                    Comprehensive 5      4\n", "16     EXCLUSIVE1PRIME–ALDAR EX USCAN     61\n", "17     EXCLUSIVE1–AL DAR WW EXC USCAN     29\n", "18             Enhanced Bronze TC1_ND      2\n", "19             Enhanced Bronze TC2 SG      2\n", "20                        Essential 5    140\n", "21                        Exclusive 1      5\n", "22                 Exclusive 1 Asia 1      4\n", "23                  Exclusive 1 Prime     78\n", "24  Exclusive 1 Prime WW Exc. USA CAN      3\n", "25     Exclusive 1 Prime WW exc. US C     30\n", "26                     Exclusive 1 WW      8\n", "27                               Gold     95\n", "28                          Gold Plus     46\n", "29                 HC COMPREHENSIVE 5      4\n", "30                                KEY      1\n", "31                               NW 1     11\n", "32                           Platinum    230\n", "33                                 RN      4\n", "34                                RN3     28\n", "35                              ROYAL      1\n", "36                         Restricted     37\n", "37                             Silver   1505"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>plan_name</th>\n", "      <th>count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ABM3</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ADVANCED</td>\n", "      <td>27</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>BASIC</td>\n", "      <td>612</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Basic</td>\n", "      <td>273</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Bronze</td>\n", "      <td>13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>COMPREHENSIVE 3 - ALDAR</td>\n", "      <td>1276</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Comprehensive</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Comprehensive 1 WW</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Comprehensive 2</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Comprehensive 2 Aldar</td>\n", "      <td>53</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Comprehensive 2 WW</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Comprehensive 2 prime</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Comprehensive 3</td>\n", "      <td>367</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>Comprehensive 3 WW</td>\n", "      <td>30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>Comprehensive 3 – <PERSON></td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>Comprehensive 5</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>EXCLUSIVE1PRIME–ALDAR EX USCAN</td>\n", "      <td>61</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>EXCLUSIVE1–AL DAR WW EXC USCAN</td>\n", "      <td>29</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>Enhanced Bronze TC1_ND</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>Enhanced Bronze TC2 SG</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>Essential 5</td>\n", "      <td>140</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>Exclusive 1</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>Exclusive 1 Asia 1</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>Exclusive 1 Prime</td>\n", "      <td>78</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>Exclusive 1 Prime WW Exc. USA CAN</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>Exclusive 1 Prime WW exc. US C</td>\n", "      <td>30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>Exclusive 1 WW</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>Gold</td>\n", "      <td>95</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>Gold Plus</td>\n", "      <td>46</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>HC COMPREHENSIVE 5</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>KEY</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>NW 1</td>\n", "      <td>11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>Platinum</td>\n", "      <td>230</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>RN</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>RN3</td>\n", "      <td>28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>ROYAL</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>Restricted</td>\n", "      <td>37</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>Silver</td>\n", "      <td>1505</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "execution_count": 17}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-22T07:26:17.141146Z", "start_time": "2025-05-22T07:26:17.121640Z"}}, "cell_type": "code", "source": "df.groupby(['provider_id', 'institution_name']).size().reset_index('provider_id', name='count').head(5)", "id": "df5bc46cc4b3260f", "outputs": [{"data": {"text/plain": ["                 provider_id  count\n", "institution_name                   \n", "BURJEEL-SHARJAH         2151     11\n", "BDSC                  MF4252   2934\n", "BMC-SHAMKHA           MF4435    162\n", "BMC-BARARI            MF4463      3\n", "LLH MC -MS            MF4498     28"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>provider_id</th>\n", "      <th>count</th>\n", "    </tr>\n", "    <tr>\n", "      <th>institution_name</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>BURJEEL-SHARJAH</th>\n", "      <td>2151</td>\n", "      <td>11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BDSC</th>\n", "      <td>MF4252</td>\n", "      <td>2934</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BMC-SHAMKHA</th>\n", "      <td>MF4435</td>\n", "      <td>162</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BMC-BARARI</th>\n", "      <td>MF4463</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>LLH MC -MS</th>\n", "      <td>MF4498</td>\n", "      <td>28</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "execution_count": 14}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-19T13:32:16.788178Z", "start_time": "2025-05-19T13:32:16.742712Z"}}, "cell_type": "code", "source": ["import os\n", "import datetime\n", "\n", "def preprocess_data(src_csv: str, output_path: str = \".\"):\n", "\n", "    timestamp_sufix = datetime.datetime.now().strftime(\"%Y-%m-%d_%H-%M-%S\")\n", "\n", "    df = pd.read_csv(src_csv)\n", "\n", "    df['start_activity_date'] = pd.to_datetime(df['start_activity_date'], format='%d/%m/%Y')\n", "    df['remittance_date'] = pd.to_datetime(df['remittance_date'], format='%d/%m/%Y')\n", "    df['encounter_start_date'] = pd.to_datetime(df['encounter_start_date'], format='%d/%m/%Y')\n", "    df['encounter_end_date'] = pd.to_datetime(df['encounter_end_date'], format='%d/%m/%Y')\n", "    df['submission_date'] = pd.to_datetime(df['submission_date'], format='%d/%m/%Y')\n", "    df['encounter_duration_days'] = (df['encounter_end_date'] - df['encounter_start_date']).dt.days + 1\n", "    \n", "    df = df[['aio_patient_id', 'institution_name', 'clinician_name', 'case_type', 'encounter_start_date', 'encounter_end_date']]\n", "    df = df.drop_duplicates()\n", "    df = df.reset_index(drop=True)\n", "    \n", "    df = df.sort_values(by=[\"aio_patient_id\", \"encounter_end_date\"])\n", "    df['next_institution'] = df.groupby('aio_patient_id')['institution_name'].shift(-1)\n", "    df['next_clinician'] = df.groupby('aio_patient_id')['clinician_name'].shift(-1)\n", "    df['next_start_date'] = df.groupby('aio_patient_id')['encounter_start_date'].shift(-1)\n", "    df = df.dropna()\n", "    df_transitions = df[\n", "        (df['institution_name'] != df['next_institution']) |\n", "        (df['clinician_name'] != df['next_clinician'])\n", "    ].copy()\n", "    \n", "    df_institution_flows = df_transitions.groupby(\n", "        ['institution_name', 'next_institution']\n", "    ).size().reset_index(name='patient_count')\n", "    \n", "    df_clinician_flows = df_transitions.groupby(\n", "        ['clinician_name', 'next_clinician']\n", "    ).size().reset_index(name='patient_count')\n", "\n", "    df_activities = df.groupby(['code_activity', 'activity_desc']).size().reset_index('code_activity', name='count')\n", "    df_activity_types = df.groupby(['type_activity', 'act_type_desc']).size().reset_index('type_activity', name='count')\n", "    df_plan_names = df.groupby(['plan_name']).size().reset_index('plan_name', name='count')\n", "    df_providers = df.groupby(['provider_id', 'institution_name']).size().reset_index('provider_id', name='count')\n", "\n", "    df_transitions.to_csv(os.path.join(output_path, f\"transitions_{timestamp_sufix}.csv\"), index=False)\n", "    df_institution_flows.to_csv(os.path.join(output_path, f\"institution_flows_{timestamp_sufix}.csv\"), index=False)\n", "    df_clinician_flows.to_csv(os.path.join(output_path, f\"clinician_flows_{timestamp_sufix}.csv\"), index=False)\n", "\n", "    df_activities.to_csv(os.path.join(output_path, f\"activities_{timestamp_sufix}.csv\"), index=False)\n", "    df_activity_types.to_csv(os.path.join(output_path, f\"activity_types_{timestamp_sufix}.csv\"), index=False)\n", "    df_plan_names.to_csv(os.path.join(output_path, f\"plan_names_{timestamp_sufix}.csv\"), index=False)\n", "    df_providers.to_csv(os.path.join(output_path, f\"providers_{timestamp_sufix}.csv\"), index=False)\n", "\n", "    return df_transitions, df_institution_flows, df_clinician_flows\n"], "id": "24161e2701e1329a", "outputs": [], "execution_count": 3}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-19T13:37:08.336246Z", "start_time": "2025-05-19T13:37:08.075653Z"}}, "cell_type": "code", "source": ["src_csv = \"../data/claim_anonymized.csv\"\n", "df_transitions, df_institution_flows, df_clinician_flows = preprocess_data(src_csv)"], "id": "2a68749552f657ea", "outputs": [], "execution_count": 14}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-19T13:33:28.159142Z", "start_time": "2025-05-19T13:33:27.977481Z"}}, "cell_type": "code", "source": "df_institution_flows.head()", "id": "3c93a5d7981f2e20", "outputs": [{"data": {"text/plain": ["  institution_name next_institution  patient_count\n", "0             BDSC             BDSC            368\n", "1       BMC-BARARI       BMC-BARARI              1\n", "2       BMC-BARARI  BURJEEL ASHAREJ              1\n", "3      BMC-SHAMKHA             BDSC              1\n", "4      BMC-SHAMKHA      BMC-SHAMKHA             37"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>institution_name</th>\n", "      <th>next_institution</th>\n", "      <th>patient_count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>BDSC</td>\n", "      <td>BDSC</td>\n", "      <td>368</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>BMC-BARARI</td>\n", "      <td>BMC-BARARI</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>BMC-BARARI</td>\n", "      <td>BURJEEL ASHAREJ</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>BMC-SHAMKHA</td>\n", "      <td>BDSC</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>BMC-SHAMKHA</td>\n", "      <td>BMC-SHAMKHA</td>\n", "      <td>37</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "execution_count": 6}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-19T13:41:39.681634Z", "start_time": "2025-05-19T13:41:39.628802Z"}}, "cell_type": "code", "source": ["import plotly.graph_objects as go\n", "\n", "def plot_sankey(df, name_col, next_col, n_threshold=0):\n", "\n", "    df = df[df['patient_count'] >= n_threshold]\n", "\n", "    labels = list(set(df[name_col]) | set(df[next_col]))\n", "    label_idx = {label: i for i, label in enumerate(labels)}\n", "\n", "    fig = go.Figure(data=[go.Sankey(\n", "        node=dict(\n", "            label=labels,\n", "            pad=15,\n", "            thickness=20\n", "        ),\n", "        link=dict(\n", "            source=[label_idx[src] for src in df[name_col]],\n", "            target=[label_idx[tgt] for tgt in df[next_col]],\n", "            value=df['patient_count']\n", "        ))])\n", "\n", "    fig.show()"], "id": "3e6ae2a6e793b37d", "outputs": [], "execution_count": 20}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-19T13:42:06.441083Z", "start_time": "2025-05-19T13:42:06.355668Z"}}, "cell_type": "code", "source": "plot_sankey(df_institution_flows, 'institution_name', 'next_institution', 1)", "id": "8c50501fdc0511a7", "outputs": [{"data": {"application/vnd.plotly.v1+json": {"data": [{"link": {"source": [4, 1, 1, 2, 2, 2, 7, 7, 5, 5, 5, 3, 3, 3, 0, 6], "target": [4, 1, 7, 4, 2, 5, 7, 3, 4, 2, 5, 2, 7, 3, 0, 6], "value": {"dtype": "i2", "bdata": "cAEBAAEAAQAlAAEADAACABcAAgAxAAIAAQBPAAUAAgA="}}, "node": {"label": ["LLH MC -MS", "BMC-BARARI", "BMC-SHAMKHA", "BURJEEL-AL AIN", "BDSC", "BURJEEL-AD", "LLH OASIS", "BURJEEL ASHAREJ"], "pad": 15, "thickness": 20}, "type": "sankey"}], "layout": {"template": {"data": {"histogram2dcontour": [{"type": "histogram2dcontour", "colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}], "choropleth": [{"type": "choropleth", "colorbar": {"outlinewidth": 0, "ticks": ""}}], "histogram2d": [{"type": "histogram2d", "colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}], "heatmap": [{"type": "heatmap", "colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}], "contourcarpet": [{"type": "contourcarpet", "colorbar": {"outlinewidth": 0, "ticks": ""}}], "contour": [{"type": "contour", "colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}], "surface": [{"type": "surface", "colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}], "mesh3d": [{"type": "mesh3d", "colorbar": {"outlinewidth": 0, "ticks": ""}}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "parcoords": [{"type": "parcoords", "line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "scatterpolargl": [{"type": "scatterpolargl", "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "scattergeo": [{"type": "scattergeo", "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "scatterpolar": [{"type": "scatterpolar", "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "scattergl": [{"type": "scattergl", "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "scatter3d": [{"type": "scatter3d", "line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "scattermap": [{"type": "scattermap", "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "scattermapbox": [{"type": "scattermapbox", "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "scatterternary": [{"type": "scatterternary", "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "scattercarpet": [{"type": "scattercarpet", "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "pie": [{"automargin": true, "type": "pie"}]}, "layout": {"autotypenumbers": "strict", "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "hovermode": "closest", "hoverlabel": {"align": "left"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"bgcolor": "#E5ECF6", "angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "ternary": {"bgcolor": "#E5ECF6", "aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"sequential": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "sequentialminus": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]]}, "xaxis": {"gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "automargin": true, "zerolinewidth": 2}, "yaxis": {"gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "automargin": true, "zerolinewidth": 2}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white", "gridwidth": 2}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white", "gridwidth": 2}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white", "gridwidth": 2}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "geo": {"bgcolor": "white", "landcolor": "#E5ECF6", "subunitcolor": "white", "showland": true, "showlakes": true, "lakecolor": "white"}, "title": {"x": 0.05}, "mapbox": {"style": "light"}}}}, "config": {"plotlyServerURL": "https://plot.ly"}}}, "metadata": {}, "output_type": "display_data"}], "execution_count": 22}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-19T13:42:36.363417Z", "start_time": "2025-05-19T13:42:36.324361Z"}}, "cell_type": "code", "source": "plot_sankey(df_clinician_flows, 'clinician_name', 'next_clinician', 3)", "id": "484d72597553d9f3", "outputs": [{"data": {"application/vnd.plotly.v1+json": {"data": [{"link": {"source": [15, 14, 19, 19, 13, 13, 13, 1, 12, 12, 3, 3, 3, 3, 2, 20, 8, 4, 18, 0, 9, 16, 17, 17, 7], "target": [6, 12, 8, 9, 3, 10, 20, 13, 14, 7, 13, 10, 2, 8, 3, 3, 3, 0, 13, 13, 18, 11, 18, 5, 12], "value": {"dtype": "i1", "bdata": "AwMDBAMDBQQDAwMDBQMFAwMDAwQDAwMDAw=="}}, "node": {"label": ["SANDHYA RANI VARREY", "MONICA SINGH CHAUHAN", "NERMINE KAMEL GOUBRAN", "MOUSTAPHA AWADA", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "RODAENA JABRA MANSOUR", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "MOUNIR HAIDER HAIDER .", "<PERSON><PERSON><PERSON>", "BASSEL SAFI", "AJIT RAVINDRA DHAKE", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "SAMI MORSI ABDALLA", "<PERSON><PERSON>", "<PERSON><PERSON>"], "pad": 15, "thickness": 20}, "type": "sankey"}], "layout": {"template": {"data": {"histogram2dcontour": [{"type": "histogram2dcontour", "colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}], "choropleth": [{"type": "choropleth", "colorbar": {"outlinewidth": 0, "ticks": ""}}], "histogram2d": [{"type": "histogram2d", "colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}], "heatmap": [{"type": "heatmap", "colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}], "contourcarpet": [{"type": "contourcarpet", "colorbar": {"outlinewidth": 0, "ticks": ""}}], "contour": [{"type": "contour", "colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}], "surface": [{"type": "surface", "colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}], "mesh3d": [{"type": "mesh3d", "colorbar": {"outlinewidth": 0, "ticks": ""}}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "parcoords": [{"type": "parcoords", "line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "scatterpolargl": [{"type": "scatterpolargl", "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "scattergeo": [{"type": "scattergeo", "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "scatterpolar": [{"type": "scatterpolar", "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "scattergl": [{"type": "scattergl", "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "scatter3d": [{"type": "scatter3d", "line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "scattermap": [{"type": "scattermap", "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "scattermapbox": [{"type": "scattermapbox", "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "scatterternary": [{"type": "scatterternary", "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "scattercarpet": [{"type": "scattercarpet", "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "pie": [{"automargin": true, "type": "pie"}]}, "layout": {"autotypenumbers": "strict", "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "hovermode": "closest", "hoverlabel": {"align": "left"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"bgcolor": "#E5ECF6", "angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "ternary": {"bgcolor": "#E5ECF6", "aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"sequential": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "sequentialminus": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]]}, "xaxis": {"gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "automargin": true, "zerolinewidth": 2}, "yaxis": {"gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "automargin": true, "zerolinewidth": 2}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white", "gridwidth": 2}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white", "gridwidth": 2}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white", "gridwidth": 2}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "geo": {"bgcolor": "white", "landcolor": "#E5ECF6", "subunitcolor": "white", "showland": true, "showlakes": true, "lakecolor": "white"}, "title": {"x": 0.05}, "mapbox": {"style": "light"}}}}, "config": {"plotlyServerURL": "https://plot.ly"}}}, "metadata": {}, "output_type": "display_data"}], "execution_count": 25}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-19T13:51:26.410995Z", "start_time": "2025-05-19T13:51:26.375592Z"}}, "cell_type": "code", "source": "df_clinician_flows", "id": "d67ea3cb619b980d", "outputs": [{"data": {"text/plain": ["                          clinician_name  \\\n", "0                            ABHAY JOSHI   \n", "1    ABOUBAKR ABDELFATTAH IBRAHIM MITKIS   \n", "2        ADHAM EL SAYED MOHAMED EL SAYED   \n", "3        ADHAM EL SAYED MOHAMED EL SAYED   \n", "4        ADHAM EL SAYED MOHAMED EL SAYED   \n", "..                                   ...   \n", "469                        ZUHAIR SHIHAB   \n", "470                     Zil E <PERSON>rbab   \n", "471                  anas <PERSON>   \n", "472                     karim <PERSON>   \n", "473                            test test   \n", "\n", "                              next_clinician  patient_count  \n", "0                          Pooja Yogesh More              1  \n", "1                  Archana <PERSON>              1  \n", "2    AMR ABDEL HAMID ABDELRAHMAN El Shawarbi              1  \n", "3                            <PERSON><PERSON><PERSON>              1  \n", "4                                 ENAS YEHYA              1  \n", "..                                       ...            ...  \n", "469                      <PERSON><PERSON><PERSON><PERSON>              1  \n", "470                      Rania <PERSON><PERSON><PERSON>              1  \n", "471                       <PERSON><PERSON><PERSON><PERSON>              2  \n", "472                   <PERSON><PERSON><PERSON>              1  \n", "473                     <PERSON><PERSON>              2  \n", "\n", "[474 rows x 3 columns]"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>clinician_name</th>\n", "      <th>next_clinician</th>\n", "      <th>patient_count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ABHAY JOSHI</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ABOUBAKR ABDELFATTAH IBRAHIM MITKIS</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ADHAM EL SAYED MOHAMED EL SAYED</td>\n", "      <td>AMR ABDEL HAMID ABDELRAHMAN El Shawarbi</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ADHAM EL SAYED MOHAMED EL SAYED</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>ADHAM EL SAYED MOHAMED EL SAYED</td>\n", "      <td>ENAS YEHYA</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>469</th>\n", "      <td>ZUHAIR SHIHAB</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>470</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>471</th>\n", "      <td>an<PERSON></td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>472</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>473</th>\n", "      <td>test test</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>474 rows × 3 columns</p>\n", "</div>"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "execution_count": 27}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-19T13:52:04.673723Z", "start_time": "2025-05-19T13:52:04.627607Z"}}, "cell_type": "code", "source": "df_institution_flows", "id": "7dbef315984db8b0", "outputs": [{"data": {"text/plain": ["   institution_name next_institution  patient_count\n", "0              BDSC             BDSC            368\n", "1        BMC-BARARI       BMC-BARARI              1\n", "2        BMC-BARARI  BURJEEL ASHAREJ              1\n", "3       BMC-SHAMKHA             BDSC              1\n", "4       BMC-SHAMKHA      BMC-SHAMKHA             37\n", "5       BMC-SHAMKHA       BURJEEL-AD              1\n", "6   BURJEEL ASHAREJ  BURJEEL ASHAREJ             12\n", "7   BURJEEL ASHAREJ   BURJEEL-AL AIN              2\n", "8        BURJEEL-AD             BDSC             23\n", "9        BURJEEL-AD      BMC-SHAMKHA              2\n", "10       BURJEEL-AD       BURJEEL-AD             49\n", "11   BURJEEL-AL AIN      BMC-SHAMKHA              2\n", "12   BURJEEL-AL AIN  BURJEEL ASHAREJ              1\n", "13   BURJEEL-AL AIN   BURJEEL-AL AIN             79\n", "14       LLH MC -MS       LLH MC -MS              5\n", "15        LLH OASIS        LLH OASIS              2"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>institution_name</th>\n", "      <th>next_institution</th>\n", "      <th>patient_count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>BDSC</td>\n", "      <td>BDSC</td>\n", "      <td>368</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>BMC-BARARI</td>\n", "      <td>BMC-BARARI</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>BMC-BARARI</td>\n", "      <td>BURJEEL ASHAREJ</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>BMC-SHAMKHA</td>\n", "      <td>BDSC</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>BMC-SHAMKHA</td>\n", "      <td>BMC-SHAMKHA</td>\n", "      <td>37</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>BMC-SHAMKHA</td>\n", "      <td>BURJEEL-AD</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>BURJEEL ASHAREJ</td>\n", "      <td>BURJEEL ASHAREJ</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>BURJEEL ASHAREJ</td>\n", "      <td>BURJEEL-AL AIN</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>BURJEEL-AD</td>\n", "      <td>BDSC</td>\n", "      <td>23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>BURJEEL-AD</td>\n", "      <td>BMC-SHAMKHA</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>BURJEEL-AD</td>\n", "      <td>BURJEEL-AD</td>\n", "      <td>49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>BURJEEL-AL AIN</td>\n", "      <td>BMC-SHAMKHA</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>BURJEEL-AL AIN</td>\n", "      <td>BURJEEL ASHAREJ</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>BURJEEL-AL AIN</td>\n", "      <td>BURJEEL-AL AIN</td>\n", "      <td>79</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>LLH MC -MS</td>\n", "      <td>LLH MC -MS</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>LLH OASIS</td>\n", "      <td>LLH OASIS</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "execution_count": 28}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-19T13:57:57.828258Z", "start_time": "2025-05-19T13:57:57.757486Z"}}, "cell_type": "code", "source": "df_transitions.head(10)", "id": "4b41164cf7c7a7a2", "outputs": [{"data": {"text/plain": ["     aio_patient_id institution_name             clinician_name  \\\n", "16         AIO00007             BDSC        SYED TAUQHEER AHMED   \n", "6          AIO00007             BDSC                 Amit Kumar   \n", "223        AIO00007             BDSC        SANDHYA RANI VARREY   \n", "336        AIO00007             BDSC        SYED TAUQHEER AHMED   \n", "537        AIO00007             BDSC        MUSTAFA AHMAD GHURA   \n", "631        AIO00007             B<PERSON><PERSON>              <PERSON><PERSON><PERSON>   \n", "11         AIO00012             BDSC               Saira Haider   \n", "1650       AIO00014       BURJEEL-<PERSON>          <PERSON>   \n", "17         AIO00017             BDSC       MONICA SINGH CHAUHAN   \n", "535        AIO00017             BDSC  <PERSON><PERSON>   \n", "\n", "            case_type encounter_start_date encounter_end_date  \\\n", "16    Outpatient Case           2023-06-15         2023-06-15   \n", "6     Outpatient Case           2023-06-16         2023-06-16   \n", "223   Outpatient Case           2023-06-20         2023-06-20   \n", "336   Outpatient Case           2023-06-21         2023-06-21   \n", "537   Outpatient Case           2023-08-21         2023-08-21   \n", "631   Outpatient Case           2023-09-23         2023-09-23   \n", "11    Outpatient Case           2023-06-16         2023-06-16   \n", "1650  Outpatient Case           2023-02-22         2023-02-22   \n", "17    Outpatient Case           2023-06-15         2023-06-15   \n", "535   Outpatient Case           2023-08-20         2023-08-20   \n", "\n", "     next_institution              next_clinician next_start_date  \n", "16               BDSC                  <PERSON><PERSON>      2023-06-16  \n", "6                BDSC         SANDHYA RANI VARREY      2023-06-20  \n", "223              BDSC         SYED TAUQHEER AHMED      2023-06-21  \n", "336              BDSC         MUSTAFA AHMAD GHURA      2023-08-21  \n", "537              B<PERSON><PERSON>      2023-09-23  \n", "631              BDSC                Saira Haider      2023-11-13  \n", "11               BDSC  Sri<PERSON>vas <PERSON>      2023-06-17  \n", "1650             BDS<PERSON>      2023-06-16  \n", "17               BDSC   <PERSON><PERSON>      2023-08-20  \n", "535              BDSC        MONICA SINGH CHAUHAN      2023-09-14  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>aio_patient_id</th>\n", "      <th>institution_name</th>\n", "      <th>clinician_name</th>\n", "      <th>case_type</th>\n", "      <th>encounter_start_date</th>\n", "      <th>encounter_end_date</th>\n", "      <th>next_institution</th>\n", "      <th>next_clinician</th>\n", "      <th>next_start_date</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>AIO00007</td>\n", "      <td>BDSC</td>\n", "      <td>SYED TAUQHEER AHMED</td>\n", "      <td>Outpatient Case</td>\n", "      <td>2023-06-15</td>\n", "      <td>2023-06-15</td>\n", "      <td>BDSC</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>2023-06-16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>AIO00007</td>\n", "      <td>BDSC</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Outpatient Case</td>\n", "      <td>2023-06-16</td>\n", "      <td>2023-06-16</td>\n", "      <td>BDSC</td>\n", "      <td>SANDHYA RANI VARREY</td>\n", "      <td>2023-06-20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>223</th>\n", "      <td>AIO00007</td>\n", "      <td>BDSC</td>\n", "      <td>SANDHYA RANI VARREY</td>\n", "      <td>Outpatient Case</td>\n", "      <td>2023-06-20</td>\n", "      <td>2023-06-20</td>\n", "      <td>BDSC</td>\n", "      <td>SYED TAUQHEER AHMED</td>\n", "      <td>2023-06-21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>336</th>\n", "      <td>AIO00007</td>\n", "      <td>BDSC</td>\n", "      <td>SYED TAUQHEER AHMED</td>\n", "      <td>Outpatient Case</td>\n", "      <td>2023-06-21</td>\n", "      <td>2023-06-21</td>\n", "      <td>BDSC</td>\n", "      <td>MUSTAFA AHMAD GHURA</td>\n", "      <td>2023-08-21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>537</th>\n", "      <td>AIO00007</td>\n", "      <td>BDSC</td>\n", "      <td>MUSTAFA AHMAD GHURA</td>\n", "      <td>Outpatient Case</td>\n", "      <td>2023-08-21</td>\n", "      <td>2023-08-21</td>\n", "      <td>BDSC</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>2023-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>631</th>\n", "      <td>AIO00007</td>\n", "      <td>BDSC</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Outpatient Case</td>\n", "      <td>2023-09-23</td>\n", "      <td>2023-09-23</td>\n", "      <td>BDSC</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>2023-11-13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>AIO00012</td>\n", "      <td>BDSC</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Outpatient Case</td>\n", "      <td>2023-06-16</td>\n", "      <td>2023-06-16</td>\n", "      <td>BDSC</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>2023-06-17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1650</th>\n", "      <td>AIO00014</td>\n", "      <td>BURJEEL-AD</td>\n", "      <td><PERSON></td>\n", "      <td>Outpatient Case</td>\n", "      <td>2023-02-22</td>\n", "      <td>2023-02-22</td>\n", "      <td>BDSC</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>2023-06-16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>AIO00017</td>\n", "      <td>BDSC</td>\n", "      <td>MONICA SINGH CHAUHAN</td>\n", "      <td>Outpatient Case</td>\n", "      <td>2023-06-15</td>\n", "      <td>2023-06-15</td>\n", "      <td>BDSC</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>2023-08-20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>535</th>\n", "      <td>AIO00017</td>\n", "      <td>BDSC</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Outpatient Case</td>\n", "      <td>2023-08-20</td>\n", "      <td>2023-08-20</td>\n", "      <td>BDSC</td>\n", "      <td>MONICA SINGH CHAUHAN</td>\n", "      <td>2023-09-14</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "execution_count": 30}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "73dcf7f13d27c7db"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}