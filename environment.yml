name: fhir-omop
channels:
  - conda-forge
  - defaults
dependencies:
  # Core Python and data processing
  - python=3.11
  - pip
  - numpy
  - pandas
  - sqlalchemy
  - psycopg2-binary
  - requests
  - python-dotenv
  - pyyaml  # For YAML configuration files in ETL pipelines
  - psutil>=5.8.0  # For system resource monitoring and performance metrics

  # Testing (minimal required)
  - pytest
  - pytest-cov

  # Data visualization
  - matplotlib
  - plotly

  # FHIR resources
  - pip:
      - fhir.resources==8.0.0
      - tabulate>=0.9.0