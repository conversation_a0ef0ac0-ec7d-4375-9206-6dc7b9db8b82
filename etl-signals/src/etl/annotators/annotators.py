import etl.decorators.decorators as deco

class AnnotatorParams(object):
    """
    Class used to set default parameters for the annotator.
    """

    def __init__(self, **params):
        """
        Initialize the class with the given parameters.

        Parameters
        ------------
        - **params: dict
            Keyword arguments containing the parameters.
        """
        for param in params:
            self.__setattr__(param, params[param])

    def set_default_params(self, default_params: dict):
        """
        Sets specific default params for each annotator.

        Parameters
        ------------
        default_params: dict
            Dictionary of default params.

        Returns
        ----------
        AnnotatorParams
            An instance of the class with the updated parameters.
        """
        
        for this_param in default_params:
            if not hasattr(self, this_param):
                setattr(self, this_param, default_params[this_param])

        return self


class Annotator(object):
    """
    Base class is the base of all the Annotators.
    """

    def __init__(self, spark_session=None, **kwargs):
        """
        Initialize the class with the given kwargs as arguments to a AnnotatorParams instance.

        Parameters
        ------------
        - **kwargs: dict
            Keyword arguments.

        Returns
        ----------
        Annotator
            An instance of the class with the initialized parameters.

        """
        self.params = AnnotatorParams(**kwargs)
        self.spark_session = spark_session


    @deco.check_type(list, "input_cols")
    def set_input_cols(self, x: list):
        """
        Set the input columns for the annotation.

        Parameters
        ------------
        x: list
            List of input columns.

        Returns
        ----------
        Annotator
            An instance of the class with the updated parameter.

        """
        
        self.params.__setattr__('input_cols', x)
        return self

    @deco.check_type(list, "output_cols")
    def set_output_cols(self, x: list):
        """
        Set the output columns for the annotation.

        Parameters
        ----------
        x: list
            List of output columns.

        Returns
        -------
        Annotator
            An instance of the class with the updated parameter.

        Raises
        ------
        Exception
            If the parameter passed is not a list of at least one element.
        """
        
        self.params.__setattr__('output_cols', x)
        return self

    def fit(self, df=None, verbose=0):
        """
        Fits the model to the provided DataFrame.

        Returns
        ----------
        Model
            The fitted Model object.
        """
        return Model(spark_session=self.spark_session, params=self.params)


class Model(object):
    """
    Base class for all Models (fitted Annotators).
    """

    def __init__(self, spark_session=None, params=None):
        """
        Initialize the class with the given parameters.

        Parameters
        ------------
        params: AnnotatorParams
            Instance of AnnotatorParams.

        Raises
        --------
        Exception
            If params is not a AnnotatorParams instance.
        """
        if isinstance(params, AnnotatorParams):
            self.params = params
        else:
            if params is None:
                self.params = AnnotatorParams()
            else:
                raise Exception(f"params must be a AnnotatorParams instance but was {params}")

        if spark_session is not None:
            self.spark_session = spark_session