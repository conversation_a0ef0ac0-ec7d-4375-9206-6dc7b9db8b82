from etl.annotators.annotators import Annota<PERSON>, <PERSON>, AnnotatorParams
from etl.decorators import decorators as deco
import etl
from pyspark.sql import functions as F
import pyspark.sql.types as T
import warnings
import pandas as pd
import numpy as np
from scipy import signal as sp


class GetLength(Annotator):
    """Gets the lengths of the arrays contained in a column.

    Example
    -------
    .. code-block:: python

         get_length = etl.annotators.signals.characterization.GetLength() \
         .set_input_col('signal') \
         .set_output_col('signal_length') \
         .set_mode('len')
         get_length_model = get_length.fit(df)
         df1 = get_length_model.transform(df)
    """

    def __init__(self, spark_session=None, **kwargs):
        """
        Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ------------
        kwargs: dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'input_cols': None,
            'output_cols': None,
            'mode': 'timediff'
        }

        self.params.set_default_params(default_params)

    @deco.check_type(str, 'mode')
    def set_mode(self, x):
        """
        Set the type of length extraction (how many elements or how much time passed).

        Parameters
        ------------
        x: str
            The type of length extraction (how many elements or how much time passed).

        Returns
        ----------
        GetLength
            The instance of the class with the updated parameter.

        Raises
        --------
        ValueError
            If x is not 'len' or 'timediff'.
        """
        if x in ['len', 'timediff']:
            self.params.mode = x
        else:
            raise ValueError("Mode must be 'len' or 'timediff'")

        return self

    def fit(self, df = None, verbose=0):
        """
        Fits the model with the given DataFrame.

        Parameters
        ------------
        df: Spark DataFrame
            DataFrame to fit the model.
        verbose: int
            Verbosity level.

        Returns
        ----------
        GetLengthModel
            Returns an instance of GetLengthModel.
        """

        return GetLengthModel(self.spark_session, self.params)


class GetLengthModel(Model):
    """
    Gets the length of the arrays contained in a column.

    Example
    -------
    .. code-block:: python

         get_length = etl.annotators.signals.characterization.GetLength() \
         .set_input_col('signal') \
         .set_output_col('signal_length') \
         .set_mode('len')
         get_length_model = get_length.fit(df)
         df1 = get_length_model.transform(df)
    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """
        Initialize the class with the given parameters.

        Parameters
        ------------
        params: AnnotatorParams

        Returns
        ----------
        None
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, df, verbose: int = 0):
        """
        Perform the calculation from the given dataframe. Returns another dataframe with the output cols added.

        Parameters
        ------------
        df: Spark DataFrame
            input DataFrame.
        verbose: int
            level of verbosity.

        Returns
        ----------
        pd.DataFrame

        Raises
        --------
        None
        """
        if self.params.mode == 'len':
            for input_col, output_col in zip(self.params.input_cols, self.params.output_cols):
                df = df.withColumn(output_col, F.size(input_col))
        else:
            for input_col, output_col in zip(self.params.input_cols, self.params.output_cols):
                df = df.withColumn(output_col,
                                   F.col(input_col)[F.size(F.col(input_col)) - 1] - F.col(input_col)[0])

        if verbose > 0:
            print(f"Getting signal length for columns: {self.params.input_cols}")

        return df


class GetSamplingHz(Annotator):
    """Checks if a timestamp array is evenly sampled and returns a column with the sampling rate in Hz.
    Needs the timestamp as input col, a time unit set and outputs its results with the desired output col name or
    by default sampling_freq_hz.

    Example
    -------
    .. code-block:: python

        get_sampling_frequency = etl.annotators.signals.characterization.GetSamplingHz()\
            .set_input_col('signal_ts')\
            .set_time_unit('ms')\
            .fit().transform(df)

    """

    def __init__(self,  spark_session=None, **kwargs):
        """
        Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ------------
        kwargs: dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'input_cols': None,
            'output_cols': ['sampling_freq_hz'],
            'time_unit': 'ms',
            'float_tolerance': 0.005
        }

        self.params.set_default_params(default_params)

    @deco.check_type(str, 'time_unit')
    def set_time_unit(self, x):
        """
        Set the time unit of the passed timestamps array.

        Parameters
        ----------
        x : str
            Input unit of the timestamps.

        Returns
        -------
        GetSamplingHz
            The instance of the class with the updated parameter.

        Raises
        ------
        Exception
            If x is not one of the following: ['ms', 's'].
        """

        if x in ['ms', 's']:
            self.params.time_unit = x
        else:
            raise Exception(f"time unit {x} must be one of the following: ['ms', 's']")
        return self

    @deco.check_type(float, 'float_tolerance')
    def set_float_tolerance(self, x):
        """
        Set the tolerance for the time between timestamps in high resolution data where floating point calculations
        errors may occur.

        Parameters
        ----------
        x : float
            Output unit.

        Returns
        -------
        GetSamplingHz
            The instance of the class with the updated parameter.

        Raises
        ------
        Exception
            If x is not a float.
        """

        self.params.float_tolerance = x
        return self

    def fit(self, df = None, verbose=0):
        """
        Fits the model with the given DataFrame.

        Parameters
        ------------
        df: Spark DataFrame
            DataFrame to fit the model.
        verbose: int
            Verbosity level.

        Returns
        ----------
        GetLengthModel
            Returns an instance of GetSamplingHzModel.
        """

        return GetSamplingHzModel(self.spark_session, self.params)


class GetSamplingHzModel(Model):
    """
    Checks if a timestamp array is evenly sampled (within an error tolerance)
    and returns a column with the sampling rate in Hz.

    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """
        Initialize the class with the given parameters.

        Parameters
        ------------
        params: AnnotatorParams

        Returns
        ----------
        None
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, df, verbose: int = 0):
        """
        Perform the calculation from the given dataframe. Returns another dataframe with the output cols added.

        Parameters
        ------------
        df: Spark DataFrame
            input DataFrame.
        verbose: int
            level of verbosity.

        Returns
        ----------
        pd.DataFrame

        Raises
        --------
        None
        """
        def _get_hz(array, input_units, tolerance):
            periods = np.diff(array)
            if np.all(np.isclose(periods, periods[0], atol=tolerance)):
                period = np.mean(periods)
                if input_units == 'ms':
                    period /= 1000
                return round(1/period)
            else:
                warnings.warn("Unstable sampling rate. Hz calculation won't be reliable. Check input timestamps."
                              "This may also be related to floating point error tolerance with high resolution data.")
                return None

        udf_get_hz = F.udf(_get_hz, T.IntegerType())

        df = df.withColumn(self.params.output_cols[0], udf_get_hz(df[self.params.input_cols[0]],
                                                                  F.lit(self.params.time_unit),
                                                                  F.lit(self.params.float_tolerance)))

        return df


class FindPulses(Annotator):
    """
    Finds pulses in a signal.

    This class is used to find pulses in a signal. It provides methods to set various parameters
    such as the splitting mode, distance between peaks, prominence, time column, and output columns.
    The class also has a fit method to fit the model with a given DataFrame.

    Example
    -------
    .. code-block:: python

        t1 = np.linspace(0, 1, 8, False)
        t2 = np.linspace(0, 1, 7, False)
        test_data = [(1, [1,2,3,5,6,7,9,10], [float(e) for e in np.sin(2*np.pi*10*t1) + np.sin(2*np.pi*20*t1)], 1000),
                     (2, [1,2,4,5,7,8,10], [float(e) for e in np.sin(2*np.pi*5*t2) + np.sin(2*np.pi*15*t2)], 1000)]
        schema = T.StructType([
            T.StructField("id", T.IntegerType(), True),
            T.StructField("ts", T.ArrayType(T.IntegerType()), True),
            T.StructField("signal", T.ArrayType(T.FloatType()), True),
            T.StructField("hertz", T.IntegerType(), True),
        ])

        df = spark_session.createDataFrame(test_data, schema)

        peaks_ppg = etl.annotators.signals.characterization.FindPulses() \\
            .set_input_col('ppg_butter') \\
            .set_output_col('ppg_peaks') \\
            .set_prominence(0.25) \\
            .set_mode('max') \\
            .set_distance(100) \\
            .fit().transform(butter_ecg_inv)

        find_pulses = etl.annotators.signals.characterization.FindPulses() \
                       .set_input_cols(['signal']) \
                       .set_output_cols(['signal_peaks', 'ts_peaks']) \
                       .set_time_col('ts') \
                       .set_prominence(0.25) \
                       .set_mode('max') \
                       .set_distance(1)

        find_pulses_model = find_pulses.fit(df)
        transformed_df = find_pulses_model.transform(df)
    """

    def __init__(self, spark_session=None, **kwargs):
        """
        Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ----------
        kwargs: dict
            Keyword arguments containing the parameters.

        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'mode': 'max',
            'distance': 100,
            'prominence': None,
            'time_col': None,
            'output_cols': []
        }
        self.params.set_default_params(default_params)

    @deco.check_type(str, 'mode')
    def set_mode(self, x):
        """
        Set the splitting mode, by max peak or min valley to differentiate pulses.

        Parameters
        ----------
        x: str
            The mode of operation, which can be 'min', 'max', 'max_slope', or 'min_slope'.

        Returns
        --------
        FindPulses
            The instance of the class with the updated parameter.

        Raises
        ------
        Exception
            If x is not 'min', 'max', 'min_slope', or 'max_slope'.
        """
        if x not in ['min', 'max', 'max_slope', 'min_slope']:
            raise Exception(f"mode must be 'max', 'min', 'max_slope' or 'min_slope' but was {x}")
        self.params.mode = x
        return self

    @deco.check_type(int, 'distance')
    def set_distance(self, x):
        """
        Set the distance to determine pulses from two adjacent peaks or valleys.

        Parameters
        ----------
        x: int
            The distance between peaks.

        Returns
        -------
        FindPulses
            The instance of the class with the updated parameter.

        Raises
        ------
        Exception
            If x is not a positive integer.
        """
        if x < 0:
            raise Exception(f"distance must be an integer >= 0 but was {x}")

        self.params.distance = x
        return self

    @deco.check_type((int, float, tuple), 'prominence')
    def set_prominence(self, x):
        """
        Set the required prominence of peaks.

        Parameters
        ----------
        x: Union[int, float, tuple]
            The required prominence of peaks.

        Returns
        -------
        FindPulses
            The instance of the class with the updated parameter.

        Raises
        ------
        Exception
            If x is not a valid prominence value.
        """
        if isinstance(x, (int, float)):
            if x < 0:
                raise Exception(f"prominence must be >= 0 but was {x}")
        if isinstance(x, tuple):
            if (x[0] < 0) | (x[1] < 0):
                raise Exception(f"prominence limits must be >= 0 but was {x}")

        self.params.prominence = x
        return self

    @deco.check_type(str, 'time_col')
    def set_time_col(self, x):
        """
        Set the column name of the time column.

        Parameters
        ----------
        x: str
            The column name of the time column.

        Returns
        -------
        FindPulses:
            The instance of the class with the updated parameter.

        Raises
        ------
        Exception:
            If x is not a string.
        """
        self.params.time_col = x
        return self

    @deco.check_type(list, 'output_cols')
    def set_output_cols(self, x):
        """
        Set the column names for the output peaks/valleys indices and values.

        Parameters
        ----------
        x: list
            The column names for the output peaks/valleys indices and values.

        Returns
        -------
        FindPulses
            The instance of the class with the updated parameter.

        Raises
        ------
        Exception
            If x is not a 2 element list.
        """
        if len(x) == 2:
            print("WARNING: The output columns must have the order:"
                  " peaks/valleys index column, peaks/valleys values column")
        self.params.output_cols = x
        return self

    def fit(self, df = None, verbose: int = 0):
        """
        Fits the model with the given DataFrame.

        Parameters
        ----------
        df: Spark DataFrame, optional
            DataFrame to fit the model.
        verbose: int, optional
            Verbosity level.

        Returns
        -------
        FindPulsesModel:
            An instance of FindPulsesModel.

        Raises
        ------
        None
        """
        return FindPulsesModel(self.spark_session, self.params)


class FindPulsesModel(Model):
    """
    This class is used to perform the max or min pulse splitting from a raw signal.

    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """
        Initialize the class with the given parameters.

        Parameters
        ------------
        params: AnnotatorParams
            AnnotatorParams instance.

        Returns
        ----------
        None

        Raises
        --------
        None
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, df, verbose: int = 0):
        """
        Perform the calculation from the given dataframe. Returns another dataframe with the output cols added.

        Parameters
        ------------
        df: Spark DataFrame
            input DataFrame.
        verbose: int
            level of verbosity.

        Returns
        ----------
        Spark DataFrame

        Raises
        --------
        Exception
            If either the time column or the output columns aren't set.
        """

        if (self.params.time_col is not None) & (self.params.output_cols is None):
            raise Exception(f"If time_col {self.params.time_col} is defined, then there must be two output columns")
        if (len(self.params.output_cols) == 2) & (self.params.time_col is None):
            raise Exception(
                f"If there is two columns defined {self.params.output_cols}, then a time_col must be defined")

        def find_peaks(signal, distance, mode, prominence):
            if (mode == 'max_slope') | (mode == 'min_slope'):
                signal = np.gradient(signal)

            if (mode == 'min') | (mode == 'min_slope'):
                signal = [e * -1 for e in signal]

            if (mode == 'max_slope') | (mode == 'min_slope'):
                # Those values that are negative are set to 0
                signal = [max(0, e) for e in signal]

            peaks, _ = sp.find_peaks(signal, distance=distance, prominence=prominence)
            return [int(e) for e in peaks]

        def get_peaks_in_time_col(peaks, time_values):
            value_peaks = []
            for this_peak in peaks:
                if this_peak < 0 or this_peak >= len(time_values):
                    raise Exception(f"Peak {this_peak} is out of range for time_col in row {time_values}")
                value_peaks.append(time_values[this_peak])
            return [int(e) for e in value_peaks]

        udf_func = F.udf(find_peaks, T.ArrayType(T.IntegerType()))

        df = df.withColumn(self.params.output_cols[0], udf_func(df[self.params.input_cols[0]],
                                                   F.lit(self.params.distance),
                                                   F.lit(self.params.mode),
                                                   F.lit(self.params.prominence)))

        if self.params.time_col is not None:
            udf_func = F.udf(get_peaks_in_time_col, T.ArrayType(T.IntegerType()))
            df = df.withColumn(self.params.output_cols[1], udf_func(df[self.params.output_cols[0]],
                                                                    df[self.params.time_col]))

        if verbose > 0:
            if self.params.output_col is not None:
                print(f"Found pulses in {self.params.input_col} in mode {self.params.mode}"
                      f"and stored at {self.params.output_col}")

            if self.params.output_cols is not None:
                print(f"Found pulses in {self.params.input_col} in mode {self.params.mode}"
                      f"and stored at {self.params.output_cols[0]} and {self.params.output_cols[1]}")
        return df


class RobustPeakFinder(Annotator):
    """
        Annotator sequence containing the robust standardization, artifact clipping and peak and valley detection for ecg
        and ppg signals.

        Example
        -------
        .. code-block:: python
            results = etl.annotators.signals.characterization.RobustPeakFinder()\
            .set_ppg_col('yData')\
            .set_ecg_col('fECG')\
            .fit().transform(group)

    """

    def __init__(self, spark_session=None, **kwargs):
        """Initialize the class with the given parameters and sets the corresponding default ones.

        Parameters
        ----------
        kwargs : dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'annotators_sequence': [],
            'models_sequence': [],
            'ecg_peak_prominence': 4,
            'ecg_valley_prominence': 2,
            'ppg_peak_prominence': 1,
            'ppg_valley_prominence': 1,
            'ecg_col': None,
            'ppg_col': None,
            'min_pulse_distance': 100
        }

        self.params.set_default_params(default_params)

    @deco.check_type(int, 'ecg_peak_prominence')
    def set_ecg_peak_prominence(self, x):
        """Prominence for the ecg peak detection.

        Parameters
        ----------
        x : int
            Prominence for the ecg peak detection.

        Returns
        -------
        RobustPeakFinder
            The instance of the class with the updated parameter.
        """
        self.params.ecg_peak_prominence = x
        return self

    @deco.check_type(int, 'ecg_valley_prominence')
    def set_valley_prominence(self,x):
        """Prominence for the ecg valley detection.

        Parameters
        ----------
        x : int
            Prominence for the ecg valley detection.

        Returns
        -------
        RobustPeakFinder
            The instance of the class with the updated parameter.
        """
        self.params.ecg_valley_prominence = x
        return self

    @deco.check_type(int, 'ppg_peak_prominence')
    def set_ppg_peak_prominence(self, x):
        """Prominence for the ppg peak detection.

        Parameters
        ----------
        x : int
            Prominence for the ppg peak detection.

        Returns
        -------
        RobustPeakFinder
            The instance of the class with the updated parameter.
        """
        self.params.ppg_peak_prominence = x
        return self

    @deco.check_type(int, 'ppg_valley_prominence')
    def set_ppg_valley_prominence(self, x):
        """Prominence for the ppg valley detection.

        Parameters
        ----------
        x : int
            Prominence for the ppg valley detection.

        Returns
        -------
        RobustPeakFinder
            The instance of the class with the updated parameter.
        """
        self.params.ppg_valley_prominence = x
        return self

    @deco.check_type(str, 'ecg_col')
    def set_ecg_col(self, x):
        """Sets the col containing the ecg.

        Parameters
        ----------
        x : str
            Col name containing the ecg.

        Returns
        -------
        RobustPeakFinder
            The instance of the class with the updated parameter.
        """
        self.params.ecg_col = x
        return self

    @deco.check_type(str, 'ppg_col')
    def set_ppg_col(self, x):
        """Sets the col containing the ppg.

        Parameters
        ----------
        x : str
            Col name containing the ppg.

        Returns
        -------
        RobustPeakFinder
            The instance of the class with the updated parameter.
        """
        self.params.ppg_col = x
        return self

    @deco.check_type(int, 'min_pulse_distance')
    def set_min_pulse_distance(self, x):
        """Minimal distance between peaks/valleys for current data.

        Parameters
        ----------
        x : int
            Minimal distance between peaks/valleys for current data.

        Returns
        -------
        RobustPeakFinder
            The instance of the class with the updated parameter.
        """
        self.params.min_pulse_distance = x
        return self

    def fit(self, df = None, verbose: int = 0):
        """Fits the model with the given DataFrame.

        Parameters
        ----------
        df : Spark DataFrame, optional
            DataFrame to fit the model.
        verbose : int, optional
            Verbosity level.

        Returns
        -------
        RobustPeakFinderModel
            Returns an instance of RobustPeakFinderModel.

        Raises
        ------
        Exception
            If the output columns are not set correctly.
        """

        input_column_names = [i for i in [self.params.ppg_col, self.params.ecg_col] if i is not None]
        std_column_names = [i + '_stand' for i in input_column_names]

        robust_std = etl.annotators.signals.standardization.StandardizeSignal() \
            .set_input_cols(input_column_names) \
            .set_output_cols(std_column_names) \
            .set_mode("by_row") \
            .set_robust(True)

        clip_signals = etl.annotators.signals.filtering.LimitStandardizedSignal() \
            .set_input_cols(std_column_names) \
            .set_output_cols(std_column_names) \
            .set_lower_limit(-10) \
            .set_upper_limit(10)

        self.params.annotators_sequence = [
            robust_std,
            clip_signals]

        if self.params.ppg_col is not None:
            peaks_ppg = etl.annotators.signals.characterization.FindPulses() \
                .set_input_cols([self.params.ppg_col + '_stand']) \
                .set_output_cols([self.params.ppg_col + '_peaks']) \
                .set_prominence(self.params.ppg_peak_prominence) \
                .set_mode('max') \
                .set_distance(self.params.min_pulse_distance)

            valleys_ppg = etl.annotators.signals.characterization.FindPulses() \
                .set_input_cols([self.params.ppg_col + '_stand']) \
                .set_output_cols([self.params.ppg_col + '_valleys']) \
                .set_prominence(self.params.ppg_valley_prominence) \
                .set_mode('min') \
                .set_distance(self.params.min_pulse_distance)

            self.params.annotators_sequence.extend([peaks_ppg, valleys_ppg])

        if self.params.ecg_col is not None:
            peaks_ecg = etl.annotators.signals.characterization.FindPulses() \
                .set_input_cols([self.params.ecg_col + '_stand']) \
                .set_output_cols([self.params.ecg_col + '_peaks']) \
                .set_prominence(self.params.ecg_peak_prominence) \
                .set_mode('max') \
                .set_distance(self.params.min_pulse_distance)

            valleys_ecg = etl.annotators.signals.characterization.FindPulses() \
                .set_input_cols([self.params.ecg_col + '_stand']) \
                .set_output_cols([self.params.ecg_col + '_valleys']) \
                .set_prominence(self.params.ecg_valley_prominence) \
                .set_mode('min') \
                .set_distance(self.params.min_pulse_distance)

            self.params.annotators_sequence.extend([peaks_ecg, valleys_ecg])

        for i in self.params.annotators_sequence:
            self.params.models_sequence.append(i.fit())

        return RobustPeakFinderModel(self.spark_session, self.params)

class RobustPeakFinderModel(Model):
    """
    Annotator sequence containing the robust standardization, artifact clipping and peak and valley detection for ecg
    and ppg signals.
    """
    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """Initialize the class with the given parameters and sets the corresponding default ones.

        Parameters
        ----------
        params : AnnotatorParams
            AnnotatorParams instance.
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, df = None, verbose: int = 0) -> pd.DataFrame:
        """Perform the calculation.

        Parameters
        ----------
        df : Spark DataFrame, optional
            DataFrame to perform the operation.
        verbose : int, optional
            Verbosity level.

        Returns
        -------
        pd.DataFrame
            DataFrame with the extracted fiducial points.
        """

        for i in self.params.models_sequence:
            df = i.transform(df)

        input_column_names = [i for i in [self.params.ppg_col, self.params.ecg_col] if i is not None]
        std_column_names = [i + '_stand' for i in input_column_names]

        df = df.drop(*std_column_names)

        return df

class PPGCaseClassifier(Annotator):
    """
    Calculates the PPG segment case classification (I/II/III) according to Abdullah's et al CnD Algorithm.

    Example
    -------
    .. code-block:: python

         ppg_case_classifier = etl.annotators.signals.characterization.PPGCaseClassifier() \\
         .set_input_col('signal')\\
         ..set_output_cols(['error_present', 'case'])\\
         ppg_case_classifier_model = ppg_case_classifier.fit(df)
         df1 = ppg_case_classifier_model.transform(df)

    """

    def __init__(self, spark_session=None, **kwargs):
        """
        Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ------------
        kwargs: dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'mov_avg_window': 5
        }

        self.params.set_default_params(default_params)

    @deco.check_type(int, 'mov_avg_window')
    def set_mov_avg_window(self, x):
        """
        Set the window for the moving averages applied to the derivatives for the classification.

        Parameters
        ------------
        x: int
            The window for the moving averages applied to the derivatives.

        Returns
        ----------
        PPGCaseClassifier
            The instance of the class with the updated parameter.

        Raises
        --------
        ValueError
            If x is not a positive integer.
        """
        if x > 0:
            self.params.mov_avg_window = x
        else:
            raise ValueError('Window for the moving average must be a positive integer.')
        return self

    def fit(self, df = None, verbose=0):
        """
        Fits the model with the given DataFrame.

        Parameters
        ------------
        df: Spark DataFrame
            DataFrame to fit the model.
        verbose: int
            Verbosity level.

        Returns
        ----------
        PPGCaseClassifierModel
            Returns an instance of PPGCaseClassifierModel.
        """

        return PPGCaseClassifierModel(self.spark_session, self.params)


class PPGCaseClassifierModel(Model):
    """
    Calculates the PPG segment case classification (I/II/III) according to Abdullah's et al CnD Algorithm.

    Example
    -------
    .. code-block:: python

         ppg_case_classifier = etl.annotators.signals.characterization.PPGCaseClassifier() \\
         .set_input_col('signal')\\
         ..set_output_cols(['error_present', 'case'])\\
         ppg_case_classifier_model = ppg_case_classifier.fit(df)
         df1 = ppg_case_classifier_model.transform(df)
    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """
        Initialize the class with the given parameters.

        Parameters
        ------------
        params: AnnotatorParams

        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, df, verbose: int = 0):
        """
        Perform the calculation from the given dataframe. Returns another dataframe with the output cols added.

        Parameters
        ------------
        df: Spark DataFrame
            input DataFrame.
        verbose: int
            level of verbosity.

        Returns
        ----------
        pd.DataFrame

        Raises
        --------
        None
        """

        def _mov_avg(signal, window):
            return np.convolve(signal, np.ones(window) / window, mode='same')

        # Compensate for signal displacement when differentiating and moving averaging.
        def _nearest_valley(signal, index):
            while index < len(signal) - 1 and signal[index + 1] < signal[index]:
                index += 1
            while index > 0 and signal[index - 1] < signal[index]:
                index -= 1
            return index

        def _classifier(segment, window):

            segment = np.asarray(segment)

            # Error check
            error_present = 0

            # Get the filtered derivatives
            vpg = _mov_avg(np.diff(segment), window)
            apg = _mov_avg(np.diff(vpg), window)
            jpg = _mov_avg(np.diff(apg), window)

            # a is the first global maxima of APG and b the first global minima.
            a = np.argmax(apg)
            b = np.argmin(apg[a:]) + a

            # y corresonds to the first minima of the VPG
            y = np.argmin(vpg)

            # z corresponds to the second maxima of the VPG or the zero crossing point of the APG after the e point.
            z = np.argmax(vpg[y:]) + y
            z = _nearest_valley(jpg, z) if 0 < z < len(jpg) else z

            # Compute the zero-crossings.
            jpg_zeros = np.where(np.diff(np.sign(jpg)))[0]

            # Check only the beginning of the signal
            jpg_zeros = [i for i in jpg_zeros if i >= (a - 1)]

            try:
                # Check if there are peaks inside the target section that changes.
                jpg_cut = jpg[b:jpg_zeros[2]]
                cut_diff = np.diff(jpg_cut)

                if cut_diff.size == 0:
                    if verbose > 0:
                        warnings.warn(
                            'PPG segment case could not be calculated due to bad wave (z before b error).'
                            ' Defaulting to 1.')
                    case = None
                    error_present = 1
                else:
                    cut_diff = _mov_avg(cut_diff, window=5)
                    cut_zeros = np.where(np.diff(np.sign(cut_diff)))[0]
                    # If there is more than one peak/valley before the first zero-crossing of JPG it must be a case II:
                    if len(cut_zeros) != 1:
                        case = 2
                    else:
                        jpg_cut = jpg[b:z]
                        cut_diff = np.diff(jpg_cut)
                        if cut_diff.size == 0:
                            if verbose > 0:
                                warnings.warn(
                                    'PPG segment case could not be calculated due to bad wave (z before b error).'
                                    ' Defaulting to 1.')
                            case = None
                            error_present = 1
                        else:
                            cut_diff = _mov_avg(cut_diff, window=5)
                            cut_zeros = np.where(np.diff(np.sign(cut_diff)))[0]
                            # If there is more than one peak/valley before the z-point (valley after d in JPG) and
                            # it's not a case II:
                            case = 1 if len(cut_zeros) == 1 else 3

            except IndexError:
                if verbose > 0:
                    warnings.warn(
                        'PPG segment case could not be calculated due to bad wave signal (zero-crossings error).'
                        ' Defaulting to NaN')
                case = None
                error_present = 1

            return int(error_present), None if case is None else int(case)

        classifier_schema = T.StructType([
            T.StructField("error_present", T.IntegerType(), True),
            T.StructField("case", T.IntegerType(), True)
        ])

        classifier_udf = F.udf(
            lambda segment, window: _classifier(segment, int(window)),
            classifier_schema
        )

        df = df.withColumn(
            "classifier_struct",
            classifier_udf(F.col(self.params.input_cols[0]), F.lit(self.params.mov_avg_window))
        )

        df = (
            df
            .withColumn(self.params.output_cols[0],
                        F.col("classifier_struct.error_present"))
            .withColumn(self.params.output_cols[1],
                        F.col("classifier_struct.case"))
            .drop("classifier_struct")
        )

        if verbose > 0:
            print(f"Final dataframe with shape {df.shape}")

        return df