from etl.annotators.annotators import Annotator, Model, AnnotatorParams
from etl.decorators import decorators as deco
from pyspark.sql import functions as F
import pyspark.sql.types as T
import warnings
import numpy as np


class StandardizeSignal(Annotator):
    """
    Standarizes a given signal.

    This class provides methods to configure the input column, output column, and
    standardization mode.

    Example
    -------
    .. code-block:: python

        standardize_ppg = etl.annotators.signals.standardization.StandardizeSignal() \
            .set_input_cols(['ppg_butter', 'ekg_butter']) \
            .set_output_cols(['ppg_butter_norm', 'ekg_butter_norm']) \
            .set_mode('by_row')

        standardize_ppg_model = standardize_ppg.fit(df)
        df = standardize_ppg_model.transform(df)

        # To find peaks you can use robust standardization (based on median and interquartile range)
        robust_standardize_ppg = etl.annotators.signals.standardization.StandardizeSignal() \
            .set_input_cols(['ppg_butter', 'ekg_butter']) \
            .set_output_cols(['ppg_butter_norm', 'ekg_butter_norm']) \
            .set_mode('by_row') \
            .set_robust(True)

        robust_standardized_ppg_model = robust_standardize_ppg.fit(df)
        df = standardize_ppg_model.transform(df)
    """

    def __init__(self, spark_session=None, **kwargs):
        """
        Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ------------
        **kwargs: dict
            keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'mode': 'by_row',
            'robust': False,
            'minmax': False,
            'fit_where_col': None,
            'fit_where_values': None,
            'mean': None,
            'std': None,
            'median': None,
            'interquartile_range': None,
            'input_cols': None,
            'minval': None,
            'maxval': None

        }

        self.params.set_default_params(default_params)

    @deco.check_type(str, 'mode')
    def set_mode(self, x):
        """
        Sets mode to by_row to perform the calculations based on each signal's mean and std, all_rows to use the mean
        and std of all the signals and selected_rows so that the standarization process only uses the mean and std of
        some rows (e.g. those flagged as training test). by_row and all_rows may incur in data leakage.
        12/2023: added the mode 'external', in which a mean and std have to be provided as a dict
        and manually set.

        Parameters
        ------------
        x: str
            The mode to set. Options are 'by_row', 'all_rows', 'selected_rows', or 'external'.

        Returns
        ----------
        StandardizeSignal:
            The instance of the class with the updated parameter.

        Raises
        --------
        Exception:
            If x is not one of the supported modes.
        """
        if x in ['by_row', 'all_rows', 'selected_rows', 'external']:
            self.params.mode = x
        else:
            raise Exception(f"mode must be 'by_row', 'all_rows', 'external', or 'selected_rows' and not {x}")

        return self

    @deco.check_type(bool, 'robust')
    def set_robust(self, x):
        """
        Sets the robust parameter to True to use the median and interquartile range for standardization
        or False (default) to use the mean and standard deviation.

        Parameters
        ------------
        x: bool
            The value to set.

        Returns
        ----------
        StandardizeSignal:
            The instance of the class with the updated parameter.
        """
        self.params.robust = x
        return self

    @deco.check_type(bool, 'minmax')
    def set_minmax(self, x):
        """
        Sets the minmax parameter to True to use the min and max for standardization
        or False (default) to use the mean and standard deviation.

        Parameters
        ------------
        x: bool
            The value to set.

        Returns
        ----------
        StandardizeSignal:
            The instance of the class with the updated parameter.
        """
        self.params.minmax = x
        return self

    @deco.check_type(str, 'fit_where_col')
    def set_fit_where_col(self, col: str):
        """
        Sets the column name to filter the DataFrame before calculating the mean and standard deviation.

        Parameters
        ------------
        col: str
            The column name to set.

        Returns
        ----------
        StandardizeSignal:
            The instance of the class with the updated parameter.
        """
        self.params.fit_where_col = col
        return self

    @deco.check_type(dict, 'mean')
    def set_mean(self, x: dict):
        """
        Sets the mean dictionary containing input column names as keys and means as values.

        Parameters
        ------------
        x: dict
            The dictionary containing {col_name: mean} key-value pairs.

        Returns
        ----------
        StandardizeSignal:
            The instance of the class with the updated parameter.
        """
        self.params.mean = x
        return self

    @deco.check_type(dict, 'std')
    def set_std(self, x: dict):
        """
        Sets the std dictionary containing input column names as keys and std as values.

        Parameters
        ------------
        x: dict
            The dictionary containing {col_name: std} key-value pairs.

        Returns
        ----------
        StandardizeSignal:
            The instance of the class with the updated parameter.
        """
        self.params.std = x
        return self

    @deco.check_type(dict, 'minval')
    def set_minval(self, x: dict):
        """
        Sets the minimal value dictionary containing input column names as keys and means as values.

        Parameters
        ------------
        x: dict
            The dictionary containing {col_name: mean} key-value pairs.

        Returns
        ----------
        StandardizeSignal:
            The instance of the class with the updated parameter.
        """
        self.params.minval = x
        return self

    @deco.check_type(dict, 'maxval')
    def set_maxval(self, x: dict):
        """
        Sets the maximal value dictionary containing input column names as keys and means as values.

        Parameters
        ------------
        x: dict
            The dictionary containing {col_name: mean} key-value pairs.

        Returns
        ----------
        StandardizeSignal:
            The instance of the class with the updated parameter.
        """
        self.params.maxval = x
        return self

    @deco.check_type(dict, 'median')
    def set_median(self, x: dict):
        """
        Sets the median dictionary containing input column names as keys and medians as values.

        Parameters
        ------------
        x: dict
            The dictionary containing {col_name: median} key-value pairs.

        Returns
        ----------
        StandardizeSignal:
            The instance of the class with the updated parameter.
        """
        self.params.median = x
        return self

    @deco.check_type(dict, 'interquartile_range')
    def set_interquartile_range(self, x: dict):
        """
        Sets the interquartile range dictionary containing input column names as keys and interquartile ranges as values.

        Parameters
        ------------
        x: dict
            The dictionary containing {col_name: interquartile_range} key-value pairs.

        Returns
        ----------
        StandardizeSignal:
            The instance of the class with the updated parameter.
        """
        self.params.interquartile_range = x
        return self

    @deco.check_type(list, 'fit_where_values')
    def set_fit_where_values(self, values: list):
        """
        Sets the values to filter the DataFrame before calculating the mean and standard deviation.

        Parameters
        ------------
        values: list
            The list of values to set.

        Returns
        ----------
        StandardizeSignal:
            The instance of the class with the updated parameter.
        """
        self.params.fit_where_values = values
        return self

    def fit(self, df = None, verbose: int = 0):
        """
        Fits the model with the given DataFrame.

        Parameters
        ------------
        df: Spark DataFrame, optional
            The DataFrame to fit the model.
        verbose: int, optional
            The level of verbosity.

        Returns
        ----------
        StandardizeSignalModel:
            An instance of StandardizeSignalModel.

        Raises
        --------
        ValueError:
            If external mean and std are set, mode must be set as external.
        ValueError:
            Must fit over a DataFrame in modes all_rows and selected_rows.
        ValueError:
            Must choose a column and list value to flag the intended rows for the calculations.
        """
        if self.params.mode != 'external' and (self.params.mean is not None or self.params.std):
            raise ValueError('If external mean and std are set, mode must be set as external.')

        # Byrow mode does not need to save a mean and std value during fit, in external mode they are set manually.
        if self.params.mode not in ['by_row', 'external']:
            if df is None:
                raise ValueError('Must fit over a DataFrame in modes all_rows and selected_rows.')

            if self.params.mode == 'selected_rows':
                # If mode is selected_rows, target df will include only the flagged value
                if self.params.fit_where_col is not None and self.params.fit_where_values is not None:
                    df = df.filter(F.col(self.params.fit_where_col).isin(self.params.fit_where_values))
                else:
                    raise ValueError('Must choose a column and list value to flag the intended rows for'
                                     ' the calculations.')

            # Calculate mean, std, median and interquartile_range
            self.params.mean = {}
            self.params.std = {}
            self.params.median = {}
            self.params.interquartile_range = {}
            self.params.minval = {}
            self.params.maxval = {}
            # Save the mean and std as it's corresponding value:
            for col in self.params.input_cols:
                df_aux = df.withColumn(col, F.explode(F.col(col)))
                df_aux = df_aux.groupby().agg(F.avg(col).alias('mean_signal'),
                                              F.stddev(col).alias('std_signal'),
                                              F.min(col).alias('min_signal'),
                                              F.max(col).alias('max_signal'),
                                              F.median(col).alias('median_signal'),
                                              F.percentile_approx(col, [0.25, 0.75]).alias('percentiles_signal')) \
                    .withColumn("interquartile_range_signal",
                                F.expr("""transform(array(percentiles_signal),x-> x[1]-x[0])""")[0])

                self.params.mean[col] = float(df_aux.collect()[0]['mean_signal'])
                self.params.std[col] = float(df_aux.collect()[0]['std_signal'])
                self.params.median[col] = float(df_aux.collect()[0]['median_signal'])
                self.params.interquartile_range[col] = float(df_aux.collect()[0]['interquartile_range_signal'])
                self.params.minval[col] = float(df_aux.collect()[0]['min_signal'])
                self.params.maxval[col] = float(df_aux.collect()[0]['max_signal'])

        return StandardizeSignalModel(self.spark_session, self.params)


class StandardizeSignalModel(Model):
    """
    Standarizes a given signal.

    This class provides methods to transform the input DataFrame and standardize the signals.

    Example
    ---------
    .. code-block:: python

        standardize_ppg = etl.annotators.signals.standardization.StandardizeSignal() \
            .set_input_cols(['ppg_butter', 'ekg_butter']) \
            .set_output_cols(['ppg_butter_norm', 'ekg_butter_norm']) \
            .set_mode('by_row')

        standardize_ppg.fit(df)
        df = standardize_ppg.transform(df)
    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """
        Initialize the class with the given parameters

        Parameters
        ------------
        params: MovanoParams
            A MovanoParams instance containing the parameters of the class.
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, df, verbose: int = 0):
        """
        Perform the operation from the given dataframe. Returns another dataframe with the output cols added.

        Parameters
        ------------
        df: Spark DataFrame
            The input DataFrame.
        verbose: int, optional
            The level of verbosity.

        Returns
        ----------
        pd.DataFrame:
            The DataFrame with the output columns added.

        Raises
        --------
        Exception:
            If input row is not a list.
        Exception:
            If another mode is set (only byrow is supported for now).

                    """

        def standardize(input_array, mean=None, std=None):

            if not isinstance(input_array, (list, np.ndarray)):
                raise Exception(f"The row is not a list! {input_array}")

            # Calculate them in place if they are not provided (as in selected_rows and all_rows modes)
            if mean is None:
                mean = float(np.array(input_array).mean())
            if std is None:
                std = float(np.array(input_array).std())

            if std != 0:
                r = [((e - mean) / std) for e in input_array]
            else:
                r = [0 for e in input_array]

            return [float(e) for e in r]

        def robust_standardize(input_array, median=None, interquartile_range=None):
            if not isinstance(input_array, (list, np.ndarray)):
                raise Exception(f"The row is not a list! {input_array}")

            # Calculate them in place if they are not provided (as in selected_rows and all_rows modes)
            if median is None:
                if np.isnan(input_array).any():
                    median = float(np.nanmedian(input_array))
                    warnings.warn("NAN VALUES DETECTED ON SIGNAL STANDARDIZATION.")
                else:
                    median = float(np.median(input_array))
            if interquartile_range is None:
                if np.isnan(input_array).any():
                    interquartile_range = float(np.nanpercentile(input_array, 75) - np.nanpercentile(input_array, 25))
                else:
                    interquartile_range = float(np.percentile(input_array, 75)
                                                - np.percentile(input_array, 25))

            if interquartile_range != 0:
                r = [((e - median) / interquartile_range) for e in input_array]
            else:
                r = [0 for e in input_array]

            return [float(e) for e in r]

        def minmax_standardize(input_array, min=None, max=None):
            if not isinstance(input_array, (list, np.ndarray)):
                raise Exception(f"The row is not a list! {input_array}")
            if min is None:
                min = float(np.min(input_array))
            if max is None:
                max = float(np.max(input_array))

            if min != max:
                array_std = [(e - min) / (max - min) for e in input_array]
            else:
                array_std = [e - min for e in input_array]
            return [float(e) for e in array_std]

        if self.params.robust and self.params.minmax:
            raise ValueError('Only minmax OR robust modes can be set as True at the same time for the standardization.')

        if self.params.robust:
            funct_to_apply = robust_standardize
            central = self.params.median
            dispersion = self.params.interquartile_range

        elif self.params.minmax:
            funct_to_apply = minmax_standardize

        else:
            funct_to_apply = standardize
            central = self.params.mean
            dispersion = self.params.std

        udf_standardize = F.udf(funct_to_apply, T.ArrayType(T.FloatType()))

        # Multiple columns:
        for in_col, out_col in zip(self.params.input_cols, self.params.output_cols):
            if verbose > 0:
                if self.params.robust:
                    print(f'Robustly standardizing {in_col} and saving at {out_col}.')
                else:
                    print(f'Standardizing {in_col} and saving at {out_col}.')

            if self.params.mode == 'by_row':
                df = df.withColumn(out_col, udf_standardize(df[in_col]))

            else:
                if not self.params.minmax:
                    df = df.withColumn(out_col, udf_standardize(df[in_col], F.lit(central[in_col]),
                                                                F.lit(dispersion[in_col])))
                else:
                    df = df.withColumn(out_col, udf_standardize(df[in_col], F.lit(self.params.minval[in_col]),
                                                                F.lit(self.params.maxval[in_col])))
        return df