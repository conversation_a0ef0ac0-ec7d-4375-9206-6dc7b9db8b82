from etl.annotators.annotators import Annotator, Model, AnnotatorParams
from etl.decorators import decorators as deco
import etl
from pyspark.sql import functions as F
import pyspark.sql.types as T
import warnings
import pandas as pd
import numpy as np
from scipy import signal as sp
import heartpy as hp

class LimitStandardizedSignal(Annotator):
    """
    This class is used to limit the values of a signal with outlying artifacts after performing robust standardization.

    Example
    --------
    .. code-block:: python

        test_data = [(1, [1., 2.1, 3.3, 4.], [2.65, 3.14, 8.47]), (2, [1., 2., 3.1, 4.1], [4.32, 5.87, 1.92, 3.72])]
        df = spark_session.createDataFrame(test_data, ["id", "list_n", "list_m"])

        # Perform robust standardization
        limit_stand = etl.annotators.signals.filtering.LimitStandardizedSignal() \
            .set_input_cols(['list_n', 'list_m']) \
            .set_output_cols(['list_n_norm', 'list_m_norm']) \
            .set_lower_limit(2) \
            .set_upper_limit(4)

        limit_stand_model = limit_stand.fit(df)
        df = limit_stand_model.transform(df)
    """

    def __init__(self, spark_session=None, **kwargs):
        """
        Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ------------
        kwargs: dict
            Keyword arguments containing the parameters.

        Returns
        ----------
        None
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'input_cols': None,
            'output_cols': None,
            'upper_limit': None,
            'lower_limit': None
        }

        self.params.set_default_params(default_params)

    @deco.check_type((int, float), 'upper_limit')
    def set_upper_limit(self, x):
        """
        Set the upper limit value.

        Parameters
        ------------
        value: int
            Condition value to set.

        Returns
        ----------
        LimitStandardizedSignal
            The instance of the class with the updated parameter.
        """
        self.params.upper_limit = x
        return self

    @deco.check_type((int, float), 'lower_limit')
    def set_lower_limit(self, x):
        """
        Set the lower limit value.

        Parameters
        ------------
        value: int
            Condition value to set.

        Returns
        ----------
        LimitStandardizedSignal
            The instance of the class with the updated parameter.
        """
        self.params.lower_limit = x
        return self



    def fit(self, df = None, verbose: int = 0):
        """
        Fits the model with the given DataFrame.

        Parameters
        ------------
        df: Spark DataFrame, optional
            DataFrame to fit the model.
        verbose: int, optional
            Verbosity level.

        Returns
        ----------
        LimitStandardizedSignalModel
            An instance of LimitStandardizedSignalModel.

        """
        return LimitStandardizedSignalModel(self.spark_session, self.params)


class LimitStandardizedSignalModel(Model):
    """
    This class is used to limit the values of a signal with outlying artifacts after performing robust standardization.

    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """
        Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ------------
        params: AnnotatorParams
            An instance of AnnotatorParams.

        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, df, verbose: int = 0):
        """
        Perform the filtering.

        Parameters
        ------------
        df: Spark DataFrame
            DataFrame to perform the calculation.
        verbose: int
            Verbosity level.

        Returns
        ----------
        Spark DataFrame
            DataFrame with the new signal added to the output_col column.
        """

        def clipper(input_array, lower_limit, upper_limit):
            output_array = np.clip(input_array, lower_limit, upper_limit)
            return [float(e) for e in output_array]

        udf_func = F.udf(clipper, T.ArrayType(T.FloatType()))

        for incolumn, outcolumn in zip(self.params.input_cols, self.params.output_cols):
            df = df.withColumn(outcolumn, udf_func(df[incolumn], F.lit(self.params.lower_limit),
                                                   F.lit(self.params.upper_limit)))

        return df


class ButterworthFilter(Annotator):
    """
    Applies a Butterworth filter to the given signal.

    Example
    -------
    .. code-block:: python

        t = np.linspace(0, 1, 1000, False)
        test_data = [(1, [float(e) for e in np.sin(2*np.pi*10*t) + np.sin(2*np.pi*20*t)], 1000),
                     (2, [float(e) for e in np.sin(2*np.pi*5*t) + np.sin(2*np.pi*15*t)], 1000)]
        schema = T.StructType([
            T.StructField("id", T.IntegerType(), True),
            T.StructField("signal", T.ArrayType(T.FloatType()), True),
            T.StructField("hertz", T.IntegerType(), True),
        ])

        df = spark_session.createDataFrame(test_data, schema)

        butter_ecg_inv = etl.annotators.signals.filtering.ButterworthFilter()  \
          .set_input_cols(["signal"])  \
          .set_output_cols(["signal_butter"])  \
          .set_sampling_freq_col('hertz')  \
          .set_order(4)  \
          .set_cutoff_low(0.5)  \
          .set_cutoff_high(6.0)  \
          .set_handlenan('zero')  \
          .fit().transform(df)
    """

    def __init__(self, spark_session=None, **kwargs):
        """
        Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ------------
        params: dict
            Dictionary containing the parameters.
        kwargs: dict
            Dictionary containing the keyword arguments.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'sampling_freq_col': None,
            'order': 4,
            'cutoff_low': 0.5,
            'cutoff_high': 6.0,
            'handlenan': 'ignore'
        }
        self.params.set_default_params(default_params)

    @deco.check_type(str, 'sampling_freq_col')
    def set_sampling_freq_col(self, x):
        """
        Set the column containing signal hertz.

        Parameters
        ------------
        x: str
            Hertz of the signal.

        Returns
        ----------
        ButterworthFilter
            The instance of the class with the updated parameter.
        """
        self.params.sampling_freq_col = x
        return self

    @deco.check_type(int, 'order')
    def set_order(self, x):
        """
        Set the filter order.

        Parameters
        ------------
        x: int
            Filter order.

        Returns
        ----------
        ButterworthFilter
            The instance of the class with the updated parameter.
        """
        self.params.order = x
        return self

    @deco.check_type((float, int), 'cutoff_low')
    def set_cutoff_low(self, x):
        """
        Set the filter cutoff for low frequencies.

        Parameters
        ------------
        x: float, int
            Filter cutoff for low frequencies.

        Returns
        ----------
        ButterworthFilter
            The instance of the class with the updated parameter.
        """
        self.params.cutoff_low = x
        return self

    @deco.check_type((float, int), 'cutoff_high')
    def set_cutoff_high(self, x):
        """
        Set the filter cutoff for high frequencies.

        Parameters
        ------------
        x: float, int
            Filter cutoff for high frequencies.

        Returns
        ----------
        ButterworthFilter
            The instance of the class with the updated parameter.
        """
        self.params.cutoff_high = x
        return self

    @deco.check_type(str, 'handlenan')
    def set_handlenan(self, x):
        """
        Set how to handle signals with NaN values.

        Parameters
        ------------
        x: str
            How to handle NaN values before applying the filter. Options are 'ignore', 'zero', and 'segment'.

        Returns
        ----------
        ButterworthFilter
            The instance of the class with the updated parameter.

        Raises
        --------
        ValueError
            If the NaN handling mode is not one of 'ignore', 'zero', or 'segment'.
        """
        if x in ['ignore', 'zero', 'segment']:
            self.params.handlenan = x
        else:
            raise ValueError('NaN handling mode should be "ignore", "zero", or "segment". See documentation.')

        return self

    def fit(self, df = None, verbose: int = 0):
        """
        Fits the model with the given DataFrame.

        Parameters
        ------------
        df: Spark DataFrame, optional
            DataFrame to fit the model.
        verbose: int, optional
            Verbosity level.

        Returns
        ----------
        ButterworthFilterModel
            An instance of ButterworthFilterModel.
        """
        return ButterworthFilterModel(self.spark_session, self.params)


class ButterworthFilterModel(Model):
    """
    Applies a Butterworth filter to the model.
    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """
        Initialize the class with the given parameters.

        Parameters
        ------------
        params: MovanoParams
            MovanoParams instance.
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, df, verbose: int = 0):
        """
        Perform the calculation from the given dataframe. Returns another dataframe with the output cols added.

            Parameters
            -----------
            df: Spark DataFrame
                Input DataFrame.
            verbose: int
                Level of verbosity.

            Returns
            --------
            Spark DataFrame
                DataFrame with the output_cols added.
        """

        def _but_filt(data_input, hertzs, handlenan):
            """
            Apply the Butterworth filter to the input data.

                Parameters
                -----------
                data_input: list
                    Input data to be filtered.
                sos_ppg: array-like
                    Second-order sections (SOS) of the Butterworth filter.
                handlenan: str
                    Method to handle NaN values. Options: 'zero', 'ignore'.

                Returns
                --------
                list
                    Filtered data.
            """
            data_input = np.array(data_input)

            sos_ppg = sp.butter(self.params.order,
                                [self.params.cutoff_low, self.params.cutoff_high],
                                btype='bp',
                                analog=False,
                                output='sos',
                                fs=hertzs)

            # Turn NaNs to zeroes if the mode is selected
            if handlenan == 'zero':
                data_input = np.nan_to_num(data_input)

            # Ignore the row if it's composed of all NaN values.
            if np.isnan(data_input).all():
                return [float(e) for e in list(data_input)]

            # Ignore the row if it has a NaN value.
            if handlenan == 'ignore' and np.isnan(data_input).any():
                return [float(e) for e in list(data_input)]

            # Segment the signal. Create a boolean mask from the data input where each element is True if not NaN or
            # false otherwise.
            mask = np.isfinite(data_input)

            # Identify the start and end of each segment when the mask transitions.
            segments = np.flatnonzero((~mask[:-1] & mask[1:]) | (mask[:-1] & ~mask[1:])) + 1

            # Flat array where every two elements define the start and end indices of a non-NaN segment
            segments = np.concatenate(([0], segments, [len(data_input)]))

            # Create a new array with the same size but full of NaN
            new_data = np.full_like(data_input, np.nan)

            # Loop through each pair of start and end indices in segments.
            for start, end in zip(segments[:-1], segments[1:]):
                if np.isfinite(data_input[start:end]).all():
                    # Apply the filter if the segment is of not-NaN values and is longer than the padding needed by
                    # ScyPy
                    if len(data_input[start:end]) >= 3 * (2 * 4 + 1):
                        new_data[start:end] = sp.sosfiltfilt(sos_ppg, data_input[start:end])
                    else:
                        new_data[start:end] = data_input[start:end]
            return [float(e) for e in list(new_data)]

        udf_func = F.udf(_but_filt, T.ArrayType(T.FloatType()))

        df = df.withColumn(self.params.output_cols[0], udf_func(df[self.params.input_cols[0]],
                                                                df[self.params.sampling_freq_col],
                                                                F.lit(self.params.handlenan)))

        if verbose > 0:
            print(f"Final dataframe with shape {df.shape}")
            print(df.iloc[0][self.params.output_col][0:10])

        return df


class HeartPyEnhanceECGPeaks(Annotator):
    """
    This class is a wraper of two methods from heartpy library: filter_signal and enhance_ecg_peaks.
    It is used to generate a ECG signal where R peaks are enhanced and noise is removed.
    It helps to better identification of R peaks in ECG signals.


    Example
    --------
    .. code-block:: python

    t = np.linspace(0, 1, 1000, False)
    test_data = [
        (1, list(range(1000)), [float(e) for e in np.sin(2 * np.pi * 10 * t) + np.sin(2 * np.pi * 20 * t)], 1000),
        (2, list(range(1000)), [float(e) for e in np.sin(2 * np.pi * 5 * t) + np.sin(2 * np.pi * 15 * t)], 1000)]
    schema = T.StructType([
        T.StructField("id", T.IntegerType(), True),
        T.StructField("ts", T.ArrayType(T.IntegerType()), True),
        T.StructField("signal", T.ArrayType(T.FloatType()), True),
        T.StructField("hertz", T.IntegerType(), True),
    ])

    df = self.spark_session.createDataFrame(test_data, schema)

    heartpy_ecg_peaks = etl.annotators.signals.filtering.HeartPyEnhanceECGPeaks() \
            .set_ecg_col('ecg_episode') \
            .set_ts_col('ts') \
            .set_filter_type('notch') \
            .set_filter_cutoff(0.05) \
            .set_sample_rate_col('sampling_freq_hz') \
            .set_aggregation('median') \
            .set_iterations(3) \
            .set_output_cols(['ecg_enhanced_r_episode'])

    heartpy_ecg_peaks_model = heartpy_ecg_peaks.fit(df)
    transformed_df = heartpy_ecg_peaks_model.transform(df)

    # Then you can use FindPulses to find the R peaks in the enhanced ECG signal with a prominence of 0.05



    """

    def __init__(self, spark_session=None, **kwargs):
        """
        Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ------------
        kwargs: dict
            Keyword arguments containing the parameters.

        Returns
        ----------
        None
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'ecg_col': None,
            'ts_col': None,
            'filter_type': 'notch',
            'filter_cutoff': 0.05,
            'sample_rate_col': None,
            'aggregation': 'median',
            'iterations': 3
        }

        self.params.set_default_params(default_params)

    @deco.check_type(str, 'ecg_col')
    def set_ecg_col(self, x):
        """
        Set the column name containing the ECG signal.

        Parameters
        ------------
        x: str
            Column name containing the ECG signal.

        Returns
        ----------
        HeartPyEnhanceECGPeaks
            An instance of the class with the updated parameter.
        """
        self.params.ecg_col = x
        return self

    @deco.check_type(str, 'ts_col')
    def set_ts_col(self, x):
        """
        Set the column name containing the timestamps.

        Parameters
        ------------
        x: str
            Column name containing the timestamps.

        Returns
        ----------
        HeartPyEnhanceECGPeaks
            An instance of the class with the updated parameter.
        """
        self.params.ts_col = x
        return self

    @deco.check_type(str, 'sample_rate_col')
    def set_sample_rate_col(self, x):
        """
        Set the column name containing the sample rate.

        Parameters
        ------------
        x: str
            Column name containing the sample rate.

        Returns
        ----------
        HeartPyEnhanceECGPeaks
            An instance of the class with the updated parameter.
        """
        self.params.sample_rate_col = x
        return self

    @deco.check_type((float, None), 'filter_cutoff')
    def set_filter_cutoff(self, x):
        """
        Set the filter cutoff to be used by heartpy.filter_signal()

        The cutoff frequency for the filter. This is the frequency at which the filter will start to attenuate the signal.
        The cutoff frequency is the frequency at which the filter will start to attenuate the signal.

        Parameters
        ------------
        x: float
            Filter cutoff to be used.

        Returns
        ----------
        HeartPyEnhanceECGPeaks
            An instance of the class with the updated parameter.
        """
        self.params.filter_cutoff = x
        return self

    @deco.check_type((str, None), 'filter_type')
    def set_filter_type(self, x):
        """
        Set the filter type to be used by heartpy.filter_signal()

        The type of filter to use. Available:
        - lowpass : a lowpass butterworth filter
        - highpass : a highpass butterworth filter
        - notch : a notch filter around specified frequency range
        both the highpass and notch filter are useful for removing baseline wander. The notch
        filter is especially useful for removing baseling wander in ECG signals.

        Note: bandpass mode from heartpy not supported as it would require a different type for set_filter_cutoff.

        Parameters
        ------------
        x: str
            Filter type to be used.

        Returns
        ----------
        HeartPyEnhanceECGPeaks
            An instance of the class with the updated parameter.
        """
        if x not in ['notch', 'lowpass', 'highpass',  None]:
            raise ValueError("Filter type must be 'notch', 'lowpass', 'highpass', 'bandpass' or None.")

        self.params.filter_type = x
        return self

    @deco.check_type(str, 'aggregation')
    def set_aggregation(self, x):
        """
        Set the aggregation method to be used by heartpy.enhance_ecg_peaks()

        how the data from the different convolutions should be aggregated.
        Can be either 'mean' or 'median'.

        Parameters
        ------------
        x: str
            Aggregation method to be used.

        Returns
        ----------
        HeartPyEnhanceECGPeaks
            An instance of the class with the updated parameter.
        """
        if x not in ['median', 'mean']:
            raise ValueError("Aggregation method must be 'median', 'mean'.")

        self.params.aggregation = x
        return self

    @deco.check_type(int, 'iterations')
    def set_iterations(self, x):
        """
        Set the number of iterations to be used by heartpy.enhance_ecg_peaks()

        How many convolutional iterations should be run. More will result in
        stronger peak enhancement, but over a certain point (usually between 12-16)
        overtones start appearing in the signal. Only increase this if the peaks
        aren't amplified enough.

        Parameters
        ------------
        x: int
            Number of iterations to be used.

        Returns
        ----------
        HeartPyEnhanceECGPeaks
            An instance of the class with the updated parameter.
        """
        self.params.iterations = x
        return self

    def fit(self, df = None, verbose: int = 0):
        """
        Fits the model with the given DataFrame.

        Parameters
        ------------
        df: Spark DataFrame, optional
            DataFrame to fit the model.
        verbose: int, optional
            Verbosity level.

        Returns
        ----------
        eartPyEnhanceECGPeaksModel
            An instance of eartPyEnhanceECGPeaksModel.

        """

        return HeartPyEnhanceECGPeaksModel(self.spark_session, self.params)

class HeartPyEnhanceECGPeaksModel(Model):
    """
    This class is a wraper of two methods from heartpy library: filter_signal and enhance_ecg_peaks.
    It is used to generate a ECG signal where R peaks are enhanced and noise is removed.
    It helps to better identification of R peaks in ECG signals.


    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """
        Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ------------
        params : AnnotatorParams
            AnnotatorParams instance.

        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, df, verbose: int = 0):
        """
        Performs the enhancement of the ECG peaks using the heartpy library.
        """

        def _hp_enhance_ecg_peaks(values_array, filter_cutoff, filter_type, sample_rate_array, aggregation, iterations):
            if filter_type is not None and filter_cutoff is not None:
                filtered_signal = hp.filter_signal(
                    values_array, cutoff=filter_cutoff,
                    sample_rate=sample_rate_array, filtertype=filter_type
                )
            else:
                filtered_signal = values_array

            r = hp.enhance_ecg_peaks(
                filtered_signal, sample_rate=sample_rate_array,
                aggregation=aggregation, iterations=iterations
            )

            if len(r) < len(values_array):
                # fill the end of r with the last value of r until it has the same length as the original signal
                r = np.append(r, np.full(len(values_array) - len(r), r[-1]))

            return [float(e) for e in list(r)]

        udf_func = F.udf(_hp_enhance_ecg_peaks, T.ArrayType(T.FloatType()))

        df = df.withColumn(self.params.output_cols[0], udf_func(df[self.params.ecg_col],
                                               F.lit(self.params.filter_cutoff),
                                               F.lit(self.params.filter_type),
                                               df[self.params.sample_rate_col],
                                               F.lit(self.params.aggregation),
                                               F.lit(self.params.iterations)))

        return df


class FilterWavesBySignal(Annotator):
    """
    This annotator takes a signal+ts, its list of wave indexes and another (or self) signal values
    and applies a filter to the waves based on the signal values returning a list of the waves indexes
    that pass the filter.

    Example
    ----------
     .. code-block:: python

        test_data = [(1, [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], [1, 1, 1, 1, 1, 4, 4, 4, 4, 4, 4], [3, 6, 10])]
        schema = T.StructType([
            T.StructField("id", T.IntegerType(), True),
            T.StructField("ts", T.ArrayType(T.IntegerType()), True),
            T.StructField("values", T.ArrayType(T.IntegerType()), True),
            T.StructField("waves_idx", T.ArrayType(T.IntegerType()), True),
        ])

        df = self.spark_session.createDataFrame(test_data, schema)

        filter_waves = etl.annotators.signals.filtering.FilterWavesBySignal() \
            .set_input_cols(['ts', 'values', 'waves_idx']) \
            .set_output_cols(['waves_idx_filtered']) \
            .set_noise_col('noise_idx') \
            .set_ts_length(5) \
            .set_threshold_minmax([0, 9999]) \
            .set_threshold_mean([0, 9999]) \
            .set_threshold_std([0.0, 0.0]) \
            .set_threshold_slope([-0.0001, 0.0001])

        filter_waves_model = filter_waves.fit(df)
        transformed_df = filter_waves_model.transform(df)
    """

    def __init__(self, spark_session=None, **kwargs):
        """
        Initialize the class with the given parameters and sets the corresponding default ones.

        Parameters
        ------------
        kwargs : dict
            Dictionary containing the keyword arguments.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'input_cols': None,
            'output_cols': None,
            'ts_length': 2000,
            'threshold_minmax': [-9999, 9999],
            'threshold_mean': [-9999, 9999],
            'threshold_std': [-9999, 9999],
            'threshold_slope': [-1, 1],
            'noise_col': 'noise_segment',
            'percent_col': 'valid_segment_percent'
        }

        self.params.set_default_params(default_params)

    @deco.check_type(int, 'ts_length')
    def set_ts_length(self, x):
        """
        Set the length of the time series to be used for the filter.

        Parameters
        ------------
        x : int
            The length of the time series.

        Returns
        ----------
        FilterWavesBySignal
            The instance of the FilterWavesBySignal class.
        """
        if x <= 0:
            raise Exception("Window must be a positive integer.")
        else:
            self.params.ts_length = x
        return self

    @deco.check_type(list, 'threshold_minmax')
    def set_threshold_minmax(self, x):
        """
        Set the threshold for the mean of the signal.

        Parameters
        ------------
        x : list
            The threshold for the mean of the signal.

        Returns
        ----------
        FilterWavesBySignal
            The instance of the FilterWavesBySignal class.
        """
        if len(x) != 2:
            raise Exception("threshold_mean must be a list of two values: [min, max]")
        else:
            self.params.threshold_minmax = x
        return self

    @deco.check_type(list, 'threshold_mean')
    def set_threshold_mean(self, x):
        """
        Set the threshold for the mean of the signal.

        Parameters
        ------------
        x : list
            The threshold for the mean of the signal.

        Returns
        ----------
        FilterWavesBySignal
            The instance of the FilterWavesBySignal class.
        """
        if len(x) != 2:
            raise Exception("threshold_mean must be a list of two values: [min, max]")
        else:
            self.params.threshold_mean = x
        return self

    @deco.check_type(list, 'threshold_std')
    def set_threshold_std(self, x):
        """
        Set the threshold for the std of the signal.

        Parameters
        ------------
        x : list
            The threshold for the std of the signal.

        Returns
        ----------
        FilterWavesBySignal
            The instance of the FilterWavesBySignal class.
        """
        if len(x) != 2:
            raise Exception("threshold_std must be a list of two values: [min, max]")
        else:
            self.params.threshold_std = x
        return self

    @deco.check_type(list, 'threshold_slope')
    def set_threshold_slope(self, x):
        """
        Set the threshold for the slope of the signal.

        Parameters
        ------------
        x : list
            The threshold for the slope of the signal.

        Returns
        ----------
        FilterWavesBySignal
            The instance of the FilterWavesBySignal class.
        """
        if len(x) != 2:
            raise Exception("threshold_slope must be a list of two values: [min, max]")
        else:
            self.params.threshold_slope = x
        return self

    @deco.check_type(str, 'input_cols')
    def set_noise_col(self, noise_col: str) -> 'FilterWavesBySignal':
        if not isinstance(noise_col, str):
            raise TypeError("noise_col must be a string.")
        self.params.noise_col = noise_col
        return self

    @deco.check_type(str, 'input_cols')
    def set_percent_col(self, percent_col: str) -> 'FilterWavesBySignal':
        if not isinstance(percent_col, str):
            raise TypeError("percent_col must be a string.")
        self.params.percent_col = percent_col
        return self

    def fit(self, df = None, verbose: int = 0):
        """
        Fits the model with the given DataFrame.

        Parameters
        ------------
        df : Spark DataFrame, optional
            DataFrame to fit the model.
        verbose : int, optional
            Verbosity level.

        Returns
        ----------
        FilterWavesBySignalModel
            An instance of FilterWavesBySignalModel.
        """
        return FilterWavesBySignalModel(self.spark_session, self.params)

class FilterWavesBySignalModel(Model):
    """
    This annotator takes a signal+ts, its list of wave indexes and another (or self) signal values
    and applies a filter to the waves based on the signal values returning a list of the waves indexes
    that pass the filter.

    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """
        Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ------------
        params: AnnotatorParams

        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, df = None, verbose: int = 0) -> pd.DataFrame:
        """Perform the calculation.

        Parameters
        ------------
        df : Spark DataFrame
            DataFrame to perform the operation.
        verbose : int
            Verbosity level.

        Returns
        ----------
        Spark DataFrame
            DataFrame with the unit change performed.
        """

        def filter_waves_by_signal(
                ts_col, values, ppg_peaks, ts_length,
                threshold_minmax, threshold_mean, threshold_std, threshold_slope
        ):
            """
            Filter waves based on signal values.

            Parameters
            -----------
            ts_col : List
                Column with the times.
            values : List
                Column with the values.
            ppg_peaks : List
                Column with the peaks/valleys.
            ts_length : int
                Length of the time series.
            threshold_minmax : tuple
                Threshold for minimum and maximum values.
            threshold_mean : tuple
                Threshold for mean value.
            threshold_std : tuple
                Threshold for standard deviation.
            threshold_slope : tuple
                Threshold for slope value.

            Returns
            --------
            list
                List of valid wave indexes.
            """

            valid_idx_list = []
            noise_idx_list = []
            values_std = np.nan
            values_mean = np.nan
            values_min = np.nan
            values_max = np.nan
            values_slope = np.nan
            values_array = np.nan
            for i in ppg_peaks:
                if i > ts_length:
                    values_array = np.array(values[i - ts_length:i])
                    is_none = [x is None for x in values_array]
                    if not np.array(is_none).any():  # if there is no None in the array
                        values_std = np.std(values_array)
                        values_mean = np.mean(values_array)
                        values_min = np.min(values_array)
                        values_max = np.max(values_array)
                        poly_coeff = np.polynomial.polynomial.polyfit(range(ts_length), values_array, 1)
                        values_slope = poly_coeff[1]

                    if not np.isnan(values_std) and not np.isnan(values_mean):
                        if (
                                values_std >= threshold_std[0] and values_std <= threshold_std[1]
                                and values_mean >= threshold_mean[0] and values_mean <= threshold_mean[1]
                                and values_min >= threshold_minmax[0] and values_max <= threshold_minmax[1]
                                and values_slope >= threshold_slope[0] and values_slope <= threshold_slope[1]
                        ):
                            valid_idx_list.append(i)
                        else:
                            noise_idx_list.append(i)

            return [int(e) for e in valid_idx_list], [int(e) for e in noise_idx_list]

        schema = T.StructType([
            T.StructField(self.params.output_cols[0], T.ArrayType(T.IntegerType()), False),
            T.StructField(self.params.noise_col, T.ArrayType(T.IntegerType()), False)
        ])

        udf_func = F.udf(filter_waves_by_signal, schema)

        df = df.withColumn("output", udf_func(df[self.params.input_cols[0]],
                                              df[self.params.input_cols[1]],
                                              df[self.params.input_cols[2]],
                                              F.lit(self.params.ts_length),
                                              F.lit(self.params.threshold_minmax),
                                              F.lit(self.params.threshold_mean),
                                              F.lit(self.params.threshold_std),
                                              F.lit(self.params.threshold_slope))).select(df["*"], "output.*")

        # Calculate the percentage of waves that don't pass the filter
        total_segments = df.select("*", F.size(self.params.input_cols[2]).alias("segments_length")) \
            .agg(F.sum("segments_length")).collect()[0][0]
        filtered_segments = df.select("*", F.size(self.params.output_cols[0]).alias("output_length")) \
            .agg(F.sum("output_length")).collect()[0][0]
        percentage = (filtered_segments / total_segments) * 100 if total_segments > 0 else 0

        if verbose > 0:
            print(f"Filtering Waves By Signal: {self.params.input_cols[1]}")
            print(f"Total segments:  {total_segments}")
            print(f"Valid Segments:  {filtered_segments} ({percentage:.2f}%)")
            print(f"Noise Segments:  {total_segments - filtered_segments}  ({100 - percentage:.2f}%)")

        return df


class MultiFilteringSignal(Annotator):
    """
    Annotator sequence containing the filtering and overlapping control for cut-indices in signals. It needs values and
    corresponding timestamps as inputs. ABP, HR and PAT filters available.

    Example
    -------
    .. code-block:: python


    """

    def __init__(self, spark_session=None, **kwargs):
        """Initialize the class with the given parameters and sets the corresponding default ones.

        Parameters
        ----------
        kwargs : dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'annotators_sequence': [],
            'models_sequence': [],
            'filter_sequence': None,
            'ts_col': None,
            'hr_col': None,
            'abp_col': None,
            'pat_col': None,
            'indices_col': None,
            'pat_thresholds': None,
            'abp_thresholds': None,
            'hr_thresholds': None,
            'segment_length': None,
            'output_cols': ['merged_indices']
        }

        self.params.set_default_params(default_params)

    @deco.check_type(list, 'filter_sequence')
    def set_filter_sequence(self, x):
        """
        Set the sequence of filters to be applied. Supported features are abp, hr and pat in any order.

        Parameters
        ------------
        x: list
            Ordered list with the filters to be applied. Supported features are abp, hr and pat in any order.

        Returns
        ----------
        MultiFilteringSignal
            An instance of the annotator with updated parameters.

        Raises
        ------
        ValueError
            If any of the filtered selected are not abp, hr or pat.
        """
        if any([i not in ['abp', 'hr', 'pat'] for i in x]):
            raise ValueError("Supported features are abp, hr and pat in any order.")

        self.params.filter_sequence = x
        return self

    @deco.check_type(str, 'ts_col')
    def set_ts_col(self, x):
        """
        Set the column name containing the timestamps.

        Parameters
        ------------
        x: str
            Column name containing the timestamps.

        Returns
        ----------
        MultiFilteringSignal
            An instance of the annotator with updated parameters.
        """
        self.params.ts_col = x
        return self

    @deco.check_type(str, 'hr_col')
    def set_hr_col(self, x):
        """
        Set the column name containing the hr.

        Parameters
        ------------
        x: str
            Column name containing the hr.

        Returns
        ----------
        MultiFilteringSignal
            An instance of the annotator with updated parameters.
        """
        self.params.hr_col = x
        return self

    @deco.check_type(str, 'pat_col')
    def set_pat_col(self, x):
        """
        Set the column name containing the pat.

        Parameters
        ------------
        x: str
            Column name containing the pat.

        Returns
        ----------
        MultiFilteringSignal
            An instance of the annotator with updated parameters.
        """
        self.params.pat_col = x
        return self

    @deco.check_type(str, 'abp_col')
    def set_abp_col(self, x):
        """
        Set the column name containing the abp.

        Parameters
        ------------
        x: str
            Column name containing the abp.

        Returns
        ----------
        MultiFilteringSignal
            An instance of the annotator with updated parameters.
        """
        self.params.abp_col = x
        return self

    @deco.check_type(str, 'indices_col')
    def set_indices_col(self, x):
        """
        Set the column name containing the indices to be filtered.

        Parameters
        ------------
        x: str
            Column name containing the indices to be filtered.

        Returns
        ----------
        MultiFilteringSignal
            An instance of the annotator with updated parameters.
        """
        self.params.indices_col = x
        return self

    @deco.check_type(dict, 'abp_thresholds')
    def set_abp_thresholds(self, x):
        """
        Set the filter thresholds to be applied.

        Parameters
        ------------
        x: dict
            Dict with the thresholds to be applied.

        Returns
        ----------
        MultiFilteringSignal
            An instance of the annotator with updated parameters.
        """
        self.params.abp_thresholds = x
        return self

    @deco.check_type(dict, 'hr_thresholds')
    def set_hr_thresholds(self, x):
        """
        Set the filter thresholds to be applied.

        Parameters
        ------------
        x: dict
            Dict with the thresholds to be applied.

        Returns
        ----------
        MultiFilteringSignal
            An instance of the annotator with updated parameters.
        """
        self.params.hr_thresholds = x
        return self

    @deco.check_type(dict, 'pat_thresholds')
    def set_pat_thresholds(self, x):
        """
        Set the filter thresholds to be applied.

        Parameters
        ------------
        x: dict
            Dict with the thresholds to be applied.

        Returns
        ----------
        MultiFilteringSignal
            An instance of the annotator with updated parameters.
        """
        self.params.pat_thresholds = x
        return self

    @deco.check_type(int, 'segment_length')
    def set_segment_length(self, x):
        """
        Set the desired number of elements that will be considered of the same segment, to avoid overlapping "good"
        indices.

        Parameters
        ------------
        x: int
            Number of elements that will be considered of the same segment, to avoid overlapping "good"
        indices.

        Returns
        ----------
        MultiFilteringSignal
            An instance of the annotator with updated parameters.
        """
        self.params.segment_length = x
        return self

    def fit(self, df = None, verbose: int = 0):
        """Fits the model with the given DataFrame.

        Parameters
        ----------
        df : Spark DataFrame, optional
            DataFrame to fit the model.
        verbose : int, optional
            Verbosity level.

        Returns
        -------
        MultiFilteringSignalModel
            Returns an instance of MultiFilteringSignalModel.

        Raises
        ------
        Exception
            If the output columns are not set correctly.
        """

        self.params.annotator_sequence = []

        for filter_number in range(len(self.params.filter_sequence)):

            # Create the pair of annotators for a given filter
            filter_annot = etl.annotators.signals.filtering.FilterWavesBySignal()
            merger_annot = etl.annotators.signals.rearrange.MergeOverlapsByIndex()

            # Setters not dependant on filter type
            filter_annot = filter_annot.set_ts_length(self.params.segment_length)
            merger_annot = merger_annot.set_output_cols([self.params.output_cols[0]])
            merger_annot = merger_annot.set_ts_length(self.params.segment_length)

            if self.params.filter_sequence[filter_number] == 'abp':
                # Set the output of the filter annotator and input of the merger annotator
                filter_annot = filter_annot.set_output_cols(['filtered_indices_abp'])
                merger_annot = merger_annot.set_input_cols(['filtered_indices_abp'])

                # The first one takes the indices as input, the others take the merged version after filtering
                if filter_number == 0:
                    filter_annot = filter_annot.set_input_cols([
                        self.params.ts_col,
                        self.params.abp_col,
                        self.params.indices_col
                    ])
                else:
                    filter_annot = filter_annot.set_input_cols([
                        self.params.ts_col,
                        self.params.abp_col,
                        self.params.output_cols[0]
                    ])

                # Set the thresholds
                filter_annot = filter_annot.set_threshold_std(self.params.abp_thresholds['std_threshold'])
                filter_annot = filter_annot.set_threshold_mean(self.params.abp_thresholds['mean_threshold'])
                filter_annot = filter_annot.set_threshold_minmax(self.params.abp_thresholds['range_threshold'])
                filter_annot = filter_annot.set_threshold_slope(self.params.abp_thresholds['slope_threshold'])

            elif self.params.filter_sequence[filter_number] == 'hr':
                filter_annot = filter_annot.set_output_cols(['filtered_indices_hr'])
                merger_annot = merger_annot.set_input_cols(['filtered_indices_hr'])

                if filter_number == 0:
                    filter_annot = filter_annot.set_input_cols([
                        self.params.ts_col,
                        self.params.hr_col,
                        self.params.indices_col
                    ])
                else:
                    filter_annot = filter_annot.set_input_cols([
                        self.params.ts_col,
                        self.params.hr_col,
                        self.params.output_cols[0]
                    ])

                filter_annot = filter_annot.set_threshold_std(self.params.hr_thresholds['std_threshold'])
                filter_annot = filter_annot.set_threshold_mean(self.params.hr_thresholds['mean_threshold'])
                filter_annot = filter_annot.set_threshold_minmax(self.params.hr_thresholds['range_threshold'])
                filter_annot = filter_annot.set_threshold_slope(self.params.hr_thresholds['slope_threshold'])

            elif self.params.filter_sequence[filter_number] == 'pat':
                filter_annot = filter_annot.set_output_cols(['filtered_indices_pat'])
                merger_annot = merger_annot.set_input_cols(['filtered_indices_pat'])

                if filter_number == 0:
                    filter_annot = filter_annot.set_input_cols([
                        self.params.ts_col,
                        self.params.pat_col,
                        self.params.indices_col
                    ])
                else:
                    filter_annot = filter_annot.set_input_cols([
                        self.params.ts_col,
                        self.params.pat_col,
                        self.params.output_cols[0]
                    ])

                filter_annot = filter_annot.set_threshold_std(self.params.pat_thresholds['std_threshold'])
                filter_annot = filter_annot.set_threshold_mean(self.params.pat_thresholds['mean_threshold'])
                filter_annot = filter_annot.set_threshold_minmax(self.params.pat_thresholds['range_threshold'])
                filter_annot = filter_annot.set_threshold_slope(self.params.pat_thresholds['slope_threshold'])

            self.params.annotator_sequence.append(filter_annot)
            self.params.annotator_sequence.append(merger_annot)

        for i in self.params.annotator_sequence:
            self.params.models_sequence.append(i.fit())

        return MultiFilteringSignalModel(self.spark_session, self.params)

class MultiFilteringSignalModel(Model):
    """
    Annotator sequence containing the filtering and overlapping control for cut-indices in signals. It needs values and
    corresponding timestamps as inputs. ABP, HR and PAT filters available.
    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """Initialize the class with the given parameters and sets the corresponding default ones.

        Parameters
        ----------
        params : AnnotatorParams
            AnnotatorParams instance.
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, df = None, verbose: int = 0) -> pd.DataFrame:
        """Perform the calculation.

        Parameters
        ----------
        df : Spark DataFrame, optional
            DataFrame to perform the operation.
        verbose : int, optional
            Verbosity level.

        Returns
        -------
        Spark DataFrame
            DataFrame with the extracted fiducial points.
        """

        for i in range(len(self.params.models_sequence)):
            df = self.params.models_sequence[i].transform(df, verbose=verbose)

        df = df.withColumn(self.params.output_cols[0], F.col('filtered_indices_' + self.params.filter_sequence[-1]))
        std_column_names = ['filtered_indices_' + i for i in self.params.filter_sequence]

        df = df.drop(*std_column_names)
        return df