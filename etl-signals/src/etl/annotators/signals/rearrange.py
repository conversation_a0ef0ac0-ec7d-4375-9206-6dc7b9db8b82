from etl.annotators.annotators import Annota<PERSON>, <PERSON>, AnnotatorParams
from etl.decorators import decorators as deco
from pyspark.sql import functions as F
from pyspark.sql import types as T
import numpy as np
import warnings
import pandas as pd
from scipy import interpolate


class ExplodeSignal(Annotator):
    """
    Annotator to explode a signal into a DataFrame with one row per signal segment.

    This class splits a signal into multiple segments based on specified parameters such as segment length,
    cuts column, split method, and interpolator kind.

    The output DataFrame will contain one row per segment, with the original signal columns repeated

    """

    def __init__(self, spark_session=None, **kwargs):
        """
        Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ------------
        kwargs: dict
            keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'segment_length': None,
            'cuts_col': None,
            'split_method': 'all_indices',
        }
        self.params.set_default_params(default_params)

    @deco.check_type(str, 'cuts_col')
    def set_cuts_col(self, x):
        """
        Set cuts column.

        Parameters
        ------------
        x: str
            cuts column name.

        Returns
        ----------
        ExplodeSignal:
            the instance of the class with the updated parameter

        Raises
        --------
        Exception:
            If x is not a string.
        """
        self.params.cuts_col = x
        return self

    @deco.check_type(str, 'split_method')
    def set_split_method(self, x):
        """
        Set the split method.

        Parameters
        ------------
        x: str
            split method.

        Returns
        ----------
        ExplodeSignal:
            the instance of the class with the updated parameter

        Raises
        --------
        Exception:
            If x is not a string.
        """
        if x in ['defined_length', 'all_indices']:
            self.params.split_method = x
        else:
            raise Exception(f"split_method must be 'defined_length', 'all_indices and not {x}.")
        return self

    @deco.check_type(int, 'segment_length')
    def set_segment_length(self, x):
        """
        Set segment_length to decide where to cut.

        Parameters
        ------------
        x: int
            length of the segment that need extraction.

        Returns
        ----------
        ExplodeSignal:
            the instance of the class with the updated parameter

        Raises
        --------
        Exception:
            If x is not an int.
        """
        self.params.segment_length = x
        return self

    def fit(self, df=None, verbose: int = 0):
        """
        Fits the model with the given DataFrame.

        Parameters
        ------------
        df: MetaDataFrame, optional
            DataFrame to fit the model.
        verbose: int, optional
            verbosity level.

        Returns
        ----------
        ExplodeSignalModel:
            an instance of ExplodeSignalModel.
        """
        return ExplodeSignalModel(self.spark_session, self.params)


class ExplodeSignalModel(Model):
    """
    Model to explode a signal into a DataFrame with one row per signal segment.

    This class performs the actual splitting of the signal into segments using the parameters set in the parent class.

    The output DataFrame will contain one row per segment, with the output columns repeated for each segment.
    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """
        Initialize the class with the given parameters

        Parameters
        ------------
        params: AnnotatorParams
            AnnotatorParams instance.
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, df, verbose: int = 0):
        """
        Perform the calculation from the given dataframe. Returns another dataframe with the output cols added.

        Parameters
        ------------
        df: MetaDataFrame
            input DataFrame.
        verbose: int, optional
            level of verbosity.

        Returns
        ----------
        DataFrame
            DataFrame with the output_cols added.
        """

        # Perform the split by defined length
        def defined_length_split_zip(signal, cutpoints, segment_length):
            if signal is None or cutpoints is None or segment_length is None:
                return []

                # Filter out cutpoints that are <= segment_length
            valid_cutpoints = [cp for cp in cutpoints if cp > segment_length]

            result = []
            for i, cp in enumerate(valid_cutpoints):
                # Compute begin/end
                b = cp - segment_length
                e = cp

                # Safety checks to avoid out-of-bounds
                if b < 0:
                    b = 0
                if e > len(signal):
                    e = len(signal)

                # Slice out the segment
                seg = signal[b:e]
                end_index = e - 1 if e > b else b

                result.append({
                    "split_number": i,
                    "begin": b,
                    "end": end_index,
                    "segment": seg
                })

            return result

        # Perform the split by all cutpoints
        def all_segments_split(signal, cutpoints):
            if signal is None or cutpoints is None:
                return []
            segment_list = []
            start = 0
            for cp in cutpoints:
                segment_list.append(signal[start:cp])
                start = cp
            segment_list.append(signal[start:])
            return segment_list

        # Generate a struct from the split segments containing the beginning, end, split number and actual segments.
        # This will later be unzipped in their corresponding rows.
        def my_zip_all_indices(signal_segments):

            if signal_segments is None:
                return []

            result = []
            start_idx = 0
            for i, seg in enumerate(signal_segments):
                length_of_segment = len(seg)
                end_idx = start_idx + length_of_segment - 1 if length_of_segment > 0 else start_idx

                result.append({
                    "split_number": i,
                    "begin": start_idx,
                    "end": end_idx,
                    "segment": seg
                })
                start_idx += length_of_segment  # so the next segment starts after this one

            return result

        # Wrap the functions as UDFs and define the custom struct for returning the zipped values.
        all_segments_split_udf = F.udf(
            all_segments_split,
            T.ArrayType(T.ArrayType(T.FloatType()))
        )

        zipped_struct = T.StructType([
            T.StructField("split_number", T.IntegerType(), True),
            T.StructField("begin", T.IntegerType(), True),
            T.StructField("end", T.IntegerType(), True),
            T.StructField("segment", T.ArrayType(T.FloatType()), True),
        ])

        build_zipped_udf = F.udf(
            my_zip_all_indices,
            T.ArrayType(zipped_struct)
        )

        defined_length_split_udf = F.udf(
            lambda signal, cuts, seg_len: defined_length_split_zip(signal, cuts, seg_len),
            T.ArrayType(zipped_struct)
        )

        # Convert to arrays of floats to avoid problems with different input types
        for c in self.params.input_cols:
            df = df.withColumn(c, F.col(c).cast("array<float>"))

        # Main logic: all indices split
        if self.params.split_method == 'all_indices':
            df = df.withColumn(
                "segments_array",
                all_segments_split_udf(
                    F.col(self.params.input_cols[0]),
                    F.col(self.params.cuts_col)
                )
            )

            df = df.withColumn(
                "zipped",
                build_zipped_udf(F.col("segments_array"))
            )

        # Main logic: defined length split
        else:
            df = df.withColumn(
                "zipped",
                defined_length_split_udf(
                    F.col(self.params.input_cols[0]),
                    F.col(self.params.cuts_col),
                    F.lit(self.params.segment_length)
                )
            )

        # Explode the array of structs so that each struct becomes its own row
        df = df.withColumn("zipped_exploded", F.explode(F.col("zipped")))

        # Extract fields from the struct
        df = df.withColumn("split_number", F.col("zipped_exploded.split_number"))
        df = df.withColumn("begin", F.col("zipped_exploded.begin"))
        df = df.withColumn("end", F.col("zipped_exploded.end"))

        df = df.withColumn(self.params.output_cols[0], F.col("zipped_exploded.segment"))

        # Drop the intermediate columns
        df = df.drop("zipped", "zipped_exploded")
        if self.params.split_method == 'all_indices':
            df = df.drop('segments_array')

        # Slice the other signals if any using the same cutpoints.
        slice_udf = F.udf(
            lambda arr, b, e: arr[b:e+1] if arr and e >=b else [],
            T.ArrayType(T.FloatType())
        )

        if len(self.params.input_cols) > 1:
            for input_col, output_col in zip(self.params.input_cols[1:], self.params.output_cols[1:]):
                df = df.withColumn(
                    output_col,
                    slice_udf(F.col(input_col), F.col("begin"), F.col("end"))
                )

        return df

class SignalBeatExtractor(Annotator):
    """
    Extracts a piece of a signal in an array based on an indices list and generates a new column with the
    cropped segment.

    Example
    ---------
    .. code-block:: python

        signal_beat_extractor = movano.etl.annotators.signals.rearrange_signal.SignalBeatExtractor() \\
          .set_indices_col('valleys') \\
          .set_how_many_segments(1) \\
          .set_count_from('end') \\
          .set_input_col('signal') \\
          .set_output_col('last_wave')
        signal_beat_extractor_model = signal_beat_extractor.fit(df)
        df = signal_beat_extractor_model.transform(df)
    """

    def __init__(self, spark_session=None, **kwargs):
        """
        Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ------------
        kwargs: dict
            keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'signal_col': None,
            'timestamp_col': None,
            'indices_col': None,
            'how_many_segments': 1,
            'count_from': 'end',
            'select_segments': None,
            'output_cols': ['extracted_beat', 'extracted_beat_ts']
        }
        self.params.set_default_params(default_params)

    @deco.check_type(str, 'signal_col')
    def set_signal_col(self, x):
        """
        Set the column containing the signal values.

        Parameters
        ------------
        x: str
            Name of the column containing the cut indices.

        Returns
        ----------
        SignalBeatExtractor:
            The instance of the class with the updated parameter.

        Raises
        --------
        ValueError:
            If x is not a str.
        """
        self.params.signal_col = x
        return self

    @deco.check_type(str, 'timestamp_col')
    def set_timestamp_col(self, x):
        """
        Set the column containing the timestamps of the signal.

        Parameters
        ------------
        x: str
            Name of the column containing the cut indices.

        Returns
        ----------
        SignalBeatExtractor:
            The instance of the class with the updated parameter.

        Raises
        --------
        ValueError:
            If x is not a str.
        """
        self.params.timestamp_col = x
        return self

    @deco.check_type(str, 'indices_col')
    def set_indices_col(self, x):
        """
        Set the column by which the segment will be separated.

        Parameters
        ------------
        x: str
            Name of the column containing the cut indices.

        Returns
        ----------
        SignalBeatExtractor:
            The instance of the class with the updated parameter.

        Raises
        --------
        ValueError:
            If x is not a str.
        """
        self.params.indices_col = x
        return self

    @deco.check_type(int, 'how_many_segments')
    def set_how_many_segments(self, x):
        """
        Set the number of segments to obtain.

        Parameters
        ------------
        x: int
            Number of segments to obtain.

        Returns
        ----------
        SignalBeatExtractorModel:
            The instance of the class with the updated parameter.

        Raises
        --------
        ValueError:
            If x is not an int.
        """
        self.params.how_many_segments = x
        return self

    @deco.check_type(str, 'count_from')
    def set_count_from(self, x):
        """
        Set where to begin counting segments if a number was chosen (not a list of segments).

        Parameters
        ------------
        x: str
            Where to begin counting if a number of segments has been selected but not which exact segments.

        Returns
        ----------
        SignalBeatExtractorModel:
            The instance of the class with the updated parameter.

        Raises
        --------
        ValueError:
            If x is not in ['begin', 'end'].
        """
        if x in ['begin', 'end']:
            self.params.count_from = x
        else:
            raise ValueError('Must select begin or end as where to count from.')
        return self

    @deco.check_type(list, 'select_segments')
    def set_select_segments(self, x):
        """
        Set the list of segment positions you need to extract.

        Parameters
        ------------
        x: list
            List of segment positions you need to extract.

        Returns
        ----------
        SignalBeatExtractorModel:
            The instance of the class with the updated parameter.

        Raises
        --------
        ValueError:
            If x is not a list of integers.
        """
        if all([isinstance(i, int) for i in x]):
            self.params.select_segments = x
        else:
            raise ValueError('Must select the position of the segments you want to extract.')
        return self

    def fit(self, df=None, verbose: int = 0):
        """
        Fits the model with the given DataFrame.

        Parameters
        ------------
        df: MetaDataFrame, optional
            DataFrame to fit the model.
        verbose: int, optional
            verbosity level.

        Returns
        ----------
        SignalBeatExtractorModel:
            an instance of SignalBeatExtractorModel.
        """
        return SignalBeatExtractorModel(self.spark_session, self.params)


class SignalBeatExtractorModel(Model):
    """
    Model to extract a segment of a signal from an array based on an indices list and generates a new column with the
    cropped segment.

    This class performs the actual extraction of signal segments using the parameters set in the parent class.

    Example
    ---------

    .. code-block:: python

        signal_beat_extractor = movano.etl.annotators.signals.rearrange_signal.SignalBeatExtractor() \\
          .set_indices_col('valleys') \\
          .set_how_many_segments(1) \\
          .set_count_from('end') \\
          .set_input_col('signal') \\
          .set_output_col('last_wave')
        signal_beat_extractor_model = signal_beat_extractor.fit(df)
        df = signal_beat_extractor_model.transform(df)
    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """
        Initialize the class with the given parameters

        Parameters
        ------------
        params: AnnotatorParams
            AnnotatorParams instance.
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, df, verbose: int = 0):
        """
        Perform the calculation from the given dataframe. Returns another dataframe with the output cols added.

        Parameters
        ------------
        df: MetaDataFrame
            input DataFrame.
        verbose: int, optional
            level of verbosity.

        Returns
        ----------
        DataFrame
            DataFrame with the output_cols added.
        """

        # This function returns a list containing the separated segments of the original signal.
        def _get_listed_segments(signal, timestamps, indices_list):

            # Check for out-of-bounds indices
            if any(index >= len(signal) for index in indices_list):
                raise ValueError("Indices out of bounds of the signal.")

            # Add one element pair cut-indices pair
            segments_list = []
            timestamp_list = []
            start = 0
            for cut_index in indices_list:
                segment = signal[start:cut_index]
                segments_list.append(segment)

                if timestamps is not None:
                    timestamp_segment = timestamps[start:cut_index]
                    timestamp_list.append(timestamp_segment)
                start = cut_index
            # Add tail
            segments_list.append(signal[start:])
            if timestamps is not None:
                timestamp_list.append(timestamps[start:])
                return segments_list, timestamp_list
            else:
                return segments_list, None

        # This function gets the segments selected by the user (or the number asked for, counting from begin or end)
        # and returns the list of segment and the list of timestamps.
        def _select_segments(segments_list, timestamp_list, select_segments, how_many_segments, count_from):
            flat_timestamp_segments = None

            if select_segments is not None:
                # Get selected elements and return in a flattened list
                selected_signal_elements = [segments_list[i] for i in select_segments]
                flat_signal_segments = np.concatenate(selected_signal_elements)

                if timestamp_list is not None:
                    selected_timestamp_elements = [timestamp_list[i] for i in select_segments]
                    flat_timestamp_segments = np.concatenate(selected_timestamp_elements)
            else:
                # Get how many segments set from the set position, return flattened list
                selected_signal_segments = segments_list[-how_many_segments:] if count_from == 'end' else \
                    segments_list[:how_many_segments]

                flat_signal_segments = np.concatenate(selected_signal_segments)

                if timestamp_list is not None:
                    selected_timestamp_segments = timestamp_list[-how_many_segments:] if count_from == 'end' else \
                        timestamp_list[:how_many_segments]
                    flat_timestamp_segments = np.concatenate(selected_timestamp_segments)

            return flat_signal_segments.tolist(), flat_timestamp_segments.tolist()

        # This function combines the segmentation and selection for each row.
        def _process_row(signal,
                         timestamps,
                         indices_list,
                         select_segments_param,
                         how_many_segments_param,
                         count_from_param):

            seg_list, ts_list = _get_listed_segments(
                signal=signal,
                timestamps=timestamps,
                indices_list=indices_list
            )

            selected_seg_list, selected_ts_list = _select_segments(
                segments_list=seg_list,
                timestamp_list=ts_list,
                select_segments=select_segments_param,
                how_many_segments=how_many_segments_param,
                count_from=count_from_param
            )

            return selected_seg_list, selected_ts_list

        # Prepare a combined function that will be transformed to a UDF:
        def _process_row_udf(signal,
                         timestamps,
                         indices_list,
                         select_segments_param,
                         how_many_segments_param,
                         count_from_param):
            flat_signal, flat_timestamps = _process_row(
                signal=signal or [],
                timestamps=timestamps or [],
                indices_list=indices_list or [],
                select_segments_param=select_segments_param,
                how_many_segments_param=how_many_segments_param,
                count_from_param=count_from_param
            )

            return {
                "signal": flat_signal if flat_signal is not None else None,
                "timestamps": (flat_timestamps if flat_timestamps is not None else None
                               )
            }

        # Create the schema the UDF will output:
        output_schema = T.StructType([
            T.StructField("signal", T.ArrayType(T.DoubleType()), True),
            T.StructField("timestamps", T.ArrayType(T.DoubleType()), True)
        ])

        process_row_udf = F.udf(_process_row_udf, output_schema)

        # Apply the UDF
        # If timestamp_col is None, pass None, else pass the column.
        df_result = df.withColumn(
            "processed",
            process_row_udf(
                F.col(self.params.signal_col),
                F.col(self.params.timestamp_col) if self.params.timestamp_col else F.lit(None),
                F.col(self.params.indices_col),
                F.lit(self.params.select_segments),
                F.lit(self.params.how_many_segments),
                F.lit(self.params.count_from)
            )
        )

        # Now extract the fields from the "processed" struct. First output col will be the signal and the second one
        # the timestamps.
        df_result = df_result.withColumn(
            self.params.output_cols[0],
            F.col("processed.signal")
        )

        if self.params.timestamp_col is not None:
            df_result = df_result.withColumn(
                self.params.output_cols[1],
                F.col("processed.timestamps")
            )

        df_result = df_result.drop("processed")

        return df_result

class InvertSignal(Annotator):
    """
    Inverts signal.
    This class represents an annotator that inverts a signal.

    Example
    ---------
    .. code-block:: python

        invert_signal = etl.annotators.signals.rearrange_signal.InvertSignal()\
          .set_input_cols(['signal'])\
          .set_output_cols(['inverted_signal'])
        invert_signal_model = invert_signal.fit(verbose=1)
        df = invert_signal_model.transform(df)
    """

    def __init__(self,  spark_session=None, **kwargs):
        """
        Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ------------
        kwargs: dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)


        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'flag_col': None
        }
        self.params.set_default_params(default_params)

    @deco.check_type(str, "flag_col")
    def set_flag_col(self, x):
        """
        If a column is selected, it will look for a bool value to know which segment it has to invert. None by default,
        inverts all signals in the given column.

        Parameters
        ----------
        x: str
            The name of the column containing the flag (if any).

        Returns
        -------
        InvertSignal
            The instance of the class with the updated parameter

        Raises
        ------
        TypeError
            If x is not a string.
        """
        self.params.flag_col = x
        return self

    def fit(self, df = None, verbose: int = 0):
        """
        Fits the model with the given DataFrame.

        Parameters
        ------------
        df: Spark DataFrame, optional
            DataFrame to fit the model.
        verbose: int, optional
            Verbosity level.

        Returns
        ----------
        InvertSignalModel:
            An instance of InvertSignalModel.
        """
        return InvertSignalModel(self.spark_session, self.params)


class InvertSignalModel(Model):
    """
    Model to invert the values of a given signal.

    This class performs the actual inversion of signal values using the parameters set in the parent class.

    Example
    -------
    .. code-block:: python

        inverter_model = etl.annotators.signals.InvertSignal() \
              .set_input_cols(['signal']) \
              .set_output_cols(['inverted_signal']) \
              .fit()
        transformed_df = inverter_model.transform(df)
    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """Initialize the class with the given parameters

        Parameters
        ------------
        params: AnnotatorParams
            AnnotatorParams instance.
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, df, verbose: int = 0):
        """
        Perform the operation from the given dataframe. Returns another dataframe with the output cols added.

        Parameters
        ------------
        df: Spark DataFrame
            Input DataFrame.
        verbose: int, optional
            Verbosity level.

        Returns
        ----------
        pd.DataFrame
            Transformed DataFrame.
        """
        for this_col, this_output_col in zip(self.params.input_cols, self.params.output_cols):
            if verbose > 0:
                print(f"Inverting column {this_col} into {this_output_col}.")

            if self.params.flag_col is not None:
                df = df.withColumn(this_output_col, F.when(F.col(self.params.flag_col) == True,
                                                           F.expr(f"""transform({this_col},x -> x*-1)"""))
                                   .otherwise(F.col(this_col)))
            else:
                df = df.withColumn(this_output_col, F.expr(f"""transform({this_col},x -> x*-1)"""))

        if verbose > 0:
            print(f"Final dataframe with shape {df.shape}")

        return df


class TimeGapSegmentator(Annotator):
    """
    Cuts a signal into separate rows if they contain time gaps. Takes as input the signals to cut and needs  a set
    reference column containing the timestamps in the same unit as the threshold.

    Example
    ---------
    .. code-block:: python
        results = etl.annotators.signals.rearrangesignal.TimeGapSegmentator()\
            .set_input_cols(['signal_1', 'signal_2'])\
            .set_output_cols(['signal_1_segmented', 'signal_2_segmented'])\
            .set_time_ref_col('timestamp')\
            .set_gap_threshold(1)\
            .fit().transform(df)
    """

    def __init__(self, spark_session=None, **kwargs):
        """
        Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ------------
        kwargs: dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'time_ref_col': None,
            'gap_threshold': None,
            'output_ts_col': None
        }

        self.params.set_default_params(default_params)

    @deco.check_type(str, 'time_ref_col')
    def set_time_ref_col(self, x):
        """
        Set the column name containing the timestamps to evaluate.

        Parameters
        ------------
        x: str
            Reduction factor.

        Returns
        ----------
        TimeGapSegmentator:
            The instance of the class with the updated parameter.

        """
        self.params.time_ref_col = x
        return self

    @deco.check_type(str, 'output_ts_col')
    def set_output_ts_col(self, x):
        """
        Set the name for the segmented ts column.

        Parameters
        ------------
        x: str
            Reduction factor.

        Returns
        ----------
        TimeGapSegmentator:
            The instance of the class with the updated parameter.

        """
        self.params.output_ts_col = x
        return self

    @deco.check_type(int, 'gap_threshold')
    def set_gap_threshold(self, x):
        """
        Set the threshold that would be considered a time gap.

        Parameters
        ------------
        x: int
            Reduction factor.

        Returns
        ----------
        TimeGapSegmentator:
            The instance of the class with the updated parameter.

        """
        self.params.gap_threshold = x
        return self

    def fit(self, df = None, verbose=0):
        """
        Fits the model with the given DataFrame.

        Parameters
        ------------
        df: Spark DataFrame, optional
            DataFrame to fit the model.
        verbose: int, optional
            Verbosity level.

        Returns
        ----------
        TimeGapSegmentatorModel:
            An instance of TimeGapSegmentatorModel.
        """

        # Automatic naming for the ts_col
        if self.params.output_ts_col is None:
            self.params.output_ts_col = self.params.time_ref_col + '_segment'

        return TimeGapSegmentatorModel(self.spark_session, self.params)


class TimeGapSegmentatorModel(Model):
    """
    Cuts a signal into separate rows if they contain time gaps. Takes as input the signals to cut and needs a set
    reference column containing the timestamps in the same unit as the threshold.
    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """
        Initialize the class with the given parameters

        Parameters
        ------------
        params: AnnotatorParams
            An instance of AnnotatorParams.
        """
        super().__init__(spark_session=spark_session, params=params)
        self.spark_session = spark_session

    def transform(self, df, verbose: int = 0):
        """
        Perform the calculation from the given dataframe. Returns another dataframe with the output cols added.

        Parameters
        ------------
            df: Spark DataFrame
                input DataFrame.
            verbose: int, optional
                level of verbosity.

        Returns
        ----------
            Spark DataFrame
                DataFrame with the output_cols added.
        """

        def _segment_row(x, timestamps, threshold, input_col_names, output_col_names, new_ts_col_name):

            # Get the split indices
            ts_array = np.array(x[timestamps])
            time_diffs = np.diff(ts_array)
            split_indices = np.where(time_diffs > threshold)[0] + 1

            # Split which elements of the whole array will go to each row
            all_splits = np.split(np.arange(len(ts_array)), split_indices)

            # Generate the new rows
            new_rows = []
            for split_list in all_splits:
                new_row = x.copy()
                new_row[new_ts_col_name] = [int(e) for e in ts_array[split_list]]
                for input_signal, output_signal in zip(input_col_names, output_col_names):
                    new_row[output_signal] = [float(e) for e in np.array(x[input_signal])[split_list]]
                new_rows.append(new_row)

            # Return the new rows from the original in dataframe form
            return pd.DataFrame(new_rows)

        # Change the dataframe to pandas
        df_pd = df.toPandas()

        # Generate a series of dataframes
        segmented_dataframes = df_pd.apply(lambda x: _segment_row(
            x,
            self.params.time_ref_col,
            self.params.gap_threshold,
            self.params.input_cols,
            self.params.output_cols,
            self.params.output_ts_col
        ), axis=1)

        final_df_pd = pd.concat(segmented_dataframes.values, ignore_index=True)

        final_df = self.spark_session.createDataFrame(final_df_pd)

        return final_df


class MergeOverlapsByIndex(Annotator):
    """
    Merges the overlapping segments of a signal by index.

    Example
    ---------
    .. code-block:: python

        test_data = [(1, [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], [1, 1, 1, 1, 1, 4, 4, 4, 4, 4, 4], [3, 8, 10])]
        schema = T.StructType([
            T.StructField("id", T.IntegerType(), True),
            T.StructField("ts", T.ArrayType(T.IntegerType()), True),
            T.StructField("values", T.ArrayType(T.IntegerType()), True),
            T.StructField("waves_idx", T.ArrayType(T.IntegerType()), True),
        ])

        df = self.spark_session.createDataFrame(test_data, schema)

        merge_waves = etl.annotators.signals.rearrange.MergeOverlapsByIndex() \
            .set_input_cols(['waves_idx']) \
            .set_output_cols(['waves_idx_merged']) \
            .set_ts_length(3)

        merge_waves_model = merge_waves.fit(df)
        transformed_df = merge_waves_model.transform(df)
    """

    def __init__(self, spark_session=None, **kwargs):
        """
        Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ------------
        kwargs: dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'input_cols': None,
            'output_cols': None,
            'ts_length': 1
        }
        self.params.set_default_params(default_params)

    @deco.check_type(int, 'ts_length')
    def set_ts_length(self, x: int):
        """
        Sets the length of the time series.

        Parameters
        ------------
        x: int
            Length of the time series.

        Returns
        ----------
        MergeOverlapsByIndex:
            The instance of the class with the updated parameter.

        Raises
        --------
        Exception:
            If x is not an int.
        """
        self.params.ts_length = x
        return self

    def fit(self, df = None, verbose: int = 0):
        """
        Fits the model with the given DataFrame.

        Parameters
        ------------
        df: Spark DataFrame, optional
            DataFrame to fit the model.
        verbose: int, optional
            Verbosity level.

        Returns
        ----------
        MergeOverlapsByIndexModel:
            An instance of MergeOverlapsByIndexModel.
        """
        return MergeOverlapsByIndexModel(self.spark_session, self.params)


class MergeOverlapsByIndexModel(Model):
    """
    Merges the overlapping segments of a signal by index.
    This class performs the actual merging of overlapping signal segments using the parameters set in the parent class.

    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """
        Initialize the class with the given parameters.

        Parameters
        ------------
        params: AnnotatorParams
            AnnotatorParams instance.
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, df, verbose: int = 0):
        """
        This function merges the overlapping segments of a signal by index so
        that the output signal has no overlapping segments and the segment
        length is greater than or equal to the given ts_length.

        Parameters
        ------------
        df: Spark DataFrame
            Input DataFrame.
        verbose: int, optional
            Verbosity level.

        Returns
        ----------
        Spark DataFrame
            Transformed DataFrame.
        """

        def merge_overlapped_waves(waves, timeframes=1000, verbose=0):
            """
            Merge the overlapping waves in a row.

            Parameters
            -----------
            row: pd.Series
                Input row.
            waves_col: str
                Column name containing the waves.
            timeframes: int
                Minimum timeframes for a segment to be considered non-overlapping.

            Returns
            --------
            list
                Merged waves.
            """
            # from end to beginning
            rev_waves = list(reversed(waves))
            idx = 0
            while idx < len(rev_waves) - 1:
                if rev_waves[idx] - rev_waves[idx + 1] < timeframes:
                    rev_waves.pop(idx + 1)
                else:
                    idx += 1
            waves_merged = list(reversed(rev_waves))
            if verbose > 0:
                print(f"Original waves: {len(waves)}, Merged waves: {len(waves_merged)}")
            return [int(e) for e in waves_merged]

        udf_func = F.udf(merge_overlapped_waves, T.ArrayType(T.IntegerType()))

        df = df.withColumn(self.params.output_cols[0], udf_func(df[self.params.input_cols[0]],
                                                    F.lit(self.params.ts_length)))

        return df


class ResampleAlignedSignals(Annotator):
    """
    Takes a timestamp array and one or more signals which should already be aligned, and resamples them to a new
    sampling frequency by interpolation.
    Must set a timestamp col, a new timestamp col name (resampled_ts by default) and the original time unit (ms by
    default). Also, the target_sampling_frequency.
    Input_col or input_cols will be the signals outputted to output_col or output_cols.

    Example
    ---------
    .. code-block:: python
        test_data = [(1, [0.0, 1.0, 2.0, 3.0], [1.0, 2.0, 3.0, 2.5], [0.5, 1.5, 2.5, 3.0]),
                     (2, [10.0, 11.0, 12.0, 13.0], [5.0, 6.0, 6.5, 7.0], [3.1, 3.2, 3.1, 3.0])]
        schema = T.StructType([
            T.StructField("id", T.IntegerType(), True),
            T.StructField("timestamp", T.ArrayType(T.FloatType()), True),
            T.StructField("signal1", T.ArrayType(T.FloatType()), True),
            T.StructField("signal2", T.ArrayType(T.FloatType()), True)
        ])

        df = self.spark_session.createDataFrame(test_data, schema)

        resample_aligned_signals = etl.annotators.signals.rearrange.ResampleAlignedSignals() \
            .set_input_cols(['signal1', 'signal2']) \
            .set_output_cols(['signal1_new', 'signal2_new']) \
            .set_timestamp_col('timestamp') \
            .set_original_time_unit('s') \
            .set_new_timestamp_col('timestamp_new') \
            .set_target_sampling_frequency(2)

        resample_aligned_signals_model = resample_aligned_signals.fit(df)
        transformed_df = resample_aligned_signals_model.transform(df)

    """

    def __init__(self, spark_session=None, **kwargs):
        """
        Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ------------
        kwargs: dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'timestamp_col': None,
            'input_cols': None,
            'output_cols': None,
            'target_sampling_frequency': None,
            'new_timestamp_col': 'resampled_ts',
            'original_time_unit': 'ms'
        }

        self.params.set_default_params(default_params)

    @deco.check_type(int, 'target_sampling_frequency')
    def set_target_sampling_frequency(self, x):
        """
        Set the column name containing the target sampling frequency.

        Parameters
        ------------
        x: int
            Target sampling frequency.

        Returns
        ----------
        ResampleAlignedSignals:
            The instance of the class with the updated parameter.

        """
        self.params.target_sampling_frequency = x
        return self

    @deco.check_type(str, 'timestamp_col')
    def set_timestamp_col(self, x):
        """
        Set the column name containing the timestamps to evaluate.

        Parameters
        ------------
        x: str
            Column name containing the timestamps.

        Returns
        ----------
        ResampleAlignedSignals:
            The instance of the class with the updated parameter.

        """
        self.params.timestamp_col = x
        return self

    @deco.check_type(str, 'new_timestamp_col')
    def set_new_timestamp_col(self, x):
        """
        Set the column name that will contain the new timestamps.

        Parameters
        ------------
        x: str
            Column name that will contain the new timestamps.

        Returns
        ----------
        ResampleAlignedSignals:
            The instance of the class with the updated parameter.

        """
        self.params.new_timestamp_col = x
        return self

    @deco.check_type(str, 'original_time_unit')
    def set_original_time_unit(self, x):
        """
        Set the original time unit as 'ms' or 's'.

        Parameters
        ------------
        x: str
            original time unit as 'ms' or 's'.

        Returns
        ----------
        ResampleAlignedSignals:
            The instance of the class with the updated parameter.

        """
        if x in ['ms', 's']:
            self.params.original_time_unit = x
        else:
            raise ValueError('Supported origin time units are ms or s.')
        return self


    def fit(self, df = None, verbose=0):
        """
        Fits the model with the given DataFrame.

        Parameters
        ------------
        df: Spark DataFrame, optional
            DataFrame to fit the model.
        verbose: int, optional
            Verbosity level.

        Returns
        ----------
        ResampleAlignedSignalsModel:
            An instance of ResampleAlignedSignalsModel.
        """

        return ResampleAlignedSignalsModel(self.spark_session, self.params)


class ResampleAlignedSignalsModel(Model):
    """
    Takes a timestamp array and one or more signals which should already be aligned, and resamples them to a new
    sampling frequency by interpolation.
    Must set a timestamp col, a new timestamp col name (resampled_ts by default) and the original time unit (ms by
    default). Also, the target_sampling_frequency.
    Input_col or input_cols will be the signals outputted to output_col or output_cols.
    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """
        Initialize the class with the given parameters

        Parameters
        ------------
        params: AnnotatorParams
            An instance of AnnotatorParams.
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, df):
        """
        Perform the calculation from the given dataframe. Returns another dataframe with the output cols added.

        Parameters
        ------------
            df: Spark DataFrame
                input DataFrame.
            verbose: int, optional
                level of verbosity.

        Returns
        ----------
            Spark DataFrame
                DataFrame with the output_cols added.
        """

        if len(self.params.input_cols) != len(self.params.output_cols):
            raise ValueError("Input and output cols don't match. Each signal must have an output col")

        def _interpolate_timestamps(original_timestamps, target_freq, original_time_unit):
            if original_time_unit == 's':
                new_period = 1.0 / target_freq
            elif original_time_unit == 'ms':
                new_period = 1000.0 / target_freq
            else:
                raise ValueError("Original time unit must be set to s or ms.")

            t_min = original_timestamps[0]
            t_max = original_timestamps[-1]

            new_timestamps = np.arange(t_min, t_max, new_period)

            return [float(e) for e in new_timestamps]

        def _interpolate_signal(original_timestamps, original_signal, new_timestamps):

            interpolator = interpolate.interp1d(original_timestamps, original_signal, kind='linear',
                                                bounds_error=False, fill_value="extrapolate")

            new_signal = interpolator(new_timestamps)

            return [float(e) for e in new_signal]

        udf_func = F.udf(_interpolate_timestamps, T.ArrayType(T.DoubleType()))

        df = df.withColumn(self.params.new_timestamp_col, udf_func(df[self.params.timestamp_col],
                                                       F.lit(self.params.target_sampling_frequency),
                                                       F.lit(self.params.original_time_unit)))

        udf_func = F.udf(_interpolate_signal, T.ArrayType(T.DoubleType()))

        for incol, outcol in zip(self.params.input_cols, self.params.output_cols):
            df = df.withColumn(outcol, udf_func(df[self.params.timestamp_col],
                                                df[incol],
                                                df[self.params.new_timestamp_col]))

        return df