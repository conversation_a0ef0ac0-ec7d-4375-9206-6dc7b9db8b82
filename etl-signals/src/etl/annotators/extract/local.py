from etl.annotators.annotators import Annotator, <PERSON>, AnnotatorParams
import etl.decorators.decorators as deco
import os
import glob
from pyspark.sql import functions as F
from pyspark.sql import DataFrame
from functools import reduce
import pandas as pd


class ExtractCSV(Annotator):
    """
    This class is used to load a CSV into a spark dataframe.
    """

    def __init__(self, spark_session=None, **kwargs):
        """
        Initialize the class with the given parameters and sets the corresponding default ones.

        Parameters
        ------------
        kwargs: dict
            keyword arguments containing the parameters.
            """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'empty',
            'transform_output_type': 'dataframe',
            'csv_path': ".",
            'single_file': True,
            'input_cols': None
        }

        self.params.set_default_params(default_params)

    @deco.check_type(str, 'csv_path')
    def set_csv_path(self, x: str):
        """
        Setter the CSV path.

        Parameters
        ----------
        x: str
            CSV path.

        Returns
        -------
        ExtractCSV
            the instance of the class with the updated parameter.

        Raises
        ------
        Exception
            If x is not a string.
        """

        self.params.__setattr__('csv_path', x)
        return self

    @deco.check_type(bool, 'single_file')
    def set_single_file(self, x: bool):
        """
        Specifies if a single file or multiple files should be loaded.

        Parameters
        ------------
        x: bool
            Specifies if a single file or multiple files should be loaded.

        Returns
        ----------
        LoadCSV
            the instance of the class with the updated parameter.

        Raises
        --------
        Exception
            If x is not a bool.
        """
        
        self.params.__setattr__('single_file', x)
        return self

    def fit(self, df=None, verbose: int = 0):
        """
        Fits the model with the given DataFrame and returns an instance of LoadCSVModel.

        Parameters
        ------------
        df: pd.DataFrame
            DataFrame to fit the model.
        verbose: int
            verbosity level.

        Returns
        ----------
        ExtractCSVModel
            returns an instance of ExtractCSVModel.

        """
        return ExtractCSVModel(self.spark_session, self.params)


class ExtractCSVModel(Model):
    """
    Model for loading a CSV to a pandas dataframe, fitted to the needed settings

    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """
        Initialize the class with the given parameters and sets the corresponding default ones.

        Parameters
        ------------
        params: AnnotatorParams
            AnnotatorParams instance.

        Raises
        --------
        Exception
            If params is not an instance of AnnotatorParams.
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, verbose: int = 0):
        """
        Loads the CSV file and returns the data as a DataFrame.

        Parameters
        ------------
        df: pd.DataFrame
            DataFrame input for pipeline consistency, not needed.
        verbose: int
            verbosity level.

        Returns
        ----------
        DataFrame
            DataFrame from the CSV file.

        Raises
        --------
        Exception
            If csv_path does not exist.
        """
        if self.params.single_file:

            if not os.path.isfile(os.path.abspath(self.params.csv_path)):
                raise Exception(f"csv_path does not exist: {self.params.csv_path}")

            if verbose > 0:
                print(f"Loading {self.params.csv_path}")

            df = (
                self.spark_session.read.format("csv")
                .option("header", True)
                .option("inferSchema", True)
                .load(self.params.csv_path)
            )

            if verbose > 0:
                num_rows = df.count()
                num_cols = len(df.columns)
                print(f"Loaded {self.params.csv_path} with shape: ({num_rows}, {num_cols})")

            if self.params.input_cols is not None:
                missing_cols = [col for col in self.params.input_cols if col not in df.columns]
                if missing_cols:
                    raise Exception(f"Missing columns in DataFrame: {missing_cols}")

                df = df.select(*self.params.input_cols)

            # Create a 'file_from' column
            file_name = os.path.basename(self.params.csv_path)
            df = df.withColumn("file_from", F.lit(file_name))

        else:
            if not os.path.isdir(os.path.abspath(self.params.csv_path)):
                raise Exception(f"csv_path does not exist: {self.params.csv_path}")

            if verbose > 0:
                print(f"Loading all csvs from {self.params.csv_path}")

            files = glob.glob(os.path.join(self.params.csv_path, '*.csv'))

            files = [x for x in files if os.path.isfile(x)]

            df = (
                self.spark_session.read.format("csv")
                .option("header", True)
                .option("inferSchema", True)
                .load(files)
            )

            if verbose > 0:
                num_rows = df.count()
                num_cols = len(df.columns)
                print(
                    f"Loaded CSVs from {self.params.csv_path} with shape: ({num_rows}, {num_cols})"
                )

            if self.params.input_cols is not None:
                missing_cols = [col for col in self.params.input_cols if col not in df.columns]
                if missing_cols:
                    raise Exception(f"Missing columns in DataFrame: {missing_cols}")

            df = df.select(*self.params.input_cols)

            df = df.withColumn(
                "file_from",
                F.element_at(F.split(F.input_file_name(), '/'), -1)
            )

        if verbose > 0:
            num_rows = df.count()
            num_cols = len(df.columns)
            print(f"Final dataframe with shape ({num_rows}, {num_cols})")

        return df


class ExtractXLSX(Annotator):
    """
    This class is used to load a xlsx into a spark dataframe.
    """

    def __init__(self, spark_session=None, **kwargs):
        """
        Initialize the class with the given parameters and sets the corresponding default ones.

        Parameters
        ------------
        kwargs: dict
            keyword arguments containing the parameters.
            """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'empty',
            'transform_output_type': 'dataframe',
            'xlsx_path': ".",
            'single_file': True,
            'input_cols': None,
            'ignorestr': None
        }

        self.params.set_default_params(default_params)

    @deco.check_type(str, 'xlsx_path')
    def set_xlsx_path(self, x: str):
        """
        Setter the xlsx path.

        Parameters
        ----------
        x: str
            CSV path.

        Returns
        -------
        Extractxlsx
            the instance of the class with the updated parameter.

        Raises
        ------
        Exception
            If x is not a string.
        """

        self.params.xlsx_path = x
        return self

    @deco.check_type(bool, 'single_file')
    def set_single_file(self, x: bool):
        """
        Specifies if a single file or multiple files should be loaded.

        Parameters
        ------------
        x: bool
            Specifies if a single file or multiple files should be loaded.

        Returns
        ----------
        Extractxlsx
            the instance of the class with the updated parameter.

        Raises
        --------
        Exception
            If x is not a bool.
        """

        self.params.single_file = x
        return self

    @deco.check_type(list, 'ignorestr')
    def set_ignorestr(self, x: list):
        """
        Setter for a list of strings which, if present in the filename, the loader will not download from the folder.
        Case-sensitive.

        Parameters
        ------------
        x: list
            List of strings which, if present in the filename, will mark it as ignored.

        Returns
        ----------
        Extractxlsx:
            The instance of the class with the updated parameter.

        Raises
        --------
        Exception:
            If x is not a list of strings.
        """
        if all(isinstance(i, str) for i in x):
            self.params.ignorestr = x
        else:
            raise TypeError("single_file must be a list of strings")

        return self

    def fit(self, df=None, verbose: int = 0):
        """
        Fits the model with the given DataFrame and returns an instance of LoadCSVModel.

        Parameters
        ------------
        df: pd.DataFrame
            DataFrame to fit the model.
        verbose: int
            verbosity level.

        Returns
        ----------
        ExtractCSVModel
            returns an instance of ExtractCSVModel.

        """
        return ExtractXLSXModel(self.spark_session, self.params)


class ExtractXLSXModel(Model):
    """
    Model for loading a xlsx to a spark dataframe, fitted to the needed settings

    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """
        Initialize the class with the given parameters and sets the corresponding default ones.

        Parameters
        ------------
        params: AnnotatorParams
            AnnotatorParams instance.

        Raises
        --------
        Exception
            If params is not an instance of AnnotatorParams.
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, verbose: int = 0):
        """
        Loads the xlsx file and returns the data as a DataFrame.

        Parameters
        ------------
        df: DataFrame
            DataFrame input for pipeline consistency, not needed.
        verbose: int
            verbosity level.

        Returns
        ----------
        DataFrame
            DataFrame from the xlsx file.

        Raises
        --------
        Exception
            If xlsx_path does not exist.
        """
        # single_file branch
        if self.params.single_file:
            xlsx_file_path = os.path.abspath(self.params.xlsx_path)
            if not os.path.isfile(xlsx_file_path):
                raise Exception(f"xlsx_path does not exist: {self.params.xlsx_path}")
            if verbose > 0:
                print(f"Loading {xlsx_file_path}")

            # Load with pandas
            pd_df = pd.read_excel(xlsx_file_path)

            if verbose > 0:
                print(f"Loaded {xlsx_file_path} with shape: {pd_df.shape}")

            # If user requested certain columns
            if self.params.input_cols is not None:
                pd_df = pd_df[self.params.input_cols]

            # Add column 'file_from'
            pd_df['file_from'] = os.path.basename(xlsx_file_path)

            # 4) Convert the pandas DataFrame to Spark
            df_spark = self.spark_session.createDataFrame(pd_df)

        # multiple-file branch
        else:
            #TODO: This loader still uses a first loading step in pandas but the combination of multiple files is
            # still distributed
            xlsx_dir_path = os.path.abspath(self.params.xlsx_path)
            if not os.path.isdir(xlsx_dir_path):
                raise Exception(f"xlsx_path does not exist: {self.params.xlsx_path}")
            if verbose > 0:
                print(f"Loading all excel files from {xlsx_dir_path}")

            files = glob.glob(os.path.join(xlsx_dir_path, '*.xlsx'))
            # Filter out anything that isn't an actual file
            files = [f for f in files if os.path.isfile(f)]

            # If you want to ignore files containing any substring in self.params.ignorestr
            if self.params.ignorestr is not None:
                files = [
                    f for f in files
                    if not any(ignore_str in f for ignore_str in self.params.ignorestr)
                ]

            # We'll accumulate Spark DataFrames here
            df_list_spark = []

            for file_path in files:
                if os.path.isfile(file_path):
                    # 1) Load with pandas
                    if self.params.input_cols is not None:
                        pd_df_aux = pd.read_excel(file_path, usecols=self.params.input_cols)
                    else:
                        pd_df_aux = pd.read_excel(file_path)

                    # 2) Add the 'file_from' column in pandas
                    pd_df_aux['file_from'] = os.path.basename(file_path)

                    if verbose > 0:
                        print(f"Loaded {file_path} with shape: {pd_df_aux.shape}")

                    # 3) Convert pandas DataFrame to Spark
                    df_spark_aux = self.spark_session.createDataFrame(pd_df_aux)
                    df_list_spark.append(df_spark_aux)

                else:
                    if verbose > 0:
                        print(f"{file_path} is not a file")

            # Union all Spark DataFrames in df_list_spark
            if df_list_spark:
                df_spark = df_list_spark[0]
                for other_df in df_list_spark[1:]:
                    df_spark = df_spark.unionByName(other_df)
                if verbose > 0:
                    print(f"Final dataframe row count: {df_spark.count()}")
            else:
                # If we have no DataFrames (no files found or everything filtered),
                # create an empty Spark DataFrame
                df_spark = self.spark_session.createDataFrame([], schema=None)

        return df_spark
    

class ExtractParquet(Annotator):
    """This class is used to load a parquet file into a pandas dataframe.

    This is an example of using ExtractParquet in a pipeline:

    Example
        -------
        .. code-block:: python

            loader = etl.annotators.extract.local.ExtractParquet() \
                .set_input_cols(["labels"]) \
                .set_path("test_etl/assets/parquet-test-file1.parquet")

            df = loader.fit().transform()
    """

    def __init__(self, spark_session=None, **kwargs):
        """
        Initialize the class with the given parameters and sets the corresponding default ones.

        Parameters
        ------------
        kwargs: dict
            keyword arguments containing the parameters.
            """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'empty',
            'transform_output_type': 'dataframe',
            'path': None,
            'single_file': True,
            'input_cols': None
        }

        self.params.set_default_params(default_params)

    @deco.check_type(str, 'path')
    def set_path(self, x: str):
        """
        Setter the Parquet path.

        Parameters
        ------------
        x: str
            Path.

        Returns
        ----------
        ExtractParquet
            the instance of the class with the updated parameter.

        Raises
        --------
        Exception
            If x is not a string.
        Exception
            If x is not a valid path.
        """
        self.params.__setattr__('path', x)
        return self

    @deco.check_type(bool, 'single_file')
    def set_single_file(self, x: bool):
        """
        Specifies if a single file or multiple files should be loaded.

        Parameters
        ------------
        x: bool
            Specifies if a single file or multiple files should be loaded.

        Returns
        ----------
        ExtractParquet
            the instance of the class with the updated parameter.

        Raises
        --------
        Exception
            If x is not a bool.
        """

        self.params.__setattr__('single_file', x)
        return self

    def fit(self, df = None, verbose: int = 0):
        """
        Fits the model with the given DataFrame and returns an instance of ExtractParquet.

        Parameters
        ------------
        df: Spark DataFrame
            DataFrame to fit the model.
        verbose: int
            verbosity level.

        Returns
        ----------
        ExtractParquetModel
            returns an instance of ExtractParquetModel.
        """
        return ExtractParquetModel(self.spark_session, self.params)

class ExtractParquetModel(Model):
    """
    Model for loading a parquet file to a pandas dataframe, fitted to the needed settings

    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """
        Initialize the class with the given parameters and sets the corresponding default ones.

        Parameters
        ------------
        params: AnnotatorParams
            AnnotatorParams instance.

        Raises
        --------
        Exception
            If params is not an instance of AnnotatorParams.
        """
        super().__init__(spark_session=spark_session, params=params)
        self.spark_session = spark_session

    def transform(self, df = None, verbose: int = 0) -> pd.DataFrame:
        """
        Loads the parquet file and returns the data as a DataFrame.

        Parameters
        ------------
        df: Spark DataFrame
            DataFrame input for pipeline consistency, not needed.
        verbose: int
            verbosity level.

        Returns
        ----------
        Spark DataFrame
            DataFrame from the parquet file.

        Raises
        --------
        Exception
            If path does not exist.
        """
        if self.params.single_file:
            if not os.path.isfile(os.path.abspath(self.params.path)):
                raise Exception(f"path does not exist: {self.params.path}")
            if verbose > 0:
                print(f"Loading {self.params.path}")

            df = self.spark_session.read.parquet(self.params.path)

            if verbose > 0:
                print(f"Loaded {self.params.path} with shape: {df.shape}")

            if self.params.input_cols is not None:
                df = df[self.params.input_cols]

            df = df.withColumn('file_from', F.lit(self.params.path.split('/')[-1]))

        else:
            if not os.path.isdir(os.path.abspath(self.params.path)):
                raise Exception(f"path does not exist: {self.params.path}")
            if verbose > 0:
                print(f"Loading all parquets from {self.params.path}")

            files = glob.glob(os.path.join(self.params.path, '*.parquet'))
            # Only if really a file
            files = [x for x in files if os.path.isfile(x)]
            names = [os.path.basename(x) for x in files]

            df_list = []
            for file_idx in range(len(files)):
                # only if files[file_idx] is a file
                if os.path.isfile(files[file_idx]):
                    df_aux = self.spark_session.read.parquet(files[file_idx])

                    if self.params.input_cols is not None:
                        df_aux = df_aux[self.params.input_cols]

                    df_aux = df_aux.withColumn('file_from', F.lit(names[file_idx]))

                    if verbose > 0:
                        print(f"Loaded {files[file_idx]} with shape: {df_aux.shape}")
                    df_list.append(df_aux)

                else:
                    if verbose > 0:
                        print(f"{files[file_idx]} is not a file")

            df = reduce(DataFrame.unionAll, df_list)

            del df_list
        if verbose > 0:
            print(f"Final dataframe with shape {df.shape}")

        return df