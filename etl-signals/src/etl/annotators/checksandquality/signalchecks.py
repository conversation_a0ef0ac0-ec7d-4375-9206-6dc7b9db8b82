from etl.annotators.annotators import Annotator, <PERSON>, AnnotatorParams
from etl.decorators import decorators as deco
from pyspark.sql import functions as F
import pyspark.sql.types as T
import warnings
import pandas as pd
import numpy as np


class IsTimeGap(Annotator):
    """
    Flags a signal if there is a time gap.

    This class provides methods to set the time gap to decide if the value should be flagged, configure the input and output columns,

    Method:
    -------
    set_gap(x: int) -> IsTimeGap:
        Set time gap to decide if the value should be flagged.
    fit(df: Spark DataFrame = None, verbose: int = 0) -> IsTimeGapModel:
        Fits the model with the given DataFrame.

    Example
    ---------
    .. code-block:: python

        is_time_gap = etl.annotators.checksandquality.signalchecks.IsTimeGap() \
            .set_input_col('ts') \
            .set_output_col('has_time_gap') \
            .set_gap(5)

        results_df = is_time_gap.fit().transform(df, verbose=1)
    """

    def __init__(self, spark_session=None, **kwargs):
        """
        Initialize the class with the given parameters and sets the corresponding default ones.

        Parameters
        ------------
        kwargs: dict
            keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'input_cols': None,
            'output_cols': None,
            'gap': None
        }

        self.params.set_default_params(default_params)

    @deco.check_type(int, 'gap')
    def set_gap(self, x):
        """
        Set time gap to decide if the value should be flagged.

        Parameters
        ------------
        x: int
            threshold of gap in time without signal.

        Returns
        ----------
        IsTimeGap
            the instance of the class with the updated parameter.
        """
        self.params.gap = x
        return self

    def fit(self, df = None, verbose: int = 0):
        """
        Fits the model with the given DataFrame.

        Parameters
        ------------
        df: Spark DataFrame
            DataFrame to fit the model.

        verbose: int
            verbosity level.

        Returns
        ----------
        IsTimeGapModel
            returns an instance of IsTimeGapModel.
        """

        if any(param is None for param in [self.params.input_cols, self.params.output_cols, self.params.gap]):
            raise ValueError('Must set input cols, output cols and a time gap.')

        return IsTimeGapModel(self.spark_session, self.params)


class IsTimeGapModel(Model):
    """
    Flags a signal if there is a time gap.

    This class provides methods to set the time gap to decide if the value should be flagged, configure the input and output columns,

    Method:
    -------
    transform(df: Spark DataFrame = None, verbose: int = 0) -> Spark DataFrame:
        Perform the calculation.

    Example
    ---------
    .. code-block:: python

        is_time_gap = etl.annotators.checksandquality.signalchecks.IsTimeGap() \
            .set_input_col('ts') \
            .set_output_col('has_time_gap') \
            .set_gap(5)

        results_df = is_time_gap.fit().transform(df, verbose=1)
    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """
        Initialize the class with the given parameters and sets the corresponding default ones.

        Parameters
        ------------
        params: AnnotatorParams
            AnnotatorParams instance.
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, df = None, verbose: int = 0):
        """
        Perform the calculation.

        Parameters
        ------------
        df: Spark DataFrame
            DataFrame to perform the operation.

        verbose: int
            verbosity level.

        Returns
        ----------
        Spark DataFrame
            DataFrame with the unit change performed.
        """

        if verbose > 0:
            print(f"Flagging the rows that have time gaps in the columns {self.params.input_cols}.")

        def _check_gap(array, gap):
            if isinstance(array, (pd.Series, np.ndarray, list)) and len(array) > 1:
                for idx, t in enumerate(array[1:], start=1):
                    curr_gap = t - array[idx - 1]
                    if curr_gap > gap:
                        return 1
                return 0
            warnings.warn(f'Row {array} passed to check is not an iterable')
            return 1

        udf_check_gap = F.udf(_check_gap, T.IntegerType())

        df = df.withColumn(self.params.output_cols[0], udf_check_gap(df[self.params.input_cols[0]],
                                                                     F.lit(self.params.gap)))

        return df