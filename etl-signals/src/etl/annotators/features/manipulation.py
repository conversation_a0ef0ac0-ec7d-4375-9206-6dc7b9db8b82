from etl.annotators.annotators import Annota<PERSON>, Model, AnnotatorParams
from etl.decorators import decorators as deco
from pyspark.sql import functions as F
from pyspark.sql import types as T
import numpy as np
import pandas as pd

class GetDummies(Annotator):
    """
    This class is used to generate dummy columns from a categorical column.
    It inherits from the MovanoAnnotator class.

    Example
    -------
    .. code-block:: python
        get_dummies = GetDummies()
        get_dummies.set_prefix("Gender_")
        get_dummies_model = get_dummies.fit(df)
        transformed_df = get_dummies_model.transform(df)
        print(transformed_df)
    """

    def __init__(self, spark_session=None, **kwargs):
        """Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ----------
        kwargs : dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'prefix' : "",
            'drop_extra_dummies': False

        }
        self.params.set_default_params(default_params)

    @deco.check_type(str, "prefix")
    def set_prefix(self, x):
        """
        Set the prefix for the dummy columns names.

        Parameters
        ----------
        x : str
            Name of the categorical column.

        Returns
        -------
        GetDummies
            The instance of the class with the updated parameter.
        """
        self.params.prefix = x
        return self

    @deco.check_type(bool, "drop_extra_dummies")
    def set_drop_extra_dummies(self, x):
        """
        If set to True, one of the dummies will be dropped to avoid collinearity (unless there is only one).

        Parameters
        ----------
        x : bool
            Boolean for dropping the unnecessary dummies that may incur in collinearity.

        Returns
        -------
        GetDummies
            The instance of the class with the updated parameter.
        """
        self.params.drop_extra_dummies = x
        return self

    def fit(self, df=None, verbose: int = 0):
        """Fits the model with the given DataFrame.

        Parameters
        ----------
        df : DataFrame, optional
            DataFrame to fit the model.
        verbose : int, optional
            Verbosity level.

        Returns
        -------
        GetDummiesModel
            Returns an instance of GetDummiesModel.
        """
        return GetDummiesModel(self.spark_session, self.params)


class GetDummiesModel(Model):
    """
    This class is used to perform the dummies generation using the parameters set in the parent class.

    Example
    -------
    .. code-block:: python
        movano_params = MovanoParams()
        get_dummies_model = GetDummiesModel(movano_params)
        transformed_df = get_dummies_model.transform(df)
        print(transformed_df)
    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """Initialize the class with the given parameters.

        Parameters
        ----------
        params : AnnotatorParams
            AnnotatorParams instance.
        """
        super().__init__(spark_session=spark_session, params=params)


    def transform(self, df, verbose: int = 0):
        """Perform the calculation from the given dataframe. Returns another dataframe with the output cols added.

        Parameters
        ----------
        df : DataFrame, optional
            Input DataFrame.
        verbose : int, optional
            Level of verbosity.

        Returns
        -------
        DataFrame
            DataFrame with the output_cols added.
        """

        for incol in self.params.input_cols:
            if verbose > 0:
                print(f"Getting dummies from '{incol}'.")

            # Get the distinct categories for the specified column
            distinct_vals = df.select(incol).distinct().collect()
            categories = [row[0] for row in distinct_vals]  # Just the values
            n_unique = len(categories)

            # Determine whether we drop one category
            drop = not ((n_unique == 1) or (not self.params.drop_extra_dummies))

            # If we need to drop the first category, we skip encoding for the first category
            if drop and n_unique > 0:
                categories_to_encode = categories[1:]
            else:
                categories_to_encode = categories

            # Create the dummy columns for the categories.
            for cat in categories_to_encode:
                # Build the new column name
                dummy_col = self.params.prefix + str(cat)
                # Compare the input_col to the category; if they match, assign 1, else 0
                df = df.withColumn(dummy_col, F.when(F.col(incol) == cat, 1).otherwise(0))

            if verbose > 0:
                print(f"Final dataframe with {len(df.columns)} columns.")

        return df
    
class FeatureValuesMap(Annotator):
    """
    This class is used to transform the values of a column following a given mapping.

    Example
    -------
    .. code-block:: python
        values_map = FeatureValuesMap()
        values_map.set_input_col('category')
        values_map.set_output_col('category_mapped')
        values_map.set_maps({'A': 1, 'B': 2, 'C': 3})
        values_map_model = values_map.fit(df)
        transformed_df = values_map_model.transform(df)
        print(transformed_df)
    """

    def __init__(self, spark_session=None, **kwargs):
        """Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ----------
        kwargs : dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'input_col': None,
            'input_cols': None,
            'output_col': None,
            'output_cols': None,
            'maps': None

        }
        self.params.set_default_params(default_params)

    @deco.check_type((dict, list), 'maps')
    def set_maps(self, x: (dict, list)):
        """
        Set the map for the value transformation. Accepts a dict or a list of dicts.

        Parameters
        ----------
        x : dict, list
            Map with the transformations.

        Returns
        -------
        FeatureValuesMap
            Instance of the class with the updated parameter.
        """

        if isinstance(x, dict):
            self.params.maps = x

        elif isinstance(x, list) and all(isinstance(i, dict) for i in x):
            self.params.maps = x

        else:
            raise TypeError(f"input map must be a dict or a list of dicts.")

        return self

    def fit(self, df=None, verbose: int = 0):
        """Fits the model with the given DataFrame.

        Parameters
        ----------
        df : DataFrame, optional
            DataFrame to fit the model.
        verbose : int, optional
            Verbosity level.

        Returns
        -------
        FeatureValuesMapModel
            Returns an instance of FeatureValuesMapModel.
        """
        return FeatureValuesMapModel(self.spark_session, self.params)


class FeatureValuesMapModel(Model):
    """
    This class is used to perform the value transformation using the parameters set in the Annotator class.

    Example
    -------
    .. code-block:: python
        values_map = FeatureValuesMap()
        values_map.set_input_col('category')
        values_map.set_output_col('category_mapped')
        values_map.set_maps({'A': 1, 'B': 2, 'C': 3})
        values_map_model = values_map.fit(df)
        transformed_df = values_map_model.transform(df)
        print(transformed_df)
    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """Initialize the class with the given parameters.

        Parameters
        ----------
        params : AnnotatorParams
            AnnotatorParams instance.
        """
        super().__init__(spark_session=spark_session, params=params)


    def transform(self, df, verbose: int = 0):
        """Perform the calculation from the given dataframe. Returns another dataframe with the output cols added.

        Parameters
        ----------
        df : DataFrame, optional
            Input DataFrame.
        verbose : int, optional
            Level of verbosity.

        Returns
        -------
        DataFrame
            DataFrame with the output_cols added.
        """

        # Helper function to build the Spark map expression for a dictionary
        def build_map(mapping_dict):
            pairs = []
            for k, v in mapping_dict.items():
                pairs.extend([F.lit(k), F.lit(v)])
            # Create a map column expression
            return F.create_map(*pairs)

        # If multiple dictionaries are passed
        if isinstance(self.params.maps, list):
            if len(self.params.input_cols) == len(self.params.output_cols) == len(self.params.maps):
                # Each map applies to the corresponding (input_col, output_col)
                for out_col, in_col, map_dict in zip(self.params.output_cols,
                                                     self.params.input_cols,
                                                     self.params.maps):
                    map_expr = build_map(map_dict)
                    # element_at(map_expr, in_col) returns the mapped value (or null if missing)
                    df = df.withColumn(out_col, F.element_at(map_expr, F.col(in_col)))
            else:
                raise Exception(
                    "If multiple maps are passed, the same number of in-out column pair must be passed too."
                )
        else:
            # If a single dictionary is passed, apply it to each input-output pair
            if len(self.params.input_cols) == len(self.params.output_cols):
                map_expr = build_map(self.params.maps)
                for out_col, in_col in zip(self.params.output_cols, self.params.input_cols):
                    df = df.withColumn(out_col, F.element_at(map_expr, F.col(in_col)))
            else:
                raise Exception("For a single mapping, input cols must have their corresponding output cols.")

        return df