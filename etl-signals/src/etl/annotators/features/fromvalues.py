from etl.annotators.annotators import Annotator, Model, AnnotatorParams
from etl.decorators import decorators as deco
from pyspark.sql import functions as F
from pyspark.sql import types as T
import numpy as np
import pandas as pd
import datetime

class CalculateAge(Annotator):
    """
    This class is used to calculate the age of a person from a date of birth column.
    It inherits from the MovanoAnnotator class.

    Example
    -------
    .. code-block:: python
        import pandas as pd
        data = {
            'dob': ['1990-01-01', '1995-12-31']  # Date of birth values
        }

        age_calculator = CalculateAge(input_col='dob', output_col='age')
        age_calculator.set_reference_year(2023)
        model = age_calculator.fit(df)
        transformed_df = model.transform(df)
    """

    def __init__(self, spark_session=None, **kwargs):
        """Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ----------
        kwargs : dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        year = datetime.datetime.now().year

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'reference_year': year
        }
        self.params.set_default_params(default_params)

    @deco.check_type(int, 'reference_year')
    def set_reference_year(self, x):
        """Set the reference year to use in the age calculation (current year by default).

        Parameters
        ----------
        x : int
            Reference year.

        Returns
        -------
        CalculateAge
            The instance of the class with the updated parameter.

        Raises
        ------
        Exception
            If reference year is not an integer.

        Example
        -------
        .. code-block:: python
            age_calculator = CalculateAge()
            age_calculator.set_reference_year(2023)
        """
        self.params.reference_year = x
        return self


    def fit(self, df=None, verbose: int = 0):
        """Fits the model with the given DataFrame.

        Parameters
        ----------
        df : DataFrame, optional
            DataFrame to fit the model.
        verbose : int, optional
            Verbosity level.

        Returns
        -------
        CalculateAgeModel
            Returns an instance of CalculateAgeModel.
        """
        return CalculateAgeModel(self.spark_session, self.params)


class CalculateAgeModel(Model):
    """Calculates the time of arrival of the pulse in the PPG signal.
    The time of arrival is calculated as the time of the first peak in the PPG signal after the EKG peak."""

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """Initialize the class with the given parameters.

        Parameters
        ----------
        params : AnnotatorParams
            AnnotatorParams instance.
        """
        super().__init__(spark_session=spark_session, params=params)


    def transform(self, df, verbose: int = 0):
        """Perform the calculation from the given dataframe. Returns another dataframe with the output cols added.

        Parameters
        ----------
        df : DataFrame, optional
            Input DataFrame.
        verbose : int, optional
            Level of verbosity.

        Returns
        -------
        DataFrame
            DataFrame with the output_cols added.
        """

        df = df.withColumn(
            self.params.output_cols[0],
            F.lit(self.params.reference_year) - F.year(F.to_date(F.col(self.params.input_cols[0]), 'yyyy-MM-dd'))
        )

        return df

class CalculatePulsePressure(Annotator):
    """
    This class is used to calculate pulse pressure from SBP and DBP columns.
    It inherits from the MovanoAnnotator class.

    Example
    -------
    .. code-block:: python
        pulse_pressure_calculator = CalculatePulsePressure(input_cols=['SBP', 'DBP'], output_col='pulse_pressure')
        pulse_pressure_calculator.set_noise(5)
        model = pulse_pressure_calculator.fit(df)
        transformed_df = model.transform(df)
    """

    def __init__(self, spark_session=None, **kwargs):
        """Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ----------
        kwargs : dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'sbp_col': None,
            'dbp_col': None
        }
        self.params.set_default_params(default_params)

    @deco.check_type(str, 'sbp_col')
    def set_sbp_col(self, x: str):
        """Set the column containing the sbp value.

        Parameters
        ----------
        x : str
            Integer value for the noise.

        Returns
        -------
        CalculatePulsePressure
            The instance of the class with the updated parameter.

        Raises
        ------
        Exception
            If x is not a string.
        """
        self.params.sbp_col = x
        return self

    @deco.check_type(str, 'dbp_col')
    def set_dbp_col(self, x: str):
        """Set the column containing the dbp value.

        Parameters
        ----------
        x : str
            Integer value for the noise.

        Returns
        -------
        CalculatePulsePressure
            The instance of the class with the updated parameter.

        Raises
        ------
        Exception
            If x is not a string.
        """
        self.params.dbp_col = x
        return self


    def fit(self, df=None, verbose: int = 0):
        """Fits the model with the given DataFrame.

        Parameters
        ----------
        df : DataFrame, optional
            DataFrame to fit the model.
        verbose : int, optional
            Verbosity level.

        Returns
        -------
        CalculatePulsePressureModel
            Returns an instance of CalculatePulsePressureModel.
        """
        return CalculatePulsePressureModel(self.spark_session, self.params)


class CalculatePulsePressureModel(Model):
    """
    This class is used to perform the pulse pressure calculation using the parameters set in the Annotator class.
    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """Initialize the class with the given parameters.

        Parameters
        ----------
        params : AnnotatorParams
            AnnotatorParams instance.
        """
        super().__init__(spark_session=spark_session, params=params)


    def transform(self, df, verbose: int = 0):
        """Perform the calculation from the given dataframe. Returns another dataframe with the output cols added.

        Parameters
        ----------
        df : DataFrame, optional
            Input DataFrame.
        verbose : int, optional
            Level of verbosity.

        Returns
        -------
        DataFrame
            DataFrame with the output_cols added.
        """

        # Cast columns to int
        df = df.withColumn(self.params.sbp_col, F.col(self.params.sbp_col).cast("int"))
        df = df.withColumn(self.params.dbp_col, F.col(self.params.dbp_col).cast("int"))

        # Compute the difference and store it in output_col
        df = df.withColumn(
            self.params.output_cols[0],
            F.col(self.params.sbp_col) - F.col(self.params.dbp_col)
        )

        # Show shape if verbose
        if verbose > 0:
            row_count = df.count()
            col_count = len(df.columns)
            print(f"Final dataframe with shape ({row_count}, {col_count})")

        return df