from etl.annotators.annotators import Annota<PERSON>, <PERSON>, AnnotatorParams
from etl.decorators import decorators as deco
from pyspark.sql import functions as F
from pyspark.sql import types as T
import numpy as np
import pandas as pd
import warnings
from scipy import signal as sp
import scipy
import etl

class CalculatePulseArrivalTime(Annotator):
    """
    Calculates the time of arrival of the pulse in the PPG signal.
    The time of arrival is calculated as the time of the first peak in the PPG signal after the EKG peak.
    As an input column it needs a reference signal (can be ts, ppg, ecg) to get the length of the whole segment.
    Also, via setters, ppg_peaks, ecg_peaks and the sampling_freq must be assigned as their corresponding column names.

    Example
    -------
    .. code-block:: python
        pat_calculator = etl.annotators.features.fromsignals.CalculatePulseArrivalTime()\
            .set_input_col('yData')\
            .set_output_col('pat')\
            .set_ppg_peaks_col('yData_peaks')\
            .set_ecg_peaks_col('fECG_peaks')\
            .set_sampling_freq_col('sampling_freq_hz')\
            .fit().transform(get_hz)
    """

    def __init__(self, spark_session=None, **kwargs):
        """Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ----------
        kwargs : dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'ppg_peaks_col': None,
            'ecg_peaks_col': None,
            'sampling_freq_col': None,
            'input_cols': None,
            'output_cols': None
        }

        self.params.set_default_params(default_params)

    @deco.check_type(str, 'ppg_peaks_col')
    def set_ppg_peaks_col(self, x):
        """Set column containing the array of PPG peak indices.

        Parameters
        ----------
        x : str
            Column containing the array of PPG peak indices.

        Returns
        -------
        CalculatePulseArrivalTime
            The instance of the class with the updated parameter.
        """
        self.params.ppg_peaks_col = x
        return self

    @deco.check_type(str, 'ecg_peaks_col')
    def set_ecg_peaks_col(self, x):
        """Set column containing the array of ecg peak indices.

        Parameters
        ----------
        x : str
            Column containing the array of ecg peak indices.

        Returns
        -------
        CalculatePulseArrivalTime
            The instance of the class with the updated parameter.
        """
        self.params.ecg_peaks_col = x
        return self

    @deco.check_type(str, 'sampling_freq_col')
    def set_sampling_freq_col(self, x):
        """Set column containing the sampling freq.

        Parameters
        ----------
        x : str
            Column containing the sampling freq.

        Returns
        -------
        CalculatePulseArrivalTime
            The instance of the class with the updated parameter.
        """
        self.params.sampling_freq_col = x
        return self

    def fit(self, df=None, verbose: int = 0):
        """Fits the model with the given DataFrame.

        Parameters
        ----------
        df : pd.DataFrame, optional
            DataFrame to fit the model.
        verbose : int, optional
            Verbosity level.

        Returns
        -------
        CalculatePulseArrivalTimeModel
            Returns an instance of CalculatePulseArrivalTimeModel.
        """
        return CalculatePulseArrivalTimeModel(self.spark_session, self.params)


class CalculatePulseArrivalTimeModel(Model):
    """Calculates the time of arrival of the pulse in the PPG signal.
    The time of arrival is calculated as the time of the first peak in the PPG signal after the EKG peak."""

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """Initialize the class with the given parameters.

        Parameters
        ----------
        params : AnnotatorParams
            AnnotatorParams instance.
        """
        super().__init__(spark_session=spark_session, params=params)


    def transform(self, df, verbose: int = 0):
        """Perform the calculation from the given dataframe. Returns another dataframe with the output cols added.

        Parameters
        ----------
        df : pd.DataFrame, optional
            Input DataFrame.
        verbose : int, optional
            Level of verbosity.

        Returns
        -------
        DataFrame
            DataFrame with the output_cols added.
        """

        def _calculate_pat(ppg_peaks, ecg_peaks, signal_reference, signal_hz):

            if ppg_peaks is None or ecg_peaks is None or signal_reference is None or signal_hz is None:
                return None

            pat_list = []
            for ppg_peak in ppg_peaks:
                #  ekg_peak less than each ppg_peak
                ecg_peak_list = [x for x in ecg_peaks if x < ppg_peak]
                if len(ecg_peak_list) > 0:
                    ecg_peak = ecg_peak_list[-1]
                    pat_list.append((ppg_peak - ecg_peak) / signal_hz)
                else:
                    pat_list.append(None)

            # Initialize empty array with the length of the original signal. Place the PAT at the peak indices and
            # forward fill
            pat_full_list = np.full(len(signal_reference), np.nan)
            pat_full_list[ppg_peaks] = pat_list
            pat_filled_series = pd.Series(pat_full_list).ffill().to_numpy(dtype=float)

            return pat_filled_series.tolist()

        calculate_pat_udf = F.udf(
            _calculate_pat, T.ArrayType(T.FloatType())
        )

        df = df.withColumn(
            self.params.output_cols[0] if self.params.output_cols else "pat",
            calculate_pat_udf(
                F.col(self.params.ppg_peaks_col),
                F.col(self.params.ecg_peaks_col),
                F.col(self.params.input_cols[0]),
                F.col(self.params.sampling_freq_col)
            )
        )

        return df

class CalculateHeartRate(Annotator):
    """
    Calculate heart rate from peaks and raw signal. Input col must be a reference signal (ts, ppg, ecg) to get the
    expected length of the HR calculation. A column name with the peaks indices must be set via setter. A column name
    with the sampling freq must be set via setter.
    Results will be outputted in the output_col (input_col + _hr suffix if not set).

    Example
    -------
    .. code-block:: python
        calchr = movano.etl.annotators.features.fromsignals.CalculateHeartRate()\
            .set_input_cols(['yData'])\
            .set_peaks_col('yData_peaks')\
            .set_output_col('heart_rate')\
            .set_sampling_freq_col('sampling_freq_hz')\
            .fit().transform(get_hz)

    """

    def __init__(self, spark_session=None, **kwargs):
        """Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ----------
        kwargs : dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'peaks_col': None,
            'sampling_freq_col': None
        }

        self.params.set_default_params(default_params)

    @deco.check_type(str, 'peaks_col')
    def set_peaks_col(self, x):
        """Set column containing the array of peak indices.

        Parameters
        ----------
        x : str
            Column containing the array of peak indices.

        Returns
        -------
        CalculateHeartRate
            The instance of the class with the updated parameter.
        """
        self.params.peaks_col = x
        return self

    @deco.check_type(str, 'sampling_freq_col')
    def set_sampling_freq_col(self, x):
        """Set column containing the sampling frequency.

        Parameters
        ----------
        x : str
            Column containing the sampling frequency.

        Returns
        -------
        CalculateHeartRate
            The instance of the class with the updated parameter.
        """
        self.params.sampling_freq_col = x
        return self

    def fit(self, df=None, verbose: int = 0):
        """Fits the model with the given DataFrame.

        Parameters
        ----------
        df : pd.DataFrame, optional
            DataFrame to fit the model.
        verbose : int, optional
            Verbosity level.

        Returns
        -------
        CalculateHeartRateModel
            Returns an instance of CalculateHeartRateModel.
        """
        return CalculateHeartRateModel(self.spark_session, self.params)


class CalculateHeartRateModel(Model):
    """
    Calculate heart rate from peaks and raw signal. Input col must be a reference signal (ts, ppg, ecg) to get the
    expected length of the HR calculation. A column name with the peaks indices must be set via setter. A column name
    with the sampling freq must be set via setter.
    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """Initialize the class with the given parameters.

        Parameters
        ----------
        params : AnnotatorParams
            AnnotatorParams instance.
        """
        super().__init__(spark_session=spark_session, params=params)


    def transform(self, df, verbose: int = 0):
        """Perform the calculation from the given dataframe. Returns another dataframe with the output cols added.

        Parameters
        ----------
        df : pd.DataFrame, optional
            Input DataFrame.
        verbose : int, optional
            Level of verbosity.

        Returns
        -------
        DataFrame
            DataFrame with the output_cols added.
        """

        def _calculate_hr(peaks, ref_signal, sampling_freq) -> list:

            peak_indices = np.array(peaks, dtype=int)
            # Get the index distance between peaks
            inter_peak_distance = np.diff(peak_indices, prepend=np.nan)
            # Get the distance between peaks in seconds
            inter_peak_seconds = inter_peak_distance / float(sampling_freq)
            # Get the BPM from that distance in seconds (nan for the first value which has no previous peak)
            peak_hr_array = np.where(np.isnan(inter_peak_seconds),
                                     np.nan,
                                     60 / inter_peak_seconds)

            # Get the full signal corresponding heart rate with a forward fill
            hr_full_list = np.full(len(ref_signal), np.nan)

            if len(peak_indices) > 0:
                # Only if there are peaks in the signal to avoid indexing errors
                hr_full_list[peak_indices] = peak_hr_array

            hr_filled_series = pd.Series(hr_full_list).ffill()

            return list(hr_filled_series)

        calculate_hr_udf = F.udf(_calculate_hr, T.ArrayType(T.FloatType()))

        df = df.withColumn(
            self.params.output_cols[0],
            calculate_hr_udf(
                F.col(self.params.peaks_col),
                F.col(self.params.input_cols[0]),
                F.col(self.params.sampling_freq_col)
            )
        )

        if verbose > 0:
            row_count = df.count()
            col_count = len(df.columns)
            print(f"Final DataFrame with shape ({row_count}, {col_count})")

        return df

class FeaturizeSignal(Annotator):
    """
    This class summarizes the signal by extracting a given feature. The input columns
    should contain an iterable as value per row, and the output will be a DataFrame with the summarized feature in the
    output column.

    Example
    -------
    .. code-block:: python
        import pandas as pd
        data = {'signal': [1, 2, 3, 4, 5]}
        df = pd.DataFrame(data)
        featurizer = FeaturizeSignal(input_col='signal', output_col='summarized_signal')
        featurizer.set_mode('mean')
        model = featurizer.fit(df)
        transformed_df = model.transform(df)
    """

    def __init__(self, spark_session=None, **kwargs):
        """Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ----------
        kwargs : dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'modes': None,
            'length': None
        }

        self.params.set_default_params(default_params)


    @deco.check_type(list, 'modes')
    def set_modes(self, x):
        """Setter for multiple summarize modes.

        Parameters
        ----------
        x : list of str
            List of modes. Each must be one of the following: ['first', 'last', 'min', 'max', 'mean', 'median', 'std', 'sum', 'length', 'trend']

        Returns
        -------
        FeaturizeSignal
            The instance of the class with the updated parameter.

        Raises
        ------
        Exception
            If any mode in x is not in the mode list.
        """

        for m in x:
            if m not in ['first', 'last', 'min', 'max', 'mean', 'median', 'std', 'sum', 'length', 'trend']:
                raise Exception(f"mode must be first, last, min, max, mean, media, std, sum, length or trend but was"
                                f" {m}")
        self.params.modes = x
        return self

    @deco.check_type(int, 'length')
    def set_length(self, x):
        """Set the length of the aggregation. If provided the aggregation will be performed using the
        reference_index_col as the index of reference and taking previous values to the ref_index_col with a length
        equal to the provided length value.

        Parameters
        ----------
        x : int, None
            Length of the aggregation.

        Returns
        -------
        FeaturizeSignal
            The instance of the class with the updated parameter.

        Raises
        ------
        Exception
            If x is not None or an int greater or equal to zero.

        """
        if x < 0:
            raise Exception(f"length must be None or an integer greater or equal to zero.")
        self.params.length = x
        return self

    def fit(self, df=None, verbose: int = 0):
        """Fits the model with the given DataFrame.

        Parameters
        ----------
        df : pd.DataFrame, optional
            DataFrame to fit the model.
        verbose : int, optional
            Verbosity level.

        Returns
        -------
        FeaturizeSignalModel
            Returns an instance of FeaturizeSignalModel.
        """
        return FeaturizeSignalModel(self.spark_session, self.params)


class FeaturizeSignalModel(Model):
    """
    This class summarizes the signal by extracting a given feature.
    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """Initialize the class with the given parameters.

        Parameters
        ----------
        params : AnnotatorParams
            AnnotatorParams instance.
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, c_df, verbose: int = 0) -> pd.DataFrame:
        """Performs the feature extraction.

        Parameters
        ----------
        c_df : pd.DataFrame
            Input DataFrame to perform the signal processing.
        verbose : int, optional
            Level of verbosity.

        Returns
        -------
        pd.DataFrame
            Output DataFrame with the new feature in the output column.

        Raises
        ------
        Exception
            If the mode is not first, last, min, max, mean, median, std or sum.
        """

        # Normal summarizer function
        def summarize(values_list, length, mode):
            if values_list is None or len(values_list) == 0:
                warnings.warn("No values to summarize")
                return None

            arr = [v for v in values_list if v is not None]
            if len(arr) == 0:
                warnings.warn("No valid (non-None) values in the array")
                return None

            if length is not None and length < len(arr):
                arr = arr[-length:]

            if mode == 'first':
                return float(arr[0])
            elif mode == 'last':
                return float(arr[-1])
            elif mode == 'min':
                return float(np.min(np.array(arr)))
            elif mode == 'max':
                return float(np.max(np.array(arr)))
            elif mode == 'mean':
                return float(np.mean(np.array(arr)))
            elif mode == 'median':
                return float(np.median(np.array(arr)))
            elif mode == 'std':
                return float(np.std(np.array(arr), ddof=1))
            elif mode == 'sum':
                return float(np.sum(np.array(arr)))
            elif mode == 'length':
                return float(len(arr))
            else:
                warnings.warn(f"Unsupported mode: {mode}")
                return None

        # Convert the normal summarizer function to a UDF
        summarize_udf = F.udf(
            lambda values_list, length_val, mode_val: summarize(values_list, length_val, mode_val),
            T.DoubleType()
        )

        # Function for the trend mode
        def summarize_trend(values_list, length):

            if values_list is None or len(values_list) < 2:
                warnings.warn("Not enough data points to calculate a trend slope.")
                return None, None

            arr = [v for v in values_list if v is not None]
            if len(arr) < 2:
                warnings.warn("Not enough valid (non-None) data points to calculate a trend slope.")
                return None, None

            if length is not None and length < len(arr):
                arr = arr[-length:]

            if len(arr) < 2:
                warnings.warn("Not enough values after applying 'length' for trend calculation.")
                return None, None

            x_vals = np.arange(len(arr))
            y_vals = np.array(arr, dtype=float)

            # Fit a first-degree polynomial (linear fit)
            coeffs = np.polyfit(x_vals, y_vals, 1)
            slope = coeffs[0]
            intercept = coeffs[1]

            # Compute predictions to find RMSE
            y_pred = slope * x_vals + intercept
            rmse = float(np.sqrt(np.mean((y_vals - y_pred) ** 2)))

            return float(slope), rmse

        # Define a struct type that matches (slope, rmse) for the "trend" mode
        trend_struct = T.StructType([
            T.StructField("slope", T.DoubleType(), True),
            T.StructField("rmse", T.DoubleType(), True)
        ])

        # UDF for the trend summarizer mode
        summarize_trend_udf = F.udf(
            lambda values_list, length_val: summarize_trend(values_list, length_val),
            trend_struct
        )

        # Process each column and mode
        if not self.params.modes:
            raise Exception("At least one mode must be provided in 'self.params.modes'")

        for input_col in self.params.input_cols:
            for this_mode in self.params.modes:
                if this_mode == 'trend':
                    # Apply the trend UDF, yielding a struct {slope, rmse}
                    c_df = c_df.withColumn(
                        f"{input_col}_trend_struct",
                        summarize_trend_udf(F.col(input_col), F.lit(self.params.length))
                    )

                    # Extract the slope and RMSE from the struct into separate columns
                    c_df = c_df.withColumn(
                        f"{input_col}_trend",
                        F.col(f"{input_col}_trend_struct.slope")
                    ).withColumn(
                        f"{input_col}_error",
                        F.col(f"{input_col}_trend_struct.rmse")
                    ).drop(f"{input_col}_trend_struct")

                else:
                    # Apply the single-value UDF for other modes
                    c_df = c_df.withColumn(
                        f"{input_col}_{this_mode}",
                        summarize_udf(F.col(input_col), F.lit(self.params.length), F.lit(this_mode))
                    )

        return c_df


class PPGFiducialP2PI(Annotator):
    """
    Calculates the parameter mean p2pi of the PPG beat (mean of spacing between a systolic peak and the next one).
    Takes the segment peaks as input col.

    Example
    -------
    .. code-block:: python
        fidu_p2pi = movano.etl.annotators.features.fromsignals.PPGFiducialP2PI() \
        .set_input_col('ppg_peaks') \
        .set_output_col('p2pi') \
        .set_freq(200)

    """

    def __init__(self, spark_session=None, **kwargs):
        """Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ----------
        kwargs : dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'output_col': 'p2pi',
            'output_cols': None,
            'sampling_freq_col': 'sampling_freq_hz',
            'freq': None
        }

        self.params.set_default_params(default_params)

    @deco.check_type(str, 'sampling_freq_col')
    def set_sampling_freq_col(self, x):
        """Sets the column containing the wave frequency of the data source.

        Parameters
        ----------
        x : str
            Frequency in Hertz.

        Returns
        -------
        PPGFiducialP2PI
            The instance of the class with the updated parameter.
        """

        self.params.sampling_freq_col = x
        return self

    @deco.check_type(int, 'freq')
    def set_freq(self, x):
        """Sets the wave sampling frequency of the data source.

        Parameters
        ----------
        x : int
            Frequency in Hertz.

        Returns
        -------
        PPGFiducialP2PI
            The instance of the class with the updated parameter.
        """

        self.params.freq = x
        return self

    def fit(self, df=None, verbose: int = 0):
        """Fits the model with the given DataFrame.

        Parameters
        ----------
        df : pd.DataFrame, optional
            DataFrame to fit the model.
        verbose : int, optional
            Verbosity level.

        Returns
        -------
        PPGFiducialP2PIModel
            Returns an instance of PPGFiducialP2PIModel.
        """
        return PPGFiducialP2PIModel(self.spark_session, self.params)


class PPGFiducialP2PIModel(Model):
    """
    Calculates the parameter "mean p2pi" of the PPG segment (mean of spacing between a systolic peak and the next one).
    Takes the segment peaks as input col.
    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """Initialize the class with the given parameters.

        Parameters
        ----------
        params : AnnotatorParams
            AnnotatorParams instance.
        """
        super().__init__(spark_session=spark_session, params=params)


    def transform(self, c_df, verbose=0):
        """
        Perform the calculation from the given dataframe. Returns another dataframe with the output cols added.

        Parameters
        ----------
        c_df : pd.DataFrame, optional
            Input DataFrame.
        verbose : int, optional
            Level of verbosity.

        Returns
        -------
        DataFrame
            DataFrame with the output_cols added.
        """

        def compute_p2pi_error(segment_peaks, freq):
            if segment_peaks is None or len(segment_peaks) < 3:
                return None, 1
            else:
                indices_to_times = [peak / freq for peak in segment_peaks]
                time_difference = indices_to_times[-1] - indices_to_times[-2]
                return time_difference, 0

        # Struct for UDF output:
        compute_p2pi_error_udf = F.udf(
            compute_p2pi_error,
            T.StructType([
                T.StructField("time_difference", T.DoubleType(), True),
                T.StructField("error", T.IntegerType(), True)
            ])
        )

        # Struct generation using the UDF
        c_df = c_df.withColumn(
            "result_struct",
            compute_p2pi_error_udf(F.col(self.params.input_cols[0]), F.col(self.params.sampling_freq_col))
            )

        # Results distributed in output columns
        c_df = (
            c_df
            .withColumn(self.params.output_cols[0], F.col("result_struct.time_difference"))
            .withColumn(self.params.output_cols[1], F.col("result_struct.error"))
            .drop("result_struct")
        )

        return c_df


class TrigonometricExtractor(Annotator):

    def __init__(self, spark_session=None, **kwargs):
        """Initialize the class with the given parameters and sets the corresponding default ones.

        Parameters
        ----------
        kwargs : dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'input_cols': None,
            'output_cols': None,
            'output_cols_prefix': None
        }

        self.params.set_default_params(default_params)

    @deco.check_type(list, 'input_cols')
    def set_input_cols(self, x):
        if len(x) != 4:
            raise ValueError("The input columns must be a list of 4 columns: x_time, x_value, y_time, y_value.")

        self.params.input_cols = x
        return self

    @deco.check_type(list, 'output_cols')
    def set_output_cols(self, x):
        """Sets the output columns.

        Parameters
        ----------
        x : list
            List of output columns.

        Returns
        -------
        TrigonometricExtractor
            The instance of the class with the updated parameter.
        """
        if len(x) != 5:
            raise ValueError("The output columns must be a list of 5 columns: cosine, sine, magnitude, angle, degrees.")

        self.params.output_cols = x
        return self

    @deco.check_type(str, 'output_cols_prefix')
    def set_output_cols_prefix(self, x):
        """Sets the prefix for the output columns.

        Parameters
        ----------
        x : str
            Prefix for the output columns.

        Returns
        -------
        TrigonometricExtractor
            The instance of the class with the updated parameter.
        """
        if x == "":
            raise ValueError("The prefix cannot be an empty string.")

        self.params.prefix_output_cols = x
        return self

    def fit(self, df = None, verbose: int = 0):

        if len(self.params.input_cols) != 4:
            raise ValueError('Set the four input cols: [x_value, x_time, y_value, y_time]')
        return TrigonometricExtractorModel(self.spark_session, self.params)


class TrigonometricExtractorModel(Model):

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, df = None, verbose: int = 0):

        if self.params.output_cols is not None:
            # Use the output cols if they are set
            output_cols_names = self.params.output_cols
        else:
            output_cols_names = [
                self.params.prefix_output_cols + '_cosine',
                self.params.prefix_output_cols + '_sine',
                self.params.prefix_output_cols + '_magnitude',
                self.params.prefix_output_cols + '_angle',
                self.params.prefix_output_cols + '_degrees',
            ]

        df = df.withColumn("v_x", F.col(self.params.input_cols[2]) - F.col(self.params.input_cols[0]))
        df = df.withColumn("v_y", F.col(self.params.input_cols[3]) - F.col(self.params.input_cols[1]))

        # Magnitude
        df = df.withColumn(output_cols_names[2], F.sqrt(F.col("v_x") ** 2 + F.col("v_y") ** 2))

        # Cosine
        df = df.withColumn(output_cols_names[0], F.col("v_x") / F.col(output_cols_names[2]))

        # Sine
        df = df.withColumn(output_cols_names[1], F.col("v_y") / F.col(output_cols_names[2]))

        # Angle
        df = df.withColumn(output_cols_names[3], F.atan2(F.col(output_cols_names[1]), F.col(output_cols_names[0])))

        # Degrees
        df = df.withColumn(output_cols_names[4], F.degrees(F.col(output_cols_names[3])))

        df = df.drop("v_x", "v_y")

        return df


class PPGFiducialPoints(Annotator):
    """
    Uses a rule-based algorithm and the sequential derivatives of a PPG segment (VPG, APG, JPG) to extract relevant
    fiducial points in the signal. Based on PPGToolbox MATLAB program by Abdullah et al.

    Example
    -------
    .. code-block:: python
        import pandas as pd
        data = {
            'ppg_signal': [...],  # PPG signal values
            'case_col': [...]     # Case values indicating segment type
        }
        df = pd.DataFrame(data)
        ppg_fiducials = PPGFiducialPoints(input_col='ppg_signal', output_cols=['error', 'o', 's', 'n', 'dias'])
        ppg_fiducials.set_window(5)
        ppg_fiducials.set_case_col('case_col')
        model = ppg_fiducials.fit(df)
        transformed_df = model.transform(df)
    """

    def __init__(self, spark_session=None, **kwargs):
        """Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ----------
        kwargs : dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'output_cols': None,
            'case_col': None,
            'window': 5,
            'complete_results': False
        }

        self.params.set_default_params(default_params)

    @deco.check_type(int, 'window')
    def set_window(self, x):
        """Set window for the moving average that's going to be applied to the derivatives.

        Parameters
        ----------
        x : int
            Window for the moving average.

        Returns
        -------
        PPGFiducialPoints
            The instance of the class with the updated parameter.

        Raises
        ------
        Exception
            If x is not a positive integer.

        Example
        -------
        .. code-block:: python
            ppg_fiducials = PPGFiducialPoints()
            ppg_fiducials.set_window(5)

        """
        if x <= 0:
            raise Exception("Window must be a positive integer.")
        else:
            self.params.window = x
        return self

    @deco.check_type(str, 'case_col')
    def set_case_col(self, x):
        """Set column containing the case of the segment, needed to perform the extraction. Calculated using the
        annotator PPGCaseClassifier in signal.characterization.

        Parameters
        ----------
        x : str
            Column containing the case of the segment.

        Returns
        -------
        PPGFiducialPoints
            The instance of the class with the updated parameter.
        """
        self.params.case_col = x
        return self

    @deco.check_type(bool, 'complete_results')
    def set_complete_results(self, x):
        """Choose if it should output the full list of fiduciary points or just the main ones.
        False by default, meaning the output will be:
        [error_present, o, s, n, dias].

        Parameters
        ----------
        x : bool
            Whether to output all fiducial points or just the main ones.

        Returns
        -------
        PPGFiducialPoints
            The instance of the class with the updated parameter.
        """

        self.params.complete_results = x
        return self

    def fit(self, df=None, verbose: int = 0):
        """Fits the model with the given DataFrame.

        Parameters
        ----------
        df : pd.DataFrame, optional
            DataFrame to fit the model.
        verbose : int, optional
            Verbosity level.

        Returns
        -------
        PPGFiducialPointsModel
            Returns an instance of PPGFiducialPointsModel.
        """
        if self.params.complete_results and (len(self.params.output_cols) not in [15, 19]):
            raise Exception('Output columns must be: presence of error, o, s, n, dias, w, x, y, z, a, b, c, d, e, f +/-'
                            'the columns for values o_val, s_val, n_val, dias_val')
        elif not self.params.complete_results and (len(self.params.output_cols) not in [5, 9]):
            raise Exception('Output columns must be: presence of error, o, s, n, dias +/- the columns for values o_val,'
                            's_val, n_val, dias_val')
        else:
            if verbose == 1:
                warnings.warn(
                    'Remember fiducial point extraction for PPG currently assumes segments are cut by valley.')
        return PPGFiducialPointsModel(self.spark_session, self.params)


class PPGFiducialPointsModel(Model):
    """
    Uses a rule-based algorithm and the sequential derivatives of a PPG segment (VPG, APG, JPG) to extract relevant
    fiducial points in the signal. Based on PPGToolbox MATLAB program by Abdullah et al.
    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """Initialize the class with the given parameters.

        Parameters
        ----------
        params : AnnotatorParams
            AnnotatorParams instance.
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, c_df, verbose=0):
        """
        Perform the calculation from the given dataframe. Returns another dataframe with the output cols added.

        Parameters
        ----------
        c_df : DataFrame, optional
            Input DataFrame.
        verbose : int, optional
            Level of verbosity.

        Returns
        -------
        DataFrame
            DataFrame with the output_cols added.
        """

        def _mov_avg(signal, window):
            return np.convolve(signal, np.ones(window) / window, mode='same')

        def _get_nth_zero_crossing(zero_crossings, crossing_index):
            if len(zero_crossings) > crossing_index:
                return zero_crossings[crossing_index]
            return None

        def fiducial_extraction(segment_ppg, case, window):
            # Set error checker
            error_present = 0
            segment_ppg = np.asarray(segment_ppg)

            # Breakpoint for badly classified beats
            if case is None:
                error_present = 1
                o = s = n = dias = w = x = y = z = a = b = c = d = e = f = o_val = s_val = n_val = dias_val = None
                return error_present, o, s, n, dias, w, x, y, z, a, b, c, d, e, f, o_val, s_val, n_val, dias_val

            # The following line is to avoid the IDE thinking that the variables are being referenced before assignment.
            # This won't happen unless there is a PPG type different from 1, 2 or 3 which is not possible.
            e = f = c = d = None

            # Find O: First minima. This annotator assumes segments are cut by valleys, so it would be the first point.
            o = 0

            # Find S: First maxima (technically, but I'm using the max value for robustness).
            s = segment_ppg.argmax()

            # Derivatives are computed and put through a moving average to get the velocity,
            # acceleration and jerk signals:
            segment_vpg = _mov_avg(np.gradient(segment_ppg), window)
            segment_apg = _mov_avg(np.gradient(segment_vpg), window)
            segment_jpg = _mov_avg(np.gradient(segment_apg), window)

            # w corresponds to the  first maxima of the VPG
            pks, _ = sp.find_peaks(segment_vpg)

            if len(pks) > 0:
                w = pks[0]
            else:
                error_present = 1
                w = None

            # x corresponds to the systolic point of the PPG
            x = s

            # y corresponds to the first minima of the VPG
            vly, _ = sp.find_peaks(-segment_vpg)

            if len(vly) > 0:
                y = vly[0]
            else:
                error_present = 1
                y = None

            # z corresponds to the second maxima of the VPG or the zero crossing point of the APG after the e point.
            z_piece = segment_vpg[y:]
            pks, _ = sp.find_peaks(z_piece)

            if len(pks) > 0:
                z = pks[0] + y  # Since we found the peak on the cropped segment, add the cropped part.
            else:
                error_present = 1
                z = None

            # Point "a" is the global maxima of APG and b the global minima.
            a = segment_apg.argmax()
            b = segment_apg.argmin()

            # We need the zero-crossings indices of the JPG (third derivative).
            apg_zero_crossings = np.where(np.diff(np.sign(segment_apg)))[0]
            jpg_zero_crossings = np.where(np.diff(np.sign(segment_jpg)))[0]

            # Due to possible artifacts with variations of the cut signal,
            # we will consider only the zero crossings from before point 'a' onwards.
            apg_zero_crossings = [point for point in apg_zero_crossings if point >= a - 1]
            jpg_zero_crossings = [point for point in jpg_zero_crossings if point >= a - 1]

            if case == 1:
                # c is the first maxima of JPG after the second zero-crossing.
                if len(jpg_zero_crossings) > 1:
                    c_piece = segment_jpg[jpg_zero_crossings[1]:]
                    pks, _ = sp.find_peaks(c_piece)

                    if len(pks) > 0:
                        c = pks[0] + jpg_zero_crossings[1]
                    else:
                        error_present = 1
                        c = None
                else:
                    error_present = 1
                    c = None

                # d is the second zero-crossing in APG
                d = _get_nth_zero_crossing(apg_zero_crossings, 1)
                if d is None:
                    error_present = 1

                # e is the third zero-crossing of the JPG or the second maxima of the APG
                e = _get_nth_zero_crossing(jpg_zero_crossings, 2)
                if e is None:
                    error_present = 1

                # f is the fourth zero-crossing of the JPG or the second minima of the APG
                f = _get_nth_zero_crossing(jpg_zero_crossings, 3)
                if f is None:
                    error_present = 1

            elif case == 2:
                # Locate the second minima of the JPG as reference and get the total wavelength. Points c and
                # d are besides the second JPG minima.
                wlen = len(segment_ppg)
                vly, _ = sp.find_peaks(-segment_jpg)
                vly = [point for point in vly if point >= a - 1]

                if len(vly) > 1:
                    c = round(vly[1] - (0.025 * wlen))
                    d = round(vly[1] + (0.025 * wlen))

                else:
                    c = d = None
                    error_present = 1

                # e is the third zero-crossing of the JPG or the second maxima of the APG
                e = _get_nth_zero_crossing(jpg_zero_crossings, 2)
                if e is None:
                    error_present = 1

                # f is the fourth zero-crossing of the JPG or the second minima of the APG
                f = _get_nth_zero_crossing(jpg_zero_crossings, 3)
                if f is None:
                    error_present = 1

            elif case == 3:
                # c is the second APG maxima and d is the second APG minima.
                pks, _ = sp.find_peaks(segment_apg)
                vly, _ = sp.find_peaks(-segment_apg)

                pks = [point for point in pks if point >= a - 1]
                vly = [point for point in vly if point >= a - 1]

                if len(pks) > 1:
                    c = pks[1]
                else:
                    c = None
                    error_present = 1

                if len(vly) > 1:
                    d = vly[1]
                else:
                    d = None
                    error_present = 1

                # e is the first zero-crossing of the JPG after d
                jpg_after_d = [i for i in jpg_zero_crossings if i > d]

                e = _get_nth_zero_crossing(jpg_after_d, 0)
                if e is None:
                    error_present = 1

                # f is the second zero-crossing of the JPG after d
                f = _get_nth_zero_crossing(jpg_after_d, 1)
                if f is None:
                    error_present = 1

            n = e
            dias = f

            # Fiducial value extraction:
            o_val = s_val = n_val = dias_val = None  # default NULL
            if o is not None: o_val = float(segment_ppg[o])
            if s is not None: s_val = float(segment_ppg[s])
            if n is not None: n_val = float(segment_ppg[n])
            if dias is not None: dias_val = float(segment_ppg[dias])

            # Avoid remaining numpy types
            def _no_np_types(x):
                if x is None:  # already NULL
                    return None
                if isinstance(x, np.generic):  # unwrap numpy.int64, numpy.float64, …
                    x = x.item()
                if isinstance(x, float) and np.isnan(x):
                    return None  # NaN → NULL
                return x

            out = [
                error_present, o, s, n, dias,
                w, x, y, z, a, b, c, d, e, f,
                o_val, s_val, n_val, dias_val
            ]
            return tuple(map(_no_np_types, out))

        struct_schema = T.StructType([
            T.StructField("error_present", T.IntegerType()),
            T.StructField("o", T.IntegerType()),
            T.StructField("s", T.IntegerType()),
            T.StructField("n", T.IntegerType()),
            T.StructField("dias", T.IntegerType()),
            T.StructField("w", T.IntegerType()),
            T.StructField("x", T.IntegerType()),
            T.StructField("y", T.IntegerType()),
            T.StructField("z", T.IntegerType()),
            T.StructField("a", T.IntegerType()),
            T.StructField("b", T.IntegerType()),
            T.StructField("c", T.IntegerType()),
            T.StructField("d", T.IntegerType()),
            T.StructField("e", T.IntegerType()),
            T.StructField("f", T.IntegerType()),
            T.StructField("o_val", T.DoubleType()),
            T.StructField("s_val", T.DoubleType()),
            T.StructField("n_val", T.DoubleType()),
            T.StructField("dias_val", T.DoubleType())
        ])

        def fiducial_extraction_wrapper(segment_ppg, case):
            return fiducial_extraction(segment_ppg, case, self.params.window)

        fiducial_struct_fields = [
            "error_present", "o", "s", "n", "dias",
            "w", "x", "y", "z", "a", "b", "c", "d", "e", "f",
            "o_val", "s_val", "n_val", "dias_val"
        ]

        fiducial_extraction_udf = F.udf(fiducial_extraction_wrapper,
                                        struct_schema)

        c_df = c_df.withColumn("fiducial_struct",
                               fiducial_extraction_udf(
                                   F.col(self.params.input_cols[0]),
                                   F.col(self.params.case_col)))

        # Extract the 5 main outputs (all cases)
        for out_col, struct_idx in zip(self.params.output_cols, [0, 1, 2, 3, 4]):
            c_df = c_df.withColumn(out_col, F.col("fiducial_struct")[fiducial_struct_fields[struct_idx]])

        # Extract value outputs for any mode only if the output col length works out (backwards compatibility)
        if not self.params.complete_results and len(self.params.output_cols) == 9:
            for out_col, struct_idx in zip(self.params.output_cols[5:], [15, 16, 17, 18]):
                c_df = c_df.withColumn(out_col, F.col("fiducial_struct")[fiducial_struct_fields[struct_idx]])

        # Extract complete results:
        if self.params.complete_results:
            for out_col, struct_idx in zip(self.params.output_cols[6:], [5, 6, 7, 8, 9, 10, 11, 12, 13, 14]):
                c_df = c_df.withColumn(out_col, F.col("fiducial_struct")[fiducial_struct_fields[struct_idx]])

        # Extract value outputs for complete mode with corresponding output columns:
        if self.params.complete_results and len(self.params.output_cols) == 19:
            for out_col, struct_idx in zip(self.params.output_cols[6:], [15, 16, 17, 18]):
                c_df = c_df.withColumn(out_col, F.col("fiducial_struct")[fiducial_struct_fields[struct_idx]])

        c_df = c_df.drop("fiducial_struct")
        return c_df


class PPGFiducialDerivative(Annotator):
    """
    Calculates the parameters tb1 and tb2 from a PPG beat.

    Example
    -------
    .. code-block:: python
        fidu_derivatives = etl.annotators.features.fromsignals.PPGFiducialDerivative() \
            .set_input_col('last_beat') \
            .set_output_cols(['error', 'tb1', 'tb2'])\
            .set_freq(200)
    """

    def __init__(self, spark_session=None, **kwargs):
        """Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ----------
        kwargs : dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'output_cols': ['error_derivatives', 'tb1', 'tb2'],
            'sampling_freq_col': 'sampling_freq_hz',
            'freq': None
        }

        self.params.set_default_params(default_params)

    @deco.check_type(str, 'sampling_freq_col')
    def set_sampling_freq_col(self, x):
        """Sets the column containing the wave frequency of the data source.

        Parameters
        ----------
        x : str
            Frequency in Hertz.

        Returns
        -------
        PPGFiducialDerivative
            The instance of the class with the updated parameter.
        """

        self.params.sampling_freq_col = x
        return self

    @deco.check_type(int, 'freq')
    def set_freq(self, x):
        """Sets the wave frequency of the data source.

        Parameters
        ----------
        x : int
            Frequency in Hertz.

        Returns
        -------
        PPGFiducialDerivative
            The instance of the class with the updated parameter.
        """

        self.params.freq = x
        return self

    def fit(self, df=None, verbose: int = 0):
        """Fits the model with the given DataFrame.

        Parameters
        ----------
        df : pd.DataFrame, optional
            DataFrame to fit the model.
        verbose : int, optional
            Verbosity level.

        Returns
        -------
        PPGFiducialDerivativesModel
            Returns an instance of PPGFiducialDerivativesModel.
        """
        return PPGFiducialDerivativeModel(self.spark_session, self.params)


class PPGFiducialDerivativeModel(Model):
    """
    Calculates the parameters tb1 and tb2 from a PPG beat.
    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """Initialize the class with the given parameters.

        Parameters
        ----------
        params : AnnotatorParams
            AnnotatorParams instance.
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, c_df, verbose=0):
        """
        Perform the calculation from the given dataframe. Returns another dataframe with the output cols added.

        Parameters
        ----------
        c_df : pd.DataFrame, optional
            Input DataFrame.
        verbose : int, optional
            Level of verbosity.

        Returns
        -------
        DataFrame
            DataFrame with the output_cols added.
        """

        def extract_fidus(segment_ppg, freq):
            # Set error checker
            error_present = 0

            segment_ppg = np.asarray(segment_ppg)

            # Derivatives are computed:
            first_der = np.diff(segment_ppg)
            second_der = np.diff(first_der)

            peaks_first_der, _ = sp.find_peaks(first_der)
            valleys_first_der, _ = sp.find_peaks(-first_der)
            peaks_second_der, _ = sp.find_peaks(second_der)
            valleys_second_der, _ = sp.find_peaks(-second_der)

            # a1 corresponds to the  first maxima of the VPG (first derivative).
            a1 = peaks_first_der[0] if len(peaks_first_der) > 0 else None

            # b1 corresponds to the first minima of the first derivative after a1
            if not np.isnan(a1):
                b1 = valleys_first_der[valleys_first_der > a1][0] if len(
                    valleys_first_der[valleys_first_der > a1]) > 0 else None
            else:
                b1 = None

            # a2 corresponds to the first maxima of the second derivative after a1
            if not np.isnan(a1):
                a2 = peaks_second_der[peaks_second_der > a1][0] if len(
                    peaks_second_der[peaks_second_der > a1]) > 0 else None
            else:
                a2 = None

            # b2 corresponds to the first minima of the second derivative after a2
            if not np.isnan(a2):
                b2 = valleys_second_der[valleys_second_der > a2][0] if len(
                    valleys_second_der[valleys_second_der > a2]) > 0 else None
            else:
                b2 = None

            # Get the times which are the actual features
            period = 1 / freq

            # tb1 is the time interval from the first value to the time at which b1 occurred
            tb1 = period * b1 if not np.isnan(b1) else None

            # tb2 is the time interval from the first value to the time at which b2 occurred
            tb2 = period * b2 if not np.isnan(b2) else None

            if any([i is None for i in [a1, b1, a2, b2, tb1, tb2]]):
                error_present = 1

            error_present = int(error_present)
            tb1 = float(tb1) if not None else None
            tb2 = float(tb2) if not None else None

            return error_present, tb1, tb2

        fidus_schema = T.StructType([
            T.StructField("error_present", T.IntegerType(), True),
            T.StructField("tb1", T.DoubleType(), True),
            T.StructField("tb2", T.DoubleType(), True),
        ])

        extract_fidus_udf = F.udf(extract_fidus, fidus_schema)

        if self.params.freq is None:
            freq_col = F.col(self.params.sampling_freq_col)
        else:
            freq_col = F.lit(float(self.params.freq))

        c_df = c_df.withColumn(
            "_fidus",
            extract_fidus_udf(
                F.col(self.params.input_cols[0]),
                freq_col
            )
        )

        c_df = (c_df
                .withColumn(self.params.output_cols[0], F.col("_fidus.error_present"))
                .withColumn(self.params.output_cols[1], F.col("_fidus.tb1"))
                .withColumn(self.params.output_cols[2], F.col("_fidus.tb2"))
                .drop("_fidus"))

        return c_df


class PPGFiducialFFT(Annotator):
    """
    Calculates parameters extracted from the Fast Fourier Transformation of the beat. Mode AUC calculates the area
    under the curve generated by the frequency bins, with set lower and upper limits. Mode PeakFreq calculates at
    which frequency does a certain peak in the signal occurs.

    Example
    -------
    .. code-block:: python
        fidu_fft = etl.annotators.features.fromsignals.PPGFiducialFFT() \
                .set_input_col('last_beat') \
                .set_output_col('auc2to5')\
                .set_freq(200)\
                .set_f_min(2)\
                .set_f_max(5)

        results = fidu_fft.fit().annotate(input_df)

    """

    def __init__(self, spark_session=None, **kwargs):
        """Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ----------
        kwargs : dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'output_col': None,
            'output_cols': None,
            'sampling_freq_col': 'sampling_freq_hz',
            'f_min': 2,
            'f_max': 5,
            'mode': 'auc',
            'peak_number': 0,
            'freq': None
        }

        self.params.set_default_params(default_params)

    @deco.check_type(str, 'sampling_freq_col')
    def set_sampling_freq_col(self, x):
        """Sets the column containing the wave frequency of the data source.

        Parameters
        ----------
        x : str
            Frequency in Hertz.

        Returns
        -------
        PPGFiducialP2PI
            The instance of the class with the updated parameter.
        """

        self.params.sampling_freq_col = x
        return self

    @deco.check_type(int, 'freq')
    def set_freq(self, x):
        """Sets the wave sampling frequency of the data source.

        Parameters
        ----------
        x : int
            Frequency in Hertz.

        Returns
        -------
        PPGFiducialP2PI
            The instance of the class with the updated parameter.
        """

        self.params.freq = x
        return self

    @deco.check_type(int, 'f_min')
    def set_f_min(self, x):
        """Sets the min freq bin for calculating the AUC.

        Parameters
        ----------
        x : int
            Frequency in Hertz.

        Returns
        -------
        PPGFiducialFFT
            The instance of the class with the updated parameter.
        """

        self.params.f_min = x
        return self

    @deco.check_type(int, 'f_max')
    def set_f_max(self, x):
        """Sets the max freq bin for calculating the AUC.

        Parameters
        ----------
        x : int
            Frequency in Hertz.

        Returns
        -------
        PPGFiducialFFT
            The instance of the class with the updated parameter.
        """

        self.params.f_max = x
        return self

    @deco.check_type(str, 'mode')
    def set_mode(self, x):
        """Sets the mode (calculate AUC or Freq)

        Parameters
        ----------
        x : str
            Type of calculation ('auc' or 'peakfreq').

        Returns
        -------
        PPGFiducialFFT
            The instance of the class with the updated parameter.
        """
        if x in ['auc', 'peakfreq']:
            self.params.mode = x
            return self
        else:
            raise ValueError("Mode must be auc or peakfreq.")

    @deco.check_type(int, 'peak_number')
    def set_peak_number(self, x):
        """Sets the target peak to get the frequency bin in which it occurs. SET TO THE INDEX (e.g. 0 for first peak).

        Parameters
        ----------
        x : int
            Index of the target peak to obtain.

        Returns
        -------
        PPGFiducialFFT
            The instance of the class with the updated parameter.
        """

        self.params.peak_number = x
        return self

    def fit(self, df=None, verbose: int = 0):
        """Fits the model with the given DataFrame.

        Parameters
        ----------
        df : pd.DataFrame, optional
            DataFrame to fit the model.
        verbose : int, optional
            Verbosity level.

        Returns
        -------
        PPGFiducialFFTModel
            Returns an instance of PPGFiducialFFTModel.
        """

        if self.params.mode == 'peakfreq':
            if len(self.params.output_cols) != 3:
                raise ValueError('Set 2 output cols, corresponding to "Freq1", "SPL" and "presence of errors"'
                                 ' -in that order- for peak frequency mode.')

        return PPGFiducialFFTModel(self.spark_session, self.params)


class PPGFiducialFFTModel(Model):
    """
    Calculates parameters extracted from the Fast Fourier Transformation of the beat. Mode AUC calculates the area
    under the curve generated by the frequency bins, with set lower and upper limits. Mode PeakFreq calculates at
    which frequency does a certain peak in the signal occurs.
    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """Initialize the class with the given parameters.

        Parameters
        ----------
        params : AnnotatorParams
            AnnotatorParams instance.
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, c_df, verbose=0):
        """
        Perform the calculation from the given dataframe. Returns another dataframe with the output cols added.

        Parameters
        ----------
        c_df : pd.DataFrame, optional
            Input DataFrame.
        verbose : int, optional
            Level of verbosity.

        Returns
        -------
        DataFrame
            DataFrame with the output_cols added.
        """

        # FFT AUC mode calculation
        def _auc_core(segment_ppg, freq, f_min, f_max):
            fft_mag = np.abs(np.fft.fft(segment_ppg))
            freqs = np.fft.fftfreq(len(segment_ppg), 1.0 / freq)
            idx = np.where((freqs >= f_min) & (freqs <= f_max))[0]
            if idx.size == 0:
                return None
            return np.trapz(fft_mag[idx], freqs[idx])

        # FFT PEAKFREQ mode calculation
        def _peak_core(segment_ppg, freq, peak_number):
            fft_mag = np.abs(np.fft.fft(segment_ppg))
            freqs = np.fft.fftfreq(len(segment_ppg), 1.0 / freq)
            peaks, _ = sp.find_peaks(fft_mag)

            if peaks.size == 0:
                return None, None, 1

            spectral_peak = freqs[peaks[np.argmax(fft_mag[peaks])]]
            if len(peaks) >= peak_number + 1:
                peak_freq = freqs[peaks[peak_number]]
                err = 0
            else:  # not enough peaks
                peak_freq = 0.0
                err = 1
            return peak_freq, abs(spectral_peak), err

        # UDF builders:
        def build_auc_udf(f_min, f_max):
            """Returns a pandas UDF for AUC mode (returns one double per row)."""

            @F.pandas_udf(T.DoubleType())
            def _udf(ppg_series, freq_series):
                return pd.Series([
                    _auc_core(ppg, f, f_min, f_max)
                    for ppg, f in zip(ppg_series, freq_series)
                ])

            return _udf

        def build_peak_udf(peak_number):
            """Returns a pandas UDF returning a struct with three fields for the peakfreq mode."""
            peak_schema = T.StructType([
                T.StructField("peak_freq", T.DoubleType(), True),
                T.StructField("spectral_peak", T.DoubleType(), True),
                T.StructField("fft_error", T.IntegerType(), True),
            ])

            @F.pandas_udf(peak_schema)
            def _udf(ppg_series, freq_series):
                data = [
                    _peak_core(ppg, f, peak_number)
                    for ppg, f in zip(ppg_series, freq_series)
                ]
                return pd.DataFrame(data, columns=peak_schema.fieldNames())

            return _udf

        # The actual execution logic:
        if self.params.freq is None:
            freq_col = F.col(self.params.sampling_freq_col)
        else:
            freq_col = F.lit(self.params.freq)

        # AUC
        if self.params.mode == "auc":
            auc_udf = build_auc_udf(self.params.f_min, self.params.f_max)
            c_df = c_df.withColumn(
                self.params.output_cols[0],
                auc_udf(F.col(self.params.input_cols[0]), freq_col)
            )

        #  PEAKFREQ
        elif self.params.mode == "peakfreq":
            peak_udf = build_peak_udf(self.params.peak_number)

            c_df = (
                c_df
                .withColumn("fft_struct", peak_udf(F.col(self.params.input_cols[0]), freq_col))
                .withColumn(self.params.output_cols[0], F.col("fft_struct.peak_freq"))
                .withColumn(self.params.output_cols[1], F.col("fft_struct.spectral_peak"))
                .withColumn(self.params.output_cols[2], F.col("fft_struct.fft_error"))
                .drop("fft_struct")
            )

        else:
            raise ValueError("mode must be 'auc' or 'peakfreq'")

        return c_df


class PPGFiducialPercentiles(Annotator):
    """
    Calculates statistical features from the segment such as the IQR and the percentiles.

    Example
    -------
    .. code-block:: python
        fidu_percentiles = etl.annotators.features.fromsignals.PPGFiducialPercentiles() \
            .set_input_col('last_beat') \
            .set_output_cols(['perc75', 'iqr'])

    """

    def __init__(self, spark_session=None, **kwargs):
        """Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ----------
        kwargs : dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'output_cols': None
        }

        self.params.set_default_params(default_params)

    def fit(self, df=None, verbose: int = 0):
        """Fits the model with the given DataFrame.

        Parameters
        ----------
        df : pd.DataFrame, optional
            DataFrame to fit the model.
        verbose : int, optional
            Verbosity level.

        Returns
        -------
        PPGFiducialPercentilesModel
            Returns an instance of PPGFiducialPercentilesModel.
        """
        if len(self.params.output_cols) != 2:
            raise ValueError("Must set output cols in the order: percentile75, interquartilic range")
        else:
            return PPGFiducialPercentilesModel(self.spark_session, self.params)


class PPGFiducialPercentilesModel(Model):
    """
    Calculates statistical features from the segment such as the IQR and the percentiles.
    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """Initialize the class with the given parameters.

        Parameters
        ----------
        params : AnnotatorParams
            AnnotatorParams instance.
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, c_df, verbose=0):
        """
        Perform the calculation from the given dataframe. Returns another dataframe with the output cols added.

        Parameters
        ----------
        c_df : pd.DataFrame, optional
            Input DataFrame.
        verbose : int, optional
            Level of verbosity.

        Returns
        -------
        DataFrame
            DataFrame with the output_cols added.
        """

        in_col = self.params.input_cols[0]
        p75_col, iqr_col = self.params.output_cols

        schema = T.StructType([
            T.StructField(p75_col, T.DoubleType(), True),
            T.StructField(iqr_col, T.DoubleType(), True)
        ])

        @F.pandas_udf(schema)
        def _pct_udf(arr_ser: pd.Series) -> pd.DataFrame:
            p75 = arr_ser.apply(lambda a: float(np.percentile(a, 75)) if len(a) > 0 else np.nan)
            p25 = arr_ser.apply(lambda a: float(np.percentile(a, 25)) if len(a) > 0 else np.nan)
            return pd.DataFrame({p75_col: p75, iqr_col: p75 - p25})

        c_df = c_df.withColumn("_percentiles_", _pct_udf(F.col(in_col))) \
            .select("*", F.col("_percentiles_.*")) \
            .drop("_percentiles_")

        return c_df


class PPGFiducialAreas(Annotator):
    """
    Calculates the area related features from a PPG beat. Takes as an input the segment, the sampling frequency and
    the fiducial points which should have been obtained first.

    Example
    -------
    .. code-block:: python
        fidu_areas = etl.annotators.features.fromsignals.PPGFiducialAreas() \
            .set_input_col('last_beat') \
            .set_dn_index_col('n')\
            .set_sampling_freq_col('sampling_freq_hz')\
            .set_output_cols(['a1','a2', 'arearatio' , 'a0toslope'])
    """

    def __init__(self, spark_session=None, **kwargs):
        """Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ----------
        kwargs : dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'output_col': None,
            'output_cols': None,
            'sampling_freq_col': 'sampling_freq_hz',
            'dn_index_col': None,
            'freq': None
        }

        self.params.set_default_params(default_params)

    @deco.check_type(str, 'sampling_freq_col')
    def set_sampling_freq_col(self, x):
        """Sets the column containing the wave frequency of the data source.

        Parameters
        ----------
        x : str
            Frequency in Hertz.

        Returns
        -------
        PPGFiducialP2PI
            The instance of the class with the updated parameter.
        """

        self.params.sampling_freq_col = x
        return self

    @deco.check_type(int, 'freq')
    def set_freq(self, x):
        """Sets the wave sampling frequency of the data source.

        Parameters
        ----------
        x : int
            Frequency in Hertz.

        Returns
        -------
        PPGFiducialP2PI
            The instance of the class with the updated parameter.
        """

        self.params.freq = x
        return self

    @deco.check_type(str, 'dn_index_col')
    def set_dn_index_col(self, x):
        """Sets the name of the column that contains the dicrotic notch index.

        Parameters
        ----------
        x : str
            Name of the column that contains the dicrotic notch index..

        Returns
        -------
        PPGFiducialAreas
            The instance of the class with the updated parameter.
        """

        self.params.dn_index_col = x
        return self

    def fit(self, df=None, verbose: int = 0):
        """Fits the model with the given DataFrame.

        Parameters
        ----------
        df : pd.DataFrame, optional
            DataFrame to fit the model.
        verbose : int, optional
            Verbosity level.

        Returns
        -------
        PPGFiducialAreasModel
            Returns an instance of PPGFiducialAreasModel.
        """
        if self.params.output_col is not None:
            raise ValueError("Please use output cols (list) even if it's one."
                             " For future proofing (more features incoming).")

        if len(self.params.output_cols) != 4 or self.params.dn_index_col is None:
            raise ValueError(
                'Remember to set frequency and DN index col . Input column must be: segment. Output'
                'columns should be: A1, A2, RA, Area0ToSlope')
        else:
            return PPGFiducialAreasModel(self.spark_session, self.params)


class PPGFiducialAreasModel(Model):
    """
    Calculates the area related features from a PPG beat. Takes as an input the segment, the sampling frequency and
    the fiducial points which should have been obtained first.
    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """Initialize the class with the given parameters.

        Parameters
        ----------
        params : AnnotatorParams
            AnnotatorParams instance.
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, c_df, verbose=0):
        """
        Perform the calculation from the given dataframe. Returns another dataframe with the output cols added.

        Parameters
        ----------
        c_df : pd.DataFrame, optional
            Input DataFrame.
        verbose : int, optional
            Level of verbosity.

        Returns
        -------
        DataFrame
            DataFrame with the output_cols added.
        """

        def _compute_areas(segment_ppg: np.ndarray, freq: float, dn_index: float):
            if dn_index is None or np.isnan(dn_index):
                return [None, None, None, None]

            dn_index = int(dn_index)
            dt = 1.0 / freq

            a1 = scipy.integrate.trapezoid(segment_ppg[:dn_index + 1]) * dt
            a2 = scipy.integrate.trapezoid(segment_ppg[dn_index:]) * dt
            ra = a2 / a1

            slopes = np.diff(segment_ppg) / dt
            max_slope_point = np.argmax(slopes) + 1
            area_to_maxSlope = scipy.integrate.trapezoid(segment_ppg[:max_slope_point + 1]) * dt

            return [a1, a2, ra, area_to_maxSlope]

        areas_schema = T.StructType([
            T.StructField("a1", T.DoubleType()),
            T.StructField("a2", T.DoubleType()),
            T.StructField("ra", T.DoubleType()),
            T.StructField("area_begin_to_slope", T.DoubleType())
        ])

        @F.pandas_udf(areas_schema)
        def compute_areas_udf(
                segment_ppg: pd.Series,
                freq_col: pd.Series,
                dn_index: pd.Series) -> pd.DataFrame:
            out = [_compute_areas(np.array(seg), f, dn)
                   for seg, f, dn in zip(segment_ppg, freq_col, dn_index)]

            return pd.DataFrame(out, columns=["a1", "a2", "ra", "area_begin_to_slope"])

        if self.params.freq is None:  # frequency per‑row
            c_df = c_df.withColumn('areas_struct', compute_areas_udf(F.col(self.params.input_cols[0]),
                                                                     F.col(self.params.sampling_freq_col),
                                                                     F.col(self.params.dn_index_col))
                                   )
        else:  # fixed frequency for everyone
            c_df = c_df.withColumn('areas_struct', compute_areas_udf(F.col(self.params.input_cols[0]),
                                                                     F.lit(float(self.params.freq)),
                                                                     F.col(self.params.dn_index_col))
                                   )

        # explode the struct into four new columns
        c_df = (
            c_df
            .withColumn(self.params.output_cols[0], F.col("areas_struct.a1"))
            .withColumn(self.params.output_cols[1], F.col("areas_struct.a2"))
            .withColumn(self.params.output_cols[2], F.col("areas_struct.ra"))
            .withColumn(self.params.output_cols[3], F.col("areas_struct.area_begin_to_slope"))
            .drop("areas_struct")
        )

        return c_df


class PPGFiducialIP(Annotator):
    """
    Calculates the inflection point between the diastolic notch and the diastolic peak. Takes 'notch' index, 'dias'
    index, last_beat and the sampling frequency as inputs and returns the IP index, value and time to index since the
    start of the wave.

    Example
    -------
    .. code-block:: python
        fidu_ip = etl.annotators.features.fromsignals.PPGFiducialIP()\
        .set_input_col("last_beat")\
        .set_freq(200)\
        .set_notch_index_col('fidu_index_notch')\
        .set_dias_peak_index_col('fidu_index_dias')\
        .set_output_cols(['error_ip', 'ip_index', 'ip_value', 'time_ip'])

    """

    def __init__(self, spark_session=None, **kwargs):
        """Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ----------
        kwargs : dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'output_cols': ['error_ip', 'ip_index', 'ip_value', 'time_ip'],
            'freq': None,
            'notch_index_col': None,
            'dias_peak_index_col': None,
            'sampling_freq_col': 'sampling_freq_hz',
        }

        self.params.set_default_params(default_params)

    @deco.check_type(str, 'sampling_freq_col')
    def set_sampling_freq_col(self, x):
        """Sets the column containing the wave frequency of the data source.

        Parameters
        ----------
        x : str
            Frequency in Hertz.

        Returns
        -------
        PPGFiducialIP
            The instance of the class with the updated parameter.
        """

        self.params.sampling_freq_col = x
        return self

    @deco.check_type(int, 'freq')
    def set_freq(self, x):
        """Sets the wave sampling frequency of the data source.

        Parameters
        ----------
        x : int
            Frequency in Hertz.

        Returns
        -------
        PPGFiducialIP
            The instance of the class with the updated parameter.
        """

        self.params.freq = x
        return self

    @deco.check_type(str, 'notch_index_col')
    def set_notch_index_col(self, x):
        """Sets the col containing the index of the notch.

        Parameters
        ----------
        x : str
            Col name containing the index of the dicrotic notch.

        Returns
        -------
        PPGFiducialIP
            The instance of the class with the updated parameter.
        """
        self.params.notch_index_col = x
        return self

    @deco.check_type(str, 'dias_peak_index_col')
    def set_dias_peak_index_col(self, x):
        """Sets the col containing the index of the diastolic peak.

        Parameters
        ----------
        x : str
            Col name containing the index of the diastolic peak.

        Returns
        -------
        PPGFiducialIP
            The instance of the class with the updated parameter.
        """
        self.params.dias_peak_index_col = x
        return self

    def fit(self, df=None, verbose: int = 0):
        """Fits the model with the given DataFrame.

        Parameters
        ----------
        df : pd.DataFrame, optional
            DataFrame to fit the model.
        verbose : int, optional
            Verbosity level.

        Returns
        -------
        PPGFiducialIPModel
            Returns an instance of PPGFiducialIPModel.
        """
        if len(self.params.output_cols) != 4:
            raise ValueError('Set 4 output cols, corresponding to "error present", "IP index", "IP value"'
                             ' and "Time to IP" in that order.')
        return PPGFiducialIPModel(self.spark_session, self.params)


class PPGFiducialIPModel(Model):
    """
    Calculates the inflection point between the diastolic notch and the diastolic peak. Takes 'notch' index, 'dias'
    index, last_beat and the sampling frequency as inputs and returns the IP index, value and time to index since the
    start of the wave.
    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """Initialize the class with the given parameters.

        Parameters
        ----------
        params : AnnotatorParams
            AnnotatorParams instance.
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, c_df, verbose=0):
        """
        Perform the calculation from the given dataframe. Returns another dataframe with the output cols added.

        Parameters
        ----------
        c_df : pd.DataFrame, optional
            Input DataFrame.
        verbose : int, optional
            Level of verbosity.

        Returns
        -------
        DataFrame
            DataFrame with the output_cols added.
        """

        @F.pandas_udf(
            T.StructType([
                T.StructField("error_present", T.IntegerType()),
                T.StructField("inflection_index", T.DoubleType()),
                T.StructField("inflection_value", T.DoubleType()),
                T.StructField("inflection_time", T.DoubleType())
            ])
        )
        def calculate_ip_udf(segment_ppg: pd.Series,
                             freq: pd.Series,
                             notch_idx: pd.Series,
                             dias_idx: pd.Series) -> pd.DataFrame:

            # Lists collect batch results, one element per row
            err_flag, idx_out, val_out, time_out = [], [], [], []

            # Iterate row-wise inside each Arrow/pandas batch
            for seg, f, n_i, d_i in zip(segment_ppg, freq, notch_idx, dias_idx):
                if (pd.isna(n_i) or n_i is None) or (pd.isna(d_i) or d_i is None):  # NaN guard
                    err_flag.append(1)
                    idx_out.append(None)
                    val_out.append(None)
                    time_out.append(None)
                    continue

                n_i, d_i = int(n_i), int(d_i)
                seg_arr = np.asarray(seg)[n_i:d_i + 1]  # slice

                # first & second derivative
                second_der = np.gradient(np.gradient(seg_arr))

                # sign-change: first inflection inside window
                crosses = np.where(np.diff(np.sign(second_der)))[0]
                if len(crosses) == 0:
                    err_flag.append(1)
                    idx_out.append(None)
                    val_out.append(None)
                    time_out.append(None)
                    continue

                inf_idx = crosses[0] + n_i
                err_flag.append(0)
                idx_out.append(float(inf_idx))
                val_out.append(float(seg[inf_idx]))
                time_out.append(float(inf_idx) / f)

            # Return a *pandas* DataFrame whose columns match the schema order
            return pd.DataFrame({
                "error_present": err_flag,
                "inflection_index": idx_out,
                "inflection_value": val_out,
                "inflection_time": time_out
            })

        if self.params.freq is None:
            freq_col = F.col(self.params.sampling_freq_col)  # per-row
        else:
            freq_col = F.lit(float(self.params.freq))  # constant

        c_df = c_df.withColumn('ip_struct', calculate_ip_udf(
            F.col(self.params.input_cols[0]),
            freq_col,
            F.col(self.params.notch_index_col),
            F.col(self.params.dias_peak_index_col)
        )
                               )

        # explode the struct into four new columns
        c_df = (
            c_df
            .withColumn(self.params.output_cols[0], F.col("ip_struct.error_present"))
            .withColumn(self.params.output_cols[1], F.col("ip_struct.inflection_index"))
            .withColumn(self.params.output_cols[2], F.col("ip_struct.inflection_value"))
            .withColumn(self.params.output_cols[3], F.col("ip_struct.inflection_time"))
            .drop("ip_struct")
        )

        return c_df


class PPGSegmentFiducialExtraction(Annotator):
    """
    Annotator sequence containing all the PPG beat analysis sequence. Requires as an input a three-beat segment and
    its corresponding timestamps.

    Example
    -------
    .. code-block:: python
        results = etl.annotators.features.fromsignals.PPGSegmentFiducialExtraction()\
            .set_input_cols(['ppg_episode', 'ts_episode'])\
            .fit().transform(df)

    """

    def __init__(self, spark_session=None, **kwargs):
        """Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ----------
        kwargs : dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'annotators_sequence': {},
            'models_sequence': {},
            'drop_errors': True
        }

        self.params.set_default_params(default_params)

    @deco.check_type(bool, 'drop_errors')
    def set_drop_errors(self, x):
        """Sets the automatic filtering of non-classifiable beats and those with errors in the fiduciary localization.

        Parameters
        ----------
        x : bool
            Boolean set for activating the filtering.

        Returns
        -------
        PPGSegmentFiducialExtraction
            The instance of the class with the updated parameter.
        """

        self.params.drop_errors = x
        return self

    def fit(self, df=None, verbose: int = 0):
        """Fits the model with the given DataFrame.

        Parameters
        ----------
        df : pd.DataFrame, optional
            DataFrame to fit the model.
        verbose : int, optional
            Verbosity level.

        Returns
        -------
        PPGSegmentFiducialExtractionModel
            Returns an instance of PPGSegmentFiducialExtractionModel.
        """

        # Unpack input column names
        vals_col, ts_col = self.params.input_cols

        # Standardize to find valleys and peaks
        robust_peak_finder = etl.annotators.signals.characterization.RobustPeakFinder() \
            .set_ppg_col(vals_col) \
            .set_ppg_peak_prominence(1) \
            .set_ppg_valley_prominence(1) \
            .set_min_pulse_distance(100)

        # Get the sampling frequency of the segment
        get_hz = etl.annotators.signals.characterization.GetSamplingHz() \
            .set_input_cols([ts_col])

        # Calculate the p2pi of the signal using the peaks.
        get_p2pi = etl.annotators.features.fromsignals.PPGFiducialP2PI() \
            .set_input_cols([vals_col + '_peaks']) \
            .set_output_cols(['p2pi', 'p2pi_error']) \
            .set_sampling_freq_col('sampling_freq_hz')

        self.params.annotators_sequence['common_path'] = [robust_peak_finder, get_hz, get_p2pi]

        # Get the second-to-last beat
        get_target_beat = etl.annotators.signals.rearrange.SignalBeatExtractor() \
            .set_signal_col(vals_col)\
            .set_timestamp_col(ts_col)\
            .set_output_cols(['beat_vals', 'beat_ts']) \
            .set_indices_col(vals_col + '_valleys') \
            .set_select_segments([1])

        # Apply the minmax scaler
        minmax = etl.annotators.signals.standardization.StandardizeSignal() \
            .set_input_cols(['beat_vals']) \
            .set_output_cols(['beat_vals']) \
            .set_mode('by_row') \
            .set_minmax(True)

        # PPGCaseClassifier to get the wave type
        get_beat_class = etl.annotators.signals.characterization.PPGCaseClassifier() \
            .set_input_cols(['beat_vals']) \
            .set_output_cols(['class_error', 'beat_class'])

        # PPGFiducialPoints to get the o, s, n and dias indices and values
        get_fidu_points = etl.annotators.features.fromsignals.PPGFiducialPoints() \
            .set_input_cols(['beat_vals']) \
            .set_output_cols(['f_error', 'f_idx_o', 'f_idx_s', 'f_idx_n', 'f_idx_d',
                              'f_val_o', 'f_val_s', 'f_val_n', 'f_val_d']) \
            .set_case_col('beat_class')

        # Fiducial point sequence error
        get_sequence_error = etl.annotators.dataprep.basic.ApplyFunc() \
            .set_input_cols(['f_idx_o', 'f_idx_s', 'f_idx_n', 'f_idx_d']) \
            .set_output_cols(['f_sequence_error']) \
            .set_function(
            # now we take four args, one per input column
            lambda o, s, n, d: 1
            if (
                    any(pd.isna(v) for v in (o, s, n, d))
                    or not (o < s < n < d)
            )
            else 0
        ) \
            .set_output_type(T.IntegerType())

        # Cast the beat class to string and then get dummies to use it as a feature.
        cast_beat_class = etl.annotators.dataprep.basic.CastColumns() \
            .set_input_cols(['beat_class']) \
            .set_output_cols(['beat_class']) \
            .set_errors('ignore') \
            .set_dtype('string')

        dummies_beat_class = etl.annotators.features.manipulation.GetDummies() \
            .set_input_cols(['beat_class']) \
            .set_prefix('beat_class_')

        self.params.annotators_sequence['beat_extraction_path'] = [get_target_beat, minmax, get_beat_class,
                                                                   get_fidu_points, get_sequence_error,
                                                                   cast_beat_class, dummies_beat_class]

        # Extract error_derivatives, tb1 and tb2 with PPGFiducialDerivative()
        fidu_derivatives = etl.annotators.features.fromsignals.PPGFiducialDerivative() \
            .set_input_cols(['beat_vals']) \
            .set_output_cols(['error_derivatives', 'tb1', 'tb2']) \
            .set_sampling_freq_col('sampling_freq_hz')

        # Extract tb1_tb2_p2pi_coeff, tb1_p2pi_coeff and tb2_p2pi_coeff with ApplyFuncs
        fidu_feats_1 = etl.annotators.dataprep.basic.ApplyFunc() \
            .set_input_cols(['tb1', 'tb2', 'p2pi']) \
            .set_output_cols(['tb1_tb2_p2pi_coeff']) \
            .set_function(
            # now takes tb1, tb2, p2pi
            lambda tb1, tb2, p2pi: None
            if (pd.isna(tb1) or pd.isna(tb2) or pd.isna(p2pi))
            else (tb2 - tb1) / p2pi
        ) \
            .set_output_type(T.DoubleType())

        fidu_feats_2 = etl.annotators.dataprep.basic.ApplyFunc() \
            .set_input_cols(['tb1', 'p2pi']) \
            .set_output_cols(['tb1_p2pi_coeff']) \
            .set_function(
            lambda tb1, p2pi: None
            if (pd.isna(tb1) or pd.isna(p2pi))
            else tb1 / p2pi
        ) \
            .set_output_type(T.DoubleType())

        fidu_feats_3 = etl.annotators.dataprep.basic.ApplyFunc() \
            .set_input_cols(['tb2', 'p2pi']) \
            .set_output_cols(['tb2_p2pi_coeff']) \
            .set_function(
            lambda tb2, p2pi: None
            if (pd.isna(tb2) or pd.isna(p2pi))
            else tb2 / p2pi
        ) \
            .set_output_type(T.DoubleType())

        self.params.annotators_sequence['derivatives_path'] = [fidu_derivatives, fidu_feats_1, fidu_feats_2,
                                                               fidu_feats_3]

        # Extract Pulse Interval (PI)
        fidu_pi = etl.annotators.dataprep.basic.ApplyFunc() \
            .set_input_cols(['beat_ts']) \
            .set_output_cols(['pulse_interval']) \
            .set_function(
            # single argument is the list of timestamps:
            lambda ts: None
            if (ts is None
                or len(ts) < 2
                or pd.isna(ts).any()
                )
            else (ts[-1] - ts[0]) / 1000
        ) \
            .set_output_type(T.DoubleType())

        # FFT: Frequency at which the first peak from the FFT of the beat occurred
        fidu_fft_freq = etl.annotators.features.fromsignals.PPGFiducialFFT() \
            .set_input_cols(['beat_vals']) \
            .set_output_cols(['fft_freq1', 'spl', 'error_fft']) \
            .set_mode('peakfreq') \
            .set_peak_number(0) \
            .set_sampling_freq_col('sampling_freq_hz')

        # FFT: Area from 2 to 5
        fidu_fft_auc_1 = etl.annotators.features.fromsignals.PPGFiducialFFT() \
            .set_input_cols(['beat_vals']) \
            .set_output_cols(['auc2to5']) \
            .set_sampling_freq_col('sampling_freq_hz')

        # FFT: Area from 0 to 2
        fidu_fft_auc_2 = etl.annotators.features.fromsignals.PPGFiducialFFT() \
            .set_input_cols(['beat_vals']) \
            .set_output_cols(['auc0to2']) \
            .set_f_max(2) \
            .set_f_min(0) \
            .set_sampling_freq_col('sampling_freq_hz')

        # 75 percentile and inter-quartilic range
        fidu_percentiles = etl.annotators.features.fromsignals.PPGFiducialPercentiles() \
            .set_input_cols(['beat_vals']) \
            .set_output_cols(['perc75', 'iqr'])

        self.params.annotators_sequence['independent_path'] = [fidu_pi, fidu_fft_freq, fidu_fft_auc_1,
                                                               fidu_fft_auc_2, fidu_percentiles]

        # Extract coeff_s_dias, coeff_s_notch and coeff_d_notch with ApplyFunc
        fidu_diffs_1 = etl.annotators.dataprep.basic.ApplyFunc() \
            .set_input_cols(['f_val_s', 'f_val_d']) \
            .set_output_cols(['f_val_diff_s_d']) \
            .set_function(
            lambda vs, vd: None
            if (pd.isna(vs) or pd.isna(vd))
            else vs - vd
        ) \
            .set_output_type(T.DoubleType())

        fidu_diffs_2 = etl.annotators.dataprep.basic.ApplyFunc() \
            .set_input_cols(['f_val_s', 'f_val_n']) \
            .set_output_cols(['f_val_diff_s_n']) \
            .set_function(
            lambda vs, vn: None
            if (pd.isna(vs) or pd.isna(vn))
            else vs - vn
        ) \
            .set_output_type(T.DoubleType())

        fidu_diffs_3 = etl.annotators.dataprep.basic.ApplyFunc() \
            .set_input_cols(['f_val_d', 'f_val_n']) \
            .set_output_cols(['f_val_diff_d_n']) \
            .set_function(
            lambda vd, vn: None
            if (pd.isna(vd) or pd.isna(vn))
            else vd - vn
        ) \
            .set_output_type(T.DoubleType())

        fidu_diffs_4 = etl.annotators.dataprep.basic.ApplyFunc() \
            .set_input_cols(['f_idx_d', 'f_idx_s', 'sampling_freq_hz']) \
            .set_output_cols(['f_time_diff_s_d']) \
            .set_function(
            lambda idx_d, idx_s, hz: None
            if (pd.isna(idx_d) or pd.isna(idx_s) or pd.isna(hz))
            else (idx_d - idx_s) / hz
        ) \
            .set_output_type(T.DoubleType())

        fidu_diffs_5 = etl.annotators.dataprep.basic.ApplyFunc() \
            .set_input_cols(['f_idx_n', 'f_idx_s', 'sampling_freq_hz']) \
            .set_output_cols(['f_time_diff_s_n']) \
            .set_function(
            lambda idx_n, idx_s, hz: None
            if (pd.isna(idx_n) or pd.isna(idx_s) or pd.isna(hz))
            else (idx_n - idx_s) / hz
        ) \
            .set_output_type(T.DoubleType())

        fidu_diffs_6 = etl.annotators.dataprep.basic.ApplyFunc() \
            .set_input_cols(['f_idx_n', 'f_idx_d', 'sampling_freq_hz']) \
            .set_output_cols(['f_time_diff_n_d']) \
            .set_function(
            lambda idx_n, idx_d, hz: None
            if (pd.isna(idx_n) or pd.isna(idx_d) or pd.isna(hz))
            else (idx_d - idx_n) / hz
        ) \
            .set_output_type(T.DoubleType())

        # Extract IP
        fidu_ip = etl.annotators.features.fromsignals.PPGFiducialIP() \
            .set_input_cols(["beat_vals"]) \
            .set_sampling_freq_col('sampling_freq_hz') \
            .set_notch_index_col('f_idx_n') \
            .set_dias_peak_index_col('f_idx_d') \
            .set_output_cols(['error_ip', 'ip_index', 'ip_value', 'time_ip'])

        fidu_feats_4 = etl.annotators.dataprep.basic.ApplyFunc() \
            .set_input_cols(['time_ip', 'f_idx_s', 'sampling_freq_hz']) \
            .set_output_cols(['inverse_tip_tsp_coeff']) \
            .set_function(
            lambda tip, idx_s, hz: None
            if (pd.isna(tip) or pd.isna(idx_s) or pd.isna(hz))
            else (tip - (idx_s / hz)) ** -1
        ) \
            .set_output_type(T.DoubleType())

        # ADP / (PI - TDN)
        fidu_feats_5 = etl.annotators.dataprep.basic.ApplyFunc() \
            .set_input_cols(['f_val_d', 'pulse_interval', 'f_idx_n', 'sampling_freq_hz']) \
            .set_output_cols(['adp_pi_tdn_coeff']) \
            .set_function(
            lambda vd, pi, idx_n, hz: None
            if (pd.isna(vd) or pd.isna(pi) or pd.isna(idx_n) or pd.isna(hz))
            else vd / (pi - (idx_n / hz))
        ) \
            .set_output_type(T.DoubleType())

        # TP / p2pi. TP is the time difference between systolic and diastolic peaks.
        fidu_feats_6 = etl.annotators.dataprep.basic.ApplyFunc() \
            .set_input_cols(['f_time_diff_s_d', 'p2pi']) \
            .set_output_cols(['tp_p2pi_coeff']) \
            .set_function(
            lambda dt_sd, p2pi: None
            if (pd.isna(dt_sd) or pd.isna(p2pi))
            else dt_sd / p2pi
        ) \
            .set_output_type(T.DoubleType())

        # TDN / p2pi. TDN is the time passed until the notch, so the index multiplied by the period.
        fidu_feats_7 = etl.annotators.dataprep.basic.ApplyFunc() \
            .set_input_cols(['f_idx_n', 'p2pi', 'sampling_freq_hz']) \
            .set_output_cols(['tdn_p2pi_coeff']) \
            .set_function(
            lambda idx_n, p2pi, hz: None
            if (pd.isna(idx_n) or pd.isna(p2pi) or pd.isna(hz))
            else (idx_n / hz) / p2pi
        ) \
            .set_output_type(T.DoubleType())

        # 1 / TDN - TSP. Inverse of the difference between the times of the notch and the systolic peak.
        fidu_feats_8 = etl.annotators.dataprep.basic.ApplyFunc() \
            .set_input_cols(['f_time_diff_s_n']) \
            .set_output_cols(['inverse_time_s_notch']) \
            .set_function(
            lambda dt_sn: None
            if pd.isna(dt_sn)
            else 1 / dt_sn
        ) \
            .set_output_type(T.DoubleType())

        # Area related. A1 from the start to the notch. A2 notch to end. RA ratio between those. And a0toslope is from the start to the max slope point
        fidu_areas = etl.annotators.features.fromsignals.PPGFiducialAreas() \
            .set_input_cols(['beat_vals']) \
            .set_dn_index_col('f_idx_n') \
            .set_sampling_freq_col('sampling_freq_hz') \
            .set_output_cols(['a1', 'a2', 'arearatio', 'a0toslope'])

        self.params.annotators_sequence['good_fidus_path'] = [
            fidu_diffs_1, fidu_diffs_2, fidu_diffs_3, fidu_diffs_4, fidu_diffs_5, fidu_diffs_6,
            fidu_ip, fidu_feats_4, fidu_feats_5, fidu_feats_6, fidu_feats_7, fidu_feats_8,
            fidu_areas
        ]

        # Filter out those segments that are not actually 3 good beats.
        filter_p2pi = etl.annotators.dataprep.basic.FilterValues() \
            .set_input_cols(['p2pi_error']) \
            .set_conditions(['<']) \
            .set_conditions_values([1])

        filter_sequence_error = etl.annotators.dataprep.basic.FilterValues() \
            .set_input_cols(['f_sequence_error']) \
            .set_conditions(['<']) \
            .set_conditions_values([1])

        # Filter out those with classification or fiducial extraction errors
        filter_class = etl.annotators.dataprep.basic.FilterValues() \
            .set_input_cols(['class_error']) \
            .set_conditions(['<']) \
            .set_conditions_values([1])

        filter_fidu = etl.annotators.dataprep.basic.FilterValues() \
            .set_input_cols(['f_error']) \
            .set_conditions(['<']) \
            .set_conditions_values([1])

        # Filter out those with error in derivative calculations.
        filter_derivatives = etl.annotators.dataprep.basic.FilterValues() \
            .set_input_cols(['error_derivatives']) \
            .set_conditions(['<']) \
            .set_conditions_values([1])

        # Generates error filtering in the derivatives calculations
        filter_fft = etl.annotators.dataprep.basic.FilterValues() \
            .set_input_cols(['error_fft']) \
            .set_conditions(['<']) \
            .set_conditions_values([1])

        # Filter those with bad IP calculation
        filter_ip = etl.annotators.dataprep.basic.FilterValues() \
            .set_input_cols(['error_ip']) \
            .set_conditions(['<']) \
            .set_conditions_values([1])

        self.params.annotators_sequence['filters_path'] = [filter_p2pi, filter_class, filter_fidu,
                                                           filter_sequence_error,
                                                           filter_derivatives, filter_ip, filter_fft]

        # Annotator for dropping unnecessary intermediate columns
        drop_cols = etl.annotators.dataprep.basic.RemoveColumns() \
            .set_input_cols([vals_col + '_peaks', vals_col + '_valleys', 'beat_class'])

        self.params.annotators_sequence['drop_cols_path'] = [drop_cols]

        # Pre-fit all the annotators in the multiple sequences
        for key, value_list in self.params.annotators_sequence.items():
            fit_annotators = []
            for item in value_list:
                fit_annotators.append(item.fit())
            self.params.models_sequence[key] = fit_annotators

        return PPGSegmentFiducialExtractionModel(self.spark_session, self.params)


class PPGSegmentFiducialExtractionModel(Model):
    """
    Calculates the inflection point between the diastolic notch and the diastolic peak. Takes 'notch' index, 'dias'
    index, last_beat and the sampling frequency as inputs and returns the IP index, value and time to index since the
    start of the wave.
    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """Initialize the class with the given parameters.

        Parameters
        ----------
        params : AnnotatorParams
            AnnotatorParams instance.
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, c_df, verbose=0):
        """
        Perform the calculation from the given dataframe. Returns another dataframe with the output cols added.

        Parameters
        ----------
        c_df : pd.DataFrame, optional
            Input DataFrame.
        verbose : int, optional
            Level of verbosity.

        Returns
        -------
        DataFrame
            DataFrame with the output_cols added.
        """

        # First run the common path. This runs until p2pi error.
        for annotator_model in self.params.models_sequence['common_path']:
            c_df = annotator_model.transform(c_df)

        # Rows with error p2pi should NOT be processed (there are not 3 beats in the 3-beat-context segment).
        bad_rows = c_df.filter(F.col('p2pi_error') == 1)
        error_flag_cols = [
            'class_error', 'f_error', 'f_sequence_error',
            'error_derivatives', 'error_ip', 'error_fft'
        ]
        for col in error_flag_cols:
            bad_rows = bad_rows.withColumn(col, F.lit(1))

        c_df = c_df.filter(F.col('p2pi_error') == 0)

        # Target beat extraction, classification and fiducial location. Everything should handle nans sequentially.
        for annotator_model in self.params.models_sequence['beat_extraction_path']:
            c_df = annotator_model.transform(c_df)

        # Now the df has extracted beats even though some of those will not have the fiducials needed for most of the
        # feature extraction. First we run those paths that do not need a correct fiducial sequence:
        for annotator_model in self.params.models_sequence['independent_path']:
            c_df = annotator_model.transform(c_df)

        # Derivatives path has some features that we won't be able to calculate, but it should handle it automatically
        for annotator_model in self.params.models_sequence['derivatives_path']:
            c_df = annotator_model.transform(c_df)

        # Lastly the path that needs correct classification and fiducial localization. After the changes it should also
        # be able to handle nan values correctly
        for annotator_model in self.params.models_sequence['good_fidus_path']:
            c_df = annotator_model.transform(c_df)

        # Now we will concat the bad rows without usable beats with the mixed df with good and bad features
        c_df = c_df.unionByName(bad_rows, allowMissingColumns=True)

        # As a final step we will delete intermediate unneeded columns
        for annotator_model in self.params.models_sequence['drop_cols_path']:
            c_df = annotator_model.transform(c_df)

        if self.params.drop_errors:

            filter_id = {
                0: 'p2pi filter (correct beat context)',
                1: 'classification filter (classifiable beat)',
                2: 'fiducial location filter (able to locate fidu points)',
                3: 'fiducial sequence filter (fidu points in the correct order)',
                4: 'fiducial derivatives filter (derivatives features computable)',
                5: 'ip filter (beat inflection point computable)',
                6: 'fft filter (fast fourier transform computable)'
            }

            for i in range(len(self.params.models_sequence['filters_path'])):

                if verbose > 0:
                    print(f"Rows before {filter_id[i]}: {c_df.count()}")

                c_df = self.params.models_sequence['filters_path'][i].transform(c_df)

                if verbose > 0:
                    print(f"Rows after {filter_id[i]}: {c_df.count()}")


            # Drop error columns (there shouldn't be any error anyway)
            error_columns = ['p2pi_error', 'class_error', 'f_error', 'f_sequence_error',
                             'error_derivatives', 'error_ip', 'error_fft']

            c_df = c_df.drop(*error_columns)

        return c_df


class MultipleTrigonometricExtractorAndFilter(Annotator):
    """
    Annotator sequence capable of extracting the vector trigonometric features of the fiducial points and filter
    depending on the angles obtained.
    Takes as input the beat timestamps, the indices of the fiducial points o, s, n and d and the values of said
    points (assuming they have already been minmaxed). Performs a minmax scaling over the timesamps,
    extracts the trigonometrics features and, if tuples with thresholds are present,
    performs a filter given a certain range of degrees.

    Example
    -------
    .. code-block:: python


    """

    def __init__(self, spark_session=None, **kwargs):
        """Initialize the class with the given parameters and sets the corresponding default ones.

        Parameters
        ----------
        kwargs : dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'annotator_sequence': [],
            'models_sequence': [],
            's_n_filter_threshold': None,
            'n_d_filter_threshold': None,
            'beat_ts_col': None,
            'f_idx_o_col': None,
            'f_idx_s_col': None,
            'f_idx_n_col': None,
            'f_idx_d_col': None,
            'f_val_o_col': None,
            'f_val_s_col': None,
            'f_val_n_col': None,
            'f_val_d_col': None,
        }

        self.params.set_default_params(default_params)

    @deco.check_type((tuple, None), 's_n_filter_threshold')
    def set_s_n_filter_threshold(self, x):
        """Set, as a tuple, the minimum and maximum angle for the s_n degrees filtering.

        Parameters
        ----------
        x : tuple, None
            Min and max degrees.

        Returns
        -------
        MultipleTrigonometricExtractorAndFilter
            The instance of the class with the updated parameter.
        """

        self.params.s_n_filter_threshold = x
        return self

    @deco.check_type((tuple, None), 'n_d_filter_threshold')
    def set_n_d_filter_threshold(self, x):
        """Set, as a tuple, the minimum and maximum angle for the n_d degrees filtering.

        Parameters
        ----------
        x : tuple, None
            Min and max degrees.

        Returns
        -------
        MultipleTrigonometricExtractorAndFilter
            The instance of the class with the updated parameter.
        """

        self.params.n_d_filter_threshold = x
        return self

    @deco.check_type(str, 'beat_ts_col')
    def set_beat_ts_col(self, x):
        """Set the column name containing the timestamps for the beat to analyze.

        Parameters
        ----------
        x : str
            True for enabling the filter.

        Returns
        -------
        MultipleTrigonometricExtractorAndFilter
            The instance of the class with the updated parameter.
        """

        self.params.beat_ts_col = x
        return self

    @deco.check_type(str, 'f_idx_o_col')
    def set_f_idx_o_col(self, x):
        """Set the column name containing the f_idx_o to analyze.

        Parameters
        ----------
        x : str
            True for enabling the filter.

        Returns
        -------
        MultipleTrigonometricExtractorAndFilter
            The instance of the class with the updated parameter.
        """

        self.params.f_idx_o_col = x
        return self

    @deco.check_type(str, 'f_idx_s_col')
    def set_f_idx_s_col(self, x):
        """Set the column name containing the f_idx_s to analyze.

        Parameters
        ----------
        x : str
            True for enabling the filter.

        Returns
        -------
        MultipleTrigonometricExtractorAndFilter
            The instance of the class with the updated parameter.
        """

        self.params.f_idx_s_col = x
        return self

    @deco.check_type(str, 'f_idx_n_col')
    def set_f_idx_n_col(self, x):
        """Set the column name containing the f_idx_n to analyze.

        Parameters
        ----------
        x : str
            True for enabling the filter.

        Returns
        -------
        MultipleTrigonometricExtractorAndFilter
            The instance of the class with the updated parameter.
        """

        self.params.f_idx_n_col = x
        return self

    @deco.check_type(str, 'f_idx_d_col')
    def set_f_idx_d_col(self, x):
        """Set the column name containing the f_idx_d to analyze.

        Parameters
        ----------
        x : str
            True for enabling the filter.

        Returns
        -------
        MultipleTrigonometricExtractorAndFilter
            The instance of the class with the updated parameter.
        """

        self.params.f_idx_d_col = x
        return self

    @deco.check_type(str, 'f_val_o_col')
    def set_f_val_o_col(self, x):
        """Set the column name containing the f_val_o to analyze.

        Parameters
        ----------
        x : str
            True for enabling the filter.

        Returns
        -------
        MultipleTrigonometricExtractorAndFilter
            The instance of the class with the updated parameter.
        """

        self.params.f_val_o_col = x
        return self

    @deco.check_type(str, 'f_val_s_col')
    def set_f_val_s_col(self, x):
        """Set the column name containing the f_val_s to analyze.

        Parameters
        ----------
        x : str
            True for enabling the filter.

        Returns
        -------
        MultipleTrigonometricExtractorAndFilter
            The instance of the class with the updated parameter.
        """

        self.params.f_val_s_col = x
        return self

    @deco.check_type(str, 'f_val_n_col')
    def set_f_val_n_col(self, x):
        """Set the column name containing the f_val_n to analyze.

        Parameters
        ----------
        x : str
            True for enabling the filter.

        Returns
        -------
        MultipleTrigonometricExtractorAndFilter
            The instance of the class with the updated parameter.
        """

        self.params.f_val_n_col = x
        return self

    @deco.check_type(str, 'f_val_d_col')
    def set_f_val_d_col(self, x):
        """Set the column name containing the f_val_d to analyze.

        Parameters
        ----------
        x : str
            True for enabling the filter.

        Returns
        -------
        MultipleTrigonometricExtractorAndFilter
            The instance of the class with the updated parameter.
        """

        self.params.f_val_d_col = x
        return self

    def fit(self, df = None, verbose: int = 0):
        """Fits the model with the given DataFrame.

        Parameters
        ----------
        df : Spark DataFrame, optional
            DataFrame to fit the model.
        verbose : int, optional
            Verbosity level.

        Returns
        -------
        MultipleTrigonometricExtractorAndFilterModel
            Returns an instance of MultipleTrigonometricExtractorAndFilterModel.

        Raises
        ------
        Exception
            If the output columns are not set correctly.
        """
        self.params.annotator_sequence = []

        # Add the minmax scaler for the timestamps
        self.params.annotator_sequence.append(
            etl.annotators.signals.standardization.StandardizeSignal() \
                .set_input_cols([self.params.beat_ts_col]) \
                .set_output_cols(['scaled_beat_ts']) \
                .set_mode('by_row') \
                .set_minmax(True)
        )

        # Add the new (scaled) indices for the fiducial points
        for index_col_name in [self.params.f_idx_o_col,
                               self.params.f_idx_s_col,
                               self.params.f_idx_n_col,
                               self.params.f_idx_d_col]:
            self.params.annotator_sequence.append(
                etl.annotators.dataprep.basic.ApplyFunc() \
                    .set_function(lambda x, y: float(x[int(y)])) \
                    .set_input_cols(['scaled_beat_ts', index_col_name]) \
                    .set_output_cols(['scaled_' + index_col_name]) \
                    .set_output_type(T.FloatType())
            )

        # Calculate the trigonometric features with the scaled time axis:
        trigonometric_features_1 = etl.annotators.features.fromsignals.TrigonometricExtractor() \
            .set_input_cols(['scaled_' + self.params.f_idx_o_col,
                             self.params.f_val_o_col,
                             'scaled_' + self.params.f_idx_s_col,
                             self.params.f_val_s_col]) \
            .set_output_cols(['o_s_cosine', 'o_s_sine', 'o_s_magnitude', 'o_s_angle', 'o_s_degrees'])

        trigonometric_features_2 = etl.annotators.features.fromsignals.TrigonometricExtractor() \
            .set_input_cols(['scaled_' + self.params.f_idx_s_col,
                             self.params.f_val_s_col,
                             'scaled_' + self.params.f_idx_n_col,
                             self.params.f_val_n_col]) \
            .set_output_cols(['s_n_cosine', 's_n_sine', 's_n_magnitude', 's_n_angle', 's_n_degrees'])

        trigonometric_features_3 = etl.annotators.features.fromsignals.TrigonometricExtractor() \
            .set_input_cols(['scaled_' + self.params.f_idx_n_col,
                             self.params.f_val_n_col,
                             'scaled_' + self.params.f_idx_d_col,
                             self.params.f_val_d_col]) \
            .set_output_cols(['n_d_cosine', 'n_d_sine', 'n_d_magnitude', 'n_d_angle', 'n_d_degrees'])

        self.params.annotator_sequence.extend([trigonometric_features_1,
                                               trigonometric_features_2,
                                               trigonometric_features_3])

        # Apply the filters if thresholds have been set:
        if self.params.s_n_filter_threshold is not None:
            self.params.annotator_sequence.extend([
                etl.annotators.dataprep.basic.FilterValues() \
                    .set_input_cols(['s_n_degrees']) \
                    .set_conditions(['>']) \
                    .set_conditions_values([self.params.s_n_filter_threshold[0]]),
                etl.annotators.dataprep.basic.FilterValues() \
                    .set_input_cols(['s_n_degrees']) \
                    .set_conditions(['<']) \
                    .set_conditions_values([self.params.s_n_filter_threshold[1]])
            ]
            )

        if self.params.n_d_filter_threshold is not None:
            self.params.annotator_sequence.extend([
                etl.annotators.dataprep.basic.FilterValues() \
                    .set_input_cols(['n_d_degrees']) \
                    .set_conditions(['>']) \
                    .set_conditions_values([self.params.n_d_filter_threshold[0]]),
                etl.annotators.dataprep.basic.FilterValues() \
                    .set_input_cols(['n_d_degrees']) \
                    .set_conditions(['<']) \
                    .set_conditions_values([self.params.n_d_filter_threshold[1]])
            ]
            )

        for i in self.params.annotator_sequence:
            self.params.models_sequence.append(i.fit())

        return MultipleTrigonometricExtractorAndFilterModel(self.spark_session, self.params)


class MultipleTrigonometricExtractorAndFilterModel(Model):
    """
    Annotator sequence capable of extracting the vector trigonometric features of the fiducial points and filter
    depending on the angles obtained.
    Takes as input the beat timestamps, the indices of the fiducial points o, s, n and d and the values of said
    points (assuming they have already been minmaxed). Performs a minmax scaling over the timestamps,
    extracts the trigonometric features and, if tuples with thresholds are present,
    performs a filter given a certain range of degrees.
    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """Initialize the class with the given parameters and sets the corresponding default ones.

        Parameters
        ----------
        params : AnnotatorParams
            AnnotatorParams instance.
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, df = None, verbose: int = 0) -> pd.DataFrame:
        """Perform the calculation.

        Parameters
        ----------
        df : Spark DataFrame, optional
            DataFrame to perform the operation.
        verbose : int, optional
            Verbosity level.

        Returns
        -------
        Spark DataFrame
            DataFrame with the extracted fiducial points.
        """

        # Divide the df in those rows that should not be processed by this mega-annotator and those who do
        cols_to_check = [
            self.params.beat_ts_col,
            self.params.f_idx_o_col, self.params.f_idx_s_col, self.params.f_idx_n_col, self.params.f_idx_d_col,
            self.params.f_val_o_col, self.params.f_val_s_col, self.params.f_val_n_col, self.params.f_val_d_col
        ]

        df = df.na.drop(subset=(cols_to_check))

        for i in range(len(self.params.models_sequence)):
            df = self.params.models_sequence[i].transform(df)

        return df