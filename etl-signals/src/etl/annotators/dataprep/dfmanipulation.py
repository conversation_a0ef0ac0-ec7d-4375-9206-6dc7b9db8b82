from etl.annotators.annotators import Annota<PERSON>, Model, AnnotatorParams
from etl.decorators import decorators as deco
from pyspark.sql import functions as F
from pyspark.sql import types as T
import pyspark

class MergeDataFrames(Annotator):
    """
    Class to merge multiple pandas DataFrames based on common columns.

    This class takes a list of DataFrames and merges them based on specified columns.

    Example
    -------
    .. code-block:: python
        merger = MergeDataFrames().set_merging_cols(["Patient_ID", "Session_Number"])
        merger_model = merger.fit()
        df_merged = merger_model.transform([df1, df2])

    Parameters
    ----------
    kwargs : dict
        Additional arguments to pass to parent class.
    """

    def __init__(self, spark_session=None, **kwargs):
        """Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ----------
        kwargs : dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe_list',
            'transform_output_type': 'dataframe',
            'merging_cols': None,
            'dropna': False,
            'merge_how': 'left'
        }
        self.params.set_default_params(default_params)

    @deco.check_type(list, 'merging_cols')
    def set_merging_cols(self, x: list):
        """
        Set the columns to be used for merging the DataFrames.

        Parameters
        ----------
        x : list
            List of column names.

        Returns
        -------
        MergeDataFrames
            The updated MergeDataFrames object.

        Raises
        ------
        Exception
            If merging_cols is not a list or has a length less than 1.

        Example
        -------
        .. code-block:: python
            merger = MergeDataFrames()
            merger.set_merging_cols(["Patient_ID", "Session_Number"])
        """
        if len(x) > 0:
            self.params.__setattr__('merging_cols', x)
        else:
            raise Exception(f"merging_cols must have a length of at least 1: {x}")
        return self

    @deco.check_type(bool, 'dropna')
    def set_dropna(self, x: bool):
        """
        Set whether to drop NaN values from the merged DataFrame.

        Parameters
        ----------
        x : bool
            Boolean value indicating whether to drop NaN values.

        Returns
        -------
        MergeDataFrames
            The updated MergeDataFrames object.

        Raises
        ------
        Exception
            If dropna is not a boolean value.

        Example
        -------
        .. code-block:: python
            merger = MergeDataFrames()
            merger.set_dropna(False)
        """
        self.params.dropna = x
        return self

    @deco.check_type(str, 'merge_how')
    def set_merge_how(self, x: str):
        """
        Set how to do the merge.

        Parameters
        ----------
        x : str
            Type of merging to perform: ['left', 'right', 'inner', 'outer'].

        Returns
        -------
        MergeDataFrames
            The updated MergeDataFrames object.

        Raises
        ------
        ValueError
            If x is not one of the possible merge methods (left, right, inner, outer).

        Example
        -------
        .. code-block:: python
            merger = MergeDataFrames()
            merger.set_merge_how('inner')
        """
        if x in ['left', 'right', 'inner', 'outer']:
            self.params.merge_how = x
        else:
            raise ValueError("Merge method must be one of ['left', 'right', 'inner', 'outer']")
        return self


    def fit(self, df=None, verbose: int = 0):
        """Fits the model with the given DataFrame.

        Parameters
        ----------
        df : DataFrame, optional
            DataFrame to fit the model.
        verbose : int, optional
            Verbosity level.

        Returns
        -------
        CalculateAgeModel
            Returns an instance of MergeDataFramesModel.
        """
        return MergeDataFramesModel(self.spark_session, self.params)


class MergeDataFramesModel(Model):
    """
    Class to merge multiple pandas DataFrames based on common columns.

    This class is used to perform the actual merging of DataFrames as specified in the parameters.

    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """Initialize the class with the given parameters.

        Parameters
        ----------
        params : AnnotatorParams
            AnnotatorParams instance.
        """
        super().__init__(spark_session=spark_session, params=params)


    def transform(self, df_list, verbose: int = 0):
        """Perform the calculation from the given dataframe. Returns another dataframe with the output cols added.

        Parameters
        ----------
        df : DataFrame, optional
            Input DataFrame.
        verbose : int, optional
            Level of verbosity.

        Returns
        -------
        DataFrame
            DataFrame with the output_cols added.
        """
        if len(df_list) < 2:
            raise Exception(f"Number of dataframes to merge must be at least 2.")

        # Convert merging columns to string type in each DataFrame
        for idx in range(len(df_list)):
            spark_df = df_list[idx]
            for col_name in self.params.merging_cols:
                spark_df = spark_df.withColumn(
                    col_name,
                    F.col(col_name).cast(T.StringType())
                )
            df_list[idx] = spark_df

        merged_df = df_list[0]
        for new_df in df_list[1:]:
            merged_df = merged_df.join(
                new_df.dropDuplicates(self.params.merging_cols),
                on=self.params.merging_cols,
                how=self.params.merge_how
            )

            # Optionally drop rows with null values
        if self.params.dropna:
            merged_df = merged_df.na.drop()

            # Verbose output of final shape (rows, columns)
        if verbose > 0:
            row_count = merged_df.count()
            col_count = len(merged_df.columns)
            print(f"Final dataframe with shape ({row_count}, {col_count})")

        return merged_df