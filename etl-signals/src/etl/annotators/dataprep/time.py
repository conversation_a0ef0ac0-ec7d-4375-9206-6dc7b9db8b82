from etl.annotators.annotators import Annotator, <PERSON>, AnnotatorParams
from etl.decorators import decorators as deco
from pyspark.sql import functions as F
import pyspark.sql.types as T
import warnings
import pandas as pd
import numpy as np

class DatetimeToAbsolute(Annotator):
    """This class is used to transform a datetime to an absolute time value.

    The class expects a dataframe as input, where each value is an individual datetime or an array of datetimes.

    Example
    -------
    .. code-block:: python
        test_data = [("2025-01-01 12:01:19.000", 1, 1),
                     ("2025-01-01 13:05:24.656", 1, 2),
                     ("2025-01-01 16:50:56.241", 2, 1),
                     ("2025-01-01 20:21:32.986", 2, 2),
                     ("2025-01-01 21:12:54.394", 1, 1)]
        df = self.spark_session.createDataFrame(test_data, ["timestamp", "segment", "session"])

        datetime_to_abs = etl.annotators.dataprep.time.DatetimeToAbsolute() \
            .set_input_cols(['timestamp']) \
            .set_output_cols(['timestamp_absolute']) \
            .set_input_type('str') \
            .set_output_unit('m') \
            .set_session_number_col('session') \
            .set_segment_number_col('segment') \
            .fit()
        transformed_df = datetime_to_abs.transform(df)
        result_df.show()
    """

    def __init__(self, spark_session=None, **kwargs):
        """
        Initialize the class with the given parameters and sets the corresponding default ones.

        Parameters
        ----------
        kwargs : dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'input_cols': None,
            'output_cols': None,
            'input_type': 'timedelta[ns]',
            'output_unit': 'ms',
            'session_number_col': 'Session_Number',
            'segment_number_col': 'Segment',
            'time_reference': None,
            'column_start_ref': None,
            'groupby': True
        }
        self.params.set_default_params(default_params)

    @deco.check_type(str, 'input_type')
    def set_input_type(self, x: str):
        """Set the input unit. Unit supported for the moment are datetime64[ns], timedelta[ns] and Timestamp (equivalent
        to python's built-in datetime.datetime object). Also strings/pandas objects with the correct format that can be
        transformed to datetime. Other less common data types compatible with Pandas such as DatetimeIndex, Period,
        PeriodIndex or DateOffset are not yet supported.

        Parameters
        ----------
        x : str
            Input data type.

        Returns
        -------
        DatetimeToAbsolute
            The instance of the class with the updated parameter.

        Raises
        ------
        Exception
            If x is not one of the following: 'Timestamp', 'str'.
        """

        if x in ['Timestamp', 'str']:
            self.params.input_type = x
        else:
            raise Exception(f"input unit {x} must be one of the following: 'Timestamp', 'str'.")
        return self

    @deco.check_type(str, 'output_unit')
    def set_output_unit(self, x: str):
        """Set the output unit.

        Parameters
        ----------
        x : str
            Output unit.

        Returns
        -------
        DatetimeToAbsolute
            The instance of the class with the updated parameter.

        Raises
        ------
        Exception
            If x is not one of the following: ['ms', 's', 'm', 'h', 'D'].
        """

        if x in ['ms', 's', 'm', 'h', 'D']:
            self.params.output_unit = x
        else:
            raise Exception(f"output unit {x} must be one of the following: ['ms', 's', 'm', 'h', 'D']")
        return self

    @deco.check_type(str, 'session_number_col')
    def set_session_number_col(self, x: str):
        """Set the column name with the session numbers.

        Parameters
        ----------
        x : str
            Column with the session identifiers.

        Returns
        -------
        DatetimeToAbsolute
            The instance of the class with the updated parameter.

        Raises
        ------
        Exception
            If x is not a str.
        """

        self.params.session_number_col = x
        return self

    @deco.check_type(str, 'segment_number_col')
    def set_segment_number_col(self, x: str):
        """Set the column name with the segment numbers.

        Parameters
        ----------
        x : str
            Column with the segment identifiers.

        Returns
        -------
        DatetimeToAbsolute
            The instance of the class with the updated parameter.

        Raises
        ------
        Exception
            If x is not a str.
        """

        self.params.segment_number_col = x
        return self

    def set_time_reference(self, x: str):
        """Set which time value will be considered zero.

        Parameters
        ----------
        x : str
            Time value which will be considered zero for the given segment. It must be convertable to a datetime,
            so it can be subtracted from the objective columns.

        Returns
        -------
        DatetimeToAbsolute
            The instance of the class with the updated parameter.

        Raises
        ------
        Exception
            If x is not in a parsable format to become a datetime.
        """

        self.params.time_reference = x
        return self

    @deco.check_type(str, 'column_start_ref')
    def set_column_start_ref(self, x: str):
        """Set the earliest value in a column as a time reference.

        Parameters
        ----------
        x : str
            Name of the column whose earliest value will be set as a reference.

        Returns
        -------
        DatetimeToAbsolute
            The instance of the class with the updated parameter.

        Raises
        ------
        Exception
            If x is not a str.
        """

        self.params.column_start_ref = x
        return self

    @deco.check_type(bool, 'groupby')
    def set_groupby(self, x: bool):
        """Set if grouping by segment and session is needed.

        Parameters
        ----------
        x : bool
            Set if grouping by segment and session is needed.

        Returns
        -------
        DatetimeToAbsolute
            The instance of the class with the updated parameter.

        Raises
        ------
        Exception
            If x is not a boolean.
        """

        self.params.groupby = x
        return self

    def fit(self, df = None, verbose: int = 0):
        """Fits the model with the given DataFrame.

        Parameters
        ----------
        df : pd.DataFrame
            DataFrame to fit the model.
        verbose : int
            Verbosity level.

        Returns
        -------
        DatetimeToAbsoluteModel
            Returns an instance of DatetimeToAbsoluteModel.
        """
        return DatetimeToAbsoluteModel(self.spark_session, self.params)


class DatetimeToAbsoluteModel(Model):
    """This class is used to perform the unit change using the parameters set in the Annotator class."""

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """Initialize the class with the given parameters.

        Parameters
        ----------
        params : AnnotatorParams
            AnnotatorParams instance.
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, df, verbose: int = 0):
        """Perform the unit change on the input DataFrame.

        Parameters
        ----------
        df : pd.DataFrame
            DataFrame to perform the unit change.
        verbose : int
            Verbosity level.

        Returns
        -------
        pd.DataFrame
            DataFrame with the unit change performed.
        """

        if verbose > 0:
            print(f"Performing unit change {self.params.input_cols[0]} to {self.params.output_unit} "
                  f"and saving the result in {self.params.output_cols[0]}")

        # Map the output unit to a pandas Timedelta string.
        time_unit_map = {'ms': 1000, 's': 1, 'm': 1/60, 'h': 1/(60*60), 'D': 1/(60*60*24)}

        if self.params.input_type == 'str':
            df = df.withColumn(self.params.input_cols[0], F.to_timestamp(F.col(self.params.input_cols[0]), "yyyy-MM-dd HH:mm:ss.SSS"))

        if self.params.groupby:
            if (self.params.column_start_ref is not None) | (self.params.time_reference is None):
                if self.params.column_start_ref is not None:
                    df_grp = df.groupBy(self.params.session_number_col, self.params.segment_number_col).agg(
                        F.min(self.params.column_start_ref).alias("time_reference"))
                if self.params.time_reference is None:
                    df_grp = df.groupBy(self.params.session_number_col, self.params.segment_number_col).agg(
                        F.min(self.params.input_cols[0]).alias("time_reference"))
                df = df.join(df_grp, [self.params.session_number_col, self.params.segment_number_col])
            else:
                df = df.withColumn("time_reference", F.lit(self.params.time_reference))
                try:
                    df = df.withColumn("time_reference", F.to_timestamp(F.col("time_reference"), "yyyy-MM-dd HH:mm:ss.SSS"))
                except Exception:
                    raise Exception(
                        "WARNING: time references, if strings or objects,"
                        " must be written in yyyy-MM-dd HH:mm:ss.SSS to be parsed correctly.")
        else:
            if (self.params.column_start_ref is not None) | (self.params.time_reference is None):
                if self.params.column_start_ref is not None:
                    df = df.withColumn("time_reference", F.lit(df.agg(F.min(self.params.column_start_ref)).collect()[0][0]))
                if self.params.time_reference is None:
                    df = df.withColumn("time_reference", F.lit(df.agg(F.min(self.params.input_cols[0])).collect()[0][0]))
            else:
                df = df.withColumn("time_reference", F.lit(self.params.time_reference))
                try:
                    df = df.withColumn("time_reference", F.to_timestamp(F.col("time_reference"), "yyyy-MM-dd HH:mm:ss.SSS"))
                except Exception:
                    raise Exception(
                        "WARNING: time references, if strings or objects,"
                        " must be written in yyyy-MM-dd HH:mm:ss.SSS to be parsed correctly.")

        df = df.withColumn("time_dif", F.col(self.params.input_cols[0]).cast("long") - F.col("time_reference").cast("long")) \
            .withColumn(self.params.output_cols[0], F.col("time_dif")*time_unit_map[self.params.output_unit]) \
            .drop("time_dif", "time_reference")

        if verbose > 0:
            print(f"Computed the result in {self.params.output_cols[0]}")

        return df