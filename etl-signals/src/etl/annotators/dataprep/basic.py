from etl.annotators.annotators import Annota<PERSON>, Model, AnnotatorParams
from etl.decorators import decorators as deco
from pyspark.sql import functions as F
from pyspark.sql import types as T
import numpy as np

class FilterValues(Annotator):
    """
    This class is used to filter the values of a column using the logic:
    Greater than or equal to, Less than or equal to, Greater than, and Less than.
    It inherits from the Annotator class.

    Example
    -------
    .. code-block:: python
        filter_values = FilterValues().set_conditions(['>=']).set_conditions_values([2]).fit()
        filtered_df = filter_values.transform(df)
        print(filtered_df)
    """

    def __init__(self, spark_session=None, **kwargs):
        """
        Initialize the class with the given parameters and sets the corresponding default ones.

        Parameters
        ----------
        kwargs : dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'conditions': [],
            'condition_values': [],
            'apply_on_all': False
        }
        self.params.set_default_params(default_params)

    @deco.check_type(list, 'conditions')
    def set_conditions(self, x: list):
        """
        Set the conditions to perform the filtering.

        Parameters
        ----------
        x : list
            List with the conditions.

        Returns
        -------
        FilterValues
            The instance of the class with the updated parameter.

        Example
        -------
        .. code-block:: python
            filter_values = FilterValues()
            filter_values.set_conditions(['>='])
        """
        if len(x) > 0:
            self.params.conditions = x
        else:
            raise Exception(f"conditions {x} can't be empty")
        return self

    @deco.check_type(list, 'conditions_values')
    def set_conditions_values(self, x: list):
        """
        Set the condition values to perform the filtering.

        Parameters
        ------------
        x: list
            List with the condition values.

        Returns
        ----------
        FilterValues:
            The instance of the class with the updated parameter.

        Raises
        --------
        Exception:
            If x is not a non-empty list.

        Example
        --------
        .. code-block:: python

            filter_values = FilterValues()
            filter_values.set_conditions_values([30])
        """
        if len(x) > 0:
            self.params.condition_values = x
        else:
            raise Exception(f"condition values {x} can't be empty")
        return self

    @deco.check_type(bool, 'apply_on_all')
    def set_apply_on_all(self, x: bool):
        """
        Set the condition applied to every column in input_cols.

        Parameters
        ------------
        x: bool
            The condition.

        Returns
        ----------
        FilterValues:
            The instance of the class with the updated parameter.

        Raises
        --------
        Exception:
            If x is not a bool.

        Example
        --------
        .. code-block:: python
            filter_values = FilterValues()
            filter_values.set_condition_for_all('==')
        """
        self.params.apply_on_all = x
        return self

    def fit(self, df=None, verbose: int = 0):
        """
        Fits the model with the given DataFrame.

        Parameters
        ------------
        df: MetaDataFrame, optional
            DataFrame to fit the model.
        verbose: int, optional
            Verbosity level.

        Returns
        ----------
        FilterValuesModel:
            An instance of FilterValuesModel.

        Example
        --------
        .. code-block:: python

            df = DataFrame({'col1': [1,2,3], 'col2': [4,5,6]})
            filter_values = FilterValues()
            filter_values.fit(df)
        """
        return FilterValuesModel(self.spark_session, self.params)


class FilterValuesModel(Model):
    """
    Apply filtering to values in a DataFrame based on specified parameters.

    This class performs the filtering operation on the specified columns of the DataFrame.

    Example
    ---------------
    .. code-block:: python

        filter_values = movano.etl.annotators.data_prep.basic_prep.FilterValues() \\
             .set_input_cols(['fidu_error']) \\
             .set_conditions(['<']) \\
             .set_conditions_values([1]) \\
             .set_remove_cols(['fidu_error'])
        results_df = filter_values.fit().transform(df, verbose=1)

    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """Initialize the class with the given parameters and sets the corresponding default ones.

        Parameters
        ----------
        params : AnnotatorParams
            AnnotatorParams instance.
        """
        super().__init__(spark_session=spark_session, params=params)


    def transform(self, df, verbose: int = 0):
        """
        Perform the filtering on the input DataFrame.

        Parameters
        ----------
        df : DataFrame
            DataFrame to perform the filtering.
        verbose : int, optional
            The verbosity level.

        Returns
        -------
        DataFrame
            DataFrame with the filtering performed.
        """

        def filter_by_condition(df, col_name, condition, value=None, verbose=0):
            if condition == '>=':
                if verbose > 0:
                    print(f"Filtering {col_name} >= {value}")
                return df.filter(F.col(col_name) >= value)

            elif condition == '<=':
                if verbose > 0:
                    print(f"Filtering {col_name} <= {value}")
                return df.filter(F.col(col_name) <= value)

            elif condition == '>':
                if verbose > 0:
                    print(f"Filtering {col_name} > {value}")
                return df.filter(F.col(col_name) > value)

            elif condition == '<':
                if verbose > 0:
                    print(f"Filtering {col_name} < {value}")
                return df.filter(F.col(col_name) < value)

            elif condition == '==':
                if verbose > 0:
                    print(f"Filtering {col_name} == {value}")
                return df.filter(F.col(col_name) == value)

            elif condition == '!=':
                if verbose > 0:
                    print(f"Filtering {col_name} != {value}")
                return df.filter(F.col(col_name) != value)

            elif condition == 'isna':
                if verbose > 0:
                    print(f"Filtering {col_name} must not be NaN/Null")
                return df.filter(~F.isnan(F.col(col_name)) & F.col(col_name).isNotNull())

            else:
                raise ValueError("Unexpected condition. Available conditions are <, <=, >, >=, ==, !=, isna")

        if self.params.apply_on_all:
            for col_name in self.params.input_cols:
                if verbose > 0:
                    print(f"Applying all conditions on column {col_name}")
                for cond, val in zip(self.params.conditions, self.params.condition_values):
                    df = filter_by_condition(df, col_name, cond, val, verbose=verbose)

        else:
            for col_name, cond, val in zip(
                self.params.input_cols,
                self.params.conditions,
                self.params.condition_values
            ):
                if verbose > 0:
                    print(f"Applying condition {cond} on column {col_name}")
                df = filter_by_condition(df, col_name, cond, val, verbose=verbose)

        return df

class Round(Annotator):
    """
    Apply rounding to values in a DataFrame.

    This class is used to round values in a DataFrame to a specified number of decimal places.
    It allows setting the mode for rounding (single value or list) and the number of decimals.

    Example
    ---------------
    .. code-block:: python

        rounder = movano.etl.annotators.data_prep.basic_prep.Round()\\
             .set_input_cols(['bp', 'hr'])\\
             .set_output_cols(['rounded_bp', 'rounded_hr'])\\
             .set_decimals(0)
        results_df = rounder.fit().transform(df, verbose=1)
    """

    def __init__(self, spark_session=None, **kwargs):
        """
        Initialize the class with the given parameters and sets the corresponding default ones.

        Parameters
        ----------
        kwargs : dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'mode': 'list',
            'input_cols': None,
            'output_cols': None,
            'decimals': 0
        }
        self.params.set_default_params(default_params)

    @deco.check_type(str, 'mode')
    def set_mode(self, x):
        """
        Set the mode for the rounding (array or single element input).

        Parameters
        ------------
        x: str
            Mode for the rounding ('single' or 'list').

        Returns
        ----------
        Round:
            The instance of the class with the updated parameter.

        Raises
        --------
        ValueError:
            If x is not 'single' or 'list'.
        """
        if x in ['single', 'list']:
            self.params.mode = x
        else:
            raise ValueError("Mode must be set to ['single', 'list']")
        return self

    @deco.check_type(int, 'decimals')
    def set_decimals(self, x):
        """
        Set the decimals until which to round.

        Parameters
        ------------
        x: int
            Decimals to round.

        Returns
        ----------
        Round:
            The instance of the class with the updated parameter.

        Raises
        --------
        ValueError:
            If x is not a positive integer.
        """
        if x >= 0:
            self.params.decimals = x
        else:
            raise ValueError("Must be a positive integer")
        return self

    def fit(self, df=None, verbose: int = 0):
        """
        Fits the model with the given DataFrame.

        Parameters
        ------------
        df: MetaDataFrame, optional
            DataFrame to fit the model.
        verbose: int, optional
            Verbosity level.

        Returns
        ----------
        RoundModel:
            An instance of RoundModel.

        Example
        --------
        .. code-block:: python

            df = DataFrame({'col1': [1,2,3], 'col2': [4,5,6]})
            filter_values = FilterValues()
            filter_values.fit(df)
        """
        return RoundModel(self.spark_session, self.params)


class RoundModel(Model):
    """
    Apply rounding to values in a DataFrame.

    This class is used to round values in a DataFrame to a specified number of decimal places.
    It allows setting the mode for rounding (single value or list) and the number of decimals.

    Example
    ---------------
    .. code-block:: python

        rounder = etl.annotators.data_prep.basic_prep.Round()\\
             .set_input_cols(['bp', 'hr'])\\
             .set_output_cols(['rounded_bp', 'rounded_hr'])\\
             .set_decimals(0)
        results_df = rounder.fit().transform(df, verbose=1)
    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """Initialize the class with the given parameters and sets the corresponding default ones.

        Parameters
        ----------
        params : AnnotatorParams
            AnnotatorParams instance.
        """
        super().__init__(spark_session=spark_session, params=params)


    def transform(self, df, verbose: int = 0):
        """
        Perform the filtering on the input DataFrame.

        Parameters
        ----------
        df : DataFrame
            DataFrame to perform the filtering.
        verbose : int, optional
            The verbosity level.

        Returns
        -------
        DataFrame
            DataFrame with the filtering performed.
        """

        def _rounder(number):
            if number is None:
                return None
            if self.params.mode == 'list':
                return [round(i, self.params.decimals) for i in number]
            else:
                return round(number, self.params.decimals)

        if self.params.mode == "list":
            return_type = T.ArrayType(T.FloatType())
        else:
            return_type = T.FloatType()

        rounder_udf = F.udf(_rounder, return_type)

        for incol, outcol in zip(self.params.input_cols, self.params.output_cols):
            df = df.withColumn(
                outcol,
                rounder_udf(F.col(incol))
            )

        return df

class RenameColumns(Annotator):
    """
    A class to rename the columns of a DataFrame.

    This class provides functionality to rename columns in a DataFrame based on specified input and output column names.

    Example
    -------
    .. code-block:: python

        df = spark.createDataFrame([(1, "foo"), (2, "bar")], ['old_col1', 'old_col2'])
        rename_annotator = RenameColumns(input_cols=['old_col1', 'old_col2'], output_cols=['new_col1', 'new_col2'])
        rename_model = rename_annotator.fit()
        transformed_df = rename_model.transform(df)
        print(transformed_df)
    """

    def __init__(self, spark_session=None, **kwargs):
        """Initialize the class with the given parameters and sets the corresponding default ones.

        Parameters
        ----------
        kwargs : dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'input_cols': None,
            }
        self.params.set_default_params(default_params)

    def fit(self, df = None, verbose: int = 0):
        """
        Fits the model with the given DataFrame.

        Parameters
        ----------
        df : Spark DataFrame, optional
            The DataFrame to fit the model.
        verbose : int, optional
            The verbosity level.

        Returns
        -------
        RenameColumnsModel
            An instance of the RenameColumnsModel class.
        """
        return RenameColumnsModel(self.spark_session, self.params)


class RenameColumnsModel(Model):
    """
    This class is used to rename the columns of a DataFrame.

    Example
    -------
    .. code-block:: python

        renamer = etl.annotators.data_prep.basic_prep.RenameColumns()\
                 .set_input_cols(['old_col1', 'old_col2']) \
                 .set_output_cols(['new_col1', 'new_col2']) \
                 .fit()
        transformed_df = renamer.transform(df)
    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """
        Initializes a new instance of the RenameColumnsModel class.

        Parameters
        ----------
        params : AnnotatorParams, optional
            The parameters for the model.
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, df, verbose: int = 0):
        """
        Performs  the logic.

        Applies the renaming transformation to the DataFrame.

        Parameters
        ------------
        df: Spark DataFrame
            DataFrame to transform.
        verbose: int, optional
            Verbosity level.

        Returns
        ----------
        pd.DataFrame
            Transformed DataFrame with renamed columns.

        Raises
        --------
        Exception:
            If input or output columns are not specified.
        """

        for old, new in zip(self.params.input_cols, self.params.output_cols):
            df = df.withColumnRenamed(old, new)

        return df


class CastColumns(Annotator):
    """
    A class to cast columns of a DataFrame to a specified type.

    This class provides functionality to cast the data types of specified columns in a DataFrame.

    Example
    -------
    .. code-block:: python

        test_data = [(1, [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], [1, 1, 1, 1, 1, 4, 4, 4, 4, 4, 4],
                      [1, 1, 2, 2, 2, 4, 4, 4, 6, 6, 6], [3, 8, 10])]
        schema = T.StructType([
            T.StructField("id", T.IntegerType(), True),
            T.StructField("ts", T.ArrayType(T.IntegerType()), True),
            T.StructField("abp", T.ArrayType(T.IntegerType()), True),
            T.StructField("hr", T.ArrayType(T.IntegerType()), True),
            T.StructField("waves_idx", T.ArrayType(T.IntegerType()), True),
        ])

        df = self.spark_session.createDataFrame(test_data, schema)

        cast_column = etl.annotators.dataprep.basic.CastColumns() \
            .set_input_cols(['abp', 'hr']) \
            .set_output_cols(['abp_str', 'hr_str']) \
            .set_dtype('string') \
            .set_input_type('array') \
            .fit()
        transformed_df = cast_column.transform(df)
    """
    def __init__(self, spark_session=None, **kwargs):
        """
        Initialize the class with the given parameters and sets the corresponding default ones.

        Parameters
        ------------
        kwargs: dict
            Keyword arguments containing the parameters.

        Raises
        --------
        Exception:
            If x is not 'raise', 'ignore', or 'coerce'.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'input_cols': None,
            'input_type': "single",
            'output_cols': None,
            'dtype': str,
            'errors': 'raise'
        }
        self.params.set_default_params(default_params)

    @deco.check_type(str, 'errors')
    def set_errors(self, x):
        """
        Set the error handling method for casting.

        Parameters
        ------------
        x: str
            Error handling method ('raise', 'ignore').

        Returns
        ----------
        CastColumns
            The instance of the class with the updated parameter.

        Raises
        --------
        Exception:
            If x is not 'raise', 'ignore'.
        """
        if x in ['raise', 'ignore']:
            self.params.__setattr__('errors', x)
        else:
            raise Exception("Errors must be in ['raise', 'ignore'].")
        return self

    @deco.check_type(str, 'input_type')
    def set_input_type(self, x):
        """
        Set the input type for casting.

        Parameters
        ------------
        x: str
            Input type (single value or array).

        Returns
        ----------
        CastColumns
            The instance of the class with the updated parameter.

        Raises
        --------
        Exception:
            If x is not 'single', 'array'.
        """
        if x in ['single', 'array']:
            self.params.__setattr__('input_type', x)
        else:
            raise Exception("Input type must be in ['single', 'array'].")
        return self

    # dtype must be a valid python type
    @deco.check_type(str, 'dtype')
    def set_dtype(self, x):
        """
        Sets the target data type for casting.

        Parameters
        ----------
        x : string
            The target data type.

        Returns
        -------
        CastColumns
            The instance of the CastColumns class.
        """

        if x in ['string', 'float', 'int']:
            self.params.__setattr__('dtype', x)
        else:
            raise Exception("Errors must be in ['string', 'float', 'int'].")
        return self

    def fit(self, df = None, verbose: int = None):
        """
        Fits the model with the given DataFrame.

        Parameters
        ----------
        df : Spark DataFrame, optional
            The DataFrame to fit.
        verbose : int, optional
            The verbosity level.

        Returns
        -------
        CastColumnsModel
            An instance of the CastColumnsModel class.
        """
        return CastColumnsModel(self.spark_session, self.params)


class CastColumnsModel(Model):
    """
    Performs the casting of columns to a specified type.

    This class is used to apply the column casting transformation as specified by CastColumns.

    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """
        Initializes a new instance of the CastColumnsModel class.

        Parameters
        ----------
        params : AnnotatorParams, optional
            The parameters for the model.
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, df, verbose: int = 0):
        """
        Applies the casting transformation to the DataFrame.

        Parameters
        ------------
        df: Spark DataFrame
            DataFrame to transform.
        verbose: int, optional
            Verbosity level.

        Returns
        ----------
        Spark DataFrame
            Transformed DataFrame with the output stored in output columns.

        Raises
        --------
        Exception:
            If input or output columns are not specified.
        """

        def _change_type(input_array, dtype):
            if dtype == 'string':
                output = [str(e) for e in input_array]
            elif dtype == 'float':
                output = [float(e) for e in input_array]
            elif dtype == 'int':
                output = [int(e) for e in input_array]
            return output

        if self.params.dtype == 'string':
            udf_func = F.udf(_change_type, T.ArrayType(T.StringType()))
        elif self.params.dtype == 'float':
            udf_func = F.udf(_change_type, T.ArrayType(T.FloatType()))
        elif self.params.dtype == 'int':
            udf_func = F.udf(_change_type, T.ArrayType(T.IntegerType()))


        if self.params.input_cols is not None and self.params.output_cols is not None:
            if self.params.input_type == "array":
                for incol, outcol in zip(self.params.input_cols, self.params.output_cols):
                    df = df.withColumn(outcol, udf_func(df[incol], F.lit(self.params.dtype)))
            else:
                for incol, outcol in zip(self.params.input_cols, self.params.output_cols):
                    df = df.withColumn(outcol, F.col(incol).cast(self.params.dtype))
        else:
            raise Exception(f"Input columns {self.params.input_cols} and output columns {self.params.output_cols}"
                            f" must be specified.")

        return df


class RemoveColumns(Annotator):
    """
    A class to remove the columns of a DataFrame.

    Example
    -------
    .. code-block:: python
        results = movano.etl.annotators.dataprep.basic.RemoveColumns()\
            .set_input_cols(['col4', 'col5'])\
            .fit().transform(df)

    """

    def __init__(self, spark_session=None, **kwargs):
        """Initialize the class with the given parameters and sets the corresponding default ones.

        Parameters
        ----------
        kwargs : dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'input_cols': None
            }
        self.params.set_default_params(default_params)

    def fit(self, df = None, verbose: int = 0):
        """
        Fits the model with the given DataFrame.

        Parameters
        ----------
        df : Spark DataFrame, optional
            The DataFrame to fit the model.
        verbose : int, optional
            The verbosity level.

        Returns
        -------
        RenameColumnsModel
            An instance of the RenameColumnsModel class.
        """
        return RemoveColumnsModel(self.spark_session, self.params)


class RemoveColumnsModel(Model):
    """
    This class is used to remove the columns of a DataFrame.

    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """
        Initializes a new instance of the RenameColumnsModel class.

        Parameters
        ----------
        params : AnnotatorParams, optional
            The parameters for the model.
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, df, verbose: int = 0):
        """
        Performs  the logic.

        Applies the renaming transformation to the DataFrame.

        Parameters
        ------------
        df: Spark DataFrame
            DataFrame to transform.
        verbose: int, optional
            Verbosity level.

        Returns
        ----------
        Spark DataFrame
            Transformed DataFrame with renamed columns.

        Raises
        --------
        Exception:
            If input or output columns are not specified.
        """

        df = df.drop(*self.params.input_cols)

        return df


class ApplyFunc(Annotator):
    """
    Class used to apply a function to a DataFrame.

    This class provides functionality to apply a specified function to the data in a DataFrame, either to entire rows,
    columns, or specific subsets defined by input and output columns.

    Example
    -------
    .. code-block:: python

        test_data = [(1, [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], [1, 1, 1, 1, 1, 4, 4, 4, 4, 4, 4],
                      [1.5, 1.2, 2.1, 2.3, 2.6, 4.8, 4.4, 4.2, 6.0, 6.5, 6.7], "Masc")]
        schema = T.StructType([
            T.StructField("id", T.IntegerType(), True),
            T.StructField("ts", T.ArrayType(T.IntegerType()), True),
            T.StructField("abp", T.ArrayType(T.IntegerType()), True),
            T.StructField("hr", T.ArrayType(T.FloatType()), True),
            T.StructField("gender", T.StringType(), True),
        ])

        df = self.spark_session.createDataFrame(test_data, schema)

        apply_func = etl.annotators.dataprep.basic.ApplyFunc() \
            .set_input_cols(['id']) \
            .set_output_cols(['id_str']) \
            .set_function(lambda x: str(x)) \
            .set_output_type(T.StringType()) \
            .fit()
        transformed_df = apply_func.transform(df)

    """

    def __init__(self, spark_session=None, **kwargs):
        """
        Initialize the class with the given parameters and sets the corresponding default ones.

        Parameters
        ----------
        kwargs : dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'function': None,
            'output_type': None,
            'input_cols': None,
            'output_cols': None
        }
        self.params.set_default_params(default_params)

    def set_function(self, x):
        """
        Set the function to apply.

        Parameters
        ----------
        x: callable
            An expression to be applied to the DataFrame.

        Returns
        -------
        ApplyFunc
            The ApplyFunc object.

        Raises
        ------
        TypeError
            If x is not a function.
        """
        if callable(x):
            self.params.__setattr__('function', x)
        else:
            raise TypeError('Func must be a function.')

        return self

    def set_output_type(self, x):
        """
        Set the output type using pyspark types.

        Parameters
        ----------
        x : pyspark.sql.types

        Returns
        -------
        ApplyFunc
            The instance of the ApplyFunc class with the updated parameter.
        """
        self.params.__setattr__('output_type', x)

        return self

    def fit(self, df = None, verbose: int = 0):
        """
        Fits the model with the given DataFrame.

        Parameters
        ----------
        df : Spark DataFrame, optional
            The DataFrame to fit the model.
        verbose : int, optional
            The verbosity level.

        Returns
        -------
        ApplyFuncModel
            An instance of the ApplyFuncModel class.
        """
        return ApplyFuncModel(self.spark_session, self.params)


class ApplyFuncModel(Model):
    """
    This class is used to apply a function to a pandas DataFrame.

    """
    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """
        Initializes a new instance of the ApplyFuncModel class.

        Parameters
        ----------
        params : AnnotatorParams, optional
            The parameters for the model.
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, df, verbose: int = 0):
        """
        Performs the logic.

        Parameters
        ------------
        df: Spark DataFrame
            Input DataFrame.
        verbose: int, optional
            Level of verbosity.

        Returns
        ----------
        Spark DataFrame
            Transformed DataFrame with the output stored in output_col.

        Raises
        --------
        Exception:
            If input or output columns are not specified.
        """
        udf_func = F.udf(self.params.function, self.params.output_type)
        if len(self.params.input_cols) == len(self.params.output_cols):
            for incol, outcol in zip(self.params.input_cols, self.params.output_cols):
                df = df.withColumn(outcol, udf_func(df[incol]))
        else:
            if len(self.params.input_cols) == 2:
                df = df.withColumn(self.params.output_cols[0], udf_func(df[self.params.input_cols[0]],
                                                                        df[self.params.input_cols[1]]))
            elif len(self.params.input_cols) == 3:
                df = df.withColumn(self.params.output_cols[0], udf_func(df[self.params.input_cols[0]],
                                                                        df[self.params.input_cols[1]],
                                                                        df[self.params.input_cols[2]]))
            elif len(self.params.input_cols) == 4:
                df = df.withColumn(self.params.output_cols[0], udf_func(df[self.params.input_cols[0]],
                                                                        df[self.params.input_cols[1]],
                                                                        df[self.params.input_cols[2]],
                                                                        df[self.params.input_cols[3]]))

        return df