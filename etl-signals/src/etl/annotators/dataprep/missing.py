from etl.annotators.annotators import Annota<PERSON>, Model, AnnotatorParams
from etl.decorators import decorators as deco
from pyspark.sql import functions as F

class DropNAs(Annotator):
    """
    This class is used to drop the rows with NAs in the given columns.

    This class takes a DataFrame and removes rows where any of the specified columns have NA values.

    Example
    -------
    .. code-block:: python
        drop_na = DropNAs().set_input_cols(['col1', 'col2']).set_output_cols(['col1', 'col2'])
        drop_na_model = drop_na.fit()
        df_cleaned = drop_na_model.transform(df)

    """

    def __init__(self, spark_session=None, **kwargs):
        """
        Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ----------
        kwargs : dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'input_cols': None,
            'output_cols': None,
            'mode': "selected"
        }

        self.params.set_default_params(default_params)

    def set_mode(self, x):
        """
        Set the mode for the NaN detection (all, selected).

        Parameters
        ----------
        x : str
            Mode for the NaN filling .

        Returns
        -------
        DropNAs
            The instance of the class with the updated parameter.

        Raises
        ------
        ValueError
            If the mode is not one of ['selected', 'any'].

        Example
        -------
        .. code-block:: python
            fill_na = DropNAs().set_mode('any')
        """
        if x in ['selected', 'any']:
            self.params.mode = x
        else:
            raise ValueError("NaN Filler mode must be set to ['selected', 'any']")
        return self

    def fit(self, df = None, verbose: int = 0):
        """
        Fits the model with the given DataFrame.

        Parameters
        ----------
        df : Spark DataFrame, optional
            DataFrame to fit the model.
        verbose : int, optional
            Verbosity level.

        Returns
        -------
        DropNAsModel
            Returns an instance of DropNAsModel.

        Example
        -------
        .. code-block:: python
            drop_na = DropNAs().set_input_cols(['col1', 'col2'])
            drop_na_model = drop_na.fit()
        """

        return DropNAsModel(self.spark_session, self.params)


class DropNAsModel(Model):
    """
    This class takes a DataFrame and drops rows with NAs in specified columns.

    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """
        Initialize the class with the given parameters.

        Parameters
        ----------
        params : AnnotatorParams
            AnnotatorParams instance.
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, df, verbose: int = 0):
        """
        Drop the values with NAs in any of the given columns.
        If any of the columns has a NA, all the values in the other columns with the same index are dropped.

        Parameters
        ----------
        df : Spark DataFrame
            DataFrame to perform the annotation.
        verbose : int, optional
            Verbosity level.

        Returns
        -------
        Spark DataFrame
            DataFrame with the performed transformation.

        Raises
        ------
        ValueError
            If either input_col or input_cols are not specified.

        Example
        -------
        .. code-block:: python
            df = pd.DataFrame({'col1': [1, 2, None], 'col2': [4, None, 6]})
            drop_na = etl.annotators.dataprep.missing.DropNAs() \
                .set_input_cols(['col1', 'col2'])
            drop_na_model = drop_na.fit()
            df_cleaned = drop_na_model.transform(df)
        """

        if self.params.mode == 'any':
            df = df.na.drop()
        else:
            if self.params.input_cols is None:
                raise ValueError("input_cols must be specified.")

            if verbose > 0:
                print(f"Dropping NaN values from {self.params.input_cols}")

            df = df.na.drop(subset=self.params.input_cols)

        return df