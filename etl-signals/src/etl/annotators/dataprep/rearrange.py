from etl.annotators.annotators import Annotator, Model, AnnotatorParams
from etl.decorators import decorators as deco
from pyspark.sql import functions

class GroupRowsValues(Annotator):
    """
    Transforms a dataframe with a 'one row per value' structure to a 'one row per segment' structure,
    grouping by another column value.

    """

    def __init__(self, spark_session=None, **kwargs):
        """
        Initialize the class with the given parameters and sets the corresponding default params.

        Parameters
        ----------
        kwargs : dict
            Keyword arguments containing the parameters.
        """
        super().__init__(spark_session=spark_session, **kwargs)

        default_params = {
            'transform_input_type': 'dataframe',
            'transform_output_type': 'dataframe',
            'groupby_cols': None
        }
        self.params.set_default_params(default_params)

    @deco.check_type(list, 'groupby_cols')
    def set_groupby_cols(self, x: list):
        """
        Set cols containing the values for separating or grouping the segments.

        Parameters
        ----------
        x : str or list
            Group_by columns name/s.

        Returns
        -------
        GroupRowsValues
            The instance of the class with the updated parameter.
        """
        self.params.groupby_cols = x
        return self


    def fit(self, verbose: int = 0):
        """
        Fits the model with the given DataFrame.

        Parameters
        ----------
        verbose : int, optional
            Verbosity level.

        Returns
        -------
        GroupRowsValuesModel
            Returns an instance of GroupRowsValuesModel.
        """
        return GroupRowsValuesModel(self.spark_session, self.params)


class GroupRowsValuesModel(Model):
    """
    Transforms a dataframe with a 'one row per value' structure to a 'one row per segment' structure,
    grouping by another column value.
    """

    def __init__(self, spark_session=None, params: AnnotatorParams = None):
        """
        Initialize the class with the given parameters.

        Parameters
        ----------
        params : AnnotatorParams, optional
            The parameters for the model.
        """
        super().__init__(spark_session=spark_session, params=params)

    def transform(self, df, verbose: int=0):
        """
        Perform the calculation from the given dataframe. Returns another dataframe with the output cols added.

        Parameters
        ----------
        df : pd.DataFrame
            Input DataFrame.
        verbose : int, optional
            Level of verbosity.

        Returns
        -------
        pd.DataFrame
            Transformed dataframe.
        """

        # Cols that must be aggregated
        collect_cols = [c for c in df.columns if c not in self.params.groupby_cols]

        # Aggregation expressions for collect_cols
        agg_exprs = [functions.collect_list(c).alias(c) for c in collect_cols]

        # Perform the grouping
        output = df.groupBy(*self.params.groupby_cols).agg(*agg_exprs)
        return output