from functools import wraps

def check_type(expected_type, param_name):
    def decorator(func):
        @wraps(func)
        def wrapper(self, x):
            # Handle the case when x is None separately
            if x is None and None in expected_type:
                return func(self, x)

            # If None is not explicitly allowed and x is None, raise TypeError
            if x is None and None not in expected_type:
                raise TypeError(
                    f"Expected {param_name} to be one of these types: {expected_type}, got {type(x)} instead.")

            # If expected_type is not a tuple, make it a tuple
            check_types = (expected_type,) if not isinstance(expected_type, tuple) else expected_type

            # Remove None from type checking as it's already handled
            check_types = tuple(t for t in check_types if t is not None)

            # Check if x belongs to the allowed types
            if not isinstance(x, check_types):
                raise TypeError(
                    f"Expected {param_name} to be one of these types: {expected_type}, got {type(x)} instead.")

            return func(self, x)

        return wrapper

    return decorator