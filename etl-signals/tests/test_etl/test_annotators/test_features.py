import unittest
import pandas as pd
import pyspark.sql.functions as F
import functools
import etl
from pyspark.sql import SparkSession
import numpy as np
import os
import pyspark.sql.types as T


class TestFeatures(unittest.TestCase):
    def __init__(self, methodName="runTest"):
        super().__init__(methodName)

        self.spark_session = SparkSession.builder \
            .appName("LocalTestApp") \
            .master("local[*]") \
            .getOrCreate()

        _cwd = os.getcwd()

        if _cwd.endswith(os.path.join("tests", "test_etl")) or _cwd.endswith("test_etl"):
            # tests started from  …/etl_signals/tests/test_etl
            self.assets_basefolder = os.path.abspath(os.path.join(_cwd, "assets"))

        elif _cwd.endswith("tests"):
            # tests started from  …/etl_signals/tests
            self.assets_basefolder = os.path.abspath(os.path.join(_cwd, "test_etl", "assets"))

        elif _cwd.endswith("etl_signals"):
            # tests started from the package root  …/etl_signals
            self.assets_basefolder = os.path.abspath(
                os.path.join(_cwd, "tests", "test_etl", "assets")
            )

        elif _cwd.endswith("test_annotators"):
            # tests started from the package root  …/etl_signals
            self.assets_basefolder = os.path.abspath(
                os.path.join("..", "assets")
            )

        else:
            print(f"Assets base folder not found: Current wd is {_cwd}")
            self.assets_basefolder = None


    def test_pat_calc(self):
        data = [
            (
                [3, 6, 9],  # ppg_peaks
                [2, 4, 8],  # ecg_peaks
                [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],  # dummy signal
                2  # sampling frequency in Hz
            )
        ]

        df = self.spark_session.createDataFrame(data, ["ppg_peaks", "ecg_peaks", "signal", "sampfreq"])

        pat_calc = etl.annotators.features.fromsignals.CalculatePulseArrivalTime()\
            .set_input_cols(['signal'])\
            .set_ppg_peaks_col("ppg_peaks")\
            .set_ecg_peaks_col("ecg_peaks")\
            .set_sampling_freq_col("sampfreq")\
            .set_output_cols(["pat"])
        pat_calc = pat_calc.fit()

        results = pat_calc.transform(df)

        value = results.select('pat').collect()[0][0]
        value_without_nans = value[3:]
        self.assertListEqual([0.5, 0.5, 0.5, 1.0, 1.0, 1.0, 0.5], list(value_without_nans))

    def test_calculateage(self):

        data = [
            ("1990-01-01",),
            ("2000-05-15",),
            ("1985-10-20",),
            ("1975-07-30",)
        ]

        # Column name must match your transformer’s expected input col
        df = self.spark_session.createDataFrame(data, ["dob"])

        agecalc = etl.annotators.features.fromvalues.CalculateAge()\
            .set_input_cols(["dob"])\
            .set_output_cols(["age"])

        result = agecalc.fit().transform(df)

        actual_ages = [row["age"] for row in result.select("age").collect()]
        expected_ages = [35, 25, 40, 50]

        self.assertListEqual(expected_ages, actual_ages)

    def test_pulsepressure(self):
        df = self.spark_session.createDataFrame(
            [
                (120, 80),
                (130, 90),
                (110, 70),
                (120, 80),
            ],
            ["SBP", "DBP"]
        )

        # Instantiate your CalculatePulsePressure
        calculate_pp = etl.annotators.features.fromvalues.CalculatePulsePressure() \
            .set_sbp_col("SBP")\
            .set_dbp_col("DBP")\
            .set_output_cols(['PP'])

        result = calculate_pp.fit().transform(df)
        rows = result.collect()
        pp_values = [row["PP"] for row in rows]

        # Verify the output
        self.assertListEqual([40, 40, 40, 40], pp_values)

    def test_heart_rate(self):
        df = self.spark_session.createDataFrame(
            [
                ([100, 90, 110, 120, 140, 115, 160, 130, 125, 145], [2, 5, 8], 200),
                ([105, 85, 130, 100, 105, 95, 155, 125, 165, 170], [1, 3, 6, 8], 200),
            ],
            ["ppg", "peaks", "freq"]
        )

        # Instantiate your CalculateHeartRate class
        hr_calculator = etl.annotators.features.fromsignals.CalculateHeartRate() \
            .set_input_cols(['ppg']) \
            .set_peaks_col('peaks') \
            .set_output_cols(['heart_rate']) \
            .set_sampling_freq_col('freq')

        # Perform the transformation
        result_df = hr_calculator.fit().transform(df)

        # Row/column shape check (Spark doesn't have .shape)
        row_count = result_df.count()
        col_count = len(result_df.columns)

        self.assertEqual((row_count, col_count), (2, 4))

        # Collect rows to inspect array contents
        rows = result_df.collect()

        # Check that heart_rate array length == ppg array length for the first row
        self.assertEqual(len(rows[0]["heart_rate"]), len(rows[0]["ppg"]))

        # Check that for the second row, heart_rate[8] == 6000.0
        self.assertEqual(rows[1]["heart_rate"][8], 6000.0)

    def test_getdummies(self):
        data = [
            ("Carlos", "Barcelona", "Male"),
            ("Manuel", "Madrid", "Male"),
            ("Alejandro", "Madrid", "Male"),
            ("Maria", "Barcelona", "Female"),
            ("Silvia", "Madrid", "Female"),
            ("Jose", "Sevilla", "Male"),
            ("Diego", "Sevilla", "Male"),
            ("Adrian", "Valencia", "Male")
        ]
        df_spark = self.spark_session.createDataFrame(data, ["name", "city", "Gender"])

        # 2. Test #1: get_dummies on 'city'
        get_dummies = (
            etl.annotators.features.manipulation.GetDummies()
            .set_input_cols(["city"])
        )
        df1 = get_dummies.fit().transform(df_spark)

        # 3. Check shape => 8 rows, 7 columns
        self.assertEqual(df1.count(), 8)  # rows
        self.assertEqual(len(df1.columns), 7)  # columns

        # 4. Collect rows and verify dummy values in row index 1 (Manuel)
        rows = df1.collect()
        self.assertEqual(rows[1]["Madrid"], 1)
        self.assertEqual(rows[1]["Sevilla"], 0)
        self.assertEqual(rows[1]["Valencia"], 0)

        # 5. Test #2: get_dummies on 'Gender' with prefix "gender_"
        get_dummies = (
            etl.annotators.features.manipulation.GetDummies()
            .set_input_cols(["Gender"])
            .set_prefix("gender_")
        )
        df2 = get_dummies.fit().transform(df_spark)

        # 6. Check that 'gender_Male' column exists
        self.assertIn("gender_Male", df2.columns)

    def test_featuresvaluesmap(self):
        data = [
            (0, 1, 0),
            (0, 0, 0),
            (1, 1, 0),
            (2, 2, 0),
            (2, 1, 0)
        ]
        df_spark = self.spark_session.createDataFrame(data, ["col_A", "col_B", "col_C"])

        dictmap = {
            0: "No",
            1: "Maybe",
            2: "Yes"
        }

        # Single column use case
        mapper = (
            etl.annotators.features.manipulation.FeatureValuesMap()
            .set_input_cols(["col_A"])
            .set_output_cols(["col_A_mapped"])
            .set_maps(dictmap)
            .fit()
        )

        result_spark = mapper.transform(df_spark)
        self.assertEqual(result_spark.count(), 5)
        self.assertEqual(len(result_spark.columns), 4)

        rows = result_spark.select("col_A_mapped").collect()
        mapped_vals = [row["col_A_mapped"] for row in rows]
        self.assertListEqual(mapped_vals, ["No", "No", "Maybe", "Yes", "Yes"])

        # Multiple columns, single map use case
        mapper = (
            etl.annotators.features.manipulation.FeatureValuesMap()
            .set_input_cols(["col_A", "col_B"])
            .set_output_cols(["col_A_mapped", "col_B_mapped"])
            .set_maps(dictmap)
            .fit()
        )

        result_spark = mapper.transform(df_spark)
        self.assertEqual(result_spark.count(), 5)
        self.assertEqual(len(result_spark.columns), 5)

        rows = result_spark.select("col_B_mapped").collect()
        mapped_vals = [row["col_B_mapped"] for row in rows]
        self.assertListEqual(mapped_vals, ["Maybe", "No", "Maybe", "Yes", "Maybe"])


        # Multiple columns, multiple maps in a list use case
        dictmap_list = [
            {0: "No", 1: "Maybe", 2: "Yes"},
            {0: "Yes", 1: "Maybe", 2: "No"}
        ]
        mapper = (
            etl.annotators.features.manipulation.FeatureValuesMap()
            .set_input_cols(["col_A", "col_B"])
            .set_output_cols(["col_A_mapped", "col_B_mapped"])
            .set_maps(dictmap_list)
            .fit()
        )

        result_spark = mapper.transform(df_spark)
        self.assertEqual(result_spark.count(), 5)
        self.assertEqual(len(result_spark.columns), 5)

        rows = result_spark.select("col_B_mapped").collect()
        mapped_vals = [row["col_B_mapped"] for row in rows]
        self.assertListEqual(mapped_vals, ["Maybe", "Yes", "Maybe", "No", "Maybe"])


    def test_summarize_signal(self):
        data = [
            ([1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 5]),
            ([1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 5]),
        ]

        df = self.spark_session.createDataFrame(data, ["signal", "heart_rate"])

        annotator = etl.annotators.features.fromsignals.FeaturizeSignal() \
            .set_input_cols(['signal']) \
            .set_modes(['first', 'last', 'min', 'max', 'mean', 'median', 'std', 'sum', 'length'])

        df2 = annotator.fit().transform(df)
        df2_rows = df2.collect()

        self.assertEqual(df2_rows[0]["signal_length"], 6)
        self.assertEqual(df2_rows[0]["signal_sum"], 21)

        annotator = etl.annotators.features.fromsignals.FeaturizeSignal() \
            .set_input_cols(['signal', 'heart_rate']) \
            .set_modes(['first', 'last', 'min', 'max', 'mean', 'median', 'std', 'sum', 'length', 'trend'])

        df3 = annotator.fit().transform(df)

        df3_rows = df3.collect()

        self.assertEqual(2, df3.count())
        self.assertEqual(24, len(df3.columns))
        self.assertEqual(df3_rows[0]["heart_rate_length"], 5)
        self.assertEqual(df3_rows[0]["heart_rate_sum"], 15)

    def test_p2pi(self):
        data = [
            ([10.0, 20.0, 30.0, 40.0], 100.0),  # (30->40)/freq = 10/100=0.1, error=0
            ([5.0, 6.0], 200.0),  # None, error=1
            ([0.0, 100.0, 200.0], 50.0),  #  (100->200)/50=2.0, error=0
            ([], 100.0)  # None, error=1
        ]

        df_spark = self.spark_session.createDataFrame(data, ['peak_list', 'samp_freq'])

        results = etl.annotators.features.fromsignals.PPGFiducialP2PI()\
            .set_input_cols(['peak_list'])\
            .set_output_cols(["p2pi", "error"])\
            .set_sampling_freq_col("samp_freq")\
            .fit().transform(df_spark)

        rows = results.orderBy("samp_freq").select("p2pi", "error").collect()
        actual_results = [(r["p2pi"], r["error"]) for r in rows]

        expected_results = [
            (2.0, 0),  # row with freq=50.0
            (0.10000000000000003, 0),  # row with freq=100.0 (4 peaks)
            (None, 1),  # row with freq=100.0 (0 peaks)
            (None, 1)  # row with freq=200.0 (2 peaks)
        ]

        self.assertListEqual(actual_results, expected_results)

    def test_fiducial(self):
        df = pd.read_pickle(
            os.path.abspath(
                os.path.join(self.assets_basefolder, "classified_beat_sample.pkl")
            )
        )

        # Prepare the pickle for this use case (no numpy arrays, only needed cols)
        df = df.applymap(
            lambda x: x.tolist() if isinstance(x, np.ndarray) else x
        )

        df = df[['last_beat', 'beat_class']]

        df = self.spark_session.createDataFrame(df)

        results = etl.annotators.features.fromsignals.PPGFiducialPoints() \
            .set_input_cols(['last_beat']) \
            .set_output_cols(['fidu_error', 'idx_o', 'idx_s', 'idx_notch', 'idx_dias',
                              'val_o', 'val_s', 'val_notch', 'val_dias']) \
            .set_case_col('beat_class') \
            .fit().transform(df)

        row = results.select("idx_s", "idx_notch", "idx_dias", "val_s", "val_notch", "val_dias").first()

        self.assertTrue(20 <= row["idx_s"] <= 40)
        self.assertTrue(50 <= row["idx_notch"] <= 80)
        self.assertTrue(75 <= row["idx_dias"] <= 100)

        self.assertTrue(row["val_s"] > 1)
        self.assertTrue(-0.5 <= row["val_notch"] <= 0.5)
        self.assertTrue(0 <= row["val_dias"] <= 0.5)

    def test_derivatives(self):
        df = pd.read_pickle(
            os.path.abspath(
                os.path.join(self.assets_basefolder, "last_beat_sample.pkl")
            )
        )

        df = self.spark_session.createDataFrame(df)

        results = etl.annotators.features.fromsignals.PPGFiducialDerivative() \
            .set_input_cols(['last_beat']) \
            .set_output_cols(['error', 'tb1', 'tb2'])\
            .set_sampling_freq_col('sampling_freq_hz')\
            .fit().transform(df)

        self.assertEqual(results.count(), 1)
        self.assertEqual(len(results.columns), 10)
        row0 = results.first()
        self.assertAlmostEqual(row0['tb1'], 0.095, places=12)
        self.assertAlmostEqual(row0['tb2'], 0.25, places=12)

    def test_fft(self):

        df = pd.read_pickle(
            os.path.abspath(
                os.path.join(self.assets_basefolder, "last_beat_sample.pkl")
            )
        )

        df = self.spark_session.createDataFrame(df)

        results = etl.annotators.features.fromsignals.PPGFiducialFFT() \
            .set_input_cols(['last_beat']) \
            .set_output_cols(['auc2to15'])\
            .set_mode('auc')\
            .set_f_min(2)\
            .set_f_max(15)\
            .set_sampling_freq_col('sampling_freq_hz')\
            .fit().transform(df)

        self.assertEqual(
            (results.count(), len(results.columns)),
            (1, 8)
        )
        auc_val = results.select('auc2to15').first()[0]
        self.assertEqual(round(auc_val, 3), 147.527)


        results = etl.annotators.features.fromsignals.PPGFiducialFFT() \
            .set_input_cols(['last_beat']) \
            .set_output_cols(['fft_freq1', 'spl', 'error']) \
            .set_mode('peakfreq') \
            .set_peak_number(0) \
            .set_sampling_freq_col('sampling_freq_hz')\
            .fit().transform(df)

        self.assertEqual(
            (results.count(), len(results.columns)),
            (1, 10)
        )

        first_row = results.select('fft_freq1', 'spl').first()
        self.assertEqual(round(first_row['fft_freq1'], 3), 3.279)
        self.assertEqual(round(first_row['spl'], 3), 3.279)


    def test_percentiles(self):
        seg1 = np.arange(10).tolist()
        seg2 = np.arange(20).tolist()

        df = pd.DataFrame({'segment': [seg1, seg2]})

        df = self.spark_session.createDataFrame(df)

        results = etl.annotators.features.fromsignals.PPGFiducialPercentiles() \
            .set_input_cols(['segment']) \
            .set_output_cols(['perc75', 'iqr']) \
            .fit().transform(df)

        perc75_val = results.select('perc75').first()[0]
        self.assertEqual(perc75_val, 6.75)

        # Test with real data
        df = pd.read_pickle(
            os.path.abspath(
                os.path.join(self.assets_basefolder, "last_beat_sample.pkl")
            )
        )

        df = self.spark_session.createDataFrame(df)

        results = etl.annotators.features.fromsignals.PPGFiducialPercentiles() \
            .set_input_cols(['last_beat']) \
            .set_output_cols(['perc75', 'iqr'])\
            .fit().transform(df)

        # number of rows and columns
        self.assertEqual(results.count(), 1)
        self.assertEqual(len(results.columns), 9)

        # iqr value
        iqr_val = results.select('iqr').first()[0]
        self.assertAlmostEqual(iqr_val, 1.817, places=3)

    def test_fiducialareas(self):
        df = pd.read_pickle(
            os.path.abspath(
                os.path.join(self.assets_basefolder, "classified_beat_sample.pkl")
            )
        )

        df = df.applymap(
            lambda x: x.tolist() if isinstance(x, np.ndarray) else x
        )

        df = df[['last_beat', 'idx_notch']]
        df['sampling_freq_hz'] = 200

        df = self.spark_session.createDataFrame(df)

        results = etl.annotators.features.fromsignals.PPGFiducialAreas() \
            .set_input_cols(['last_beat']) \
            .set_dn_index_col('idx_notch')\
            .set_sampling_freq_col('sampling_freq_hz')\
            .set_output_cols(['a1','a2', 'arearatio' , 'a0toslope'])\
            .fit().transform(df)

        self.assertEqual((results.count(), len(results.columns)), (1, 7))
        val = results.select("a1").first()[0]
        self.assertEqual(round(val, 3), 0.147)
        val = results.select("a2").first()[0]
        self.assertEqual(round(val, 3), -0.139)
        val = results.select("arearatio").first()[0]
        self.assertEqual(round(val, 3), -0.948)
        val = results.select("a0toslope").first()[0]
        self.assertEqual(round(val, 3), -0.044)


    def test_fiducialip(self):
        df = pd.read_pickle(
            os.path.abspath(
                os.path.join(self.assets_basefolder, "classified_beat_sample.pkl")
            )
        )
        df = df.applymap(
            lambda x: x.tolist() if isinstance(x, np.ndarray) else x
        )
        df = df[['last_beat', 'idx_notch', 'idx_dias']]
        df['sampling_freq_hz'] = 200

        df = self.spark_session.createDataFrame(df)

        results = etl.annotators.features.fromsignals.PPGFiducialIP() \
            .set_input_cols(["last_beat"]) \
            .set_sampling_freq_col('sampling_freq_hz') \
            .set_notch_index_col('idx_notch') \
            .set_dias_peak_index_col('idx_dias') \
            .set_output_cols(['error_ip', 'ip_index', 'ip_value', 'time_ip'])\
            .fit().transform(df)

        self.assertEqual((results.count(), len(results.columns)), (1, 8))
        val = results.select("error_ip").first()[0]
        self.assertEqual(round(val, 3), 0)
        val = results.select("ip_index").first()[0]
        self.assertEqual(round(val, 3), 75.0)
        val = results.select("ip_value").first()[0]
        self.assertEqual(round(val, 3), -0.025)
        val = results.select("time_ip").first()[0]
        self.assertEqual(round(val, 3), 0.375)

    def test_ppg_fidu_extraction(self):

        df =  pd.read_pickle(
            os.path.abspath(
                os.path.join(self.assets_basefolder, "ruben_10s_samples.pkl")
            )
        )

        # Ensure every list element is a *Python* float
        for col in ("ts_episode", "ppg_episode"):
            df[col] = df[col].apply(
                lambda arr: [float(x) for x in  # <-- crucial bit
                             (arr.tolist()  # NumPy array ⟶ list
                              if isinstance(arr, np.ndarray) else arr)]
            )

        df = df.astype({"ts_episode": object, "ppg_episode": object})

        schema = T.StructType([
            T.StructField("id", T.StringType(), True),
            T.StructField("ts_episode", T.ArrayType(T.DoubleType()), True),
            T.StructField("ppg_episode", T.ArrayType(T.DoubleType()), True)
        ])

        df = self.spark_session.createDataFrame(df, schema)

        results = etl.annotators.features.fromsignals.PPGSegmentFiducialExtraction()\
            .set_input_cols(['ppg_episode', 'ts_episode'])\
            .fit().transform(df, verbose=1)

        self.assertEqual((results.count(), len(results.columns)), (7, 47))

        # Detect whether *any* column in *any* row is null
        null_rows = (
            df.filter(
                functools.reduce(
                    lambda x, y: x | y,
                    (F.col(c).isNull() for c in df.columns)
                )
            ).count()
        )
        self.assertEqual(null_rows, 0)

        results2 = etl.annotators.features.fromsignals.PPGSegmentFiducialExtraction() \
            .set_input_cols(['ppg_episode', 'ts_episode']) \
            .set_drop_errors(False)\
            .fit().transform(df, verbose=1)

        row1 = results.limit(1).collect()[0].asDict()
        row2 = results2.limit(1).collect()[0].asDict()

        for col in [
            'f_idx_o', 'f_idx_s', 'f_idx_n', 'f_idx_d', 'f_val_o', 'f_val_s',
            'f_val_n', 'f_val_d', 'beat_class_2', 'beat_class_3',
            'f_val_diff_s_d', 'f_val_diff_s_n', 'f_val_diff_d_n', 'f_time_diff_s_d',
            'f_time_diff_s_n', 'f_time_diff_n_d', 'tp_p2pi_coeff', 'tdn_p2pi_coeff',
            'inverse_time_s_notch', 'a1', 'a2', 'arearatio', 'a0toslope',
            'ip_index', 'ip_value', 'time_ip', 'inverse_tip_tsp_coeff',
            'pulse_interval', 'adp_pi_tdn_coeff', 'fft_freq1', 'spl',
            'auc2to5', 'auc0to2', 'perc75', 'iqr', 'tb1',
            'tb2', 'tb1_tb2_p2pi_coeff', 'tb1_p2pi_coeff', 'tb2_p2pi_coeff'
        ]:
            self.assertEqual(row1[col], row2[col])

    def test_trigonometric_extractor(self):
        test_data = [(1, 0, 0, 1, 1),
                     (2, 0, 0, 1, -1),
                     (3, 0, 0, -1, 1),
                     (4, 0, 0, 0, 1),
                     (5, 0, 0, 1, 0)]
        schema = T.StructType([
            T.StructField("id", T.IntegerType(), True),
            T.StructField("o_values", T.IntegerType(), True),
            T.StructField("o_timestamps", T.IntegerType(), True),
            T.StructField("s_values", T.IntegerType(), True),
            T.StructField("s_timestamps", T.IntegerType(), True)
        ])

        df = self.spark_session.createDataFrame(test_data, schema)


        trigonometric_extractor = etl.annotators.features.fromsignals.TrigonometricExtractor()\
            .set_input_cols(['o_timestamps', 'o_values',  's_timestamps', 's_values'])\
            .set_output_cols(['o_s_cosine', 'o_s_sine', 'o_s_magnitude', 'o_s_angle', 'o_s_degrees'])

        transformed_df = trigonometric_extractor.fit().transform(df)

        # 45 degrees (0,0) to (1,1)
        self.assertAlmostEqual(np.round(transformed_df.collect()[0]['o_s_cosine'],6), 0.707107)
        self.assertAlmostEqual(np.round(transformed_df.collect()[0]['o_s_sine'],6), 0.707107)
        self.assertAlmostEqual(np.round(transformed_df.collect()[0]['o_s_magnitude'],6), 1.414214)
        self.assertAlmostEqual(np.round(transformed_df.collect()[0]['o_s_angle'],6), 0.785398)
        self.assertAlmostEqual(np.round(transformed_df.collect()[0]['o_s_degrees'],6), 45.0)

        # 135 degrees (0,0) to (-1,1)
        self.assertAlmostEqual(np.round(transformed_df.collect()[1]['o_s_cosine'],6), -0.707107)
        self.assertAlmostEqual(np.round(transformed_df.collect()[1]['o_s_sine'],6), 0.707107)
        self.assertAlmostEqual(np.round(transformed_df.collect()[1]['o_s_magnitude'],6), 1.414214)
        self.assertAlmostEqual(np.round(transformed_df.collect()[1]['o_s_angle'],6), 2.356194)
        self.assertAlmostEqual(np.round(transformed_df.collect()[1]['o_s_degrees'],6), 135.0)

        # -45 degrees (0,0) to (1,-1)
        self.assertAlmostEqual(np.round(transformed_df.collect()[2]['o_s_cosine'],6), 0.707107)
        self.assertAlmostEqual(np.round(transformed_df.collect()[2]['o_s_sine'],6), -0.707107)
        self.assertAlmostEqual(np.round(transformed_df.collect()[2]['o_s_magnitude'],6), 1.414214)
        self.assertAlmostEqual(np.round(transformed_df.collect()[2]['o_s_angle'],6), -0.785398)
        self.assertAlmostEqual(np.round(transformed_df.collect()[2]['o_s_degrees'],6), -45.0)

        # 0 degrees (0, 0) to (1, 0)
        self.assertAlmostEqual(np.round(transformed_df.collect()[3]['o_s_cosine'],6), 1.0)
        self.assertAlmostEqual(np.round(transformed_df.collect()[3]['o_s_sine'],6), 0.0)
        self.assertAlmostEqual(np.round(transformed_df.collect()[3]['o_s_magnitude'],6), 1.0)
        self.assertAlmostEqual(np.round(transformed_df.collect()[3]['o_s_angle'],6), 0.0)
        self.assertAlmostEqual(np.round(transformed_df.collect()[3]['o_s_degrees'],6), 0.0)

        # 90 degrees (0, 0) to (0, 1)
        self.assertAlmostEqual(np.round(transformed_df.collect()[4]['o_s_cosine'],6), 0.0)
        self.assertAlmostEqual(np.round(transformed_df.collect()[4]['o_s_sine'],6), 1.0)
        self.assertAlmostEqual(np.round(transformed_df.collect()[4]['o_s_magnitude'],6), 1.0)
        self.assertAlmostEqual(np.round(transformed_df.collect()[4]['o_s_angle'],6), 1.570796)
        self.assertAlmostEqual(np.round(transformed_df.collect()[4]['o_s_degrees'],6), 90.0)

    def test_mega_annotator_trigo(self):
        df = pd.read_pickle(os.path.abspath(
                os.path.join(self.assets_basefolder, "processed_empl_sample.pkl")
            ))

        # Test for nan presence robustness
        new_row = {
            'beat_ts': [0.0],
            'f_idx_o': 10,
            'f_idx_s': 20,
            'f_idx_n': 30,
            'f_idx_d': 40,
            'f_val_o': 50.0,
            'f_val_s': 60.0,
            'f_val_n': 70.0,
            'f_val_d': np.nan
        }
        df.loc[len(df)] = new_row

        # Test for filtering robustness
        copied_row = df.loc[4].copy()

        copied_row['f_val_s'] = 0.5
        copied_row['f_val_n'] = 1.0
        copied_row['f_val_d'] = 0.7

        df.loc[len(df)] = copied_row

        lista = [[float(e) for e in range(100)],
                 [float(e) for e in range(100, 200)],
                 [float(e) for e in range(200, 300)],
                 [float(e) for e in range(300, 400)],
                 [float(e) for e in range(400, 500)],
                 [float(e) for e in range(500, 600)],
                 [float(e) for e in range(600, 700)]]

        df['beat_ts'] = lista

        schema = T.StructType([
            T.StructField("beat_ts", T.ArrayType(T.FloatType()), True),
            T.StructField("f_idx_o", T.IntegerType(), True),
            T.StructField("f_idx_s", T.IntegerType(), True),
            T.StructField("f_idx_n", T.FloatType(), True),
            T.StructField("f_idx_d", T.FloatType(), True),
            T.StructField("f_val_o", T.FloatType(), True),
            T.StructField("f_val_s", T.FloatType(), True),
            T.StructField("f_val_n", T.FloatType(), True),
            T.StructField("f_val_d", T.FloatType(), True)
        ])

        df_spark = self.spark_session.createDataFrame(df, schema)

        multiple_trigo_extractor_filter = etl.annotators.features.fromsignals.MultipleTrigonometricExtractorAndFilter() \
            .set_s_n_filter_threshold((-90, 0)) \
            .set_n_d_filter_threshold((0, 90)) \
            .set_beat_ts_col('beat_ts') \
            .set_f_idx_o_col('f_idx_o') \
            .set_f_idx_s_col('f_idx_s') \
            .set_f_idx_n_col('f_idx_n') \
            .set_f_idx_d_col('f_idx_d') \
            .set_f_val_o_col('f_val_o') \
            .set_f_val_s_col('f_val_s') \
            .set_f_val_n_col('f_val_n') \
            .set_f_val_d_col('f_val_d')

        df_transformed = multiple_trigo_extractor_filter.fit().transform(df_spark)

        self.assertEqual(3, len(df_transformed.collect()))
        self.assertEqual(29, len(df_transformed.columns))
        self.assertListEqual([
            'beat_ts', 'f_idx_o', 'f_idx_s', 'f_idx_n', 'f_idx_d', 'f_val_o',
            'f_val_s', 'f_val_n', 'f_val_d', 'scaled_beat_ts', 'scaled_f_idx_o',
            'scaled_f_idx_s', 'scaled_f_idx_n', 'scaled_f_idx_d', 'o_s_magnitude',
            'o_s_cosine','o_s_sine', 'o_s_angle', 'o_s_degrees', 's_n_magnitude',
            's_n_cosine','s_n_sine', 's_n_angle', 's_n_degrees', 'n_d_magnitude',
            'n_d_cosine','n_d_sine', 'n_d_angle', 'n_d_degrees'], list(df_transformed.columns))

        # Test for calculation without filtering
        multiple_trigo_extractor_filter = etl.annotators.features.fromsignals.MultipleTrigonometricExtractorAndFilter()\
            .set_beat_ts_col('beat_ts') \
            .set_s_n_filter_threshold(None) \
            .set_n_d_filter_threshold(None) \
            .set_f_idx_o_col('f_idx_o') \
            .set_f_idx_s_col('f_idx_s') \
            .set_f_idx_n_col('f_idx_n') \
            .set_f_idx_d_col('f_idx_d') \
            .set_f_val_o_col('f_val_o') \
            .set_f_val_s_col('f_val_s') \
            .set_f_val_n_col('f_val_n') \
            .set_f_val_d_col('f_val_d')

        df_transformed = multiple_trigo_extractor_filter.fit().transform(df_spark)

        self.assertEqual(6, len(df_transformed.collect()))
        self.assertEqual(29, len(df_transformed.columns))

if __name__ == '__main__':
    unittest.main()