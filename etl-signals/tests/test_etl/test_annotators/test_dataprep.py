import unittest
import etl
from pyspark.sql import SparkSession
import pyspark.sql.types as T


class TestDataPrep(unittest.TestCase):
    def __init__(self, methodName="runTest"):
        super().__init__(methodName)

        self.spark_session = SparkSession.builder \
            .appName("LocalTestApp") \
            .master("local[*]") \
            .getOrCreate()

    def test_groupby(self):
        data = [
            ("Group1", "Sub1", 1, "A"),
            ("Group1", "Sub1", 2, "B"),
            ("Group1", "Sub2", 3, "C"),
            ("Group2", "Sub3", 4, "D"),
            ("Group2", "Sub3", 5, "E"),
        ]
        columns = ["group_col1", "group_col2", "colA", "colB"]
        df = self.spark_session.createDataFrame(data, columns)

        grouper = etl.annotators.dataprep.rearrange.GroupRowsValues()\
            .set_groupby_cols(["group_col1"])

        results = grouper.fit().transform(df)

        rows = results.count()
        columns = len(results.columns)

        self.assertEqual(2, rows)
        self.assertEqual(4, columns)

    def test_filtervalues(self):
        sample_data = [
            (1, 10),
            (2, 9),
            (3, 8),
            (4, 7),
            (5, 6),
        ]

        df = self.spark_session.createDataFrame(sample_data, ["col1", "col2"])

        filtervals = etl.annotators.dataprep.basic.FilterValues() \
            .set_input_cols(["col1", "col2"]) \
            .set_conditions([">=", "<"]) \
            .set_conditions_values([3, 9])

        results = filtervals.fit().transform(df)

        col1_values = [row["col1"] for row in results.select("col1").collect()]
        col2_values = [row["col2"] for row in results.select("col2").collect()]
        self.assertListEqual([3, 4, 5], col1_values)
        self.assertListEqual([8, 7, 6], col2_values)

        filtervals = etl.annotators.dataprep.basic.FilterValues() \
            .set_input_cols(["col1", "col2"]) \
            .set_conditions([">="]) \
            .set_conditions_values([2])\
            .set_apply_on_all(True)

        results = filtervals.fit().transform(df)

        col1_values = [row["col1"] for row in results.select("col1").collect()]
        col2_values = [row["col2"] for row in results.select("col2").collect()]
        self.assertListEqual([2, 3, 4, 5], col1_values)
        self.assertListEqual([9, 8, 7, 6], col2_values)

    def test_merger(self):
        # Create sample data for 'src_a_df'
        src_a_df = self.spark_session.createDataFrame(
            [
                (1, 1, 1),
                (1, 2, 2),
                (2, 1, 3),
                (2, 2, 4),
            ],
            ["Patient_ID", "Session_Number", "signal_a"]
        )

        # Create sample data for 'src_b_df'
        src_b_df = self.spark_session.createDataFrame(
            [
                (1, 1, 5),
                (1, 2, 6),
                (2, 1, 7),
                (2, 2, 8),
            ],
            ["Patient_ID", "Session_Number", "signal_b"]
        )

        # Instantiate the merger (your class may have a different import path)
        merger = etl.annotators.dataprep.dfmanipulation.MergeDataFrames() \
            .set_merging_cols(["Patient_ID", "Session_Number"])

        # Perform the merge operation with .transform()
        # Passing a list of Spark DataFrames
        merged_df = merger.fit().transform([src_a_df, src_b_df])

        # Collect row/column info since PySpark DataFrames don't have a .shape attribute
        row_count = merged_df.count()
        col_count = len(merged_df.columns)

        # Check that we have 4 rows and 4 columns
        self.assertEqual((row_count, col_count), (4, 4))

        # Check that the column names match expected
        self.assertListEqual(
            merged_df.columns,
            ["Patient_ID", "Session_Number", "signal_a", "signal_b"]
        )

    def test_rounder(self):
        df = self.spark_session.createDataFrame(
            [
                ([122.5, 122.3, 122.7], [55.5, 51.2, 63.4]),
                ([122.1, 132.2, 132.1], [80.33, 91.55, 58.66])
            ],
            ["bp", "hr"]
        )

        rounder = etl.annotators.dataprep.basic.Round()\
            .set_input_cols(['bp', 'hr'])\
            .set_output_cols(['rounded_bp', 'rounded_hr'])\
            .set_decimals(0)

        results_df = rounder.fit().transform(df, verbose=1)
        first_row = results_df.collect()[0]
        first_element = first_row['rounded_hr'][0]
        self.assertEqual(56.0, first_element)

        df = self.spark_session.createDataFrame(
            [
                (55.2,),
                (55.3,),
                (55.9,)
            ],
            ["bp"]
        )

        rounder = etl.annotators.dataprep.basic.Round()\
            .set_input_cols(['bp'])\
            .set_output_cols(['rounded_bp'])\
            .set_mode('single')\
            .set_decimals(0)

        results_df = rounder.fit().transform(df)
        first_row = results_df.collect()[0]
        self.assertEqual(55.0, first_row['rounded_bp'])

    def test_is_time_gap(self):
        test_data = [(1, [1, 2, 3, 5]), (2, [1, 2, 3, 4])]
        df = self.spark_session.createDataFrame(test_data, ["id", "list_n"])

        is_time_gap = etl.annotators.checksandquality.signalchecks.IsTimeGap() \
                .set_input_cols(['list_n']) \
                .set_output_cols(['has_time_gap']) \
                .set_gap(1)

        transformed_df = is_time_gap.fit().transform(df, verbose=1)

        self.assertEqual(1, transformed_df.collect()[0]['has_time_gap'])
        self.assertEqual(0, transformed_df.collect()[1]['has_time_gap'])

    def test_rename_columns(self):
        df = self.spark_session.createDataFrame([(1, "foo"), (2, "bar")], ['old_col1', 'old_col2'])

        renamer = etl.annotators.dataprep.basic.RenameColumns()\
                .set_input_cols(['old_col1', 'old_col2']) \
                .set_output_cols(['new_col1', 'new_col2']) \
                .fit()
        transformed_df = renamer.transform(df)

        self.assertEqual(['new_col1', 'new_col2'], transformed_df.columns)

    def test_drop_na(self):
        test_data = [(1, 4), (2, None), (None, 6)]
        df = self.spark_session.createDataFrame(test_data, ["number_A", "number_B"])
        drop_na = etl.annotators.dataprep.missing.DropNAs() \
            .set_input_cols(["number_A", "number_B"])
        drop_na_model = drop_na.fit()
        transformed_df = drop_na_model.transform(df)

        self.assertEqual(1, len(transformed_df.collect()))

    def test_datetime_to_absolute(self):
        test_data = [("2025-01-01 12:01:19.000", 1, 1),
                     ("2025-01-01 13:05:24.656", 1, 2),
                     ("2025-01-01 16:50:56.241", 2, 1),
                     ("2025-01-01 20:21:32.986", 2, 2),
                     ("2025-01-01 21:12:54.394", 1, 1)]
        df = self.spark_session.createDataFrame(test_data, ["timestamp", "segment", "session"])

        datetime_to_abs = etl.annotators.dataprep.time.DatetimeToAbsolute() \
            .set_input_cols(['timestamp']) \
            .set_output_cols(['timestamp_absolute']) \
            .set_input_type('str') \
            .set_output_unit('m') \
            .set_session_number_col('session') \
            .set_segment_number_col('segment') \
            .fit()
        transformed_df = datetime_to_abs.transform(df)

        self.assertEqual(0, round(transformed_df.collect()[0]['timestamp_absolute']))
        self.assertEqual(552, round(transformed_df.collect()[4]['timestamp_absolute']))

    def test_cast_column(self):
        test_data = [(1, [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], [1, 1, 1, 1, 1, 4, 4, 4, 4, 4, 4],
                      [1, 1, 2, 2, 2, 4, 4, 4, 6, 6, 6], [3, 8, 10])]
        schema = T.StructType([
            T.StructField("id", T.IntegerType(), True),
            T.StructField("ts", T.ArrayType(T.IntegerType()), True),
            T.StructField("abp", T.ArrayType(T.IntegerType()), True),
            T.StructField("hr", T.ArrayType(T.IntegerType()), True),
            T.StructField("waves_idx", T.ArrayType(T.IntegerType()), True),
        ])

        df = self.spark_session.createDataFrame(test_data, schema)

        cast_column = etl.annotators.dataprep.basic.CastColumns() \
            .set_input_cols(['abp', 'hr']) \
            .set_output_cols(['abp_str', 'hr_str']) \
            .set_dtype('string') \
            .set_input_type('array') \
            .fit()
        transformed_df = cast_column.transform(df)

        self.assertEqual(['1', '1', '1', '1', '1', '4', '4', '4', '4', '4', '4'],
                         transformed_df.collect()[0]['abp_str'])

        cast_column = etl.annotators.dataprep.basic.CastColumns() \
            .set_input_cols(['id']) \
            .set_output_cols(['id_float']) \
            .set_dtype('float') \
            .set_input_type('single') \
            .fit()
        transformed_df = cast_column.transform(df)

        self.assertEqual(1.0, transformed_df.collect()[0]['id_float'])

    def test_remove_columns(self):
        test_data = [(1, [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], [1, 1, 1, 1, 1, 4, 4, 4, 4, 4, 4],
                      [1, 1, 2, 2, 2, 4, 4, 4, 6, 6, 6], [3, 8, 10])]
        schema = T.StructType([
            T.StructField("id", T.IntegerType(), True),
            T.StructField("ts", T.ArrayType(T.IntegerType()), True),
            T.StructField("abp", T.ArrayType(T.IntegerType()), True),
            T.StructField("hr", T.ArrayType(T.IntegerType()), True),
            T.StructField("waves_idx", T.ArrayType(T.IntegerType()), True),
        ])

        df = self.spark_session.createDataFrame(test_data, schema)

        remove_column = etl.annotators.dataprep.basic.RemoveColumns() \
            .set_input_cols(['abp', 'hr']) \
            .fit()
        transformed_df = remove_column.transform(df)

        self.assertEqual(3, len(transformed_df.columns))

    def test_apply_func(self):
        test_data = [(1, [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], [1, 1, 1, 1, 1, 4, 4, 4, 4, 4, 4],
                      [1.5, 1.2, 2.1, 2.3, 2.6, 4.8, 4.4, 4.2, 6.0, 6.5, 6.7], "Masc")]
        schema = T.StructType([
            T.StructField("id", T.IntegerType(), True),
            T.StructField("ts", T.ArrayType(T.IntegerType()), True),
            T.StructField("abp", T.ArrayType(T.IntegerType()), True),
            T.StructField("hr", T.ArrayType(T.FloatType()), True),
            T.StructField("gender", T.StringType(), True),
        ])

        df = self.spark_session.createDataFrame(test_data, schema)

        apply_func = etl.annotators.dataprep.basic.ApplyFunc() \
            .set_input_cols(['id']) \
            .set_output_cols(['id_str']) \
            .set_function(lambda x: str(x)) \
            .set_output_type(T.StringType()) \
            .fit()
        transformed_df = apply_func.transform(df)

        self.assertEqual('1', transformed_df.collect()[0]['id_str'])

        apply_func = etl.annotators.dataprep.basic.ApplyFunc() \
            .set_input_cols(['gender']) \
            .set_output_cols(['gender_cat']) \
            .set_function(lambda x: x[0]) \
            .set_output_type(T.StringType()) \
            .fit()
        transformed_df = apply_func.transform(df)

        self.assertEqual('M', transformed_df.collect()[0]['gender_cat'])

        apply_func = etl.annotators.dataprep.basic.ApplyFunc() \
            .set_input_cols(['hr']) \
            .set_output_cols(['hr_ref']) \
            .set_function(lambda x: int(round(float(x[0]), 0))) \
            .set_output_type(T.IntegerType()) \
            .fit()
        transformed_df = apply_func.transform(df)

        self.assertEqual(2, transformed_df.collect()[0]['hr_ref'])

        apply_func = etl.annotators.dataprep.basic.ApplyFunc() \
            .set_input_cols(['id', 'abp']) \
            .set_output_cols(['id/abp']) \
            .set_function(lambda x, y: [x / (i) if i is not None else None for i in y]) \
            .set_output_type(T.ArrayType(T.FloatType())) \
            .fit()
        transformed_df = apply_func.transform(df)

        self.assertEqual([1.0, 1.0, 1.0, 1.0, 1.0, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25],
                         transformed_df.collect()[0]['id/abp'])

if __name__ == '__main__':
    unittest.main()