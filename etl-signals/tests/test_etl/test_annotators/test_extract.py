import unittest
import etl
from pyspark.sql import SparkSession
import os

class TestExtractors(unittest.TestCase):
    def __init__(self, methodName="runTest"):
        super().__init__(methodName)

        _cwd = os.getcwd()

        if _cwd.endswith(os.path.join("tests", "test_etl")) or _cwd.endswith("test_etl"):
            self.assets_basefolder = os.path.abspath(os.path.join(_cwd, "assets"))

        elif _cwd.endswith("tests"):
            self.assets_basefolder = os.path.abspath(os.path.join(_cwd, "test_etl", "assets"))

        elif _cwd.endswith("etl_signals"):
            self.assets_basefolder = os.path.abspath(os.path.join(_cwd, "tests", "test_etl", "assets"))

        elif _cwd.endswith("test_annotators"):
            self.assets_basefolder = os.path.abspath(os.path.join("../", "assets"))

        else:
            print("Assets base folder not found")
            self.assets_basefolder = None

        self.spark_session = SparkSession.builder \
            .appName("LocalTestApp") \
            .master("local[*]") \
            .getOrCreate()

    def test_local_csv(self):
        extractor_csv_local = etl.annotators.extract.local.ExtractCSV(spark_session=self.spark_session) \
            .set_csv_path(os.path.join(self.assets_basefolder, "empl-raw-sample.csv"))

        df = extractor_csv_local.fit().transform()
        rows = df.count()
        columns = len(df.columns)

        self.assertEqual(1000, rows)
        self.assertEqual(78, columns)

    def test_local_xlsx(self):

        extractor = etl.annotators.extract.local.ExtractXLSX(spark_session=self.spark_session) \
            .set_xlsx_path(os.path.join(self.assets_basefolder, "test_excel_1.xlsx")) \
            .set_single_file(True)

        result = extractor.fit().transform()
        rows = result.count()
        columns = len(result.columns)

        self.assertEqual(9, rows)
        self.assertEqual(3, columns)

        extractor = etl.annotators.extract.local.ExtractXLSX(spark_session=self.spark_session) \
            .set_xlsx_path(self.assets_basefolder) \
            .set_single_file(False)

        result = extractor.fit().transform()
        rows = result.count()
        columns = len(result.columns)

        self.assertEqual(18, rows)
        self.assertEqual(3, columns)

        extractor = etl.annotators.extract.local.ExtractXLSX(spark_session=self.spark_session) \
            .set_xlsx_path(self.assets_basefolder) \
            .set_single_file(False) \
            .set_ignorestr(["_1"]) \
            .set_input_cols(['id'])

        result = extractor.fit().transform()
        rows = result.count()
        columns = len(result.columns)

        self.assertEqual(9, rows)
        self.assertEqual(2, columns)

    def test_parquet_local(self):
        extractor = etl.annotators.extract.local.ExtractParquet(spark_session=self.spark_session) \
            .set_input_cols(["labels"]) \
            .set_path(os.path.join(self.assets_basefolder, "parquet-test-file1.parquet"))

        df = extractor.fit().transform()
        self.assertEqual(2, len(df.columns))
        self.assertEqual(6, len(df.collect()))

        extractor = etl.annotators.extract.local.ExtractParquet(spark_session=self.spark_session) \
            .set_single_file(False) \
            .set_path(self.assets_basefolder)

        df = extractor.fit().transform()
        self.assertEqual(3, len(df.columns))
        self.assertEqual(12, len(df.collect()))

if __name__ == '__main__':
    unittest.main()