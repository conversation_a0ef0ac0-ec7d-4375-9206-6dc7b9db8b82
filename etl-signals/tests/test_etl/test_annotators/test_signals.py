import unittest
import etl
from pyspark.sql import SparkSession
import numpy as np
import pandas as pd
import pyspark.sql.types as T
import os

class TestSignals(unittest.TestCase):
    def __init__(self, methodName="runTest"):
        super().__init__(methodName)

        self.spark_session = SparkSession.builder \
            .appName("LocalTestApp") \
            .master("local[*]") \
            .getOrCreate()

        _cwd = os.getcwd()

        if _cwd.endswith(os.path.join("tests", "test_etl")) or _cwd.endswith("test_etl"):
            # tests started from  …/etl_signals/tests/test_etl
            self.assets_basefolder = os.path.abspath(os.path.join(_cwd, "assets"))

        elif _cwd.endswith("tests"):
            # tests started from  …/etl_signals/tests
            self.assets_basefolder = os.path.abspath(os.path.join(_cwd, "test_etl", "assets"))

        elif _cwd.endswith("etl_signals"):
            # tests started from the package root  …/etl_signals
            self.assets_basefolder = os.path.abspath(
                os.path.join(_cwd, "tests", "test_etl", "assets")
            )

        elif _cwd.endswith("test_annotators"):
            # tests started from the package root  …/etl_signals
            self.assets_basefolder = os.path.abspath(
                os.path.join("..", "assets")
            )

        else:
            print(f"Assets base folder not found: Current wd is {_cwd}")
            self.assets_basefolder = None

    def test_explode(self):
        sample_data = [
            (1,
             [1.0, 2.0, 3.0, 4.0],
             [10, 20, 30, 40],
             [2]),
            (2,
             [10.0, 11.0, 12.0, 13.0, 14.0],
             [30, 40, 50, 60, 70],
             [2, 4])
        ]

        df = self.spark_session.createDataFrame(sample_data, ["id", "signal1", "signal2", "cutpoints"])

        # Testing all_indices split method
        exploder = etl.annotators.signals.rearrange.ExplodeSignal()\
            .set_cuts_col("cutpoints")\
            .set_input_cols(["signal1", "signal2"])\
            .set_output_cols(["segmented_signal1", "segmented_signal2"])\
            .set_split_method("all_indices")

        results = exploder.fit().transform(df)

        row_count = results.count()
        colum_count = len(results.columns)
        rows = results.select('segmented_signal1').collect()
        value = rows[0]['segmented_signal1']

        self.assertEqual(5, row_count)
        self.assertEqual(9, colum_count)

        self.assertListEqual([1.0, 2.0], list(value))

        rows = results.select('segmented_signal2').collect()
        value = rows[0]['segmented_signal2']

        self.assertListEqual([10, 20], list(value))

        # Testing defined_length split method
        sample_data = [
            (1,
             [1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0],
             [10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
             [4, 9]),
            (2,
             [10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0, 20.0],
             [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20],
             [2, 6, 10])
        ]

        df = self.spark_session.createDataFrame(sample_data, ["id", "signal1", "signal2", "cutpoints"])

        exploder = etl.annotators.signals.rearrange.ExplodeSignal() \
            .set_cuts_col("cutpoints") \
            .set_input_cols(["signal1", "signal2"]) \
            .set_output_cols(["segmented_signal1", "segmented_signal2"]) \
            .set_split_method("defined_length")\
            .set_segment_length(3)

        results = exploder.fit().transform(df)

        row_count = results.count()
        colum_count = len(results.columns)
        rows = results.select('segmented_signal1').collect()
        value = rows[0]['segmented_signal1']

        self.assertEqual(4, row_count)
        self.assertEqual(9, colum_count)

        self.assertListEqual([2.0, 3.0, 4.0], list(value))

        rows = results.select('segmented_signal2').collect()
        value = rows[0]['segmented_signal2']

        self.assertListEqual([20, 30, 40], list(value))

    def test_signal_beat_extractor(self):

        # Since the peak finder is not ready yet, we will use a preprocessed pickle file
        df = pd.read_pickle(
            os.path.abspath(
                os.path.join(self.assets_basefolder, "sample_signal_with_valleys.pkl")))

        for c in df.columns:
            # We only apply the conversion for object-dtype columns,
            # because they are the most likely to contain numpy arrays.
            if df[c].dtype == object:
                df[c] = df[c].apply(lambda x: x.tolist() if isinstance(x, np.ndarray) else x)

        df_spark = self.spark_session.createDataFrame(df)

        result = etl.annotators.signals.rearrange.SignalBeatExtractor() \
            .set_indices_col('valleys') \
            .set_how_many_segments(1) \
            .set_count_from('end') \
            .set_signal_col('signal')\
            .set_timestamp_col('timestamp')\
            .set_output_cols(['last_wave_val', 'last_wave_ts']) \
            .fit().transform(df_spark)


        self.assertEqual(result.count(), 1)
        self.assertEqual(len(result.columns), 5)
        row = result.first()
        self.assertEqual(len(row.last_wave_val), 400)
        self.assertEqual(len(row.last_wave_ts), 400)

        # Select segments manually
        result = etl.annotators.signals.rearrange.SignalBeatExtractor() \
            .set_indices_col('valleys') \
            .set_select_segments([2, 3]) \
            .set_signal_col('signal') \
            .set_timestamp_col('timestamp') \
            .set_output_cols(['second_and_third', 'second_and_third_ts']) \
            .fit().transform(df_spark)


        self.assertEqual(result.count(), 1)
        self.assertEqual(len(result.columns), 5)
        row = result.first()
        self.assertEqual(len(row.second_and_third), 799)
        self.assertEqual(len(row.second_and_third_ts), 799)

    def test_get_length(self):
        test_data = [(1, "a", [1, 2]), (2, "b", [1, 2, 3])]
        df = self.spark_session.createDataFrame(test_data, ["number", "letter", "list_n"])

        get_length1 = etl.annotators.signals.characterization.GetLength() \
                .set_input_cols(['list_n']) \
                .set_output_cols(['list_length']) \
                .set_mode('len')
        get_length_model1 = get_length1.fit(df)
        transformed_df1 = get_length_model1.transform(df)

        self.assertEqual(2, transformed_df1.collect()[0]['list_length'])
        self.assertEqual(3, transformed_df1.collect()[1]['list_length'])

        get_length2 = etl.annotators.signals.characterization.GetLength() \
            .set_input_cols(['list_n']) \
            .set_output_cols(['list_dif']) \
            .set_mode('timediff')
        get_length_model2 = get_length2.fit(df)
        transformed_df2 = get_length_model2.transform(df)

        self.assertEqual(1, transformed_df2.collect()[0]['list_dif'])
        self.assertEqual(2, transformed_df2.collect()[1]['list_dif'])

    def test_invert_signal(self):
        test_data = [(1, "a", [1, 2], True), (2, "b", [1, 2, 3], False)]
        df = self.spark_session.createDataFrame(test_data, ["number", "letter", "list_n", "flag"])

        inverter_model = etl.annotators.signals.rearrange.InvertSignal() \
            .set_input_cols(['list_n']) \
            .set_output_cols(['inverted_list']) \
            .set_flag_col("flag") \
            .fit()
        transformed_df = inverter_model.transform(df)

        self.assertEqual([-1, -2], transformed_df.collect()[0]['inverted_list'])
        self.assertEqual([1, 2, 3], transformed_df.collect()[1]['inverted_list'])

    def test_get_sampling_hz(self):
        test_data = [(1, [1., 2.1, 3.3, 4.]), (2, [1., 2., 3.1, 4.1])]
        df = self.spark_session.createDataFrame(test_data, ["id", "list_n"])

        get_sampling_frequency_model = etl.annotators.signals.characterization.GetSamplingHz() \
            .set_input_cols(['list_n']) \
            .set_output_cols(['period']) \
            .set_time_unit('s') \
            .set_float_tolerance(0.1) \
            .fit()

        transformed_df = get_sampling_frequency_model.transform(df)

        self.assertEqual(None, transformed_df.collect()[0]['period'])
        self.assertEqual(1, transformed_df.collect()[1]['period'])

    def test_limit_standardized_signal(self):
        test_data = [(1, [1., 2.1, 3.3, 4.], [2.65, 3.14, 8.47]), (2, [1., 2., 3.1, 4.1], [4.32, 5.87, 1.92, 3.72])]
        df = self.spark_session.createDataFrame(test_data, ["id", "list_n", "list_m"])

        limit_stand = etl.annotators.signals.filtering.LimitStandardizedSignal() \
            .set_input_cols(['list_n', 'list_m']) \
            .set_output_cols(['list_n_norm', 'list_m_norm']) \
            .set_lower_limit(2) \
            .set_upper_limit(4)

        limit_stand_model = limit_stand.fit(df)
        transformed_df = limit_stand_model.transform(df)

        self.assertEqual(4.0, transformed_df.collect()[1]['list_n_norm'][3])
        self.assertEqual(4.0, transformed_df.collect()[0]['list_m_norm'][2])

    def test_butterworth_filter(self):
        t = np.linspace(0, 1, 1000, False)
        test_data = [(1, [float(e) for e in np.sin(2 * np.pi * 10 * t) + np.sin(2 * np.pi * 20 * t)], 1000),
                     (2, [float(e) for e in np.sin(2 * np.pi * 5 * t) + np.sin(2 * np.pi * 15 * t)], 1000)]
        schema = T.StructType([
            T.StructField("id", T.IntegerType(), True),
            T.StructField("signal", T.ArrayType(T.FloatType()), True),
            T.StructField("hertz", T.IntegerType(), True),
        ])

        df = self.spark_session.createDataFrame(test_data, schema)

        butter = etl.annotators.signals.filtering.ButterworthFilter() \
            .set_input_cols(["signal"]) \
            .set_output_cols(["signal_butter"]) \
            .set_sampling_freq_col('hertz') \
            .set_order(4) \
            .set_cutoff_low(0.5) \
            .set_cutoff_high(6.0) \
            .set_handlenan('zero')

        butter_model = butter.fit(df)
        transformed_df = butter_model.transform(df)

        self.assertEqual(1000, len(transformed_df.collect()[0]['signal_butter']))
        self.assertEqual(2, len(transformed_df.collect()))
        self.assertEqual(4, len(transformed_df.columns))

    def test_heartpy_enhance_ecg_peaks(self):
        t = np.linspace(0, 1, 1000, False)
        test_data = [
            (1, list(range(1000)), [float(e) for e in np.sin(2 * np.pi * 10 * t) + np.sin(2 * np.pi * 20 * t)], 1000),
            (2, list(range(1000)), [float(e) for e in np.sin(2 * np.pi * 5 * t) + np.sin(2 * np.pi * 15 * t)], 1000)]
        schema = T.StructType([
            T.StructField("id", T.IntegerType(), True),
            T.StructField("ts", T.ArrayType(T.IntegerType()), True),
            T.StructField("signal", T.ArrayType(T.FloatType()), True),
            T.StructField("hertz", T.IntegerType(), True),
        ])

        df = self.spark_session.createDataFrame(test_data, schema)

        heartpy_ecg_peaks = etl.annotators.signals.filtering.HeartPyEnhanceECGPeaks() \
            .set_ecg_col('signal') \
            .set_ts_col('ts') \
            .set_filter_type('notch') \
            .set_filter_cutoff(0.05) \
            .set_sample_rate_col('hertz') \
            .set_aggregation('median') \
            .set_iterations(3) \
            .set_output_cols(['ecg_enhanced_r_episode'])

        heartpy_ecg_peaks_model = heartpy_ecg_peaks.fit(df)
        transformed_df = heartpy_ecg_peaks_model.transform(df)

        self.assertEqual(1000, len(transformed_df.collect()[0]['ecg_enhanced_r_episode']))
        self.assertEqual(2, len(transformed_df.collect()))
        self.assertEqual(5, len(transformed_df.columns))

    def test_time_gap_segmentator(self):
        t1 = np.linspace(0, 1, 8, False)
        t2 = np.linspace(0, 1, 7, False)
        test_data = [(1, [1, 2, 3, 5, 6, 7, 9, 10],
                      [float(e) for e in np.sin(2 * np.pi * 10 * t1) + np.sin(2 * np.pi * 20 * t1)], 1000),
                     (2, [1, 2, 4, 5, 7, 8, 10],
                      [float(e) for e in np.sin(2 * np.pi * 5 * t2) + np.sin(2 * np.pi * 15 * t2)], 1000)]
        schema = T.StructType([
            T.StructField("id", T.IntegerType(), True),
            T.StructField("ts", T.ArrayType(T.IntegerType()), True),
            T.StructField("signal", T.ArrayType(T.FloatType()), True),
            T.StructField("hertz", T.IntegerType(), True),
        ])

        df = self.spark_session.createDataFrame(test_data, schema)

        time_gap_segmentator = etl.annotators.signals.rearrange.TimeGapSegmentator(self.spark_session) \
            .set_input_cols(['signal']) \
            .set_output_cols(['signal_segmented']) \
            .set_time_ref_col('ts') \
            .set_output_ts_col('ts_segmented') \
            .set_gap_threshold(1)

        time_gap_segmentator_model = time_gap_segmentator.fit(df)
        transformed_df = time_gap_segmentator_model.transform(df)

        self.assertEqual(3, len(transformed_df.collect()[0]['signal_segmented']))
        self.assertEqual(7, len(transformed_df.collect()))
        self.assertEqual(6, len(transformed_df.columns))

    def test_find_pulses(self):
        t1 = np.linspace(0, 1, 8, False)
        t2 = np.linspace(0, 1, 7, False)
        test_data = [(1, [1, 2, 3, 5, 6, 7, 9, 10],
                      [float(e) for e in np.sin(2 * np.pi * 10 * t1) + np.sin(2 * np.pi * 20 * t1)], 1000),
                     (2, [1, 2, 4, 5, 7, 8, 10],
                      [float(e) for e in np.sin(2 * np.pi * 5 * t2) + np.sin(2 * np.pi * 15 * t2)], 1000)]
        schema = T.StructType([
            T.StructField("id", T.IntegerType(), True),
            T.StructField("ts", T.ArrayType(T.IntegerType()), True),
            T.StructField("signal", T.ArrayType(T.FloatType()), True),
            T.StructField("hertz", T.IntegerType(), True),
        ])

        df = self.spark_session.createDataFrame(test_data, schema)

        find_pulses = etl.annotators.signals.characterization.FindPulses() \
                       .set_input_cols(['signal']) \
                       .set_output_cols(['signal_peaks', 'ts_peaks']) \
                       .set_time_col('ts') \
                       .set_prominence(0.25) \
                       .set_mode('max') \
                       .set_distance(1)

        find_pulses_model = find_pulses.fit(df)
        transformed_df = find_pulses_model.transform(df)

        self.assertEqual([1, 5], transformed_df.collect()[0]['signal_peaks'])
        self.assertEqual([4], transformed_df.collect()[1]['ts_peaks'])
        self.assertEqual(6, len(transformed_df.columns))

    def test_standardize_signal(self):
        test_data = [(1, [1., 2.1, 3.3, 4.], [25.65, 32.14, 89.47]), (2, [1., 2., 3.1, 4.1], [49.32, 52.87, 10.92, 38.72])]
        df = self.spark_session.createDataFrame(test_data, ["id", "list_n", "list_m"])

        standardize_list = etl.annotators.signals.standardization.StandardizeSignal() \
                .set_input_cols(['list_n', 'list_m']) \
                .set_output_cols(['list_n_norm', 'list_m_norm']) \
                .set_mode('by_row')

        standardize_list_model = standardize_list.fit(df)
        transformed_df = standardize_list_model.transform(df)

        self.assertEqual([-1.3952662944793701, -0.43602073192596436, 0.6104289889335632, 1.2208579778671265],
                         transformed_df.collect()[0]['list_n_norm'])
        self.assertEqual([0.6905104517936707, 0.9062474370002747, -1.6430957317352295, 0.04633788391947746],
                         transformed_df.collect()[1]['list_m_norm'])

    def test_robust_peak_finder(self):
        t1 = np.linspace(0, 1, 1000, False)
        t2 = np.linspace(0, 1, 1000, False)
        test_data = [(1, [float(e) for e in np.sin(2 * np.pi * 10 * t1) + np.sin(2 * np.pi * 20 * t1)],
                      [float(e) for e in np.sin(2 * np.pi * 20 * t1) + np.sin(2 * np.pi * 30 * t1)], 1000),
                     (2, [float(e) for e in np.sin(2 * np.pi * 5 * t2) + np.sin(2 * np.pi * 15 * t2)],
                      [float(e) for e in np.sin(2 * np.pi * 25 * t1) + np.sin(2 * np.pi * 35 * t1)], 1000)]
        schema = T.StructType([
            T.StructField("id", T.IntegerType(), True),
            T.StructField("ppg", T.ArrayType(T.FloatType()), True),
            T.StructField("ecg", T.ArrayType(T.FloatType()), True),
            T.StructField("hertz", T.IntegerType(), True),
        ])

        df = self.spark_session.createDataFrame(test_data, schema)

        robust_peak_finder = etl.annotators.signals.characterization.RobustPeakFinder() \
            .set_ppg_col('ppg') \
            .set_ecg_col('ecg')

        robust_peak_finder_model = robust_peak_finder.fit(df)
        transformed_df = robust_peak_finder_model.transform(df)

        self.assertEqual([15, 115, 215, 315, 415, 515, 615, 715, 815, 915], transformed_df.collect()[0]['ppg_peaks'])
        self.assertEqual([108, 308, 476, 592, 708, 876], transformed_df.collect()[1]['ecg_valleys'])
        self.assertEqual(8, len(transformed_df.columns))

    def test_filter_waves_by_signal(self):
        test_data = [(1, [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], [1, 1, 1, 1, 1, 4, 4, 4, 4, 4, 4], [3, 6, 10])]
        schema = T.StructType([
            T.StructField("id", T.IntegerType(), True),
            T.StructField("ts", T.ArrayType(T.IntegerType()), True),
            T.StructField("values", T.ArrayType(T.IntegerType()), True),
            T.StructField("waves_idx", T.ArrayType(T.IntegerType()), True),
        ])

        df = self.spark_session.createDataFrame(test_data, schema)

        filter_waves = etl.annotators.signals.filtering.FilterWavesBySignal() \
            .set_input_cols(['ts', 'values', 'waves_idx']) \
            .set_output_cols(['waves_idx_filtered']) \
            .set_noise_col('noise_idx') \
            .set_ts_length(5) \
            .set_threshold_minmax([0, 9999]) \
            .set_threshold_mean([0, 9999]) \
            .set_threshold_std([0.0, 0.0]) \
            .set_threshold_slope([-0.0001, 0.0001])

        filter_waves_model = filter_waves.fit(df)
        transformed_df = filter_waves_model.transform(df)

        self.assertEqual([10], transformed_df.collect()[0]['waves_idx_filtered'])
        self.assertEqual([6], transformed_df.collect()[0]['noise_idx'])

    def test_merge_overlaps_by_index(self):
        test_data = [(1, [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], [1, 1, 1, 1, 1, 4, 4, 4, 4, 4, 4], [3, 8, 10])]
        schema = T.StructType([
            T.StructField("id", T.IntegerType(), True),
            T.StructField("ts", T.ArrayType(T.IntegerType()), True),
            T.StructField("values", T.ArrayType(T.IntegerType()), True),
            T.StructField("waves_idx", T.ArrayType(T.IntegerType()), True),
        ])

        df = self.spark_session.createDataFrame(test_data, schema)

        merge_waves = etl.annotators.signals.rearrange.MergeOverlapsByIndex() \
            .set_input_cols(['waves_idx']) \
            .set_output_cols(['waves_idx_merged']) \
            .set_ts_length(3)

        merge_waves_model = merge_waves.fit(df)
        transformed_df = merge_waves_model.transform(df)

        self.assertEqual([3, 10], transformed_df.collect()[0]['waves_idx_merged'])

    def test_multi_filtering_signal(self):
        test_data = [(1, [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], [1, 1, 1, 1, 1, 4, 4, 4, 4, 4, 4],
                      [1, 1, 2, 2, 2, 4, 4, 4, 6, 6, 6], [3, 8, 10])]
        schema = T.StructType([
            T.StructField("id", T.IntegerType(), True),
            T.StructField("ts", T.ArrayType(T.IntegerType()), True),
            T.StructField("abp", T.ArrayType(T.IntegerType()), True),
            T.StructField("hr", T.ArrayType(T.IntegerType()), True),
            T.StructField("waves_idx", T.ArrayType(T.IntegerType()), True),
        ])

        df = self.spark_session.createDataFrame(test_data, schema)

        abp_thresholds = {
            'std_threshold': [0, 30],
            'mean_threshold': [0, 100],
            'range_threshold': [0, 120],
            'slope_threshold': [-1, 1]
        }
        hr_thresholds = {
            'std_threshold': [0, 30],
            'mean_threshold': [0, 140],
            'range_threshold': [0, 180],
            'slope_threshold': [-1, 1]
        }

        multi_filtering_signal = etl.annotators.signals.filtering.MultiFilteringSignal() \
            .set_filter_sequence(['abp', 'hr']) \
            .set_ts_col('ts') \
            .set_hr_col('hr') \
            .set_abp_col('abp') \
            .set_indices_col('waves_idx') \
            .set_abp_thresholds(abp_thresholds) \
            .set_hr_thresholds(hr_thresholds) \
            .set_segment_length(4)

        multi_filtering_signal_model = multi_filtering_signal.fit(df)
        transformed_df = multi_filtering_signal_model.transform(df)

        self.assertEqual([10], transformed_df.collect()[0]['merged_indices'])

    def test_ppg_case_classifier(self):
        np.random.seed(42)
        signal = np.random.rand(250).tolist()

        df = self.spark_session.createDataFrame([(signal,)], ["signal"])

        classifier = etl.annotators.signals.characterization.PPGCaseClassifier() \
            .set_input_cols(['signal']) \
            .set_output_cols(['error_present', 'case']) \
            .fit()

        results = classifier.transform(df)

        self.assertEqual(1, results.count())
        self.assertEqual(3, len(results.columns))

        df = pd.read_pickle(
            os.path.abspath(
                os.path.join(self.assets_basefolder, "mojo_beats_sample.pkl")
            )
        )
        df = self.spark_session.createDataFrame(df)

        classifier = etl.annotators.signals.characterization.PPGCaseClassifier() \
            .set_input_cols(['beats']) \
            .set_output_cols(['error_present', 'case']) \
            .fit()

        results = classifier.transform(df)

        self.assertEqual((results.count(), len(results.columns)), (10, 3))
        self.assertTrue(results.select("case").first()[0] is None)

    def test_resample_aligned_signals(self):
        test_data = [(1, [0.0, 1.0, 2.0, 3.0], [1.0, 2.0, 3.0, 2.5], [0.5, 1.5, 2.5, 3.0]),
                     (2, [10.0, 11.0, 12.0, 13.0], [5.0, 6.0, 6.5, 7.0], [3.1, 3.2, 3.1, 3.0])]
        schema = T.StructType([
            T.StructField("id", T.IntegerType(), True),
            T.StructField("timestamp", T.ArrayType(T.FloatType()), True),
            T.StructField("signal1", T.ArrayType(T.FloatType()), True),
            T.StructField("signal2", T.ArrayType(T.FloatType()), True)
        ])

        df = self.spark_session.createDataFrame(test_data, schema)

        resample_aligned_signals = etl.annotators.signals.rearrange.ResampleAlignedSignals() \
            .set_input_cols(['signal1', 'signal2']) \
            .set_output_cols(['signal1_new', 'signal2_new']) \
            .set_timestamp_col('timestamp') \
            .set_original_time_unit('s') \
            .set_new_timestamp_col('timestamp_new') \
            .set_target_sampling_frequency(2)

        resample_aligned_signals_model = resample_aligned_signals.fit(df)
        transformed_df = resample_aligned_signals_model.transform(df)

        self.assertListEqual([10.0, 10.5, 11.0, 11.5, 12.0, 12.5], list(transformed_df.collect()[1]['timestamp_new']))
        self.assertListEqual([0.5, 1.0, 1.5, 2.0, 2.5, 2.75], list(transformed_df.collect()[0]['signal2_new']))

if __name__ == '__main__':
    unittest.main()