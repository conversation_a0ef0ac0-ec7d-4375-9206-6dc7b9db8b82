{"cells": [{"cell_type": "code", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-05-05T10:02:53.631386Z", "start_time": "2025-05-05T10:02:39.880057Z"}}, "source": ["import sys\n", "# add here the location of the movano code movano-2022/src in your local\n", "aio_src_path = \"../../src\"\n", "if aio_src_path not in sys.path:\n", "    print(f\"Adding {aio_src_path} to sys.path\")\n", "    sys.path.append(aio_src_path)\n", "\n", "from pyspark.sql import SparkSession\n", "from pyspark.sql import functions as F\n", "import pyspark.sql.types as T\n", "import pandas as pd\n", "import numpy as np\n", "import etl\n", "\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "pd.options.display.max_columns = None"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Adding ../../src to sys.path\n"]}], "execution_count": 1}, {"cell_type": "code", "source": ["TIMEFRAMES = 250\n", "\n", "PAT_FILTER = {\n", "    'timeframes': TIMEFRAMES,\n", "    'std_threshold': [0, 1],\n", "    'mean_threshold': [0.2, 0.6],\n", "    'range_threshold': [0.05, 1],\n", "    'slope_threshold': [-1, 1]\n", "}\n", "\n", "HR_FILTER = {\n", "            'timeframes': TIMEFRAMES,\n", "            'std_threshold': [0, 30],\n", "            'mean_threshold': [40, 140],\n", "            'range_threshold': [40, 180],\n", "            'slope_threshold': [-1, 1]\n", "        }\n", "\n", "ABP_FILTER = {\n", "    'timeframes': TIMEFRAMES,\n", "    'std_threshold': [0, 30],\n", "    'mean_threshold': [40, 240],\n", "    'range_threshold': [40, 240],\n", "    'slope_threshold': [-9999, 9999]\n", "}\n", "\n", "ppg_params = {\n", "        'order': 2,\n", "        'cutoff_high': 3.0,\n", "        'cutoff_low': 0.9,\n", "    }\n", "\n", "ecg_params = {\n", "        'order': 1,\n", "        'cutoff_high': 14.0,\n", "        'cutoff_low': 0.1,\n", "    }"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-05-05T10:17:01.338639Z", "start_time": "2025-05-05T10:17:01.077291Z"}}, "outputs": [], "execution_count": 2}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-05T10:17:47.037944Z", "start_time": "2025-05-05T10:17:02.865539Z"}}, "cell_type": "code", "source": ["# Load step01 parquet\n", "spark_session = SparkSession.builder \\\n", "    .appName(\"LocalTestApp\") \\\n", "    .config(\"spark.sql.parquet.columnarReaderBatchSize\", \"1024\") \\\n", "    .config(\"spark.sql.parquet.enableVectorizedReader\", \"false\") \\\n", "    .config(\"spark.driver.memory\", \"10g\") \\\n", "    .config(\"spark.executor.memory\", \"10g\") \\\n", "    .master(\"local[*]\") \\\n", "    .getOrCreate()\n", "\n", "# Load into pandas\n", "path = \"20250317-ucsf06-step01.parquet\"\n", "df = pd.read_parquet(path, engine=\"pyarrow\")\n", "df = pd.DataFrame(df.iloc[[0]])\n", "df"], "outputs": [{"name": "stderr", "output_type": "stream", "text": ["bash: warning: setlocale: LC_ALL: cannot change locale (en_US.UTF-8)\n", "bash: warning: setlocale: LC_ALL: cannot change locale (en_US.UTF-8)\n", "25/05/06 12:19:32 WARN Utils: Your hostname, PC-SANTI resolves to a loopback address: *********; using ************** instead (on interface lo)\n", "25/05/06 12:19:32 WARN Utils: Set SPARK_LOCAL_IP if you need to bind to another address\n", "Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n", "25/05/06 12:19:33 WARN NativeCodeLoader: Unable to load native-hadoop library for your platform... using builtin-java classes where applicable\n"]}, {"data": {"text/plain": "       id                                                sbp  \\\n0  UCSF-1  [112.************03, 112.84564101636667, 112.8...   \n\n                                                 dbp  \\\n0  [68.59267392594697, 68.59253828362453, 68.5924...   \n\n                                                  ts  \\\n0  [1718099110962.0, 1718099110967.0, 17180991109...   \n\n                                                 abp  weight_lbs  age  \\\n0  [82.98486025, 80.9762755, 79.708954, 78.545250...         175   37   \n\n   height_inches     ref_sbp    ref_dbp  hypertension_present  \\\n0             72  106.904556  56.530522                     0   \n\n   diabetes_present  tobacco  gender_Female  gender_Male  \\\n0                 0        0              0            1   \n\n   race_American Indian or Alaska Native  race_Asian  \\\n0                                      0           0   \n\n   race_Black or African American  race_White  BMI  sampling_freq_hz  \\\n0                               0           1   24               200   \n\n                                          ppg_butter  \\\n0  [-0.6827849436773676, -0.6598611129009028, -0....   \n\n                                          ecg_butter  \\\n0  [1.1498656020719353, -0.026403458767841986, -0...   \n\n                                        ecg_enhanced  \\\n0  [0.08002757067067848, 0.020517579240281858, 0....   \n\n                                    ppg_butter_peaks  \\\n0  [42, 176, 276, 504, 636, 741, 959, 1185, 1431,...   \n\n                                  ppg_butter_valleys  \\\n0  [89, 231, 452, 564, 696, 913, 1138, 1386, 1602...   \n\n                                  ecg_enhanced_peaks  \\\n0  [425, 671, 885, 1114, 1360, 1579, 1800, 2038, ...   \n\n                                ecg_enhanced_valleys  \\\n0  [1, 210, 432, 677, 892, 1122, 1367, 1586, 1807...   \n\n                                                 pat  \\\n0  [nan, nan, nan, nan, nan, nan, nan, nan, nan, ...   \n\n                                         hr_from_ecg  \\\n0  [nan, nan, nan, nan, nan, nan, nan, nan, nan, ...   \n\n                                         hr_from_ppg  \n0  [nan, nan, nan, nan, nan, nan, nan, nan, nan, ...  ", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>id</th>\n      <th>sbp</th>\n      <th>dbp</th>\n      <th>ts</th>\n      <th>abp</th>\n      <th>weight_lbs</th>\n      <th>age</th>\n      <th>height_inches</th>\n      <th>ref_sbp</th>\n      <th>ref_dbp</th>\n      <th>hypertension_present</th>\n      <th>diabetes_present</th>\n      <th>tobacco</th>\n      <th>gender_Female</th>\n      <th>gender_Male</th>\n      <th>race_American Indian or Alaska Native</th>\n      <th>race_Asian</th>\n      <th>race_Black or African American</th>\n      <th>race_White</th>\n      <th>BMI</th>\n      <th>sampling_freq_hz</th>\n      <th>ppg_butter</th>\n      <th>ecg_butter</th>\n      <th>ecg_enhanced</th>\n      <th>ppg_butter_peaks</th>\n      <th>ppg_butter_valleys</th>\n      <th>ecg_enhanced_peaks</th>\n      <th>ecg_enhanced_valleys</th>\n      <th>pat</th>\n      <th>hr_from_ecg</th>\n      <th>hr_from_ppg</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>UCSF-1</td>\n      <td>[112.************03, 112.84564101636667, 112.8...</td>\n      <td>[68.59267392594697, 68.59253828362453, 68.5924...</td>\n      <td>[1718099110962.0, 1718099110967.0, 17180991109...</td>\n      <td>[82.98486025, 80.9762755, 79.708954, 78.545250...</td>\n      <td>175</td>\n      <td>37</td>\n      <td>72</td>\n      <td>106.904556</td>\n      <td>56.530522</td>\n      <td>0</td>\n      <td>0</td>\n      <td>0</td>\n      <td>0</td>\n      <td>1</td>\n      <td>0</td>\n      <td>0</td>\n      <td>0</td>\n      <td>1</td>\n      <td>24</td>\n      <td>200</td>\n      <td>[-0.6827849436773676, -0.6598611129009028, -0....</td>\n      <td>[1.1498656020719353, -0.026403458767841986, -0...</td>\n      <td>[0.08002757067067848, 0.020517579240281858, 0....</td>\n      <td>[42, 176, 276, 504, 636, 741, 959, 1185, 1431,...</td>\n      <td>[89, 231, 452, 564, 696, 913, 1138, 1386, 1602...</td>\n      <td>[425, 671, 885, 1114, 1360, 1579, 1800, 2038, ...</td>\n      <td>[1, 210, 432, 677, 892, 1122, 1367, 1586, 1807...</td>\n      <td>[nan, nan, nan, nan, nan, nan, nan, nan, nan, ...</td>\n      <td>[nan, nan, nan, nan, nan, nan, nan, nan, nan, ...</td>\n      <td>[nan, nan, nan, nan, nan, nan, nan, nan, nan, ...</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "execution_count": 3}, {"cell_type": "code", "execution_count": 4, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Finished checking for arrays.\n", "Loaded df to spark.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["25/05/06 12:19:37 WARN SparkStringUtils: Truncated the string representation of a plan since it was too large. This behavior can be adjusted by setting 'spark.sql.debug.maxToStringFields'.\n", "25/05/06 12:19:38 WARN TaskSetManager: Stage 2 contains a task of very large size (33784 KiB). The maximum recommended task size is 1000 KiB.\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+------------+---------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+\n", "|    id|                 sbp|                 dbp|                  ts|                 abp|weight_lbs|age|height_inches|     ref_sbp|  ref_dbp|hypertension_present|diabetes_present|tobacco|gender_Female|gender_Male|race_American Indian or Alaska Native|race_Asian|race_Black or African American|race_White|BMI|sampling_freq_hz|          ppg_butter|          ecg_butter|        ecg_enhanced|    ppg_butter_peaks|  ppg_butter_valleys|  ecg_enhanced_peaks|ecg_enhanced_valleys|                 pat|         hr_from_ecg|         hr_from_ppg|\n", "+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+------------+---------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+\n", "|UCSF-1|[112.************...|[68.5926739259469...|[1.718099110962E1...|[82.98486025, 80....|       175| 37|           72|106.90455625|56.530522|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[-0.6827849436773...|[1.14986560207193...|[0.08002757067067...|[42, 176, 276, 50...|[89, 231, 452, 56...|[425, 671, 885, 1...|[1, 210, 432, 677...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|\n", "+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+------------+---------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+\n", "\n"]}], "source": ["# Look what cols are np.arrays\n", "array_cols = [\n", "    col\n", "    for col in df.columns\n", "    if df[col].apply(lambda x: isinstance(x, np.ndarray)).any()\n", "]\n", "\n", "# Convert np.arrays into python pure lists\n", "for col in array_cols:\n", "    df[col] = df[col].apply(\n", "        lambda x: x.tolist() if isinstance(x, np.ndarray) else x\n", "    )\n", "\n", "# Check there are no more np.arrays\n", "for col in array_cols:\n", "    has_array = df[col].apply(lambda x: isinstance(x, np.ndarray)).any()\n", "    if has_array:\n", "        print(f\"{col} has arrays\")\n", "print(\"Finished checking for arrays.\")\n", "\n", "spark_df = spark_session.createDataFrame(df)\n", "\n", "print(\"Loaded df to spark.\")\n", "\n", "spark_df.show()"], "metadata": {"collapsed": false}}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-05T10:20:35.258496Z", "start_time": "2025-05-05T10:19:56.275923Z"}}, "cell_type": "code", "source": ["# Apply the PAT, HR and ABP filters\n", "multi_filter = etl.annotators.signals.filtering.MultiFilteringSignal() \\\n", "    .set_filter_sequence(['pat', 'hr', 'abp']) \\\n", "    .set_ts_col('ts') \\\n", "    .set_hr_col('hr_from_ppg') \\\n", "    .set_pat_col('pat') \\\n", "    .set_abp_col('sbp')\\\n", "    .set_indices_col('ppg_butter_valleys') \\\n", "    .set_pat_thresholds(PAT_FILTER) \\\n", "    .set_hr_thresholds(HR_FILTER) \\\n", "    .set_abp_thresholds(ABP_FILTER)\\\n", "    .set_segment_length(TIMEFRAMES) \\\n", "    .set_output_cols(['valid_cut_valleys'])\\\n", "    .fit().transform(spark_df)\n", "\n", "multi_filter.show()"], "outputs": [{"name": "stderr", "output_type": "stream", "text": ["25/05/06 12:19:43 WARN TaskSetManager: Stage 3 contains a task of very large size (33784 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/06 12:19:44 WARN TaskSetManager: Stage 6 contains a task of very large size (33784 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/06 12:19:46 WARN TaskSetManager: Stage 9 contains a task of very large size (33784 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/06 12:19:48 WARN TaskSetManager: Stage 12 contains a task of very large size (33784 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/06 12:19:50 WARN TaskSetManager: Stage 15 contains a task of very large size (33784 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/06 12:19:52 WARN TaskSetManager: Stage 18 contains a task of very large size (33784 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/06 12:19:55 WARN TaskSetManager: Stage 23 contains a task of very large size (33784 KiB). The maximum recommended task size is 1000 KiB.\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+------------+---------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+\n", "|    id|                 sbp|                 dbp|                  ts|                 abp|weight_lbs|age|height_inches|     ref_sbp|  ref_dbp|hypertension_present|diabetes_present|tobacco|gender_Female|gender_Male|race_American Indian or Alaska Native|race_Asian|race_Black or African American|race_White|BMI|sampling_freq_hz|          ppg_butter|          ecg_butter|        ecg_enhanced|    ppg_butter_peaks|  ppg_butter_valleys|  ecg_enhanced_peaks|ecg_enhanced_valleys|                 pat|         hr_from_ecg|         hr_from_ppg|       noise_segment|   valid_cut_valleys|       noise_segment|       noise_segment|\n", "+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+------------+---------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+\n", "|UCSF-1|[112.************...|[68.5926739259469...|[1.718099110962E1...|[82.98486025, 80....|       175| 37|           72|106.90455625|56.530522|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[-0.6827849436773...|[1.14986560207193...|[0.08002757067067...|[42, 176, 276, 50...|[89, 231, 452, 56...|[425, 671, 885, 1...|[1, 210, 432, 677...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[913, 14229, 1441...|[1386, 1826, 2274...|[146860, 160276, ...|[25666, 26125, 26...|\n", "+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+------------+---------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+\n", "\n"]}], "execution_count": 5}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-05T10:20:48.163346Z", "start_time": "2025-05-05T10:20:35.622128Z"}}, "cell_type": "code", "source": ["multi_filter = multi_filter.drop('noise_segment')"], "outputs": [], "execution_count": 6}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-05T10:21:32.909276Z", "start_time": "2025-05-05T10:21:23.531295Z"}}, "cell_type": "code", "source": ["# # EJECUTAR SOLO SI QUIERES QUE LOS ARRAYS DE LA ONDA SEAN MÁS PEQUEÑOS\n", "# def smaller_list(input_values):\n", "#     output_values = input_values[1500:2500]\n", "#     return output_values\n", "#\n", "# def valid_cut_valleys_first(input_values):\n", "#     output_values = [input_values[1]-1500]\n", "#     return output_values\n", "#\n", "# udf_func = F.udf(smaller_list, T.ArrayType(T.FloatType()))\n", "#\n", "# for col in ['sbp', 'dbp', 'ts', 'abp', 'ppg_butter', 'ecg_butter', 'ecg_enhanced', 'pat', 'hr_from_ecg', 'hr_from_ppg']:\n", "#     multi_filter = multi_filter.withColumn(col, udf_func(df[col]))\n", "#\n", "# udf_func = F.udf(smaller_list, T.ArrayType(T.IntegerType()))\n", "#\n", "# for col in ['ppg_butter_peaks', 'ppg_butter_valleys', 'ecg_enhanced_peaks', 'ecg_enhanced_valleys']:\n", "#     multi_filter = multi_filter.withColumn(col, udf_func(df[col]))\n", "#\n", "# udf_func = F.udf(valid_cut_valleys_first, T.<PERSON>yType(T.IntegerType()))\n", "# multi_filter = multi_filter.withColumn('valid_cut_valleys', udf_func(multi_filter['valid_cut_valleys']))\n", "#\n", "# multi_filter.show()"], "outputs": [], "execution_count": 7}, {"cell_type": "code", "source": ["# Dividing the signal in 10 second segments.\n", "exploder = etl.annotators.signals.rearrange.ExplodeSignal() \\\n", "    .set_input_cols(['ts', 'ppg_butter', 'ecg_butter', 'pat', 'hr_from_ecg', 'hr_from_ppg', 'sbp', 'dbp']) \\\n", "    .set_output_cols(\n", "    ['ts_episode', 'ppg_episode', 'ecg_episode', 'pat_episode', 'hr_from_ecg_episode', 'hr_from_ppg_episode',\n", "     'sbp_episode', 'dbp_episode']) \\\n", "    .set_cuts_col('valid_cut_valleys') \\\n", "    .set_split_method('defined_length') \\\n", "    .set_segment_length(TIMEFRAMES) \\\n", "    .fit().transform(multi_filter)\n", "\n", "exploder.show(1)"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-05-05T10:21:55.357169Z", "start_time": "2025-05-05T10:21:43.905462Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["25/05/06 12:20:00 WARN TaskSetManager: Stage 26 contains a task of very large size (33784 KiB). The maximum recommended task size is 1000 KiB.\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+------------+---------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+------------+-----+----+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+\n", "|    id|                 sbp|                 dbp|                  ts|                 abp|weight_lbs|age|height_inches|     ref_sbp|  ref_dbp|hypertension_present|diabetes_present|tobacco|gender_Female|gender_Male|race_American Indian or Alaska Native|race_Asian|race_Black or African American|race_White|BMI|sampling_freq_hz|          ppg_butter|          ecg_butter|        ecg_enhanced|    ppg_butter_peaks|  ppg_butter_valleys|  ecg_enhanced_peaks|ecg_enhanced_valleys|                 pat|         hr_from_ecg|         hr_from_ppg|   valid_cut_valleys|split_number|begin| end|          ts_episode|         ppg_episode|         ecg_episode|         pat_episode| hr_from_ecg_episode| hr_from_ppg_episode|         sbp_episode|         dbp_episode|\n", "+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+------------+---------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+------------+-----+----+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+\n", "|UCSF-1|[112.84551, 112.8...|[68.592674, 68.59...|[1.71809912E12, 1...|[82.98486025, 80....|       175| 37|           72|106.90455625|56.530522|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[-0.6827849, -0.6...|[1.1498656, -0.02...|[0.08002757067067...|[42, 176, 276, 50...|[89, 231, 452, 56...|[425, 671, 885, 1...|[1, 210, 432, 677...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[1386, 1826, 2274...|           0| 1136|1385|[1.71809912E12, 1...|[-0.81495804, -0....|[-0.18007196, -0....|[0.37, 0.37, 0.37...|[52.401745, 52.40...|[55.04587, 55.045...|[108.635086, 108....|[57.467373, 57.09...|\n", "|UCSF-1|[112.84551, 112.8...|[68.592674, 68.59...|[1.71809912E12, 1...|[82.98486025, 80....|       175| 37|           72|106.90455625|56.530522|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[-0.6827849, -0.6...|[1.1498656, -0.02...|[0.08002757067067...|[42, 176, 276, 50...|[89, 231, 452, 56...|[425, 671, 885, 1...|[1, 210, 432, 677...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[1386, 1826, 2274...|           1| 1576|1825|[1.71809912E12, 1...|[-0.49200967, -0....|[0.8268422, 0.994...|[0.355, 0.355, 0....|[48.780487, 48.78...|[48.780487, 48.78...|[104.99959, 105.3...|[61.66541, 61.040...|\n", "|UCSF-1|[112.84551, 112.8...|[68.592674, 68.59...|[1.71809912E12, 1...|[82.98486025, 80....|       175| 37|           72|106.90455625|56.530522|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[-0.6827849, -0.6...|[1.1498656, -0.02...|[0.08002757067067...|[42, 176, 276, 50...|[89, 231, 452, 56...|[425, 671, 885, 1...|[1, 210, 432, 677...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[1386, 1826, 2274...|           2| 2024|2273|[1.71809912E12, 1...|[-0.13517319, -0....|[-0.511237, -0.43...|[0.35, 0.35, 0.35...|[54.29864, 54.298...|[55.299538, 55.29...|[104.83926, 105.2...|[55.855167, 56.23...|\n", "|UCSF-1|[112.84551, 112.8...|[68.592674, 68.59...|[1.71809912E12, 1...|[82.98486025, 80....|       175| 37|           72|106.90455625|56.530522|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[-0.6827849, -0.6...|[1.1498656, -0.02...|[0.08002757067067...|[42, 176, 276, 50...|[89, 231, 452, 56...|[425, 671, 885, 1...|[1, 210, 432, 677...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[1386, 1826, 2274...|           3| 2477|2726|[1.71809912E12, 1...|[-0.57525593, -0....|[-1.087548, -0.99...|[0.375, 0.375, 0....|[55.555557, 55.55...|[55.299538, 55.29...|[108.40708, 108.2...|[58.02125, 58.146...|\n", "|UCSF-1|[112.84551, 112.8...|[68.592674, 68.59...|[1.71809912E12, 1...|[82.98486025, 80....|       175| 37|           72|106.90455625|56.530522|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[-0.6827849, -0.6...|[1.1498656, -0.02...|[0.08002757067067...|[42, 176, 276, 50...|[89, 231, 452, 56...|[425, 671, 885, 1...|[1, 210, 432, 677...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[1386, 1826, 2274...|           4| 2906|3155|[1.71809912E12, 1...|[-0.2578166, -0.2...|[-0.2037364, -0.1...|[0.355, 0.355, 0....|[50.209206, 50.20...|[50.209206, 50.20...|[109.22568, 109.1...|[60.217373, 60.09...|\n", "|UCSF-1|[112.84551, 112.8...|[68.592674, 68.59...|[1.71809912E12, 1...|[82.98486025, 80....|       175| 37|           72|106.90455625|56.530522|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[-0.6827849, -0.6...|[1.1498656, -0.02...|[0.08002757067067...|[42, 176, 276, 50...|[89, 231, 452, 56...|[425, 671, 885, 1...|[1, 210, 432, 677...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[1386, 1826, 2274...|           5| 3369|3618|[1.71809912E12, 1...|[-0.078653865, -0...|[-0.15192032, -0....|[0.36, 0.36, 0.36...|[54.79452, 54.794...|[55.555557, 55.55...|[103.39912, 103.2...|[59.34176, 59.216...|\n", "|UCSF-1|[112.84551, 112.8...|[68.592674, 68.59...|[1.71809912E12, 1...|[82.98486025, 80....|       175| 37|           72|106.90455625|56.530522|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[-0.6827849, -0.6...|[1.1498656, -0.02...|[0.08002757067067...|[42, 176, 276, 50...|[89, 231, 452, 56...|[425, 671, 885, 1...|[1, 210, 432, 677...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[1386, 1826, 2274...|           6| 3842|4091|[1.71809912E12, 1...|[-0.630031, -0.65...|[-0.9508005, -0.9...|[0.375, 0.375, 0....|[49.79253, 49.792...|[56.338028, 56.33...|[101.72629, 101.8...|[54.273167, 54.14...|\n", "|UCSF-1|[112.84551, 112.8...|[68.592674, 68.59...|[1.71809912E12, 1...|[82.98486025, 80....|       175| 37|           72|106.90455625|56.530522|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[-0.6827849, -0.6...|[1.1498656, -0.02...|[0.08002757067067...|[42, 176, 276, 50...|[89, 231, 452, 56...|[425, 671, 885, 1...|[1, 210, 432, 677...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[1386, 1826, 2274...|           7| 4295|4544|[1.71809912E12, 1...|[-0.8484281, -0.8...|[-0.079328984, -0...|[0.36, 0.36, 0.36...|[56.338028, 56.33...|[50.63291, 50.632...|[103.32998, 102.7...|[55.087315, 55.71...|\n", "|UCSF-1|[112.84551, 112.8...|[68.592674, 68.59...|[1.71809912E12, 1...|[82.98486025, 80....|       175| 37|           72|106.90455625|56.530522|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[-0.6827849, -0.6...|[1.1498656, -0.02...|[0.08002757067067...|[42, 176, 276, 50...|[89, 231, 452, 56...|[425, 671, 885, 1...|[1, 210, 432, 677...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[1386, 1826, 2274...|           8| 4732|4981|[1.71809912E12, 1...|[-0.015552366, -0...|[0.3296289, 0.281...|[0.36, 0.36, 0.36...|[49.382717, 49.38...|[49.79253, 49.792...|[97.21142, 97.586...|[55.62494, 55.249...|\n", "|UCSF-1|[112.84551, 112.8...|[68.592674, 68.59...|[1.71809912E12, 1...|[82.98486025, 80....|       175| 37|           72|106.90455625|56.530522|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[-0.6827849, -0.6...|[1.1498656, -0.02...|[0.08002757067067...|[42, 176, 276, 50...|[89, 231, 452, 56...|[425, 671, 885, 1...|[1, 210, 432, 677...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[1386, 1826, 2274...|           9| 4986|5235|[1.71809912E12, 1...|[-0.87488705, -0....|[-0.13446471, -0....|[0.365, 0.365, 0....|[57.142857, 57.14...|[52.863438, 52.86...|[103.03407, 102.4...|[55.844288, 55.21...|\n", "|UCSF-1|[112.84551, 112.8...|[68.592674, 68.59...|[1.71809912E12, 1...|[82.98486025, 80....|       175| 37|           72|106.90455625|56.530522|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[-0.6827849, -0.6...|[1.1498656, -0.02...|[0.08002757067067...|[42, 176, 276, 50...|[89, 231, 452, 56...|[425, 671, 885, 1...|[1, 210, 432, 677...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[1386, 1826, 2274...|          10| 5446|5695|[1.71809912E12, 1...|[0.060894962, 0.0...|[0.5332834, 0.851...|[0.35, 0.35, 0.35...|[47.058823, 47.05...|[47.61905, 47.619...|[99.043274, 98.91...|[58.04876, 58.173...|\n", "|UCSF-1|[112.84551, 112.8...|[68.592674, 68.59...|[1.71809912E12, 1...|[82.98486025, 80....|       175| 37|           72|106.90455625|56.530522|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[-0.6827849, -0.6...|[1.1498656, -0.02...|[0.08002757067067...|[42, 176, 276, 50...|[89, 231, 452, 56...|[425, 671, 885, 1...|[1, 210, 432, 677...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[1386, 1826, 2274...|          11| 5914|6163|[1.71809912E12, 1...|[-0.19600137, -0....|[0.25423342, 0.49...|[0.355, 0.355, 0....|[53.81166, 53.811...|[55.04587, 55.045...|[101.813354, 101....|[55.249252, 55.12...|\n", "|UCSF-1|[112.84551, 112.8...|[68.592674, 68.59...|[1.71809912E12, 1...|[82.98486025, 80....|       175| 37|           72|106.90455625|56.530522|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[-0.6827849, -0.6...|[1.1498656, -0.02...|[0.08002757067067...|[42, 176, 276, 50...|[89, 231, 452, 56...|[425, 671, 885, 1...|[1, 210, 432, 677...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[1386, 1826, 2274...|          12| 6367|6616|[1.71809912E12, 1...|[-0.6758741, -0.6...|[-0.75989777, -0....|[0.38, 0.38, 0.38...|[55.813953, 55.81...|[54.79452, 54.794...|[102.94004, 102.3...|[58.2314, 58.8564...|\n", "|UCSF-1|[112.84551, 112.8...|[68.592674, 68.59...|[1.71809912E12, 1...|[82.98486025, 80....|       175| 37|           72|106.90455625|56.530522|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[-0.6827849, -0.6...|[1.1498656, -0.02...|[0.08002757067067...|[42, 176, 276, 50...|[89, 231, 452, 56...|[425, 671, 885, 1...|[1, 210, 432, 677...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[1386, 1826, 2274...|          13| 6789|7038|[1.71809912E12, 1...|[-0.30958933, -0....|[0.015654955, -0....|[0.36, 0.36, 0.36...|[50.0, 50.0, 50.0...|[50.42017, 50.420...|[101.47345, 101.5...|[58.42085, 57.795...|\n", "|UCSF-1|[112.84551, 112.8...|[68.592674, 68.59...|[1.71809912E12, 1...|[82.98486025, 80....|       175| 37|           72|106.90455625|56.530522|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[-0.6827849, -0.6...|[1.1498656, -0.02...|[0.08002757067067...|[42, 176, 276, 50...|[89, 231, 452, 56...|[425, 671, 885, 1...|[1, 210, 432, 677...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[1386, 1826, 2274...|          14| 7249|7498|[1.71809912E12, 1...|[-0.078413725, -0...|[-0.010854426, -0...|[0.35, 0.35, 0.35...|[55.813953, 55.81...|[57.142857, 57.14...|[98.05669, 97.431...|[57.774467, 57.89...|\n", "|UCSF-1|[112.84551, 112.8...|[68.592674, 68.59...|[1.71809912E12, 1...|[82.98486025, 80....|       175| 37|           72|106.90455625|56.530522|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[-0.6827849, -0.6...|[1.1498656, -0.02...|[0.08002757067067...|[42, 176, 276, 50...|[89, 231, 452, 56...|[425, 671, 885, 1...|[1, 210, 432, 677...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[1386, 1826, 2274...|          15| 7703|7952|[1.71809912E12, 1...|[-0.6220078, -0.6...|[-1.2372376, -1.1...|[0.375, 0.375, 0....|[54.79452, 54.794...|[55.04587, 55.045...|[103.19571, 103.5...|[55.85264, 56.227...|\n", "|UCSF-1|[112.84551, 112.8...|[68.592674, 68.59...|[1.71809912E12, 1...|[82.98486025, 80....|       175| 37|           72|106.90455625|56.530522|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[-0.6827849, -0.6...|[1.1498656, -0.02...|[0.08002757067067...|[42, 176, 276, 50...|[89, 231, 452, 56...|[425, 671, 885, 1...|[1, 210, 432, 677...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[1386, 1826, 2274...|          16| 8127|8376|[1.71809912E12, 1...|[-0.0032903752, -...|[-0.12769496, 0.0...|[0.345, 0.345, 0....|[49.79253, 49.792...|[51.06383, 51.063...|[104.226974, 104....|[59.608315, 59.98...|\n", "|UCSF-1|[112.84551, 112.8...|[68.592674, 68.59...|[1.71809912E12, 1...|[82.98486025, 80....|       175| 37|           72|106.90455625|56.530522|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[-0.6827849, -0.6...|[1.1498656, -0.02...|[0.08002757067067...|[42, 176, 276, 50...|[89, 231, 452, 56...|[425, 671, 885, 1...|[1, 210, 432, 677...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[1386, 1826, 2274...|          17| 8603|8852|[1.71809912E12, 1...|[-0.32841668, -0....|[1.5297618, 0.089...|[0.38, 0.38, 0.38...|[48.780487, 48.78...|[57.971016, 57.97...|[102.88256, 102.5...|[56.801903, 57.42...|\n", "|UCSF-1|[112.84551, 112.8...|[68.592674, 68.59...|[1.71809912E12, 1...|[82.98486025, 80....|       175| 37|           72|106.90455625|56.530522|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[-0.6827849, -0.6...|[1.1498656, -0.02...|[0.08002757067067...|[42, 176, 276, 50...|[89, 231, 452, 56...|[425, 671, 885, 1...|[1, 210, 432, 677...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[1386, 1826, 2274...|          18| 9057|9306|[1.71809912E12, 1...|[-0.7097496, -0.7...|[-0.8154783, -0.6...|[0.375, 0.375, 0....|[54.79452, 54.794...|[51.502148, 51.50...|[101.962265, 101....|[55.632225, 56.25...|\n", "|UCSF-1|[112.84551, 112.8...|[68.592674, 68.59...|[1.71809912E12, 1...|[82.98486025, 80....|       175| 37|           72|106.90455625|56.530522|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[-0.6827849, -0.6...|[1.1498656, -0.02...|[0.08002757067067...|[42, 176, 276, 50...|[89, 231, 452, 56...|[425, 671, 885, 1...|[1, 210, 432, 677...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[NaN, NaN, NaN, N...|[1386, 1826, 2274...|          19| 9490|9739|[1.71809912E12, 1...|[-0.37849623, -0....|[0.19755821, 0.36...|[0.36, 0.36, 0.36...|[50.0, 50.0, 50.0...|[49.586777, 49.58...|[102.12236, 102.7...|[58.799225, 58.67...|\n", "+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+------------+---------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+------------+-----+----+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+\n", "only showing top 20 rows\n", "\n"]}], "execution_count": 8}, {"cell_type": "code", "source": ["# Dropping empty rows\n", "drop_all_nans = etl.annotators.dataprep.missing.DropNAs()\\\n", "    .set_mode('any')\\\n", "    .fit().transform(exploder)\n", "\n", "# Summarize sbp and dbp for validating prediction results.\n", "sbp_real = etl.annotators.features.fromsignals.FeaturizeSignal() \\\n", "    .set_input_cols(['sbp_episode']) \\\n", "    .set_output_cols(['sbp']) \\\n", "    .set_modes([\"last\"]) \\\n", "    .fit().transform(drop_all_nans)\n", "\n", "dbp_real = etl.annotators.features.fromsignals.FeaturizeSignal() \\\n", "    .set_input_cols(['dbp_episode']) \\\n", "    .set_output_cols(['dbp']) \\\n", "    .set_modes([\"last\"]) \\\n", "    .fit().transform(sbp_real)\n", "\n", "dbp_real.show(1)"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-05-05T10:26:03.777683Z", "start_time": "2025-05-05T10:25:52.820781Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["25/05/06 12:20:41 WARN TaskSetManager: Stage 29 contains a task of very large size (33784 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/06 12:20:47 ERROR Utils: Uncaught exception in thread stdout writer for python3\n", "java.lang.OutOfMemoryError: Java heap space\n", "\tat org.apache.spark.sql.catalyst.expressions.UnsafeRow.copy(UnsafeRow.java:469)\n", "\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage11.generate_doConsume_0$(Unknown Source)\n", "\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage11.processNext(Unknown Source)\n", "\tat org.apache.spark.sql.execution.BufferedRowIterator.hasNext(BufferedRowIterator.java:43)\n", "\tat org.apache.spark.sql.execution.WholeStageCodegenEvaluatorFactory$WholeStageCodegenPartitionEvaluator$$anon$1.hasNext(WholeStageCodegenEvaluatorFactory.scala:43)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.ContextAwareIterator.hasNext(ContextAwareIterator.scala:39)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$GroupedIterator.fill(Iterator.scala:1211)\n", "\tat scala.collection.Iterator$GroupedIterator.hasNext(Iterator.scala:1217)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator.foreach(Iterator.scala:943)\n", "\tat scala.collection.Iterator.foreach$(Iterator.scala:943)\n", "\tat scala.collection.AbstractIterator.foreach(Iterator.scala:1431)\n", "\tat org.apache.spark.api.python.PythonRDD$.writeIteratorToStream(PythonRDD.scala:322)\n", "\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$PythonUDFWriterThread.writeIteratorToStream(PythonUDFRunner.scala:58)\n", "\tat org.apache.spark.api.python.BasePythonRunner$WriterThread.$anonfun$run$1(PythonRunner.scala:451)\n", "\tat org.apache.spark.api.python.BasePythonRunner$WriterThread$$Lambda$2357/0x00007fa984bf2280.apply(Unknown Source)\n", "\tat org.apache.spark.util.Utils$.logUncaughtExceptions(Utils.scala:1928)\n", "\tat org.apache.spark.api.python.BasePythonRunner$WriterThread.run(PythonRunner.scala:282)\n", "Exception in thread \"stdout writer for python3\" java.lang.OutOfMemoryError: Java heap space\n", "\tat org.apache.spark.sql.catalyst.expressions.UnsafeRow.copy(UnsafeRow.java:469)\n", "\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage11.generate_doConsume_0$(Unknown Source)\n", "\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage11.processNext(Unknown Source)\n", "\tat org.apache.spark.sql.execution.BufferedRowIterator.hasNext(BufferedRowIterator.java:43)\n", "\tat org.apache.spark.sql.execution.WholeStageCodegenEvaluatorFactory$WholeStageCodegenPartitionEvaluator$$anon$1.hasNext(WholeStageCodegenEvaluatorFactory.scala:43)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.ContextAwareIterator.hasNext(ContextAwareIterator.scala:39)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$GroupedIterator.fill(Iterator.scala:1211)\n", "\tat scala.collection.Iterator$GroupedIterator.hasNext(Iterator.scala:1217)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator.foreach(Iterator.scala:943)\n", "\tat scala.collection.Iterator.foreach$(Iterator.scala:943)\n", "\tat scala.collection.AbstractIterator.foreach(Iterator.scala:1431)\n", "\tat org.apache.spark.api.python.PythonRDD$.writeIteratorToStream(PythonRDD.scala:322)\n", "\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$PythonUDFWriterThread.writeIteratorToStream(PythonUDFRunner.scala:58)\n", "\tat org.apache.spark.api.python.BasePythonRunner$WriterThread.$anonfun$run$1(PythonRunner.scala:451)\n", "\tat org.apache.spark.api.python.BasePythonRunner$WriterThread$$Lambda$2357/0x00007fa984bf2280.apply(Unknown Source)\n", "\tat org.apache.spark.util.Utils$.logUncaughtExceptions(Utils.scala:1928)\n", "\tat org.apache.spark.api.python.BasePythonRunner$WriterThread.run(PythonRunner.scala:282)\n", "ERROR:root:KeyboardInterrupt while sending command.\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/.virtualenvs/aiolib/lib/python3.10/site-packages/py4j/java_gateway.py\", line 1038, in send_command\n", "    response = connection.send_command(command)\n", "  File \"/home/<USER>/.virtualenvs/aiolib/lib/python3.10/site-packages/py4j/clientserver.py\", line 511, in send_command\n", "    answer = smart_decode(self.stream.readline()[:-1])\n", "  File \"/usr/lib/python3.10/socket.py\", line 705, in readinto\n", "    return self._sock.recv_into(b)\n", "KeyboardInterrupt\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[9], line 19\u001b[0m\n\u001b[1;32m      7\u001b[0m sbp_real \u001b[38;5;241m=\u001b[39m etl\u001b[38;5;241m.\u001b[39mannotators\u001b[38;5;241m.\u001b[39mfeatures\u001b[38;5;241m.\u001b[39mfromsignals\u001b[38;5;241m.\u001b[39mFeaturizeSignal() \\\n\u001b[1;32m      8\u001b[0m     \u001b[38;5;241m.\u001b[39mset_input_cols([\u001b[38;5;124m'\u001b[39m\u001b[38;5;124msbp_episode\u001b[39m\u001b[38;5;124m'\u001b[39m]) \\\n\u001b[1;32m      9\u001b[0m     \u001b[38;5;241m.\u001b[39mset_output_cols([\u001b[38;5;124m'\u001b[39m\u001b[38;5;124msbp\u001b[39m\u001b[38;5;124m'\u001b[39m]) \\\n\u001b[1;32m     10\u001b[0m     \u001b[38;5;241m.\u001b[39mset_modes([\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlast\u001b[39m\u001b[38;5;124m\"\u001b[39m]) \\\n\u001b[1;32m     11\u001b[0m     \u001b[38;5;241m.\u001b[39mfit()\u001b[38;5;241m.\u001b[39mtransform(drop_all_nans)\n\u001b[1;32m     13\u001b[0m dbp_real \u001b[38;5;241m=\u001b[39m etl\u001b[38;5;241m.\u001b[39mannotators\u001b[38;5;241m.\u001b[39mfeatures\u001b[38;5;241m.\u001b[39mfromsignals\u001b[38;5;241m.\u001b[39mFeaturizeSignal() \\\n\u001b[1;32m     14\u001b[0m     \u001b[38;5;241m.\u001b[39mset_input_cols([\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdbp_episode\u001b[39m\u001b[38;5;124m'\u001b[39m]) \\\n\u001b[1;32m     15\u001b[0m     \u001b[38;5;241m.\u001b[39mset_output_cols([\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdbp\u001b[39m\u001b[38;5;124m'\u001b[39m]) \\\n\u001b[1;32m     16\u001b[0m     \u001b[38;5;241m.\u001b[39mset_modes([\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlast\u001b[39m\u001b[38;5;124m\"\u001b[39m]) \\\n\u001b[1;32m     17\u001b[0m     \u001b[38;5;241m.\u001b[39mfit()\u001b[38;5;241m.\u001b[39mtransform(sbp_real)\n\u001b[0;32m---> 19\u001b[0m \u001b[43mdbp_real\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mshow\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.virtualenvs/aiolib/lib/python3.10/site-packages/pyspark/sql/dataframe.py:947\u001b[0m, in \u001b[0;36mDataFrame.show\u001b[0;34m(self, n, truncate, vertical)\u001b[0m\n\u001b[1;32m    887\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mshow\u001b[39m(\u001b[38;5;28mself\u001b[39m, n: \u001b[38;5;28mint\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m20\u001b[39m, truncate: Union[\u001b[38;5;28mbool\u001b[39m, \u001b[38;5;28mint\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m, vertical: \u001b[38;5;28mbool\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mFalse\u001b[39;00m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n\u001b[1;32m    888\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Prints the first ``n`` rows to the console.\u001b[39;00m\n\u001b[1;32m    889\u001b[0m \n\u001b[1;32m    890\u001b[0m \u001b[38;5;124;03m    .. versionadded:: 1.3.0\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    945\u001b[0m \u001b[38;5;124;03m    name | Bob\u001b[39;00m\n\u001b[1;32m    946\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 947\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_show_string\u001b[49m\u001b[43m(\u001b[49m\u001b[43mn\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtruncate\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvertical\u001b[49m\u001b[43m)\u001b[49m)\n", "File \u001b[0;32m~/.virtualenvs/aiolib/lib/python3.10/site-packages/pyspark/sql/dataframe.py:965\u001b[0m, in \u001b[0;36mDataFrame._show_string\u001b[0;34m(self, n, truncate, vertical)\u001b[0m\n\u001b[1;32m    959\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m PySparkTypeError(\n\u001b[1;32m    960\u001b[0m         error_class\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNOT_BOOL\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m    961\u001b[0m         message_parameters\u001b[38;5;241m=\u001b[39m{\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124marg_name\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mvertical\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124marg_type\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28mtype\u001b[39m(vertical)\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m},\n\u001b[1;32m    962\u001b[0m     )\n\u001b[1;32m    964\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(truncate, \u001b[38;5;28mbool\u001b[39m) \u001b[38;5;129;01mand\u001b[39;00m truncate:\n\u001b[0;32m--> 965\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_jdf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mshowString\u001b[49m\u001b[43m(\u001b[49m\u001b[43mn\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m20\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvertical\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    966\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    967\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n", "File \u001b[0;32m~/.virtualenvs/aiolib/lib/python3.10/site-packages/py4j/java_gateway.py:1321\u001b[0m, in \u001b[0;36mJavaMember.__call__\u001b[0;34m(self, *args)\u001b[0m\n\u001b[1;32m   1314\u001b[0m args_command, temp_args \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_build_args(\u001b[38;5;241m*\u001b[39margs)\n\u001b[1;32m   1316\u001b[0m command \u001b[38;5;241m=\u001b[39m proto\u001b[38;5;241m.\u001b[39mCALL_COMMAND_NAME \u001b[38;5;241m+\u001b[39m\\\n\u001b[1;32m   1317\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcommand_header \u001b[38;5;241m+\u001b[39m\\\n\u001b[1;32m   1318\u001b[0m     args_command \u001b[38;5;241m+\u001b[39m\\\n\u001b[1;32m   1319\u001b[0m     proto\u001b[38;5;241m.\u001b[39mEND_COMMAND_PART\n\u001b[0;32m-> 1321\u001b[0m answer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgateway_client\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend_command\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcommand\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1322\u001b[0m return_value \u001b[38;5;241m=\u001b[39m get_return_value(\n\u001b[1;32m   1323\u001b[0m     answer, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mgateway_client, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtarget_id, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mname)\n\u001b[1;32m   1325\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m temp_arg \u001b[38;5;129;01min\u001b[39;00m temp_args:\n", "File \u001b[0;32m~/.virtualenvs/aiolib/lib/python3.10/site-packages/py4j/java_gateway.py:1038\u001b[0m, in \u001b[0;36mGatewayClient.send_command\u001b[0;34m(self, command, retry, binary)\u001b[0m\n\u001b[1;32m   1036\u001b[0m connection \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_connection()\n\u001b[1;32m   1037\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1038\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[43mconnection\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend_command\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcommand\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1039\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m binary:\n\u001b[1;32m   1040\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m response, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_create_connection_guard(connection)\n", "File \u001b[0;32m~/.virtualenvs/aiolib/lib/python3.10/site-packages/py4j/clientserver.py:511\u001b[0m, in \u001b[0;36mClientServerConnection.send_command\u001b[0;34m(self, command)\u001b[0m\n\u001b[1;32m    509\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    510\u001b[0m     \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[0;32m--> 511\u001b[0m         answer \u001b[38;5;241m=\u001b[39m smart_decode(\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mstream\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mreadline\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m[:\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m])\n\u001b[1;32m    512\u001b[0m         logger\u001b[38;5;241m.\u001b[39mdebug(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m<PERSON>ns<PERSON> received: \u001b[39m\u001b[38;5;132;01m{0}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mformat(answer))\n\u001b[1;32m    513\u001b[0m         \u001b[38;5;66;03m# Happens when a the other end is dead. There might be an empty\u001b[39;00m\n\u001b[1;32m    514\u001b[0m         \u001b[38;5;66;03m# answer before the socket raises an error.\u001b[39;00m\n", "File \u001b[0;32m/usr/lib/python3.10/socket.py:705\u001b[0m, in \u001b[0;36mSocketIO.readinto\u001b[0;34m(self, b)\u001b[0m\n\u001b[1;32m    703\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[1;32m    704\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 705\u001b[0m         \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_sock\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrecv_into\u001b[49m\u001b[43m(\u001b[49m\u001b[43mb\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    706\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m timeout:\n\u001b[1;32m    707\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_timeout_occurred \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "execution_count": 9}, {"cell_type": "code", "source": ["# Round the values of some signals and recast\n", "round_signals = etl.annotators.dataprep.basic.Round() \\\n", "    .set_input_cols(['hr_from_ecg_episode', 'hr_from_ppg_episode', 'ts_episode', 'sbp_episode', 'dbp_episode']) \\\n", "    .set_output_cols(['hr_from_ecg_episode', 'hr_from_ppg_episode', 'ts_episode', 'sbp_episode', 'dbp_episode']) \\\n", "    .set_decimals(0)\\\n", "    .fit().transform(dbp_real)\n", "\n", "# Calculate PAT/squared height ratio.\n", "pat_ratio_calc = etl.annotators.dataprep.basic.ApplyFunc() \\\n", "    .set_function(lambda x, y: [i / (x ** 2) if i is not None else None for i in y]) \\\n", "    .set_output_type(T.ArrayType(T.FloatType())) \\\n", "    .set_input_cols(['height_inches', 'pat_episode']) \\\n", "    .set_output_cols(['pat_ratio']) \\\n", "    .fit().transform(round_signals)\n", "\n", "# Get reference pulse pressure\n", "pp_calc = etl.annotators.features.fromvalues.CalculatePulsePressure() \\\n", "    .set_sbp_col(\"ref_sbp\") \\\n", "    .set_dbp_col(\"ref_dbp\") \\\n", "    .set_output_cols(['ref_pp'])\\\n", "    .fit().transform(pat_ratio_calc)\n", "\n", "pp_calc.show(1)"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-05-05T10:35:04.299549Z", "start_time": "2025-05-05T10:34:38.777138Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": ["# Standardize ppg and ecg before extracting features\n", "minmax_wave_scaler = etl.annotators.signals.standardization.StandardizeSignal() \\\n", "    .set_input_cols(['ppg_episode', 'ecg_episode']) \\\n", "    .set_output_cols(['ppg_episode', 'ecg_episode']) \\\n", "    .set_mode('by_row') \\\n", "    .set_minmax(True) \\\n", "    .fit().transform(pp_calc)\n", "\n", "# Summarize signals (extract features from segments)\n", "summarize_signals = etl.annotators.features.fromsignals.FeaturizeSignal() \\\n", "    .set_input_cols(\n", "    ['ppg_episode', 'ecg_episode', 'pat_ratio', 'pat_episode', 'hr_from_ecg_episode', 'hr_from_ppg_episode']) \\\n", "    .set_modes(['min', 'max', 'mean', 'median', 'std', 'sum', 'trend']) \\\n", "    .fit().transform(minmax_wave_scaler)\n", "\n", "summarize_signals.show(1)"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-05-05T10:39:44.822747Z", "start_time": "2025-05-05T10:39:15.588027Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": ["# Find segment valleys to cut a three-beat segment\n", "beat_indices = etl.annotators.signals.characterization.RobustPeakFinder()\\\n", "    .set_ppg_col('ppg_episode')\\\n", "    .fit().transform(summarize_signals)\n", "\n", "# Extract the last three beats from each episode\n", "beat_extract = etl.annotators.signals.rearrange.SignalBeatExtractor() \\\n", "    .set_signal_col('ppg_episode') \\\n", "    .set_timestamp_col('ts_episode') \\\n", "    .set_output_cols(['last_three_beats', 'ts_last_three_beats']) \\\n", "    .set_indices_col('ppg_episode_valleys') \\\n", "    .set_how_many_segments(3)\\\n", "    .fit().transform(beat_indices)\n", "\n", "beat_extract.show(1)"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-05-05T10:45:31.747178Z", "start_time": "2025-05-05T10:45:04.301675Z"}}, "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# Compare to pandas parquet\n", "path = \"20250317-ucsf06-processed.parquet\"\n", "df = pd.read_parquet(path, engine=\"pyarrow\")\n", "\n", "# Look what cols are np.arrays\n", "array_cols = [\n", "    col\n", "    for col in df.columns\n", "    if df[col].apply(lambda x: isinstance(x, np.ndarray)).any()\n", "]\n", "\n", "# Convert np.arrays into python pure lists\n", "for col in array_cols:\n", "    df[col] = df[col].apply(\n", "        lambda x: x.tolist() if isinstance(x, np.ndarray) else x\n", "    )\n", "\n", "# Check there are no more np.arrays\n", "for col in array_cols:\n", "    has_array = df[col].apply(lambda x: isinstance(x, np.ndarray)).any()\n", "    if has_array:\n", "        print(f\"{col} has arrays\")\n", "print(\"Finished checking.\")\n", "df = df.iloc[[0]]"]}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["def first_two(val):\n", "    # treat Python lists by slicing; leave everything else alone\n", "    if isinstance(val, list):\n", "        return val[:2]\n", "    return val\n", "\n", "# iterate and print\n", "exception_cols_pandas = []\n", "exception_cols_spark = []\n", "difference_cols = {}\n", "for col in df.columns:\n", "    # fetch Spark value\n", "    try:\n", "        spark_val = beat_extract.first()[col]\n", "    except Exception as e:\n", "        exception_cols_spark.append(col)\n", "        continue\n", "\n", "    # fetch pandas value\n", "    try:\n", "        pandas_val = df.at[0, col]\n", "    except KeyError as e:\n", "        exception_cols_pandas.append(col)\n", "        continue\n", "\n", "    # truncate lists\n", "    s = first_two(spark_val)\n", "    p = first_two(pandas_val)\n", "\n", "    # only print if different\n", "    if s != p:\n", "        difference_cols[col] = {'pandas': p, 'spark': s}\n", "\n", "print(f\"Exception cols found at spark df: {exception_cols_spark}\")\n", "print(f\"Exception cols found at pandas df: {exception_cols_pandas}\")\n", "print(f\"Missmatch found at: {difference_cols}\")"], "metadata": {"collapsed": false}}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 0}