{"cells": [{"cell_type": "code", "execution_count": 58, "metadata": {"collapsed": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["import sys\n", "aio_src_path = \"../src\"\n", "if aio_src_path not in sys.path:\n", "    print(f\"Adding {aio_src_path} to sys.path\")\n", "    sys.path.append(aio_src_path)\n", "\n", "import etl\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "pd.options.display.max_columns = None\n", "\n", "from pyspark.sql import SparkSession\n", "from pyspark.sql import functions as F\n", "from pyspark.sql import types as T"]}, {"cell_type": "code", "execution_count": 59, "outputs": [], "source": ["TIMEFRAMES = 2500\n", "SOURCE_PPG = 'ydata'\n", "unused_signals = [i for i in ['ydata', 'lr', 'li', 'lg', 'mag_0', 'mag_1', 'mag_2', 'mag_3'] if i != SOURCE_PPG]\n", "\n", "ppg_params = {\n", "        'order': 2,\n", "        'cutoff_high': 3.0,\n", "        'cutoff_low': 0.9,\n", "    }\n", "\n", "ecg_params = {\n", "        'order': 1,\n", "        'cutoff_high': 14.0,\n", "        'cutoff_low': 0.1,\n", "    }"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 60, "outputs": [], "source": ["spark_session = SparkSession.builder \\\n", "    .appName(\"LocalTestApp\") \\\n", "    .config(\"spark.sql.parquet.columnarReaderBatchSize\", \"1024\") \\\n", "    .config(\"spark.sql.parquet.enableVectorizedReader\", \"false\") \\\n", "    .config(\"spark.driver.memory\", \"4g\") \\\n", "    .config(\"spark.executor.memory\", \"8g\") \\\n", "    .master(\"local[*]\") \\\n", "    .getOrCreate()"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 61, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["+------+\n", "|    id|\n", "+------+\n", "|UCSF-1|\n", "+------+\n", "\n", "[112.84551520892103, 112.84561585508266, 112.8457165002187]\n"]}], "source": ["load_pqt = etl.annotators.extract.local.ExtractParquet(spark_session=spark_session) \\\n", "    .set_path(\"./ucsf20240724-demo-signal-raw-aligned.parquet\") \\\n", "    .set_single_file(True) \\\n", "    .set_input_cols(['id', 'sbp', 'dbp', 'ts', 'lr', 'li', 'lg',\n", "                     'ecg', 'mag_0', 'mag_1', 'mag_2', 'mag_3', 'abp', 'ydata',\n", "                     'weight_lbs', 'age', 'height_inches', 'ref_sbp', 'ref_dbp',\n", "                     'Gender', 'Hypertension', 'Presence_Of_Diabetes',\n", "                     'Race_Ethnicity', 'Tobacco_Use']) \\\n", "    .fit().transform()\n", "\n", "load_pqt = load_pqt.limit(1)\n", "load_pqt.select('id').show()\n", "print(load_pqt.select('sbp').first()['sbp'][:3])"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 62, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Row(hypertension_present=0)\n"]}], "source": ["# One hot encoding of demographics\n", "ucsf_demo_encode = etl.annotators.features.manipulation.FeatureValuesMap() \\\n", "    .set_input_cols(['Hypertension', 'Presence_Of_Diabetes', 'Tobacco_Use']) \\\n", "    .set_output_cols(['hypertension_present', 'diabetes_present', 'tobacco']) \\\n", "    .set_maps({'No': 0, 'Yes': 1}) \\\n", "    .fit().transform(load_pqt)\n", "\n", "print(ucsf_demo_encode.select('hypertension_present').first())"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 63, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Row(gender_Male=1)\n"]}], "source": ["# Get dummies for gender\n", "demo_gender_dummies = etl.annotators.features.manipulation.GetDummies()\\\n", "    .set_input_cols(['Gender'])\\\n", "    .set_prefix('gender_') \\\n", "    .fit().transform(ucsf_demo_encode)\n", "\n", "print(demo_gender_dummies.select('gender_Male').first())"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 64, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Row(race_White=1)\n"]}], "source": ["# And for ethnicity\n", "demo_race_dummies = etl.annotators.features.manipulation.GetDummies()\\\n", "    .set_input_cols(['Race_Ethnicity'])\\\n", "    .set_prefix('race_') \\\n", "    .fit().transform(demo_gender_dummies)\n", "\n", "print(demo_race_dummies.select('race_White').first())"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 65, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["+-----+-------------+----------+-----------+----------+--------------------+\n", "|  BMI|height_inches|weight_lbs|gender_Male|race_White|hypertension_present|\n", "+-----+-------------+----------+-----------+----------+--------------------+\n", "|23.73|           72|       175|          1|         1|                   0|\n", "+-----+-------------+----------+-----------+----------+--------------------+\n", "\n"]}], "source": ["# Calculate BMI\n", "demo_BMI = etl.annotators.dataprep.basic.ApplyFunc()\\\n", "    .set_input_cols(['height_inches', 'weight_lbs'])\\\n", "    .set_output_cols([\"BMI\"])\\\n", "    .set_function(lambda h, w: round((w / (h ** 2) * 703), 2))\\\n", "    .set_output_type(T.DoubleType())\\\n", "    .fit().transform(demo_race_dummies)\n", "\n", "demo_BMI.select(['BMI', 'height_inches', 'weight_lbs', 'gender_Male', 'race_White', 'hypertension_present']).show()"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 67, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[-0.3208439463935521, -0.28727836511086785, -0.25497136574933976, -0.22462222415287353, -0.19677885242338505, -0.17185089200629416, -0.15014326503469771, -0.1318090412946424, -0.11692429760302774, -0.10537011014156608]\n"]}], "source": ["# Choose the PPG source\n", "ucsf_select_source = etl.annotators.dataprep.basic.RenameColumns() \\\n", "    .set_input_cols([SOURCE_PPG]) \\\n", "    .set_output_cols([\"ppg\"]) \\\n", "    .fit().transform(demo_BMI)\n", "\n", "print(ucsf_select_source.select('ppg').first()['ppg'][:10])"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 68, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1718099110962.0, 1718099110967.0, 1718099110972.0, 1718099110977.0, 1718099110982.0, 1718099110987.0, 1718099110992.0, 1718099110997.0, 1718099111002.0, 1718099111007.0]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["# Resample at 200 Hz so that all merged data has the same sampling frequency\n", "resample_signals = etl.annotators.signals.rearrange.ResampleAlignedSignals() \\\n", "    .set_input_cols(['ppg', 'ecg', 'sbp', 'dbp']) \\\n", "    .set_output_cols(['ppg', 'ecg', 'sbp', 'dbp']) \\\n", "    .set_timestamp_col('ts') \\\n", "    .set_original_time_unit('ms') \\\n", "    .set_new_timestamp_col('ts_resampled') \\\n", "    .set_target_sampling_frequency(200) \\\n", "    .fit().transform(ucsf_select_source)\n", "\n", "print(resample_signals.select('ts_resampled').first()['ts_resampled'][:10])"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 69, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["+----------------+\n", "|sampling_freq_hz|\n", "+----------------+\n", "|             200|\n", "+----------------+\n", "\n"]}], "source": ["# Get sampling frequency (250 originally)\n", "get_hz = etl.annotators.signals.characterization.GetSamplingHz() \\\n", "    .set_input_cols(['ts_resampled']) \\\n", "    .fit().transform(resample_signals)\n", "\n", "get_hz.select('sampling_freq_hz').show()"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 72, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["[-0.3208439463935521, -0.2792016152704858, -0.23979679495110665, -0.20373969535575717, -0.17185089200629416, -0.14555970909968388, -0.12436666944883507, -0.10825865700693149, -0.09682347182743128, -0.0900035636850561]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Stage 228:====================================================>  (18 + 1) / 19]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["[-0.682784914970398, -0.6598610877990723, -0.6339375972747803, -0.604981005191803, -0.5729700326919556, -0.5378960967063904, -0.4997640550136566, -0.4585933983325958, -0.41441893577575684, -0.3672920763492584]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["# Butter the waves.\n", "butter_ppg = etl.annotators.signals.filtering.ButterworthFilter() \\\n", "    .set_input_cols([\"ppg\"]) \\\n", "    .set_output_cols([\"ppg_butter\"]) \\\n", "    .set_sampling_freq_col('sampling_freq_hz') \\\n", "    .set_order(ppg_params['order']) \\\n", "    .set_cutoff_low(ppg_params['cutoff_low']) \\\n", "    .set_cutoff_high(ppg_params['cutoff_high']) \\\n", "    .set_handlenan('zero') \\\n", "    .fit().transform(get_hz)\n", "\n", "print(butter_ppg.select('ppg').first()['ppg'][:10])\n", "print(butter_ppg.select('ppg_butter').first()['ppg_butter'][:10])"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 73, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["[286910.0, 247041.75, 265240.0, 289837.25, 251515.0, 250760.0, 285879.0, 266687.5, 241091.0, 275247.5]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Stage 234:====================================================>  (18 + 1) / 19]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["[6280.84814453125, -1659.45068359375, -5993.0791015625, -8710.5751953125, -11883.8955078125, -13438.765625, -13460.4521484375, -14569.2578125, -15509.0078125, -14607.50390625]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["butter_ecg = etl.annotators.signals.filtering.ButterworthFilter() \\\n", "    .set_input_cols([\"ecg\"]) \\\n", "    .set_output_cols([\"ecg_butter\"]) \\\n", "    .set_sampling_freq_col('sampling_freq_hz') \\\n", "    .set_order(ecg_params['order']) \\\n", "    .set_cutoff_low(ecg_params['cutoff_low']) \\\n", "    .set_cutoff_high(ecg_params['cutoff_high']) \\\n", "    .set_handlenan('zero') \\\n", "    .fit().transform(butter_ppg)\n", "\n", "print(butter_ecg.select('ecg').first()['ecg'][:10])\n", "print(butter_ecg.select('ecg_butter').first()['ecg_butter'][:10])"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 74, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 237:====================================================>  (18 + 1) / 19]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["[1.1498656272888184, -0.026403458788990974, -0.6683834791183472, -1.070950984954834, -1.5410438776016235, -1.7713810205459595, -1.774593710899353, -1.9388511180877686, -2.0780649185180664, -1.944516897201538]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["# Robust std and crop before HeartPy convolving.\n", "robust_ecg = etl.annotators.signals.standardization.StandardizeSignal() \\\n", "    .set_input_cols([\"ecg_butter\"]) \\\n", "    .set_output_cols([\"ecg_butter\"]) \\\n", "    .set_mode('by_row') \\\n", "    .set_robust(True) \\\n", "    .fit().transform(butter_ecg)\n", "\n", "print(robust_ecg.select('ecg_butter').first()['ecg_butter'][:10])"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 75, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 240:====================================================>  (18 + 1) / 19]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["[1.1498656272888184, -0.026403458788990974, -0.6683834791183472, -1.070950984954834, -1.5410438776016235, -1.7713810205459595, -1.774593710899353, -1.9388511180877686, -2.0780649185180664, -1.944516897201538]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["crop_ecg = etl.annotators.signals.filtering.LimitStandardizedSignal() \\\n", "    .set_input_cols([\"ecg_butter\"]) \\\n", "    .set_output_cols([\"ecg_butter\"]) \\\n", "    .set_lower_limit(-10) \\\n", "    .set_upper_limit(10) \\\n", "    .fit().transform(robust_ecg)\n", "\n", "print(crop_ecg.select('ecg_butter').first()['ecg_butter'][:10])"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 76, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 243:====================================================>  (18 + 1) / 19]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["[0.08002757281064987, 0.020517582073807716, 0.03243857994675636, 0.06970182061195374, 0.06384002417325974, 0.06616394221782684, 0.08049719780683517, 0.07658769935369492, 0.06983709335327148, 0.08230385929346085]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["# Perform heartpy convolutional enhancement on the ECG signal before peak finding.\n", "enhance_ecg = etl.annotators.signals.filtering.HeartPyEnhanceECGPeaks() \\\n", "    .set_ecg_col(\"ecg_butter\") \\\n", "    .set_ts_col('ts_resampled') \\\n", "    .set_filter_type('notch') \\\n", "    .set_filter_cutoff(0.05) \\\n", "    .set_sample_rate_col('sampling_freq_hz') \\\n", "    .set_aggregation('median') \\\n", "    .set_iterations(3) \\\n", "    .set_output_cols([\"ecg_enhanced\"]) \\\n", "    .fit().transform(crop_ecg)\n", "\n", "print(enhance_ecg.select('ecg_enhanced').first()['ecg_enhanced'][:10])"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 77, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 246:====================================================>  (18 + 1) / 19]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["[42, 176, 276, 504, 636, 741, 959, 1185, 1431, 1653]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["# Find peaks and valleys of ppg/ecg\n", "robust_peak_finder = etl.annotators.signals.characterization.RobustPeakFinder() \\\n", "    .set_ppg_col('ppg_butter') \\\n", "    .set_ecg_col('ecg_enhanced') \\\n", "    .set_ecg_peak_prominence(4) \\\n", "    .set_ppg_peak_prominence(1) \\\n", "    .set_ppg_valley_prominence(1) \\\n", "    .set_min_pulse_distance(100) \\\n", "    .fit().transform(enhance_ecg)\n", "\n", "print(robust_peak_finder.select('ppg_butter_peaks').first()['ppg_butter_peaks'][:10])"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 79, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 264:====================================================>  (18 + 1) / 19]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["[-0.682784914970398, -0.6598610877990723, -0.6339375972747803, -0.604981005191803, -0.5729700326919556, -0.5378960967063904, -0.4997640550136566, -0.4585933983325958, -0.41441893577575684, -0.3672920763492584]\n", "[425, 671, 885, 1114, 1360, 1579, 1800, 2038, 2250, 2466]\n", "[42, 176, 276, 504, 636, 741, 959, 1185, 1431, 1653]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["ppg_butter = robust_peak_finder.select('ppg_butter').first()['ppg_butter'][:50]\n", "ecg_enhanced_peaks = robust_peak_finder.select('ecg_enhanced_peaks').first()['ecg_enhanced_peaks'][:50]\n", "ppg_butter_peaks = robust_peak_finder.select('ppg_butter_peaks').first()['ppg_butter_peaks'][:50]\n", "\n", "print(ppg_butter[:10])\n", "print(ecg_enhanced_peaks[:10])\n", "print(ppg_butter_peaks[:10])"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 80, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["UCSF-1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["NaN count: 504\n", "Non-NaN count: 373896\n"]}], "source": ["# Calculate PAT.\n", "pat_calc = etl.annotators.features.fromsignals.CalculatePulseArrivalTime() \\\n", "    .set_input_cols(['ppg_butter']) \\\n", "    .set_output_cols(['pat']) \\\n", "    .set_ppg_peaks_col('ppg_butter_peaks') \\\n", "    .set_ecg_peaks_col('ecg_enhanced_peaks') \\\n", "    .set_sampling_freq_col('sampling_freq_hz') \\\n", "    .fit().transform(robust_peak_finder)\n", "\n", "# Test the results:\n", "values = pat_calc.select('pat').first()['pat']\n", "# cast everything to float: non-convertible entries become NaN too\n", "arr = np.array(values, dtype=float)\n", "# vectorized boolean mask\n", "nan_mask = np.isnan(arr)\n", "nan_count     = int(nan_mask.sum())\n", "non_nan_count = arr.size - nan_count\n", "print(f\"NaN count: {nan_count}\")\n", "print(f\"Non-NaN count: {non_nan_count}\")"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 81, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 273:====================================================>  (18 + 1) / 19]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["NaN count: 671\n", "Non-NaN count: 373729\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["# Calculate HR from ECG\n", "hr_calc_from_ecg = etl.annotators.features.fromsignals.CalculateHeartRate() \\\n", "    .set_input_cols(['ecg_butter']) \\\n", "    .set_peaks_col('ecg_enhanced_peaks') \\\n", "    .set_output_cols(['hr_from_ecg']) \\\n", "    .set_sampling_freq_col('sampling_freq_hz') \\\n", "    .fit().transform(pat_calc)\n", "\n", "# Test the results:\n", "values = hr_calc_from_ecg.select('hr_from_ecg').first()['hr_from_ecg']\n", "# cast everything to float: non-convertible entries become NaN too\n", "arr = np.array(values, dtype=float)\n", "# vectorized boolean mask\n", "nan_mask = np.isnan(arr)\n", "nan_count     = int(nan_mask.sum())\n", "non_nan_count = arr.size - nan_count\n", "print(f\"NaN count: {nan_count}\")\n", "print(f\"Non-NaN count: {non_nan_count}\")"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 82, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 276:====================================================>  (18 + 1) / 19]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["NaN count: 176\n", "Non-NaN count: 374224\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["# Calculate HR from PPG\n", "hr_calc_from_ppg = etl.annotators.features.fromsignals.CalculateHeartRate() \\\n", "    .set_input_cols(['ppg_butter']) \\\n", "    .set_peaks_col('ppg_butter_peaks') \\\n", "    .set_output_cols(['hr_from_ppg']) \\\n", "    .set_sampling_freq_col('sampling_freq_hz') \\\n", "    .fit().transform(hr_calc_from_ecg)\n", "\n", "# Test the results:\n", "values = hr_calc_from_ppg.select('hr_from_ppg').first()['hr_from_ppg']\n", "# cast everything to float: non-convertible entries become NaN too\n", "arr = np.array(values, dtype=float)\n", "# vectorized boolean mask\n", "nan_mask = np.isnan(arr)\n", "nan_count     = int(nan_mask.sum())\n", "non_nan_count = arr.size - nan_count\n", "print(f\"NaN count: {nan_count}\")\n", "print(f\"Non-NaN count: {non_nan_count}\")"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 51, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Row(id='UCSF-1')\n", "['id', 'sbp', 'dbp', 'ts', 'abp', 'weight_lbs', 'age', 'height_inches', 'ref_sbp', 'ref_dbp', 'hypertension_present', 'diabetes_present', 'tobacco', 'gender_Female', 'gender_Male', 'race_White', 'race_Asian', 'race_Black or African American', 'race_American Indian or Alaska Native', 'BMI', 'ts_resampled', 'sampling_freq_hz', 'ppg_butter', 'ecg_butter', 'ecg_enhanced', 'ppg_butter_peaks', 'ppg_butter_valleys', 'ecg_enhanced_peaks', 'ecg_enhanced_valleys', 'pat', 'hr_from_ecg', 'hr_from_ppg']\n"]}], "source": ["remover = etl.annotators.dataprep.basic.RemoveColumns() \\\n", "            .set_input_cols(['Hypertension', 'Presence_Of_Diabetes', 'Tobacco_Use','Gender', 'Race_Ethnicity', 'ppg', 'ecg', 'file_from'] + unused_signals)\\\n", "            .fit().transform(hr_calc_from_ppg)\n", "\n", "print(remover.select('id').first())\n", "print(remover.columns)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": [], "metadata": {"collapsed": false}}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 0}