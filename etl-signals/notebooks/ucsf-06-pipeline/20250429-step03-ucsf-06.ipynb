{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Adding ../src to sys.path\n"]}], "source": ["import sys\n", "aio_src_path = \"../src\"\n", "if aio_src_path not in sys.path:\n", "    print(f\"Adding {aio_src_path} to sys.path\")\n", "    sys.path.append(aio_src_path)\n", "\n", "import etl\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "import tarfile\n", "import io\n", "\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "pd.options.display.max_columns = None\n", "\n", "from pyspark.sql import SparkSession\n", "from pyspark.sql import functions as F\n", "from pyspark.sql import types as T"]}, {"cell_type": "code", "execution_count": 2, "outputs": [], "source": ["fiducial_features = ['p2pi',\n", "                     'f_idx_o', 'f_idx_s', 'f_idx_n', 'f_idx_d', 'scaled_f_idx_o', 'scaled_f_idx_s', 'scaled_f_idx_n',\n", "                     'scaled_f_idx_d',\n", "                     'f_val_o', 'f_val_s', 'f_val_n', 'f_val_d',\n", "                     'beat_class_1.0', 'beat_class_2.0', 'beat_class_3.0',\n", "                     'f_val_diff_s_d', 'f_val_diff_s_n', 'f_val_diff_d_n', 'f_time_diff_s_d',\n", "                     'f_time_diff_s_n', 'f_time_diff_n_d',\n", "                     'tb1', 'tb2', 'tb1_tb2_p2pi_coeff', 'tb1_p2pi_coeff', 'tb2_p2pi_coeff',\n", "                     'ip_index', 'ip_value', 'time_ip', 'inverse_tip_tsp_coeff',\n", "                     'pulse_interval', 'adp_pi_tdn_coeff',\n", "                     'tp_p2pi_coeff', 'tdn_p2pi_coeff', 'inverse_time_s_notch',\n", "                     'fft_freq1', 'spl', 'auc2to5', 'auc0to2',\n", "                     'a1', 'a2', 'arearatio', 'a0toslope',\n", "                     'perc75', 'iqr']\n", "\n", "trigo_features = ['o_s_cosine', 'o_s_sine', 'o_s_magnitude', 'o_s_angle', 'o_s_degrees',\n", "                  's_n_cosine', 's_n_sine', 's_n_magnitude', 's_n_angle', 's_n_degrees',\n", "                  'n_d_cosine', 'n_d_sine', 'n_d_magnitude', 'n_d_angle', 'n_d_degrees']"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 3, "outputs": [], "source": ["path = \"20250317-ucsf06-step02.parquet\""], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 4, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Finished checking.\n"]}], "source": ["# Load into pandas\n", "df = pd.read_parquet(path, engine=\"pyarrow\")\n", "\n", "# Look what cols are np.arrays\n", "array_cols = [\n", "    col\n", "    for col in df.columns\n", "    if df[col].apply(lambda x: isinstance(x, np.ndarray)).any()\n", "]\n", "\n", "# Convert np.arrays into python pure lists\n", "for col in array_cols:\n", "    df[col] = df[col].apply(\n", "        lambda x: x.tolist() if isinstance(x, np.ndarray) else x\n", "    )\n", "\n", "# Check there are no more np.arrays\n", "for col in array_cols:\n", "    has_array = df[col].apply(lambda x: isinstance(x, np.ndarray)).any()\n", "    if has_array:\n", "        print(f\"{col} has arrays\")\n", "print(\"Finished checking.\")"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 5, "outputs": [{"data": {"text/plain": "       id        sbp       dbp  weight_lbs  age  height_inches     ref_sbp  \\\n0  UCSF-1  98.461419  55.87494         175   37             72  106.904556   \n\n     ref_dbp  hypertension_present  diabetes_present  tobacco  gender_Female  \\\n0  56.530522                     0                 0        0              0   \n\n   gender_Male  race_American Indian or Alaska Native  race_Asian  \\\n0            1                                      0           0   \n\n   race_Black or African American  race_White  BMI  sampling_freq_hz  \\\n0                               0           1   24               200   \n\n                                        ecg_enhanced  \\\n0  [0.08002757067067848, 0.020517579240281858, 0....   \n\n                                ecg_enhanced_valleys  split_number  \\\n0  [1, 210, 432, 677, 892, 1122, 1367, 1586, 1807...           0.0   \n\n                                          ts_episode  \\\n0  [1718099122337.0, 1718099122342.0, 17180991223...   \n\n                                         ppg_episode  \\\n0  [0.048932755437917815, 0.050256341254360674, 0...   \n\n                                         ecg_episode  \\\n0  [0.30651167370037113, 0.3181750317911976, 0.29...   \n\n                                         pat_episode  \\\n0  [0.35, 0.35, 0.35, 0.35, 0.35, 0.35, 0.35, 0.3...   \n\n                                 hr_from_ecg_episode  \\\n0  [57.0, 57.0, 57.0, 57.0, 57.0, 57.0, 57.0, 57....   \n\n                                 hr_from_ppg_episode  \\\n0  [50.0, 50.0, 50.0, 50.0, 50.0, 50.0, 50.0, 50....   \n\n                                         sbp_episode  \\\n0  [107.0, 107.0, 107.0, 107.0, 107.0, 107.0, 107...   \n\n                                         dbp_episode  \\\n0  [59.0, 59.0, 58.0, 59.0, 59.0, 59.0, 60.0, 60....   \n\n                                           pat_ratio  ref_pp  ppg_episode_min  \\\n0  [6.751543209876543e-05, 6.751543209876543e-05,...      50              0.0   \n\n   ppg_episode_max  ppg_episode_mean  ppg_episode_median  ppg_episode_std  \\\n0              1.0          0.460032            0.457316         0.213736   \n\n   ppg_episode_sum  ppg_episode_trend  ppg_episode_error  ecg_episode_min  \\\n0      1150.080616          -0.000012           0.213513              0.0   \n\n   ecg_episode_max  ecg_episode_mean  ecg_episode_median  ecg_episode_std  \\\n0              1.0           0.29283             0.26117         0.159253   \n\n   ecg_episode_sum  ecg_episode_trend  ecg_episode_error  pat_ratio_min  \\\n0       732.073969       8.429266e-07            0.15922       0.000068   \n\n   pat_ratio_max  pat_ratio_mean  pat_ratio_median  pat_ratio_std  \\\n0       0.000072         0.00007          0.000069       0.000002   \n\n   pat_ratio_sum  pat_ratio_trend  pat_ratio_error  pat_episode_min  \\\n0       0.175037     3.625186e-12         0.000002             0.35   \n\n   pat_episode_max  pat_episode_mean  pat_episode_median  pat_episode_std  \\\n0            0.375          0.362956                0.36         0.008472   \n\n   pat_episode_sum  pat_episode_trend  pat_episode_error  \\\n0           907.39       1.879296e-08            0.00847   \n\n   hr_from_ecg_episode_min  hr_from_ecg_episode_max  hr_from_ecg_episode_mean  \\\n0                     48.0                     58.0                   53.4256   \n\n   hr_from_ecg_episode_median  hr_from_ecg_episode_std  \\\n0                        55.0                 3.518383   \n\n   hr_from_ecg_episode_sum  hr_from_ecg_episode_trend  \\\n0                 133564.0                  -0.001933   \n\n   hr_from_ecg_episode_error  hr_from_ppg_episode_min  \\\n0                   3.229231                     49.0   \n\n   hr_from_ppg_episode_max  hr_from_ppg_episode_mean  \\\n0                     57.0                   53.5336   \n\n   hr_from_ppg_episode_median  hr_from_ppg_episode_std  \\\n0                        55.0                 3.043251   \n\n   hr_from_ppg_episode_sum  hr_from_ppg_episode_trend  \\\n0                 133834.0                  -0.001201   \n\n   hr_from_ppg_episode_error  \\\n0                   2.916658   \n\n                                   ppg_episode_peaks  \\\n0  [50, 262, 501, 711, 927, 1174, 1387, 1624, 186...   \n\n                                 ppg_episode_valleys  \\\n0  [217, 452, 666, 881, 1128, 1344, 1578, 1817, 2...   \n\n                                    last_three_beats  \\\n0  [0.13158861651812376, 0.13182158983944692, 0.1...   \n\n                                 ts_last_three_beats  \n0  [1718099131422.0, 1718099131427.0, 17180991314...  ", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>id</th>\n      <th>sbp</th>\n      <th>dbp</th>\n      <th>weight_lbs</th>\n      <th>age</th>\n      <th>height_inches</th>\n      <th>ref_sbp</th>\n      <th>ref_dbp</th>\n      <th>hypertension_present</th>\n      <th>diabetes_present</th>\n      <th>tobacco</th>\n      <th>gender_Female</th>\n      <th>gender_Male</th>\n      <th>race_American Indian or Alaska Native</th>\n      <th>race_Asian</th>\n      <th>race_Black or African American</th>\n      <th>race_White</th>\n      <th>BMI</th>\n      <th>sampling_freq_hz</th>\n      <th>ecg_enhanced</th>\n      <th>ecg_enhanced_valleys</th>\n      <th>split_number</th>\n      <th>ts_episode</th>\n      <th>ppg_episode</th>\n      <th>ecg_episode</th>\n      <th>pat_episode</th>\n      <th>hr_from_ecg_episode</th>\n      <th>hr_from_ppg_episode</th>\n      <th>sbp_episode</th>\n      <th>dbp_episode</th>\n      <th>pat_ratio</th>\n      <th>ref_pp</th>\n      <th>ppg_episode_min</th>\n      <th>ppg_episode_max</th>\n      <th>ppg_episode_mean</th>\n      <th>ppg_episode_median</th>\n      <th>ppg_episode_std</th>\n      <th>ppg_episode_sum</th>\n      <th>ppg_episode_trend</th>\n      <th>ppg_episode_error</th>\n      <th>ecg_episode_min</th>\n      <th>ecg_episode_max</th>\n      <th>ecg_episode_mean</th>\n      <th>ecg_episode_median</th>\n      <th>ecg_episode_std</th>\n      <th>ecg_episode_sum</th>\n      <th>ecg_episode_trend</th>\n      <th>ecg_episode_error</th>\n      <th>pat_ratio_min</th>\n      <th>pat_ratio_max</th>\n      <th>pat_ratio_mean</th>\n      <th>pat_ratio_median</th>\n      <th>pat_ratio_std</th>\n      <th>pat_ratio_sum</th>\n      <th>pat_ratio_trend</th>\n      <th>pat_ratio_error</th>\n      <th>pat_episode_min</th>\n      <th>pat_episode_max</th>\n      <th>pat_episode_mean</th>\n      <th>pat_episode_median</th>\n      <th>pat_episode_std</th>\n      <th>pat_episode_sum</th>\n      <th>pat_episode_trend</th>\n      <th>pat_episode_error</th>\n      <th>hr_from_ecg_episode_min</th>\n      <th>hr_from_ecg_episode_max</th>\n      <th>hr_from_ecg_episode_mean</th>\n      <th>hr_from_ecg_episode_median</th>\n      <th>hr_from_ecg_episode_std</th>\n      <th>hr_from_ecg_episode_sum</th>\n      <th>hr_from_ecg_episode_trend</th>\n      <th>hr_from_ecg_episode_error</th>\n      <th>hr_from_ppg_episode_min</th>\n      <th>hr_from_ppg_episode_max</th>\n      <th>hr_from_ppg_episode_mean</th>\n      <th>hr_from_ppg_episode_median</th>\n      <th>hr_from_ppg_episode_std</th>\n      <th>hr_from_ppg_episode_sum</th>\n      <th>hr_from_ppg_episode_trend</th>\n      <th>hr_from_ppg_episode_error</th>\n      <th>ppg_episode_peaks</th>\n      <th>ppg_episode_valleys</th>\n      <th>last_three_beats</th>\n      <th>ts_last_three_beats</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>UCSF-1</td>\n      <td>98.461419</td>\n      <td>55.87494</td>\n      <td>175</td>\n      <td>37</td>\n      <td>72</td>\n      <td>106.904556</td>\n      <td>56.530522</td>\n      <td>0</td>\n      <td>0</td>\n      <td>0</td>\n      <td>0</td>\n      <td>1</td>\n      <td>0</td>\n      <td>0</td>\n      <td>0</td>\n      <td>1</td>\n      <td>24</td>\n      <td>200</td>\n      <td>[0.08002757067067848, 0.020517579240281858, 0....</td>\n      <td>[1, 210, 432, 677, 892, 1122, 1367, 1586, 1807...</td>\n      <td>0.0</td>\n      <td>[1718099122337.0, 1718099122342.0, 17180991223...</td>\n      <td>[0.048932755437917815, 0.050256341254360674, 0...</td>\n      <td>[0.30651167370037113, 0.3181750317911976, 0.29...</td>\n      <td>[0.35, 0.35, 0.35, 0.35, 0.35, 0.35, 0.35, 0.3...</td>\n      <td>[57.0, 57.0, 57.0, 57.0, 57.0, 57.0, 57.0, 57....</td>\n      <td>[50.0, 50.0, 50.0, 50.0, 50.0, 50.0, 50.0, 50....</td>\n      <td>[107.0, 107.0, 107.0, 107.0, 107.0, 107.0, 107...</td>\n      <td>[59.0, 59.0, 58.0, 59.0, 59.0, 59.0, 60.0, 60....</td>\n      <td>[6.751543209876543e-05, 6.751543209876543e-05,...</td>\n      <td>50</td>\n      <td>0.0</td>\n      <td>1.0</td>\n      <td>0.460032</td>\n      <td>0.457316</td>\n      <td>0.213736</td>\n      <td>1150.080616</td>\n      <td>-0.000012</td>\n      <td>0.213513</td>\n      <td>0.0</td>\n      <td>1.0</td>\n      <td>0.29283</td>\n      <td>0.26117</td>\n      <td>0.159253</td>\n      <td>732.073969</td>\n      <td>8.429266e-07</td>\n      <td>0.15922</td>\n      <td>0.000068</td>\n      <td>0.000072</td>\n      <td>0.00007</td>\n      <td>0.000069</td>\n      <td>0.000002</td>\n      <td>0.175037</td>\n      <td>3.625186e-12</td>\n      <td>0.000002</td>\n      <td>0.35</td>\n      <td>0.375</td>\n      <td>0.362956</td>\n      <td>0.36</td>\n      <td>0.008472</td>\n      <td>907.39</td>\n      <td>1.879296e-08</td>\n      <td>0.00847</td>\n      <td>48.0</td>\n      <td>58.0</td>\n      <td>53.4256</td>\n      <td>55.0</td>\n      <td>3.518383</td>\n      <td>133564.0</td>\n      <td>-0.001933</td>\n      <td>3.229231</td>\n      <td>49.0</td>\n      <td>57.0</td>\n      <td>53.5336</td>\n      <td>55.0</td>\n      <td>3.043251</td>\n      <td>133834.0</td>\n      <td>-0.001201</td>\n      <td>2.916658</td>\n      <td>[50, 262, 501, 711, 927, 1174, 1387, 1624, 186...</td>\n      <td>[217, 452, 666, 881, 1128, 1344, 1578, 1817, 2...</td>\n      <td>[0.13158861651812376, 0.13182158983944692, 0.1...</td>\n      <td>[1718099131422.0, 1718099131427.0, 17180991314...</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# We will only keep one row for testing reasons\n", "df = pd.DataFrame(df.iloc[[0]])\n", "df"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 6, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["bash: warning: setlocale: LC_ALL: cannot change locale (en_US.UTF-8)\n", "bash: warning: setlocale: LC_ALL: cannot change locale (en_US.UTF-8)\n", "25/05/05 11:32:38 WARN Utils: Your hostname, PC-SANTI resolves to a loopback address: *********; using ************** instead (on interface lo)\n", "25/05/05 11:32:38 WARN Utils: Set SPARK_LOCAL_IP if you need to bind to another address\n", "Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n", "25/05/05 11:32:39 WARN NativeCodeLoader: Unable to load native-hadoop library for your platform... using builtin-java classes where applicable\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded df to spark\n"]}], "source": ["# Load into spark\n", "spark_session = SparkSession.builder \\\n", "    .appName(\"LocalTestApp\") \\\n", "    .config(\"spark.sql.parquet.columnarReaderBatchSize\", \"1024\") \\\n", "    .config(\"spark.sql.parquet.enableVectorizedReader\", \"false\") \\\n", "    .config(\"spark.driver.memory\", \"10g\") \\\n", "    .config(\"spark.executor.memory\", \"10g\") \\\n", "    .master(\"local[*]\") \\\n", "    .getOrCreate()\n", "\n", "spark_df = spark_session.createDataFrame(df)\n", "print(\"Loaded df to spark\")\n", "# spark_df.printSchema()"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 7, "outputs": [], "source": ["# # grab the first row once\n", "# spark_row = spark_df.first()\n", "# pandas_row = df.iloc[0]\n", "#\n", "# for col in spark_df.columns:\n", "#     spark_val = spark_row[col]\n", "#     pandas_val = pandas_row[col]\n", "#\n", "#     print(f\"\\nChecking column '{col}':\")\n", "#\n", "#     # Spark side\n", "#     print(\"  Spark:\", end=\" \")\n", "#     if isinstance(spark_val, list):\n", "#         print(spark_val[:10], \"…\")   # up to 10 elements\n", "#     else:\n", "#         print(spark_val)\n", "#\n", "#     # pandas side\n", "#     print(\"  pandas:\", end=\" \")\n", "#     if isinstance(pandas_val, list):\n", "#         print(pandas_val[:10], \"…\")\n", "#     else:\n", "#         print(pandas_val)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 8, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["25/05/05 11:32:42 WARN SparkStringUtils: Truncated the string representation of a plan since it was too large. This behavior can be adjusted by setting 'spark.sql.debug.maxToStringFields'.\n", "25/05/05 11:32:42 WARN TaskSetManager: Stage 0 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:32:46 WARN TaskSetManager: Stage 3 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Rows before p2pi filter (correct beat context): 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["25/05/05 11:32:48 WARN TaskSetManager: Stage 6 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Rows after p2pi filter (correct beat context): 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["25/05/05 11:32:50 WARN TaskSetManager: Stage 9 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:32:52 WARN TaskSetManager: Stage 12 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Rows before classification filter (classifiable beat): 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["25/05/05 11:32:53 WARN TaskSetManager: Stage 15 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Rows after classification filter (classifiable beat): 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["25/05/05 11:32:54 WARN TaskSetManager: Stage 18 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Rows before fiducial location filter (able to locate fidu points): 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["25/05/05 11:32:56 WARN TaskSetManager: Stage 21 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Rows after fiducial location filter (able to locate fidu points): 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["25/05/05 11:32:58 WARN TaskSetManager: Stage 24 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Rows before fiducial sequence filter (fidu points in the correct order): 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["25/05/05 11:33:00 WARN TaskSetManager: Stage 27 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Rows after fiducial sequence filter (fidu points in the correct order): 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["25/05/05 11:33:02 WARN TaskSetManager: Stage 30 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Rows before fiducial derivatives filter (derivatives features computable): 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["25/05/05 11:33:04 WARN TaskSetManager: Stage 33 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Rows after fiducial derivatives filter (derivatives features computable): 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["25/05/05 11:33:06 WARN TaskSetManager: Stage 36 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Rows before ip filter (beat inflection point computable): 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["25/05/05 11:33:08 WARN TaskSetManager: Stage 39 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Rows after ip filter (beat inflection point computable): 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Rows before fft filter (fast fourier transform computable): 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["25/05/05 11:33:10 WARN TaskSetManager: Stage 42 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "[Stage 42:===========>                                            (5 + 19) / 24]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["Rows after fft filter (fast fourier transform computable): 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["fiducial_extraction = etl.annotators.features.fromsignals.PPGSegmentFiducialExtraction() \\\n", "    .set_input_cols(['last_three_beats', 'ts_last_three_beats'])\\\n", "    .fit().transform(spark_df, verbose=1)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 9, "outputs": [], "source": ["multi_trigo = etl.annotators.features.fromsignals.MultipleTrigonometricExtractorAndFilter() \\\n", "    .set_s_n_filter_threshold((-90, 0)) \\\n", "    .set_n_d_filter_threshold((-90, 90)) \\\n", "    .set_beat_ts_col('beat_ts') \\\n", "    .set_f_idx_o_col('f_idx_o') \\\n", "    .set_f_idx_s_col('f_idx_s') \\\n", "    .set_f_idx_n_col('f_idx_n') \\\n", "    .set_f_idx_d_col('f_idx_d') \\\n", "    .set_f_val_o_col('f_val_o') \\\n", "    .set_f_val_s_col('f_val_s') \\\n", "    .set_f_val_n_col('f_val_n') \\\n", "    .set_f_val_d_col('f_val_d') \\\n", "    .fit().transform(fiducial_extraction)\n", "\n", "multi_trigo.withColumn('dataset_source', F.lit(\"ucsf06\"))"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 10, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Finished checking.\n"]}], "source": ["# Load the pandas etl test parket into pandas to check if the values align between both etls\n", "path = \"20250317-ucsf06-processed.parquet\"\n", "df = pd.read_parquet(path, engine=\"pyarrow\")\n", "\n", "# Look what cols are np.arrays\n", "array_cols = [\n", "    col\n", "    for col in df.columns\n", "    if df[col].apply(lambda x: isinstance(x, np.ndarray)).any()\n", "]\n", "\n", "# Convert np.arrays into python pure lists\n", "for col in array_cols:\n", "    df[col] = df[col].apply(\n", "        lambda x: x.tolist() if isinstance(x, np.ndarray) else x\n", "    )\n", "\n", "# Check there are no more np.arrays\n", "for col in array_cols:\n", "    has_array = df[col].apply(lambda x: isinstance(x, np.ndarray)).any()\n", "    if has_array:\n", "        print(f\"{col} has arrays\")\n", "print(\"Finished checking.\")"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 11, "outputs": [{"data": {"text/plain": "       id dataset_source     ref_sbp    ref_dbp  ref_pp  \\\n0  UCSF-1         ucsf06  106.904556  56.530522      50   \n\n                                          ts_episode  \\\n0  [1718099122337.0, 1718099122342.0, 17180991223...   \n\n                                         ppg_episode  \\\n0  [0.048932755437917815, 0.050256341254360674, 0...   \n\n                                         ecg_episode  \\\n0  [0.30651167370037113, 0.3181750317911976, 0.29...   \n\n                                         pat_episode  \\\n0  [0.35, 0.35, 0.35, 0.35, 0.35, 0.35, 0.35, 0.3...   \n\n                                 hr_from_ecg_episode  \\\n0  [57.0, 57.0, 57.0, 57.0, 57.0, 57.0, 57.0, 57....   \n\n                                 hr_from_ppg_episode  \\\n0  [50.0, 50.0, 50.0, 50.0, 50.0, 50.0, 50.0, 50....   \n\n                                           pat_ratio  \\\n0  [6.751543209876543e-05, 6.751543209876543e-05,...   \n\n                                    last_three_beats  \\\n0  [0.13158861651812376, 0.13182158983944692, 0.1...   \n\n                                 ts_last_three_beats  \\\n0  [1718099131422.0, 1718099131427.0, 17180991314...   \n\n                                           beat_vals  \\\n0  [0.0, 0.0006245853389206598, 0.002694302921825...   \n\n                                             beat_ts  \\\n0  [1718099132472.0, 1718099132477.0, 17180991324...   \n\n                                      scaled_beat_ts        sbp       dbp  \\\n0  [0.0, 0.004132231404958678, 0.0082644628099173...  98.461419  55.87494   \n\n   height_inches  weight_lbs  BMI  age  gender_Male  race_Asian  \\\n0             72         175   24   37            1           0   \n\n   race_Black or African American  race_White  tobacco  hypertension_present  \\\n0                               0           1        0                     0   \n\n   diabetes_present  ppg_episode_min  ppg_episode_max  ppg_episode_mean  \\\n0                 0              0.0              1.0          0.460032   \n\n   ppg_episode_median  ppg_episode_std  ppg_episode_sum  ppg_episode_trend  \\\n0            0.457316         0.213736      1150.080616          -0.000012   \n\n   ppg_episode_error  ecg_episode_min  ecg_episode_max  ecg_episode_mean  \\\n0           0.213513              0.0              1.0           0.29283   \n\n   ecg_episode_median  ecg_episode_std  ecg_episode_sum  ecg_episode_trend  \\\n0             0.26117         0.159253       732.073969       8.429266e-07   \n\n   ecg_episode_error  pat_ratio_min  pat_ratio_max  pat_ratio_mean  \\\n0            0.15922       0.000068       0.000072         0.00007   \n\n   pat_ratio_median  pat_ratio_std  pat_ratio_sum  pat_ratio_trend  \\\n0          0.000069       0.000002       0.175037     3.625186e-12   \n\n   pat_ratio_error  pat_episode_min  pat_episode_max  pat_episode_mean  \\\n0         0.000002             0.35            0.375          0.362956   \n\n   pat_episode_median  pat_episode_std  pat_episode_sum  pat_episode_trend  \\\n0                0.36         0.008472           907.39       1.879296e-08   \n\n   pat_episode_error  hr_from_ecg_episode_min  hr_from_ecg_episode_max  \\\n0            0.00847                     48.0                     58.0   \n\n   hr_from_ecg_episode_mean  hr_from_ecg_episode_median  \\\n0                   53.4256                        55.0   \n\n   hr_from_ecg_episode_std  hr_from_ecg_episode_sum  \\\n0                 3.518383                 133564.0   \n\n   hr_from_ecg_episode_trend  hr_from_ecg_episode_error  \\\n0                  -0.001933                   3.229231   \n\n   hr_from_ppg_episode_min  hr_from_ppg_episode_max  hr_from_ppg_episode_mean  \\\n0                     49.0                     57.0                   53.5336   \n\n   hr_from_ppg_episode_median  hr_from_ppg_episode_std  \\\n0                        55.0                 3.043251   \n\n   hr_from_ppg_episode_sum  hr_from_ppg_episode_trend  \\\n0                 133834.0                  -0.001201   \n\n   hr_from_ppg_episode_error   p2pi  f_idx_o  f_idx_s  f_idx_n  f_idx_d  \\\n0                   2.916658  1.205      0.0     49.0    167.0    206.0   \n\n   scaled_f_idx_o  scaled_f_idx_s  scaled_f_idx_n  scaled_f_idx_d  f_val_o  \\\n0             0.0        0.202479        0.690083         0.85124      0.0   \n\n   f_val_s   f_val_n   f_val_d  o_s_cosine  o_s_sine  o_s_magnitude  \\\n0      1.0  0.518304  0.499685    0.198452  0.980111       1.020293   \n\n   o_s_angle  o_s_degrees  s_n_cosine  s_n_sine  s_n_magnitude  s_n_angle  \\\n0   1.371018    78.553541    0.711403 -0.702784       0.685411  -0.779304   \n\n   s_n_degrees  n_d_cosine  n_d_sine  n_d_magnitude  n_d_angle  n_d_degrees  \\\n0   -44.650815    0.993392 -0.114772       0.162229  -0.115025    -6.590452   \n\n   beat_class_1.0  beat_class_2.0  beat_class_3.0  f_val_diff_s_d  \\\n0             0.0             0.0             1.0        0.500315   \n\n   f_val_diff_s_n  f_val_diff_d_n  f_time_diff_s_d  f_time_diff_s_n  \\\n0        0.481696       -0.018619            0.785             0.59   \n\n   f_time_diff_n_d   tb1   tb2  tb1_tb2_p2pi_coeff  tb1_p2pi_coeff  \\\n0            0.195  0.34  0.65            0.257261        0.282158   \n\n   tb2_p2pi_coeff  ip_index  ip_value  time_ip  inverse_tip_tsp_coeff  \\\n0        0.539419     180.0  0.538064      0.9               1.526718   \n\n   pulse_interval  adp_pi_tdn_coeff  tp_p2pi_coeff  tdn_p2pi_coeff  \\\n0            1.21          1.332493       0.651452        0.692946   \n\n   inverse_time_s_notch  fft_freq1       spl   auc2to5    auc0to2        a1  \\\n0              1.694915   1.646091  1.646091  11.70738  82.000264  0.480251   \n\n         a2  arearatio  a0toslope    perc75    iqr  \n0  0.147359   0.306837   0.025669  0.567382  0.083  ", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>id</th>\n      <th>dataset_source</th>\n      <th>ref_sbp</th>\n      <th>ref_dbp</th>\n      <th>ref_pp</th>\n      <th>ts_episode</th>\n      <th>ppg_episode</th>\n      <th>ecg_episode</th>\n      <th>pat_episode</th>\n      <th>hr_from_ecg_episode</th>\n      <th>hr_from_ppg_episode</th>\n      <th>pat_ratio</th>\n      <th>last_three_beats</th>\n      <th>ts_last_three_beats</th>\n      <th>beat_vals</th>\n      <th>beat_ts</th>\n      <th>scaled_beat_ts</th>\n      <th>sbp</th>\n      <th>dbp</th>\n      <th>height_inches</th>\n      <th>weight_lbs</th>\n      <th>BMI</th>\n      <th>age</th>\n      <th>gender_Male</th>\n      <th>race_Asian</th>\n      <th>race_Black or African American</th>\n      <th>race_White</th>\n      <th>tobacco</th>\n      <th>hypertension_present</th>\n      <th>diabetes_present</th>\n      <th>ppg_episode_min</th>\n      <th>ppg_episode_max</th>\n      <th>ppg_episode_mean</th>\n      <th>ppg_episode_median</th>\n      <th>ppg_episode_std</th>\n      <th>ppg_episode_sum</th>\n      <th>ppg_episode_trend</th>\n      <th>ppg_episode_error</th>\n      <th>ecg_episode_min</th>\n      <th>ecg_episode_max</th>\n      <th>ecg_episode_mean</th>\n      <th>ecg_episode_median</th>\n      <th>ecg_episode_std</th>\n      <th>ecg_episode_sum</th>\n      <th>ecg_episode_trend</th>\n      <th>ecg_episode_error</th>\n      <th>pat_ratio_min</th>\n      <th>pat_ratio_max</th>\n      <th>pat_ratio_mean</th>\n      <th>pat_ratio_median</th>\n      <th>pat_ratio_std</th>\n      <th>pat_ratio_sum</th>\n      <th>pat_ratio_trend</th>\n      <th>pat_ratio_error</th>\n      <th>pat_episode_min</th>\n      <th>pat_episode_max</th>\n      <th>pat_episode_mean</th>\n      <th>pat_episode_median</th>\n      <th>pat_episode_std</th>\n      <th>pat_episode_sum</th>\n      <th>pat_episode_trend</th>\n      <th>pat_episode_error</th>\n      <th>hr_from_ecg_episode_min</th>\n      <th>hr_from_ecg_episode_max</th>\n      <th>hr_from_ecg_episode_mean</th>\n      <th>hr_from_ecg_episode_median</th>\n      <th>hr_from_ecg_episode_std</th>\n      <th>hr_from_ecg_episode_sum</th>\n      <th>hr_from_ecg_episode_trend</th>\n      <th>hr_from_ecg_episode_error</th>\n      <th>hr_from_ppg_episode_min</th>\n      <th>hr_from_ppg_episode_max</th>\n      <th>hr_from_ppg_episode_mean</th>\n      <th>hr_from_ppg_episode_median</th>\n      <th>hr_from_ppg_episode_std</th>\n      <th>hr_from_ppg_episode_sum</th>\n      <th>hr_from_ppg_episode_trend</th>\n      <th>hr_from_ppg_episode_error</th>\n      <th>p2pi</th>\n      <th>f_idx_o</th>\n      <th>f_idx_s</th>\n      <th>f_idx_n</th>\n      <th>f_idx_d</th>\n      <th>scaled_f_idx_o</th>\n      <th>scaled_f_idx_s</th>\n      <th>scaled_f_idx_n</th>\n      <th>scaled_f_idx_d</th>\n      <th>f_val_o</th>\n      <th>f_val_s</th>\n      <th>f_val_n</th>\n      <th>f_val_d</th>\n      <th>o_s_cosine</th>\n      <th>o_s_sine</th>\n      <th>o_s_magnitude</th>\n      <th>o_s_angle</th>\n      <th>o_s_degrees</th>\n      <th>s_n_cosine</th>\n      <th>s_n_sine</th>\n      <th>s_n_magnitude</th>\n      <th>s_n_angle</th>\n      <th>s_n_degrees</th>\n      <th>n_d_cosine</th>\n      <th>n_d_sine</th>\n      <th>n_d_magnitude</th>\n      <th>n_d_angle</th>\n      <th>n_d_degrees</th>\n      <th>beat_class_1.0</th>\n      <th>beat_class_2.0</th>\n      <th>beat_class_3.0</th>\n      <th>f_val_diff_s_d</th>\n      <th>f_val_diff_s_n</th>\n      <th>f_val_diff_d_n</th>\n      <th>f_time_diff_s_d</th>\n      <th>f_time_diff_s_n</th>\n      <th>f_time_diff_n_d</th>\n      <th>tb1</th>\n      <th>tb2</th>\n      <th>tb1_tb2_p2pi_coeff</th>\n      <th>tb1_p2pi_coeff</th>\n      <th>tb2_p2pi_coeff</th>\n      <th>ip_index</th>\n      <th>ip_value</th>\n      <th>time_ip</th>\n      <th>inverse_tip_tsp_coeff</th>\n      <th>pulse_interval</th>\n      <th>adp_pi_tdn_coeff</th>\n      <th>tp_p2pi_coeff</th>\n      <th>tdn_p2pi_coeff</th>\n      <th>inverse_time_s_notch</th>\n      <th>fft_freq1</th>\n      <th>spl</th>\n      <th>auc2to5</th>\n      <th>auc0to2</th>\n      <th>a1</th>\n      <th>a2</th>\n      <th>arearatio</th>\n      <th>a0toslope</th>\n      <th>perc75</th>\n      <th>iqr</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>UCSF-1</td>\n      <td>ucsf06</td>\n      <td>106.904556</td>\n      <td>56.530522</td>\n      <td>50</td>\n      <td>[1718099122337.0, 1718099122342.0, 17180991223...</td>\n      <td>[0.048932755437917815, 0.050256341254360674, 0...</td>\n      <td>[0.30651167370037113, 0.3181750317911976, 0.29...</td>\n      <td>[0.35, 0.35, 0.35, 0.35, 0.35, 0.35, 0.35, 0.3...</td>\n      <td>[57.0, 57.0, 57.0, 57.0, 57.0, 57.0, 57.0, 57....</td>\n      <td>[50.0, 50.0, 50.0, 50.0, 50.0, 50.0, 50.0, 50....</td>\n      <td>[6.751543209876543e-05, 6.751543209876543e-05,...</td>\n      <td>[0.13158861651812376, 0.13182158983944692, 0.1...</td>\n      <td>[1718099131422.0, 1718099131427.0, 17180991314...</td>\n      <td>[0.0, 0.0006245853389206598, 0.002694302921825...</td>\n      <td>[1718099132472.0, 1718099132477.0, 17180991324...</td>\n      <td>[0.0, 0.004132231404958678, 0.0082644628099173...</td>\n      <td>98.461419</td>\n      <td>55.87494</td>\n      <td>72</td>\n      <td>175</td>\n      <td>24</td>\n      <td>37</td>\n      <td>1</td>\n      <td>0</td>\n      <td>0</td>\n      <td>1</td>\n      <td>0</td>\n      <td>0</td>\n      <td>0</td>\n      <td>0.0</td>\n      <td>1.0</td>\n      <td>0.460032</td>\n      <td>0.457316</td>\n      <td>0.213736</td>\n      <td>1150.080616</td>\n      <td>-0.000012</td>\n      <td>0.213513</td>\n      <td>0.0</td>\n      <td>1.0</td>\n      <td>0.29283</td>\n      <td>0.26117</td>\n      <td>0.159253</td>\n      <td>732.073969</td>\n      <td>8.429266e-07</td>\n      <td>0.15922</td>\n      <td>0.000068</td>\n      <td>0.000072</td>\n      <td>0.00007</td>\n      <td>0.000069</td>\n      <td>0.000002</td>\n      <td>0.175037</td>\n      <td>3.625186e-12</td>\n      <td>0.000002</td>\n      <td>0.35</td>\n      <td>0.375</td>\n      <td>0.362956</td>\n      <td>0.36</td>\n      <td>0.008472</td>\n      <td>907.39</td>\n      <td>1.879296e-08</td>\n      <td>0.00847</td>\n      <td>48.0</td>\n      <td>58.0</td>\n      <td>53.4256</td>\n      <td>55.0</td>\n      <td>3.518383</td>\n      <td>133564.0</td>\n      <td>-0.001933</td>\n      <td>3.229231</td>\n      <td>49.0</td>\n      <td>57.0</td>\n      <td>53.5336</td>\n      <td>55.0</td>\n      <td>3.043251</td>\n      <td>133834.0</td>\n      <td>-0.001201</td>\n      <td>2.916658</td>\n      <td>1.205</td>\n      <td>0.0</td>\n      <td>49.0</td>\n      <td>167.0</td>\n      <td>206.0</td>\n      <td>0.0</td>\n      <td>0.202479</td>\n      <td>0.690083</td>\n      <td>0.85124</td>\n      <td>0.0</td>\n      <td>1.0</td>\n      <td>0.518304</td>\n      <td>0.499685</td>\n      <td>0.198452</td>\n      <td>0.980111</td>\n      <td>1.020293</td>\n      <td>1.371018</td>\n      <td>78.553541</td>\n      <td>0.711403</td>\n      <td>-0.702784</td>\n      <td>0.685411</td>\n      <td>-0.779304</td>\n      <td>-44.650815</td>\n      <td>0.993392</td>\n      <td>-0.114772</td>\n      <td>0.162229</td>\n      <td>-0.115025</td>\n      <td>-6.590452</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>1.0</td>\n      <td>0.500315</td>\n      <td>0.481696</td>\n      <td>-0.018619</td>\n      <td>0.785</td>\n      <td>0.59</td>\n      <td>0.195</td>\n      <td>0.34</td>\n      <td>0.65</td>\n      <td>0.257261</td>\n      <td>0.282158</td>\n      <td>0.539419</td>\n      <td>180.0</td>\n      <td>0.538064</td>\n      <td>0.9</td>\n      <td>1.526718</td>\n      <td>1.21</td>\n      <td>1.332493</td>\n      <td>0.651452</td>\n      <td>0.692946</td>\n      <td>1.694915</td>\n      <td>1.646091</td>\n      <td>1.646091</td>\n      <td>11.70738</td>\n      <td>82.000264</td>\n      <td>0.480251</td>\n      <td>0.147359</td>\n      <td>0.306837</td>\n      <td>0.025669</td>\n      <td>0.567382</td>\n      <td>0.083</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["df = df.iloc[[0]]\n", "df"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 13, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["25/05/05 11:39:03 WARN TaskSetManager: Stage 92 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:39:09 WARN TaskSetManager: Stage 95 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:39:13 WARN TaskSetManager: Stage 98 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:39:18 WARN TaskSetManager: Stage 101 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:39:22 WARN TaskSetManager: Stage 104 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:39:26 WARN TaskSetManager: Stage 107 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:39:30 WARN TaskSetManager: Stage 110 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:39:34 WARN TaskSetManager: Stage 113 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:39:38 WARN TaskSetManager: Stage 116 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:39:42 WARN TaskSetManager: Stage 119 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:39:46 WARN TaskSetManager: Stage 122 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:39:50 WARN TaskSetManager: Stage 125 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:39:54 WARN TaskSetManager: Stage 128 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:39:58 WARN TaskSetManager: Stage 131 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:40:02 WARN TaskSetManager: Stage 134 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:40:06 WARN TaskSetManager: Stage 137 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:40:11 WARN TaskSetManager: Stage 140 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:40:15 WARN TaskSetManager: Stage 143 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:40:19 WARN TaskSetManager: Stage 146 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:40:23 WARN TaskSetManager: Stage 149 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:40:27 WARN TaskSetManager: Stage 152 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:40:31 WARN TaskSetManager: Stage 155 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:40:35 WARN TaskSetManager: Stage 158 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:40:39 WARN TaskSetManager: Stage 161 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:40:43 WARN TaskSetManager: Stage 164 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:40:47 WARN TaskSetManager: Stage 167 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:40:51 WARN TaskSetManager: Stage 170 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:40:55 WARN TaskSetManager: Stage 173 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:40:59 WARN TaskSetManager: Stage 176 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:41:03 WARN TaskSetManager: Stage 179 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:41:07 WARN TaskSetManager: Stage 182 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:41:12 WARN TaskSetManager: Stage 185 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:41:16 WARN TaskSetManager: Stage 188 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:41:20 WARN TaskSetManager: Stage 191 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:41:24 WARN TaskSetManager: Stage 194 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:41:28 WARN TaskSetManager: Stage 197 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:41:33 WARN TaskSetManager: Stage 200 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:41:38 WARN TaskSetManager: Stage 203 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:41:42 WARN TaskSetManager: Stage 206 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:41:46 WARN TaskSetManager: Stage 209 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:41:50 WARN TaskSetManager: Stage 212 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:41:54 WARN TaskSetManager: Stage 215 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:41:58 WARN TaskSetManager: Stage 218 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:42:02 WARN TaskSetManager: Stage 221 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:42:07 WARN TaskSetManager: Stage 224 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:42:11 WARN TaskSetManager: Stage 227 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:42:15 WARN TaskSetManager: Stage 230 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:42:19 WARN TaskSetManager: Stage 233 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:42:23 WARN TaskSetManager: Stage 236 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:42:27 WARN TaskSetManager: Stage 239 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:42:31 WARN TaskSetManager: Stage 242 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:42:35 WARN TaskSetManager: Stage 245 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:42:39 WARN TaskSetManager: Stage 248 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:42:43 WARN TaskSetManager: Stage 251 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:42:47 WARN TaskSetManager: Stage 254 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:42:51 WARN TaskSetManager: Stage 257 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:42:56 WARN TaskSetManager: Stage 260 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:43:00 WARN TaskSetManager: Stage 263 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:43:04 WARN TaskSetManager: Stage 266 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:43:08 WARN TaskSetManager: Stage 269 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:43:12 WARN TaskSetManager: Stage 272 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:43:16 WARN TaskSetManager: Stage 275 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:43:20 WARN TaskSetManager: Stage 278 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:43:24 WARN TaskSetManager: Stage 281 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:43:29 WARN TaskSetManager: Stage 284 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:43:33 WARN TaskSetManager: Stage 287 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:43:37 WARN TaskSetManager: Stage 290 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:43:41 WARN TaskSetManager: Stage 293 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:43:45 WARN TaskSetManager: Stage 296 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:43:49 WARN TaskSetManager: Stage 299 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:43:53 WARN TaskSetManager: Stage 302 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:43:58 WARN TaskSetManager: Stage 305 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:44:02 WARN TaskSetManager: Stage 308 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:44:06 WARN TaskSetManager: Stage 311 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:44:10 WARN TaskSetManager: Stage 314 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:44:14 WARN TaskSetManager: Stage 317 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:44:19 WARN TaskSetManager: Stage 320 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:44:23 WARN TaskSetManager: Stage 323 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:44:27 WARN TaskSetManager: Stage 326 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:44:31 WARN TaskSetManager: Stage 329 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:44:37 WARN TaskSetManager: Stage 332 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:44:41 WARN TaskSetManager: Stage 335 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:44:45 WARN TaskSetManager: Stage 338 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:44:49 WARN TaskSetManager: Stage 341 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:44:53 WARN TaskSetManager: Stage 344 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:44:57 WARN TaskSetManager: Stage 347 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:45:01 WARN TaskSetManager: Stage 350 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:45:05 WARN TaskSetManager: Stage 353 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:45:09 WARN TaskSetManager: Stage 356 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:45:14 WARN TaskSetManager: Stage 359 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:45:18 WARN TaskSetManager: Stage 362 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:45:22 WARN TaskSetManager: Stage 365 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:45:26 WARN TaskSetManager: Stage 368 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:45:30 WARN TaskSetManager: Stage 371 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:45:34 WARN TaskSetManager: Stage 374 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:45:38 WARN TaskSetManager: Stage 377 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:45:43 WARN TaskSetManager: Stage 380 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:45:47 WARN TaskSetManager: Stage 383 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:45:51 WARN TaskSetManager: Stage 386 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:45:55 WARN TaskSetManager: Stage 389 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:45:59 WARN TaskSetManager: Stage 392 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:46:03 WARN TaskSetManager: Stage 395 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:46:08 WARN TaskSetManager: Stage 398 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:46:12 WARN TaskSetManager: Stage 401 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:46:16 WARN TaskSetManager: Stage 404 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:46:20 WARN TaskSetManager: Stage 407 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:46:24 WARN TaskSetManager: Stage 410 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:46:28 WARN TaskSetManager: Stage 413 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:46:32 WARN TaskSetManager: Stage 416 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:46:36 WARN TaskSetManager: Stage 419 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:46:41 WARN TaskSetManager: Stage 422 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:46:45 WARN TaskSetManager: Stage 425 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:46:49 WARN TaskSetManager: Stage 428 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:46:53 WARN TaskSetManager: Stage 431 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:46:58 WARN TaskSetManager: Stage 434 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:47:02 WARN TaskSetManager: Stage 437 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:47:06 WARN TaskSetManager: Stage 440 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:47:10 WARN TaskSetManager: Stage 443 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:47:14 WARN TaskSetManager: Stage 446 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:47:18 WARN TaskSetManager: Stage 449 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:47:22 WARN TaskSetManager: Stage 452 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:47:26 WARN TaskSetManager: Stage 455 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:47:31 WARN TaskSetManager: Stage 458 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:47:35 WARN TaskSetManager: Stage 461 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:47:39 WARN TaskSetManager: Stage 464 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:47:43 WARN TaskSetManager: Stage 467 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:47:48 WARN TaskSetManager: Stage 470 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:47:52 WARN TaskSetManager: Stage 473 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:47:56 WARN TaskSetManager: Stage 476 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:48:00 WARN TaskSetManager: Stage 479 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:48:04 WARN TaskSetManager: Stage 482 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:48:10 WARN TaskSetManager: Stage 485 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:48:14 WARN TaskSetManager: Stage 488 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:48:18 WARN TaskSetManager: Stage 491 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:48:23 WARN TaskSetManager: Stage 494 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:48:27 WARN TaskSetManager: Stage 497 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:48:31 WARN TaskSetManager: Stage 500 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:48:35 WARN TaskSetManager: Stage 503 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "25/05/05 11:48:39 WARN TaskSetManager: Stage 506 contains a task of very large size (3519 KiB). The maximum recommended task size is 1000 KiB.\n", "[Stage 506:=======================>                               (8 + 11) / 19]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["Exception cols found at spark df: ['dataset_source', 'beat_class_1.0', 'beat_class_2.0', 'beat_class_3.0']\n", "Exception cols found at pandas df: []\n", "Missmatch found at: {'beat_vals': {'pandas': [0.0, 0.0006245853389206598], 'spark': [0.0, 0.0006245853146538138]}, 'scaled_beat_ts': {'pandas': [0.0, 0.004132231404958678], 'spark': [0.0, 0.00413223123177886]}, 'scaled_f_idx_s': {'pandas': np.float64(0.2024793388429752), 'spark': 0.20247933268547058}, 'scaled_f_idx_n': {'pandas': np.float64(0.6900826446280992), 'spark': 0.6900826692581177}, 'scaled_f_idx_d': {'pandas': np.float64(0.8512396694214877), 'spark': 0.8512396812438965}, 'f_val_n': {'pandas': np.float64(0.5183040862849153), 'spark': 0.5183041095733643}, 'f_val_d': {'pandas': np.float64(0.4996847965920798), 'spark': 0.49968481063842773}, 'o_s_cosine': {'pandas': np.float64(0.19845214205436873), 'spark': 0.19845213625701266}, 'o_s_sine': {'pandas': np.float64(0.9801105791256579), 'spark': 0.9801105802995028}, 'o_s_magnitude': {'pandas': np.float64(1.0202930376407988), 'spark': 1.0202930364188287}, 'o_s_angle': {'pandas': np.float64(1.3710179279282755), 'spark': 1.3710179338432775}, 'o_s_degrees': {'pandas': np.float64(78.55354090706146), 'spark': 78.55354124596612}, 's_n_cosine': {'pandas': np.float64(0.7114030303237159), 'spark': 0.7114030694965642}, 's_n_sine': {'pandas': np.float64(-0.7027842687811348), 'spark': -0.7027842291278786}, 's_n_magnitude': {'pandas': np.float64(0.685410779826515), 'spark': 0.6854107853620978}, 's_n_angle': {'pandas': np.float64(-0.779303740938838), 'spark': -0.7793036851993299}, 's_n_degrees': {'pandas': np.float64(-44.65081531455189), 'spark': -44.65081212091332}, 'n_d_cosine': {'pandas': np.float64(0.9933919049822103), 'spark': 0.9933918974469975}, 'n_d_sine': {'pandas': np.float64(-0.11477161284836715), 'spark': -0.11477167806847674}, 'n_d_magnitude': {'pandas': np.float64(0.16222904976890712), 'spark': 0.1622290381066626}, 'n_d_angle': {'pandas': np.float64(-0.11502509021711922), 'spark': -0.11502515587107666}, 'n_d_degrees': {'pandas': np.float64(-6.590452207552466), 'spark': -6.5904559692471345}, 'f_val_diff_s_d': {'pandas': np.float64(0.5003152034079201), 'spark': 0.5003151893615723}, 'f_val_diff_s_n': {'pandas': np.float64(0.4816959137150847), 'spark': 0.48169589042663574}, 'f_val_diff_d_n': {'pandas': np.float64(-0.018619289692835495), 'spark': -0.018619298934936523}, 'ip_value': {'pandas': np.float64(0.5380639074011243), 'spark': 0.5380638837814331}, 'adp_pi_tdn_coeff': {'pandas': np.float64(1.3324927909122128), 'spark': 1.3324928283691406}, 'auc2to5': {'pandas': np.float64(11.707379905811994), 'spark': 11.707380093808183}, 'auc0to2': {'pandas': np.float64(82.00026423175501), 'spark': 82.00026455721311}, 'a1': {'pandas': np.float64(0.4802513938740685), 'spark': 0.48025142669677734}, 'a2': {'pandas': np.float64(0.1473589432487943), 'spark': 0.1473589515686035}, 'arearatio': {'pandas': np.float64(0.3068370964217019), 'spark': 0.30683709277482996}, 'a0toslope': {'pandas': np.float64(0.025669198766496062), 'spark': 0.025669195652008057}, 'perc75': {'pandas': np.float64(0.5673815436157184), 'spark': 0.5673815310001373}, 'iqr': {'pandas': np.float64(0.08300004120367388), 'spark': 0.08300003409385681}}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["pandas_version_cols = ['id',\n", "                       'dataset_source',\n", "                       'ref_sbp', 'ref_dbp', 'ref_pp',\n", "                       'ts_episode', 'ppg_episode', 'ecg_episode',\n", "                       'pat_episode', 'hr_from_ecg_episode', 'hr_from_ppg_episode', 'pat_ratio',\n", "                       'last_three_beats', 'ts_last_three_beats',\n", "                       'beat_vals', 'beat_ts', 'scaled_beat_ts',\n", "                       'sbp', 'dbp',\n", "                       'height_inches', 'weight_lbs', 'BMI', 'age', 'gender_Male',\n", "                       'race_Asian', 'race_Black or African American', 'race_White',\n", "                       'tobacco',\n", "                       'hypertension_present', 'diabetes_present',\n", "                       'ppg_episode_min', 'ppg_episode_max', 'ppg_episode_mean',\n", "                       'ppg_episode_median', 'ppg_episode_std', 'ppg_episode_sum', 'ppg_episode_trend',\n", "                       'ppg_episode_error',\n", "                       'ecg_episode_min', 'ecg_episode_max', 'ecg_episode_mean',\n", "                       'ecg_episode_median', 'ecg_episode_std', 'ecg_episode_sum',\n", "                       'ecg_episode_trend', 'ecg_episode_error',\n", "                       'pat_ratio_min', 'pat_ratio_max', 'pat_ratio_mean', 'pat_ratio_median',\n", "                       'pat_ratio_std', 'pat_ratio_sum', 'pat_ratio_trend', 'pat_ratio_error',\n", "                       'pat_episode_min', 'pat_episode_max', 'pat_episode_mean',\n", "                       'pat_episode_median', 'pat_episode_std', 'pat_episode_sum',\n", "                       'pat_episode_trend', 'pat_episode_error',\n", "                       'hr_from_ecg_episode_min', 'hr_from_ecg_episode_max', 'hr_from_ecg_episode_mean',\n", "                       'hr_from_ecg_episode_median',\n", "                       'hr_from_ecg_episode_std', 'hr_from_ecg_episode_sum', 'hr_from_ecg_episode_trend',\n", "                       'hr_from_ecg_episode_error',\n", "                       'hr_from_ppg_episode_min', 'hr_from_ppg_episode_max', 'hr_from_ppg_episode_mean',\n", "                       'hr_from_ppg_episode_median',\n", "                       'hr_from_ppg_episode_std', 'hr_from_ppg_episode_sum', 'hr_from_ppg_episode_trend',\n", "                       'hr_from_ppg_episode_error',\n", "                       'p2pi',\n", "                       'f_idx_o', 'f_idx_s', 'f_idx_n', 'f_idx_d', 'scaled_f_idx_o', 'scaled_f_idx_s', 'scaled_f_idx_n',\n", "                       'scaled_f_idx_d',\n", "                       'f_val_o', 'f_val_s', 'f_val_n', 'f_val_d',\n", "                       'o_s_cosine', 'o_s_sine', 'o_s_magnitude', 'o_s_angle', 'o_s_degrees',\n", "                       's_n_cosine', 's_n_sine', 's_n_magnitude', 's_n_angle', 's_n_degrees',\n", "                       'n_d_cosine', 'n_d_sine', 'n_d_magnitude', 'n_d_angle', 'n_d_degrees',\n", "                       'beat_class_1.0', 'beat_class_2.0', 'beat_class_3.0',\n", "                       'f_val_diff_s_d', 'f_val_diff_s_n', 'f_val_diff_d_n', 'f_time_diff_s_d',\n", "                       'f_time_diff_s_n', 'f_time_diff_n_d',\n", "                       'tb1', 'tb2', 'tb1_tb2_p2pi_coeff', 'tb1_p2pi_coeff', 'tb2_p2pi_coeff',\n", "                       'ip_index', 'ip_value', 'time_ip', 'inverse_tip_tsp_coeff',\n", "                       'pulse_interval', 'adp_pi_tdn_coeff',\n", "                       'tp_p2pi_coeff', 'tdn_p2pi_coeff', 'inverse_time_s_notch',\n", "                       'fft_freq1', 'spl', 'auc2to5', 'auc0to2',\n", "                       'a1', 'a2', 'arearatio', 'a0toslope',\n", "                       'perc75', 'iqr']\n", "\n", "def first_two(val):\n", "    # treat Python lists by slicing; leave everything else alone\n", "    if isinstance(val, list):\n", "        return val[:2]\n", "    return val\n", "\n", "# iterate and print\n", "exception_cols_pandas = []\n", "exception_cols_spark = []\n", "difference_cols = {}\n", "for col in pandas_version_cols:\n", "    # fetch Spark value\n", "    try:\n", "        spark_val = multi_trigo.first()[col]\n", "    except Exception as e:\n", "        exception_cols_spark.append(col)\n", "        continue\n", "\n", "    # fetch pandas value\n", "    try:\n", "        pandas_val = df.at[0, col]\n", "    except KeyError as e:\n", "        exception_cols_pandas.append(col)\n", "        continue\n", "\n", "    # truncate lists\n", "    s = first_two(spark_val)\n", "    p = first_two(pandas_val)\n", "\n", "    # only print if different\n", "    if s != p:\n", "        difference_cols[col] = {'pandas': p, 'spark': s}\n", "\n", "print(f\"Exception cols found at spark df: {exception_cols_spark}\")\n", "print(f\"Exception cols found at pandas df: {exception_cols_pandas}\")\n", "print(f\"Missmatch found at: {difference_cols}\")"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["# The column selection may fail because some beat classes are missing when the processing has been done only over one row (the current \"testing\" mode). Run over all rows to get all beat classes.\n", "multi_trigo = multi_trigo.select(['id',\n", "                                  'dataset_source',\n", "                                  'ref_sbp', 'ref_dbp', 'ref_pp',\n", "                                  'ts_episode', 'ppg_episode', 'ecg_episode',\n", "                                  'pat_episode', 'hr_from_ecg_episode', 'hr_from_ppg_episode', 'pat_ratio',\n", "                                  'last_three_beats', 'ts_last_three_beats',\n", "                                  'beat_vals', 'beat_ts', 'scaled_beat_ts',\n", "                                  'sbp', 'dbp',\n", "                                  'height_inches', 'weight_lbs', 'BMI', 'age', 'gender_Male',\n", "                                  'race_Asian', 'race_Black or African American', 'race_White',\n", "                                  'tobacco',\n", "                                  'hypertension_present', 'diabetes_present',\n", "                                  'ppg_episode_min', 'ppg_episode_max', 'ppg_episode_mean',\n", "                                  'ppg_episode_median', 'ppg_episode_std', 'ppg_episode_sum', 'ppg_episode_trend',\n", "                                  'ppg_episode_error',\n", "                                  'ecg_episode_min', 'ecg_episode_max', 'ecg_episode_mean',\n", "                                  'ecg_episode_median', 'ecg_episode_std', 'ecg_episode_sum',\n", "                                  'ecg_episode_trend', 'ecg_episode_error',\n", "                                  'pat_ratio_min', 'pat_ratio_max', 'pat_ratio_mean', 'pat_ratio_median',\n", "                                  'pat_ratio_std', 'pat_ratio_sum', 'pat_ratio_trend', 'pat_ratio_error',\n", "                                  'pat_episode_min', 'pat_episode_max', 'pat_episode_mean',\n", "                                  'pat_episode_median', 'pat_episode_std', 'pat_episode_sum',\n", "                                  'pat_episode_trend', 'pat_episode_error',\n", "                                  'hr_from_ecg_episode_min', 'hr_from_ecg_episode_max', 'hr_from_ecg_episode_mean',\n", "                                  'hr_from_ecg_episode_median',\n", "                                  'hr_from_ecg_episode_std', 'hr_from_ecg_episode_sum', 'hr_from_ecg_episode_trend',\n", "                                  'hr_from_ecg_episode_error',\n", "                                  'hr_from_ppg_episode_min', 'hr_from_ppg_episode_max', 'hr_from_ppg_episode_mean',\n", "                                  'hr_from_ppg_episode_median',\n", "                                  'hr_from_ppg_episode_std', 'hr_from_ppg_episode_sum', 'hr_from_ppg_episode_trend',\n", "                                  'hr_from_ppg_episode_error',\n", "                                  'p2pi',\n", "                                  'f_idx_o', 'f_idx_s', 'f_idx_n', 'f_idx_d', 'scaled_f_idx_o', 'scaled_f_idx_s',\n", "                                  'scaled_f_idx_n', 'scaled_f_idx_d',\n", "                                  'f_val_o', 'f_val_s', 'f_val_n', 'f_val_d',\n", "                                  'o_s_cosine', 'o_s_sine', 'o_s_magnitude', 'o_s_angle', 'o_s_degrees',\n", "                                  's_n_cosine', 's_n_sine', 's_n_magnitude', 's_n_angle', 's_n_degrees',\n", "                                  'n_d_cosine', 'n_d_sine', 'n_d_magnitude', 'n_d_angle', 'n_d_degrees',\n", "                                  'beat_class_1', 'beat_class_2', 'beat_class_3',\n", "                                  'f_val_diff_s_d', 'f_val_diff_s_n', 'f_val_diff_d_n', 'f_time_diff_s_d',\n", "                                  'f_time_diff_s_n', 'f_time_diff_n_d',\n", "                                  'tb1', 'tb2', 'tb1_tb2_p2pi_coeff', 'tb1_p2pi_coeff', 'tb2_p2pi_coeff',\n", "                                  'ip_index', 'ip_value', 'time_ip', 'inverse_tip_tsp_coeff',\n", "                                  'pulse_interval', 'adp_pi_tdn_coeff',\n", "                                  'tp_p2pi_coeff', 'tdn_p2pi_coeff', 'inverse_time_s_notch',\n", "                                  'fft_freq1', 'spl', 'auc2to5', 'auc0to2',\n", "                                  'a1', 'a2', 'arearatio', 'a0toslope',\n", "                                  'perc75', 'iqr'])"], "metadata": {"collapsed": false}}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 0}