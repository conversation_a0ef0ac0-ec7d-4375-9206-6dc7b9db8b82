{"cells": [{"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-04-25T10:38:44.913934Z", "start_time": "2025-04-25T10:38:32.233473Z"}}, "source": ["from multiprocessing.connection import default_family\n", "\n", "from pyspark.sql import SparkSession\n", "from pyspark.sql import functions as F\n", "import pyspark.sql.types as T\n", "from pyspark.sql import DataFrame\n", "from functools import reduce\n", "import warnings\n", "import pandas as pd\n", "import numpy as np\n", "from pyspark.sql.types import StructField\n", "from scipy import signal as sp\n", "from scipy import interpolate\n", "import heartpy as hp\n", "import etl\n", "\n", "spark_session = SparkSession.builder \\\n", "            .appName(\"LocalTestApp\") \\\n", "            .master(\"local[*]\") \\\n", "            .getOrCreate()"], "outputs": [{"name": "stderr", "output_type": "stream", "text": ["25/04/25 10:38:41 WARN Utils: Your hostname, ubuntu-jammy resolves to a loopback address: *********; using ********* instead (on interface enp0s3)\n", "25/04/25 10:38:41 WARN Utils: Set SPARK_LOCAL_IP if you need to bind to another address\n", "Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n", "25/04/25 10:38:42 WARN NativeCodeLoader: Unable to load native-hadoop library for your platform... using builtin-java classes where applicable\n"]}], "execution_count": 1}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["def float_spark(x):\n", "    return [float(e) for e in x]\n", "\n", "for col in ['sbp', 'dbp', 'ts', 'abp', 'ppg_butter', 'ecg_butter', 'ecg_enhanced',\n", "       'ppg_butter_peaks', 'ppg_butter_valleys', 'ecg_enhanced_peaks',\n", "       'ecg_enhanced_valleys', 'pat', 'hr_from_ecg', 'hr_from_ppg']:\n", "    hr_calc_from_ppgpd[col] = hr_calc_from_ppgpd[col].apply(float_spark)"], "id": "ebc8bbc8948e5c5d"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-23T12:00:55.972545Z", "start_time": "2025-04-23T12:00:54.942034Z"}}, "cell_type": "code", "source": ["test_data = [(1, 0, 0, 1, 1),\n", "             (2, 0, 0, 1, -1),\n", "             (3, 0, 0, -1, 1),\n", "             (4, 0, 0, 0, 1),\n", "             (5, 0, 0, 1, 0)]\n", "schema = T.StructType([\n", "    <PERSON><PERSON>(\"id\", <PERSON>.<PERSON>(), True),\n", "    <PERSON><PERSON>(\"o_values\", T.<PERSON>ger<PERSON>(), True),\n", "    <PERSON><PERSON>(\"o_timestamps\", T.<PERSON>(), True),\n", "    <PERSON><PERSON>(\"s_values\", T.<PERSON>ger<PERSON>(), True),\n", "    <PERSON><PERSON>(\"s_timestamps\", T.<PERSON>ger<PERSON>(), True)\n", "])\n", "\n", "df = spark_session.createDataFrame(test_data, schema)\n", "\n", "df.show()"], "id": "c6651c4e796d7abf", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["+---+--------+------------+--------+------------+\n", "| id|o_values|o_timestamps|s_values|s_timestamps|\n", "+---+--------+------------+--------+------------+\n", "|  1|       0|           0|       1|           1|\n", "|  2|       0|           0|       1|          -1|\n", "|  3|       0|           0|      -1|           1|\n", "|  4|       0|           0|       0|           1|\n", "|  5|       0|           0|       1|           0|\n", "+---+--------+------------+--------+------------+\n", "\n"]}], "execution_count": 23}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-25T11:11:26.881092Z", "start_time": "2025-04-25T11:11:26.829295Z"}}, "cell_type": "code", "source": ["df = pd.read_pickle(\"processed_empl_sample.pkl\")\n", "\n", "# Test for nan presence robustness\n", "new_row = {\n", "    'beat_ts': [0.0],\n", "    'f_idx_o': 10,\n", "    'f_idx_s': 20,\n", "    'f_idx_n': 30,\n", "    'f_idx_d': 40,\n", "    'f_val_o': 50.0,\n", "    'f_val_s': 60.0,\n", "    'f_val_n': 70.0,\n", "    'f_val_d': np.nan\n", "}\n", "df.loc[len(df)] = new_row\n", "\n", "# Test for filtering robustness\n", "copied_row = df.loc[4].copy()\n", "\n", "copied_row['f_val_s'] = 0.5\n", "copied_row['f_val_n'] = 1.0\n", "copied_row['f_val_d'] = 0.7\n", "\n", "df.loc[len(df)] = copied_row\n", "\n", "lista = [[float(e) for e in range(100)],\n", "         [float(e) for e in range(100, 200)],\n", "         [float(e) for e in range(200, 300)],\n", "         [float(e) for e in range(300, 400)],\n", "         [float(e) for e in range(400, 500)],\n", "         [float(e) for e in range(500, 600)],\n", "         [float(e) for e in range(600, 700)]]\n", "# for idx in range(len(df)):\n", "#     lista.append([float(e) for e in df.iloc[idx, 0]])\n", "\n", "df['beat_ts'] = lista\n", "df"], "id": "af14c55256b95d46", "outputs": [{"data": {"text/plain": ["                                             beat_ts  f_idx_o  f_idx_s  \\\n", "0  [0.0, 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, ...        0       40   \n", "1  [100.0, 101.0, 102.0, 103.0, 104.0, 105.0, 106...        0       21   \n", "2  [200.0, 201.0, 202.0, 203.0, 204.0, 205.0, 206...        0       16   \n", "3  [300.0, 301.0, 302.0, 303.0, 304.0, 305.0, 306...        0       20   \n", "4  [400.0, 401.0, 402.0, 403.0, 404.0, 405.0, 406...        0       29   \n", "5  [500.0, 501.0, 502.0, 503.0, 504.0, 505.0, 506...       10       20   \n", "6  [600.0, 601.0, 602.0, 603.0, 604.0, 605.0, 606...        0       29   \n", "\n", "   f_idx_n  f_idx_d    f_val_o  f_val_s    f_val_n   f_val_d  \n", "0     83.0     98.0   0.023040      1.0   0.320012  0.432854  \n", "1     67.0     82.0   0.000000      1.0   0.484306  0.397646  \n", "2     29.0     54.0   0.092234      1.0   0.434086  0.475492  \n", "3     63.0     77.0   0.615603      1.0   0.740480  0.856158  \n", "4     74.0     82.0   0.025845      1.0   0.390896  0.282483  \n", "5     30.0     40.0  50.000000     60.0  70.000000       NaN  \n", "6     74.0     82.0   0.025845      0.5   1.000000  0.700000  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>beat_ts</th>\n", "      <th>f_idx_o</th>\n", "      <th>f_idx_s</th>\n", "      <th>f_idx_n</th>\n", "      <th>f_idx_d</th>\n", "      <th>f_val_o</th>\n", "      <th>f_val_s</th>\n", "      <th>f_val_n</th>\n", "      <th>f_val_d</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>[0.0, 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, ...</td>\n", "      <td>0</td>\n", "      <td>40</td>\n", "      <td>83.0</td>\n", "      <td>98.0</td>\n", "      <td>0.023040</td>\n", "      <td>1.0</td>\n", "      <td>0.320012</td>\n", "      <td>0.432854</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>[100.0, 101.0, 102.0, 103.0, 104.0, 105.0, 106...</td>\n", "      <td>0</td>\n", "      <td>21</td>\n", "      <td>67.0</td>\n", "      <td>82.0</td>\n", "      <td>0.000000</td>\n", "      <td>1.0</td>\n", "      <td>0.484306</td>\n", "      <td>0.397646</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>[200.0, 201.0, 202.0, 203.0, 204.0, 205.0, 206...</td>\n", "      <td>0</td>\n", "      <td>16</td>\n", "      <td>29.0</td>\n", "      <td>54.0</td>\n", "      <td>0.092234</td>\n", "      <td>1.0</td>\n", "      <td>0.434086</td>\n", "      <td>0.475492</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>[300.0, 301.0, 302.0, 303.0, 304.0, 305.0, 306...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>63.0</td>\n", "      <td>77.0</td>\n", "      <td>0.615603</td>\n", "      <td>1.0</td>\n", "      <td>0.740480</td>\n", "      <td>0.856158</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>[400.0, 401.0, 402.0, 403.0, 404.0, 405.0, 406...</td>\n", "      <td>0</td>\n", "      <td>29</td>\n", "      <td>74.0</td>\n", "      <td>82.0</td>\n", "      <td>0.025845</td>\n", "      <td>1.0</td>\n", "      <td>0.390896</td>\n", "      <td>0.282483</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>[500.0, 501.0, 502.0, 503.0, 504.0, 505.0, 506...</td>\n", "      <td>10</td>\n", "      <td>20</td>\n", "      <td>30.0</td>\n", "      <td>40.0</td>\n", "      <td>50.000000</td>\n", "      <td>60.0</td>\n", "      <td>70.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>[600.0, 601.0, 602.0, 603.0, 604.0, 605.0, 606...</td>\n", "      <td>0</td>\n", "      <td>29</td>\n", "      <td>74.0</td>\n", "      <td>82.0</td>\n", "      <td>0.025845</td>\n", "      <td>0.5</td>\n", "      <td>1.000000</td>\n", "      <td>0.700000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "execution_count": 35}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-25T11:17:32.360315Z", "start_time": "2025-04-25T11:17:31.255819Z"}}, "cell_type": "code", "source": ["schema = T.StructType([\n", "    <PERSON><PERSON>(\"beat_ts\", T.<PERSON>(T.FloatType()), True),\n", "    <PERSON><PERSON>(\"f_idx_o\", T.<PERSON>(), True),\n", "    <PERSON><PERSON>(\"f_idx_s\", T.<PERSON>(), True),\n", "    <PERSON><PERSON>(\"f_idx_n\", T.FloatType(), True),\n", "    <PERSON><PERSON>(\"f_idx_d\", T.FloatType(), True),\n", "    <PERSON><PERSON>(\"f_val_o\", T.<PERSON>loatT<PERSON>(), True),\n", "    <PERSON><PERSON>(\"f_val_s\", T.FloatT<PERSON>(), True),\n", "    <PERSON><PERSON>(\"f_val_n\", T.FloatT<PERSON>(), True),\n", "    <PERSON><PERSON>(\"f_val_d\", T.FloatT<PERSON>(), True)\n", "])\n", "\n", "df_spark = spark_session.createDataFrame(df, schema)\n", "\n", "cols_to_check = [\n", "    beat_ts_col,\n", "    f_idx_o_col, f_idx_s_col, f_idx_n_col, f_idx_d_col,\n", "    f_val_o_col, f_val_s_col, f_val_n_col, f_val_d_col\n", "]\n", "\n", "df_spark = df_spark.na.drop(subset=(cols_to_check))\n", "\n", "df_spark.show()"], "id": "e1243a08d263411e", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["+--------------------+-------+-------+-------+-------+-----------+-------+----------+----------+\n", "|             beat_ts|f_idx_o|f_idx_s|f_idx_n|f_idx_d|    f_val_o|f_val_s|   f_val_n|   f_val_d|\n", "+--------------------+-------+-------+-------+-------+-----------+-------+----------+----------+\n", "|[0.0, 1.0, 2.0, 3...|      0|     40|   83.0|   98.0|0.023039522|    1.0|0.32001162| 0.4328536|\n", "|[100.0, 101.0, 10...|      0|     21|   67.0|   82.0|        0.0|    1.0|0.48430574| 0.3976458|\n", "|[200.0, 201.0, 20...|      0|     16|   29.0|   54.0| 0.09223417|    1.0| 0.4340864| 0.4754919|\n", "|[300.0, 301.0, 30...|      0|     20|   63.0|   77.0|  0.6156027|    1.0|0.74048036|0.85615796|\n", "|[400.0, 401.0, 40...|      0|     29|   74.0|   82.0|0.025845157|    1.0|0.39089572|0.28248277|\n", "|[600.0, 601.0, 60...|      0|     29|   74.0|   82.0|0.025845157|    0.5|       1.0|       0.7|\n", "+--------------------+-------+-------+-------+-------+-----------+-------+----------+----------+\n", "\n"]}], "execution_count": 42}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-25T10:51:54.657714Z", "start_time": "2025-04-25T10:51:53.925477Z"}}, "cell_type": "code", "source": "df_spark.collect()[0]['scaled_beat_ts'][int(83.0)]", "id": "3ac51a79a2e21e2b", "outputs": [{"data": {"text/plain": ["0.0"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "execution_count": 21}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-25T11:12:18.842473Z", "start_time": "2025-04-25T11:12:18.798949Z"}}, "cell_type": "code", "source": ["annotator_sequence = []\n", "models_sequence = []\n", "s_n_filter_threshold = (-90, 0)\n", "n_d_filter_threshold = (0, 90)\n", "beat_ts_col = 'beat_ts'\n", "f_idx_o_col = 'f_idx_o'\n", "f_idx_s_col = 'f_idx_s'\n", "f_idx_n_col = 'f_idx_n'\n", "f_idx_d_col = 'f_idx_d'\n", "f_val_o_col = 'f_val_o'\n", "f_val_s_col = 'f_val_s'\n", "f_val_n_col = 'f_val_n'\n", "f_val_d_col = 'f_val_d'\n", "\n", "# Add the minmax scaler for the timestamps\n", "annotator_sequence.append(\n", "    etl.annotators.signals.standardization.StandardizeSignal() \\\n", "        .set_input_cols([beat_ts_col]) \\\n", "        .set_output_cols(['scaled_beat_ts']) \\\n", "        .set_mode('by_row') \\\n", "        .set_minmax(True)\n", ")\n", "\n", "# Add the new (scaled) indices for the fiducial points\n", "for index_col_name in [f_idx_o_col,\n", "                       f_idx_s_col,\n", "                       f_idx_n_col,\n", "                       f_idx_d_col]:\n", "    annotator_sequence.append(\n", "        etl.annotators.dataprep.basic.ApplyFunc() \\\n", "            .set_function(lambda x, y: float(x[int(y)])) \\\n", "            .set_input_cols(['scaled_beat_ts', index_col_name]) \\\n", "            .set_output_cols(['scaled_' + index_col_name]) \\\n", "            .set_output_type(T.FloatType())\n", "    )\n", "\n", "# Calculate the trigonometric features with the scaled time axis:\n", "trigonometric_features_1 = etl.annotators.features.fromsignals.TrigonometricExtractor() \\\n", "    .set_input_cols(['scaled_' + f_idx_o_col,\n", "                     f_val_o_col,\n", "                     'scaled_' + f_idx_s_col,\n", "                     f_val_s_col]) \\\n", "    .set_output_cols(['o_s_cosine', 'o_s_sine', 'o_s_magnitude', 'o_s_angle', 'o_s_degrees'])\n", "\n", "trigonometric_features_2 = etl.annotators.features.fromsignals.TrigonometricExtractor() \\\n", "    .set_input_cols(['scaled_' + f_idx_s_col,\n", "                     f_val_s_col,\n", "                     'scaled_' + f_idx_n_col,\n", "                     f_val_n_col]) \\\n", "    .set_output_cols(['s_n_cosine', 's_n_sine', 's_n_magnitude', 's_n_angle', 's_n_degrees'])\n", "\n", "trigonometric_features_3 = etl.annotators.features.fromsignals.TrigonometricExtractor() \\\n", "    .set_input_cols(['scaled_' + f_idx_n_col,\n", "                     f_val_n_col,\n", "                     'scaled_' + f_idx_d_col,\n", "                     f_val_d_col]) \\\n", "    .set_output_cols(['n_d_cosine', 'n_d_sine', 'n_d_magnitude', 'n_d_angle', 'n_d_degrees'])\n", "\n", "annotator_sequence.extend([trigonometric_features_1,\n", "                                       trigonometric_features_2,\n", "                                       trigonometric_features_3])\n", "\n", "# Apply the filters if thresholds have been set:\n", "if s_n_filter_threshold is not None:\n", "    annotator_sequence.extend([\n", "        etl.annotators.dataprep.basic.FilterValues() \\\n", "            .set_input_cols(['s_n_degrees']) \\\n", "            .set_conditions(['>']) \\\n", "            .set_conditions_values([s_n_filter_threshold[0]]),\n", "        etl.annotators.dataprep.basic.FilterValues() \\\n", "            .set_input_cols(['s_n_degrees']) \\\n", "            .set_conditions(['<']) \\\n", "            .set_conditions_values([s_n_filter_threshold[1]])\n", "    ]\n", "    )\n", "\n", "if n_d_filter_threshold is not None:\n", "    annotator_sequence.extend([\n", "        etl.annotators.dataprep.basic.FilterValues() \\\n", "            .set_input_cols(['n_d_degrees']) \\\n", "            .set_conditions(['>']) \\\n", "            .set_conditions_values([n_d_filter_threshold[0]]),\n", "        etl.annotators.dataprep.basic.FilterValues() \\\n", "            .set_input_cols(['n_d_degrees']) \\\n", "            .set_conditions(['<']) \\\n", "            .set_conditions_values([n_d_filter_threshold[1]])\n", "    ]\n", "    )\n", "\n", "for i in annotator_sequence:\n", "    models_sequence.append(i.fit())"], "id": "898acacbfe40816c", "outputs": [], "execution_count": 38}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-25T11:12:21.221603Z", "start_time": "2025-04-25T11:12:19.952610Z"}}, "cell_type": "code", "source": ["# cols_to_check = [\n", "#     beat_ts_col,\n", "#     f_idx_o_col, f_idx_s_col, f_idx_n_col, f_idx_d_col,\n", "#     f_val_o_col, f_val_s_col, f_val_n_col, f_val_d_col\n", "# ]\n", "#\n", "# df_with_nans = df[df[cols_to_check].isna().any(axis=1)]\n", "# df_with_nans['error_trigo'] = 1\n", "# df = df[df[cols_to_check].notna().all(axis=1)]\n", "\n", "for i in range(len(models_sequence)):\n", "    df_spark = models_sequence[i].transform(df_spark)\n", "\n", "# df['error_trigo'] = 0\n", "# df = pd.concat([df, df_with_nans], axis=0)"], "id": "ba3e71e870c238e5", "outputs": [], "execution_count": 39}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-25T11:12:23.954020Z", "start_time": "2025-04-25T11:12:21.361657Z"}}, "cell_type": "code", "source": "df_spark.show()", "id": "18abfac06b1b7eba", "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["+--------------------+-------+-------+-------+-------+-----------+-------+----------+----------+--------------------+--------------+--------------+--------------+--------------+-------------------+-------------------+------------------+------------------+-----------------+------------------+-------------------+-------------------+-------------------+-------------------+-------------------+------------------+------------------+------------------+-----------------+\n", "|             beat_ts|f_idx_o|f_idx_s|f_idx_n|f_idx_d|    f_val_o|f_val_s|   f_val_n|   f_val_d|      scaled_beat_ts|scaled_f_idx_o|scaled_f_idx_s|scaled_f_idx_n|scaled_f_idx_d|      o_s_magnitude|         o_s_cosine|          o_s_sine|         o_s_angle|      o_s_degrees|     s_n_magnitude|         s_n_cosine|           s_n_sine|          s_n_angle|        s_n_degrees|      n_d_magnitude|        n_d_cosine|          n_d_sine|         n_d_angle|      n_d_degrees|\n", "+--------------------+-------+-------+-------+-------+-----------+-------+----------+----------+--------------------+--------------+--------------+--------------+--------------+-------------------+-------------------+------------------+------------------+-----------------+------------------+-------------------+-------------------+-------------------+-------------------+-------------------+------------------+------------------+------------------+-----------------+\n", "|[0.0, 1.0, 2.0, 3...|      0|     40|   83.0|   98.0|0.023039522|    1.0|0.32001162| 0.4328536|[0.0, 0.01010101,...|           0.0|     0.4040404|    0.83838385|      0.989899| 1.0572135175406745|  0.382174829880556|0.9240900385816135|1.1786476897351403|67.53153815466847|  0.80686953192084| 0.5383069257652887|-0.8427488675003509|-1.0023694996428443| -57.43154184217506|  0.188918365938607|0.8020137453315183| 0.597305576986613|0.6401373168400625|36.67716656376434|\n", "|[200.0, 201.0, 20...|      0|     16|   29.0|   54.0| 0.09223417|    1.0| 0.4340864| 0.4754919|[0.0, 0.01010101,...|           0.0|    0.16161616|     0.2929293|    0.54545456| 0.9220404230204332|0.17528099357750532|0.9845184474099419|1.3946051536768942|79.90498939287961|0.5809486150374117|0.22603226319948727|-0.9741198160354392|-1.3427937325450099| -76.93641363144772|0.25589729839723324|0.9868227275819075|0.1618051430820557|0.1625196273025583|9.311688732475695|\n", "|[300.0, 301.0, 30...|      0|     20|   63.0|   77.0|  0.6156027|    1.0|0.74048036|0.85615796|[0.0, 0.01010101,...|           0.0|     0.2020202|     0.6363636|     0.7777778|0.43425046475473733| 0.4652158477731643|0.8851972746121036| 1.086917889762173|62.27580776063819|0.5059690254746353| 0.8584387690772516|-0.5129160552616119|-0.5385782851168154|-30.858262674587042|0.18269995134154246|0.7740241004906677|0.6331561354512903|0.6856240124734074|39.28336224755115|\n", "+--------------------+-------+-------+-------+-------+-----------+-------+----------+----------+--------------------+--------------+--------------+--------------+--------------+-------------------+-------------------+------------------+------------------+-----------------+------------------+-------------------+-------------------+-------------------+-------------------+-------------------+------------------+------------------+------------------+-----------------+\n", "\n"]}], "execution_count": 40}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-23T12:11:26.110098Z", "start_time": "2025-04-23T12:11:24.877243Z"}}, "cell_type": "code", "source": ["input_cols = ['o_timestamps', 'o_values',  's_timestamps', 's_values']\n", "output_cols = ['o_s_cosine', 'o_s_sine', 'o_s_magnitude', 'o_s_angle', 'o_s_degrees']\n", "output_cols_names = output_cols\n", "\n", "df = df.withColumn(\"v_x\", <PERSON>.col(input_cols[2]) - F.col(input_cols[0]))\n", "df = df.withColumn(\"v_y\", <PERSON>.col(input_cols[3]) - F.col(input_cols[1]))\n", "df = df.withColumn(output_cols_names[2], F.sqrt(F.col(\"v_x\")**2 + F.col(\"v_y\")**2))\n", "df = df.withColumn(output_cols_names[0], F.col(\"v_x\")/F.col(output_cols_names[2]))\n", "df = df.withColumn(output_cols_names[1], <PERSON>.col(\"v_y\")/F.col(output_cols_names[2]))\n", "df = df.withColumn(output_cols_names[3], <PERSON><PERSON>atan2(<PERSON><PERSON>col(output_cols_names[1]), <PERSON>.col(output_cols_names[0])))\n", "df = df.withColumn(output_cols_names[4], F.degrees(F.col(output_cols_names[3])))\n", "df.show()"], "id": "2d7b77130889adf0", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["+---+--------+------------+--------+------------+---+---+------------------+-------------------+-------------------+-------------------+-----------+\n", "| id|o_values|o_timestamps|s_values|s_timestamps|v_x|v_y|     o_s_magnitude|         o_s_cosine|           o_s_sine|          o_s_angle|o_s_degrees|\n", "+---+--------+------------+--------+------------+---+---+------------------+-------------------+-------------------+-------------------+-----------+\n", "|  1|       0|           0|       1|           1|  1|  1|1.4142135623730951| 0.7071067811865475| 0.7071067811865475| 0.7853981633974483|       45.0|\n", "|  2|       0|           0|       1|          -1| -1|  1|1.4142135623730951|-0.7071067811865475| 0.7071067811865475|  2.356194490192345|      135.0|\n", "|  3|       0|           0|      -1|           1|  1| -1|1.4142135623730951| 0.7071067811865475|-0.7071067811865475|-0.7853981633974483|      -45.0|\n", "|  4|       0|           0|       0|           1|  1|  0|               1.0|                1.0|                0.0|                0.0|        0.0|\n", "|  5|       0|           0|       1|           0|  0|  1|               1.0|                0.0|                1.0| 1.5707963267948966|       90.0|\n", "+---+--------+------------+--------+------------+---+---+------------------+-------------------+-------------------+-------------------+-----------+\n", "\n"]}], "execution_count": 27}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-23T12:13:58.276094Z", "start_time": "2025-04-23T12:13:56.935313Z"}}, "cell_type": "code", "source": ["df = df.drop(\"v_x\", \"v_y\")\n", "df.show()"], "id": "7e3b1bf22ec51f56", "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["+---+--------+------------+--------+------------+------------------+-------------------+-------------------+-------------------+-----------+\n", "| id|o_values|o_timestamps|s_values|s_timestamps|     o_s_magnitude|         o_s_cosine|           o_s_sine|          o_s_angle|o_s_degrees|\n", "+---+--------+------------+--------+------------+------------------+-------------------+-------------------+-------------------+-----------+\n", "|  1|       0|           0|       1|           1|1.4142135623730951| 0.7071067811865475| 0.7071067811865475| 0.7853981633974483|       45.0|\n", "|  2|       0|           0|       1|          -1|1.4142135623730951|-0.7071067811865475| 0.7071067811865475|  2.356194490192345|      135.0|\n", "|  3|       0|           0|      -1|           1|1.4142135623730951| 0.7071067811865475|-0.7071067811865475|-0.7853981633974483|      -45.0|\n", "|  4|       0|           0|       0|           1|               1.0|                1.0|                0.0|                0.0|        0.0|\n", "|  5|       0|           0|       1|           0|               1.0|                0.0|                1.0| 1.5707963267948966|       90.0|\n", "+---+--------+------------+--------+------------+------------------+-------------------+-------------------+-------------------+-----------+\n", "\n"]}], "execution_count": 28}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-23T11:46:20.208197Z", "start_time": "2025-04-23T11:46:18.013185Z"}}, "cell_type": "code", "source": ["\n", "\n", "# Degrees\n", "udf_func = F.udf(lambda x: np.degrees(x), T.FloatType())\n", "\n", "df = df.withColumn(output_cols_names[4], udf_func(df[output_cols_names[3]]))\n", "\n", "df.show()"], "id": "d0315e552895d466", "outputs": [{"name": "stderr", "output_type": "stream", "text": ["25/04/23 11:46:19 ERROR PythonUDFRunner: Python worker exited unexpectedly (crashed)\n", "org.apache.spark.api.python.PythonException: Traceback (most recent call last):\n", "  File \"/home/<USER>/pyenvs/base3/lib/python3.10/site-packages/pyspark/python/lib/pyspark.zip/pyspark/worker.py\", line 1225, in main\n", "    eval_type = read_int(infile)\n", "  File \"/home/<USER>/pyenvs/base3/lib/python3.10/site-packages/pyspark/python/lib/pyspark.zip/pyspark/serializers.py\", line 596, in read_int\n", "    raise EOFError\n", "EOFError\n", "\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.handlePythonException(PythonRunner.scala:572)\n", "\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$1.read(PythonUDFRunner.scala:94)\n", "\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$1.read(PythonUDFRunner.scala:75)\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.hasNext(PythonRunner.scala:525)\n", "\tat org.apache.spark.InterruptibleIterator.hasNext(InterruptibleIterator.scala:37)\n", "\tat scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:491)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage3.processNext(Unknown Source)\n", "\tat org.apache.spark.sql.execution.BufferedRowIterator.hasNext(BufferedRowIterator.java:43)\n", "\tat org.apache.spark.sql.execution.WholeStageCodegenEvaluatorFactory$WholeStageCodegenPartitionEvaluator$$anon$1.hasNext(WholeStageCodegenEvaluatorFactory.scala:43)\n", "\tat org.apache.spark.sql.execution.SparkPlan.$anonfun$getByteArrayRdd$1(SparkPlan.scala:388)\n", "\tat org.apache.spark.rdd.RDD.$anonfun$mapPartitionsInternal$2(RDD.scala:893)\n", "\tat org.apache.spark.rdd.RDD.$anonfun$mapPartitionsInternal$2$adapted(RDD.scala:893)\n", "\tat org.apache.spark.rdd.MapPartitionsRDD.compute(MapPartitionsRDD.scala:52)\n", "\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:367)\n", "\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:331)\n", "\tat org.apache.spark.scheduler.ResultTask.runTask(ResultTask.scala:93)\n", "\tat org.apache.spark.TaskContext.runTaskWithListeners(TaskContext.scala:166)\n", "\tat org.apache.spark.scheduler.Task.run(Task.scala:141)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$4(Executor.scala:620)\n", "\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally(SparkErrorUtils.scala:64)\n", "\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally$(SparkErrorUtils.scala:61)\n", "\tat org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:94)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:623)\n", "\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\n", "\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\n", "\tat java.lang.Thread.run(Thread.java:750)\n", "Caused by: org.apache.spark.api.python.PythonException: Traceback (most recent call last):\n", "  File \"/tmp/ipykernel_5194/3458323768.py\", line 24, in cosine_of_angle\n", "TypeError: only size-1 arrays can be converted to Python scalars\n", "\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.handlePythonException(PythonRunner.scala:572)\n", "\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$1.read(PythonUDFRunner.scala:94)\n", "\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$1.read(PythonUDFRunner.scala:75)\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.hasNext(PythonRunner.scala:525)\n", "\tat org.apache.spark.InterruptibleIterator.hasNext(InterruptibleIterator.scala:37)\n", "\tat scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:491)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage2.processNext(Unknown Source)\n", "\tat org.apache.spark.sql.execution.BufferedRowIterator.hasNext(BufferedRowIterator.java:43)\n", "\tat org.apache.spark.sql.execution.WholeStageCodegenEvaluatorFactory$WholeStageCodegenPartitionEvaluator$$anon$1.hasNext(WholeStageCodegenEvaluatorFactory.scala:43)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.ContextAwareIterator.hasNext(ContextAwareIterator.scala:39)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$GroupedIterator.fill(Iterator.scala:1211)\n", "\tat scala.collection.Iterator$GroupedIterator.hasNext(Iterator.scala:1217)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator.foreach(Iterator.scala:943)\n", "\tat scala.collection.Iterator.foreach$(Iterator.scala:943)\n", "\tat scala.collection.AbstractIterator.foreach(Iterator.scala:1431)\n", "\tat org.apache.spark.api.python.PythonRDD$.writeIteratorToStream(PythonRDD.scala:322)\n", "\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$PythonUDFWriterThread.writeIteratorToStream(PythonUDFRunner.scala:58)\n", "\tat org.apache.spark.api.python.BasePythonRunner$WriterThread.$anonfun$run$1(PythonRunner.scala:451)\n", "\tat org.apache.spark.util.Utils$.logUncaughtExceptions(Utils.scala:1928)\n", "\tat org.apache.spark.api.python.BasePythonRunner$WriterThread.run(PythonRunner.scala:282)\n", "25/04/23 11:46:19 ERROR PythonUDFRunner: This may have been caused by a prior exception:\n", "org.apache.spark.api.python.PythonException: Traceback (most recent call last):\n", "  File \"/tmp/ipykernel_5194/3458323768.py\", line 24, in cosine_of_angle\n", "TypeError: only size-1 arrays can be converted to Python scalars\n", "\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.handlePythonException(PythonRunner.scala:572)\n", "\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$1.read(PythonUDFRunner.scala:94)\n", "\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$1.read(PythonUDFRunner.scala:75)\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.hasNext(PythonRunner.scala:525)\n", "\tat org.apache.spark.InterruptibleIterator.hasNext(InterruptibleIterator.scala:37)\n", "\tat scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:491)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage2.processNext(Unknown Source)\n", "\tat org.apache.spark.sql.execution.BufferedRowIterator.hasNext(BufferedRowIterator.java:43)\n", "\tat org.apache.spark.sql.execution.WholeStageCodegenEvaluatorFactory$WholeStageCodegenPartitionEvaluator$$anon$1.hasNext(WholeStageCodegenEvaluatorFactory.scala:43)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.ContextAwareIterator.hasNext(ContextAwareIterator.scala:39)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$GroupedIterator.fill(Iterator.scala:1211)\n", "\tat scala.collection.Iterator$GroupedIterator.hasNext(Iterator.scala:1217)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator.foreach(Iterator.scala:943)\n", "\tat scala.collection.Iterator.foreach$(Iterator.scala:943)\n", "\tat scala.collection.AbstractIterator.foreach(Iterator.scala:1431)\n", "\tat org.apache.spark.api.python.PythonRDD$.writeIteratorToStream(PythonRDD.scala:322)\n", "\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$PythonUDFWriterThread.writeIteratorToStream(PythonUDFRunner.scala:58)\n", "\tat org.apache.spark.api.python.BasePythonRunner$WriterThread.$anonfun$run$1(PythonRunner.scala:451)\n", "\tat org.apache.spark.util.Utils$.logUncaughtExceptions(Utils.scala:1928)\n", "\tat org.apache.spark.api.python.BasePythonRunner$WriterThread.run(PythonRunner.scala:282)\n", "25/04/23 11:46:19 ERROR Executor: Exception in task 0.0 in stage 44.0 (TID 89)\n", "org.apache.spark.api.python.PythonException: Traceback (most recent call last):\n", "  File \"/tmp/ipykernel_5194/3458323768.py\", line 24, in cosine_of_angle\n", "TypeError: only size-1 arrays can be converted to Python scalars\n", "\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.handlePythonException(PythonRunner.scala:572)\n", "\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$1.read(PythonUDFRunner.scala:94)\n", "\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$1.read(PythonUDFRunner.scala:75)\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.hasNext(PythonRunner.scala:525)\n", "\tat org.apache.spark.InterruptibleIterator.hasNext(InterruptibleIterator.scala:37)\n", "\tat scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:491)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage2.processNext(Unknown Source)\n", "\tat org.apache.spark.sql.execution.BufferedRowIterator.hasNext(BufferedRowIterator.java:43)\n", "\tat org.apache.spark.sql.execution.WholeStageCodegenEvaluatorFactory$WholeStageCodegenPartitionEvaluator$$anon$1.hasNext(WholeStageCodegenEvaluatorFactory.scala:43)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.ContextAwareIterator.hasNext(ContextAwareIterator.scala:39)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$GroupedIterator.fill(Iterator.scala:1211)\n", "\tat scala.collection.Iterator$GroupedIterator.hasNext(Iterator.scala:1217)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator.foreach(Iterator.scala:943)\n", "\tat scala.collection.Iterator.foreach$(Iterator.scala:943)\n", "\tat scala.collection.AbstractIterator.foreach(Iterator.scala:1431)\n", "\tat org.apache.spark.api.python.PythonRDD$.writeIteratorToStream(PythonRDD.scala:322)\n", "\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$PythonUDFWriterThread.writeIteratorToStream(PythonUDFRunner.scala:58)\n", "\tat org.apache.spark.api.python.BasePythonRunner$WriterThread.$anonfun$run$1(PythonRunner.scala:451)\n", "\tat org.apache.spark.util.Utils$.logUncaughtExceptions(Utils.scala:1928)\n", "\tat org.apache.spark.api.python.BasePythonRunner$WriterThread.run(PythonRunner.scala:282)\n", "25/04/23 11:46:19 WARN TaskSetManager: Lost task 0.0 in stage 44.0 (TID 89) (********* executor driver): org.apache.spark.api.python.PythonException: Traceback (most recent call last):\n", "  File \"/tmp/ipykernel_5194/3458323768.py\", line 24, in cosine_of_angle\n", "TypeError: only size-1 arrays can be converted to Python scalars\n", "\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.handlePythonException(PythonRunner.scala:572)\n", "\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$1.read(PythonUDFRunner.scala:94)\n", "\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$1.read(PythonUDFRunner.scala:75)\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.hasNext(PythonRunner.scala:525)\n", "\tat org.apache.spark.InterruptibleIterator.hasNext(InterruptibleIterator.scala:37)\n", "\tat scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:491)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage2.processNext(Unknown Source)\n", "\tat org.apache.spark.sql.execution.BufferedRowIterator.hasNext(BufferedRowIterator.java:43)\n", "\tat org.apache.spark.sql.execution.WholeStageCodegenEvaluatorFactory$WholeStageCodegenPartitionEvaluator$$anon$1.hasNext(WholeStageCodegenEvaluatorFactory.scala:43)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.ContextAwareIterator.hasNext(ContextAwareIterator.scala:39)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$GroupedIterator.fill(Iterator.scala:1211)\n", "\tat scala.collection.Iterator$GroupedIterator.hasNext(Iterator.scala:1217)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator.foreach(Iterator.scala:943)\n", "\tat scala.collection.Iterator.foreach$(Iterator.scala:943)\n", "\tat scala.collection.AbstractIterator.foreach(Iterator.scala:1431)\n", "\tat org.apache.spark.api.python.PythonRDD$.writeIteratorToStream(PythonRDD.scala:322)\n", "\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$PythonUDFWriterThread.writeIteratorToStream(PythonUDFRunner.scala:58)\n", "\tat org.apache.spark.api.python.BasePythonRunner$WriterThread.$anonfun$run$1(PythonRunner.scala:451)\n", "\tat org.apache.spark.util.Utils$.logUncaughtExceptions(Utils.scala:1928)\n", "\tat org.apache.spark.api.python.BasePythonRunner$WriterThread.run(PythonRunner.scala:282)\n", "\n", "25/04/23 11:46:19 ERROR TaskSetManager: Task 0 in stage 44.0 failed 1 times; aborting job\n"]}, {"ename": "PythonException", "evalue": "\n  An exception was thrown from the Python worker. Please see the stack trace below.\nTraceback (most recent call last):\n  File \"/tmp/ipykernel_5194/3458323768.py\", line 24, in cosine_of_angle\nTypeError: only size-1 arrays can be converted to Python scalars\n", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mPythonException\u001b[0m                           Traceback (most recent call last)", "Cell \u001b[0;32mIn[19], line 69\u001b[0m\n\u001b[1;32m     65\u001b[0m udf_func \u001b[38;5;241m=\u001b[39m F\u001b[38;5;241m.\u001b[39mudf(\u001b[38;5;28;01mlambda\u001b[39;00m x: np\u001b[38;5;241m.\u001b[39mdegrees(x), T\u001b[38;5;241m.\u001b[39mFloatType())\n\u001b[1;32m     67\u001b[0m df \u001b[38;5;241m=\u001b[39m df\u001b[38;5;241m.\u001b[39mwithColumn(output_cols_names[\u001b[38;5;241m4\u001b[39m], udf_func(df[output_cols_names[\u001b[38;5;241m3\u001b[39m]]))\n\u001b[0;32m---> 69\u001b[0m \u001b[43mdf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mshow\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/pyenvs/base3/lib/python3.10/site-packages/pyspark/sql/dataframe.py:947\u001b[0m, in \u001b[0;36mDataFrame.show\u001b[0;34m(self, n, truncate, vertical)\u001b[0m\n\u001b[1;32m    887\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mshow\u001b[39m(\u001b[38;5;28mself\u001b[39m, n: \u001b[38;5;28mint\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m20\u001b[39m, truncate: Union[\u001b[38;5;28mbool\u001b[39m, \u001b[38;5;28mint\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m, vertical: \u001b[38;5;28mbool\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mFalse\u001b[39;00m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n\u001b[1;32m    888\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Prints the first ``n`` rows to the console.\u001b[39;00m\n\u001b[1;32m    889\u001b[0m \n\u001b[1;32m    890\u001b[0m \u001b[38;5;124;03m    .. versionadded:: 1.3.0\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    945\u001b[0m \u001b[38;5;124;03m    name | Bob\u001b[39;00m\n\u001b[1;32m    946\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 947\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_show_string\u001b[49m\u001b[43m(\u001b[49m\u001b[43mn\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtruncate\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvertical\u001b[49m\u001b[43m)\u001b[49m)\n", "File \u001b[0;32m~/pyenvs/base3/lib/python3.10/site-packages/pyspark/sql/dataframe.py:965\u001b[0m, in \u001b[0;36mDataFrame._show_string\u001b[0;34m(self, n, truncate, vertical)\u001b[0m\n\u001b[1;32m    959\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m PySparkTypeError(\n\u001b[1;32m    960\u001b[0m         error_class\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNOT_BOOL\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m    961\u001b[0m         message_parameters\u001b[38;5;241m=\u001b[39m{\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124marg_name\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mvertical\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124marg_type\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28mtype\u001b[39m(vertical)\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m},\n\u001b[1;32m    962\u001b[0m     )\n\u001b[1;32m    964\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(truncate, \u001b[38;5;28mbool\u001b[39m) \u001b[38;5;129;01mand\u001b[39;00m truncate:\n\u001b[0;32m--> 965\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_jdf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mshowString\u001b[49m\u001b[43m(\u001b[49m\u001b[43mn\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m20\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvertical\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    966\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    967\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n", "File \u001b[0;32m~/pyenvs/base3/lib/python3.10/site-packages/py4j/java_gateway.py:1322\u001b[0m, in \u001b[0;36mJavaMember.__call__\u001b[0;34m(self, *args)\u001b[0m\n\u001b[1;32m   1316\u001b[0m command \u001b[38;5;241m=\u001b[39m proto\u001b[38;5;241m.\u001b[39mCALL_COMMAND_NAME \u001b[38;5;241m+\u001b[39m\\\n\u001b[1;32m   1317\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcommand_header \u001b[38;5;241m+\u001b[39m\\\n\u001b[1;32m   1318\u001b[0m     args_command \u001b[38;5;241m+\u001b[39m\\\n\u001b[1;32m   1319\u001b[0m     proto\u001b[38;5;241m.\u001b[39mEND_COMMAND_PART\n\u001b[1;32m   1321\u001b[0m answer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mgateway_client\u001b[38;5;241m.\u001b[39msend_command(command)\n\u001b[0;32m-> 1322\u001b[0m return_value \u001b[38;5;241m=\u001b[39m \u001b[43mget_return_value\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1323\u001b[0m \u001b[43m    \u001b[49m\u001b[43manswer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgateway_client\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtarget_id\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mname\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1325\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m temp_arg \u001b[38;5;129;01min\u001b[39;00m temp_args:\n\u001b[1;32m   1326\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(temp_arg, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m_detach\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n", "File \u001b[0;32m~/pyenvs/base3/lib/python3.10/site-packages/pyspark/errors/exceptions/captured.py:185\u001b[0m, in \u001b[0;36mcapture_sql_exception.<locals>.deco\u001b[0;34m(*a, **kw)\u001b[0m\n\u001b[1;32m    181\u001b[0m converted \u001b[38;5;241m=\u001b[39m convert_exception(e\u001b[38;5;241m.\u001b[39mjava_exception)\n\u001b[1;32m    182\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(converted, UnknownException):\n\u001b[1;32m    183\u001b[0m     \u001b[38;5;66;03m# Hide where the exception came from that shows a non-Pythonic\u001b[39;00m\n\u001b[1;32m    184\u001b[0m     \u001b[38;5;66;03m# JVM exception message.\u001b[39;00m\n\u001b[0;32m--> 185\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m converted \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m    186\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m:\n\u001b[1;32m    187\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m\n", "\u001b[0;31mPythonException\u001b[0m: \n  An exception was thrown from the Python worker. Please see the stack trace below.\nTraceback (most recent call last):\n  File \"/tmp/ipykernel_5194/3458323768.py\", line 24, in cosine_of_angle\nTypeError: only size-1 arrays can be converted to Python scalars\n"]}], "execution_count": 19}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "c0cfea5d0d45460f"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}