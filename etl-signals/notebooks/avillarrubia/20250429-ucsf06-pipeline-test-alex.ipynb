{"cells": [{"cell_type": "code", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-05-05T10:02:53.631386Z", "start_time": "2025-05-05T10:02:39.880057Z"}}, "source": ["import sys\n", "# add here the location of the movano code movano-2022/src in your local\n", "aio_src_path = \"../../src\"\n", "if aio_src_path not in sys.path:\n", "    print(f\"Adding {aio_src_path} to sys.path\")\n", "    sys.path.append(aio_src_path)\n", "\n", "from multiprocessing.connection import default_family\n", "\n", "from pyspark.sql import SparkSession\n", "from pyspark.sql import functions as F\n", "import pyspark.sql.types as T\n", "from pyspark.sql import DataFrame\n", "from functools import reduce\n", "import warnings\n", "import pandas as pd\n", "import numpy as np\n", "from pyspark.sql.types import StructField\n", "from scipy import signal as sp\n", "from scipy import interpolate\n", "import heartpy as hp\n", "import etl\n", "\n", "spark_session = SparkSession.builder \\\n", "            .appName(\"LocalTestApp\") \\\n", "            .config(\"spark.sql.parquet.columnarReaderBatchSize\", \"1024\") \\\n", "            .config(\"spark.sql.parquet.enableVectorizedReader\", \"false\") \\\n", "            .config(\"spark.driver.memory\", \"4g\") \\\n", "            .config(\"spark.executor.memory\", \"8g\") \\\n", "            .master(\"local[*]\") \\\n", "            .getOrCreate()\n", "\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "pd.options.display.max_columns = None\n", "\n", "import pyarrow as pa\n", "import pyarrow.parquet as pq\n", "from datetime import datetime\n", "import os\n", "\n", "def save_partitioned_parquet(df, path, row_size=10000):\n", "\n", "    current_prefix = datetime.now().strftime(\"%Y%m%d\")\n", "    actual_path = f\"./{current_prefix}-{path}\"\n", "\n", "    if not os.path.exists(actual_path):\n", "        os.makedirs(actual_path)\n", "\n", "    table = pa.Table.from_pandas(df=df)\n", "\n", "    pq.write_to_dataset(table,\n", "                        root_path=actual_path,\n", "                        row_group_size=row_size,\n", "                        partition_cols=None)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Adding ../../src to sys.path\n"]}, {"name": "stderr", "output_type": "stream", "text": ["25/05/05 10:02:49 WARN Utils: Your hostname, ubuntu-jammy resolves to a loopback address: *********; using ********* instead (on interface enp0s3)\n", "25/05/05 10:02:49 WARN Utils: Set SPARK_LOCAL_IP if you need to bind to another address\n", "Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n", "25/05/05 10:02:51 WARN NativeCodeLoader: Unable to load native-hadoop library for your platform... using builtin-java classes where applicable\n"]}], "execution_count": 1}, {"cell_type": "code", "source": ["TIMEFRAMES = 250\n", "SOURCE_PPG = 'ydata'\n", "unused_signals = [i for i in ['ydata', 'lr', 'li', 'lg', 'mag_0', 'mag_1', 'mag_2', 'mag_3'] if i != SOURCE_PPG]\n", "\n", "# PAT_FILTER = {\n", "#             'timeframes': TIMEFRAMES,\n", "#             'std_threshold': [0, 30],\n", "#             'mean_threshold': [50,100],\n", "#             'range_threshold': [40, 120],\n", "#             'slope_threshold': [-1, 1]\n", "#             }\n", "\n", "PAT_FILTER = {\n", "    'timeframes': TIMEFRAMES,\n", "    'std_threshold': [0, 1],\n", "    'mean_threshold': [0.2, 0.6],\n", "    'range_threshold': [0.05, 1],\n", "    'slope_threshold': [-1, 1]\n", "}\n", "\n", "HR_FILTER = {\n", "            'timeframes': TIMEFRAMES,\n", "            'std_threshold': [0, 30],\n", "            'mean_threshold': [40, 140],\n", "            'range_threshold': [40, 180],\n", "            'slope_threshold': [-1, 1]\n", "        }\n", "\n", "ABP_FILTER = {\n", "    'timeframes': TIMEFRAMES,\n", "    'std_threshold': [0, 30],\n", "    'mean_threshold': [40, 240],\n", "    'range_threshold': [40, 240],\n", "    'slope_threshold': [-9999, 9999]\n", "}\n", "\n", "ppg_params = {\n", "        'order': 2,\n", "        'cutoff_high': 3.0,\n", "        'cutoff_low': 0.9,\n", "    }\n", "\n", "ecg_params = {\n", "        'order': 1,\n", "        'cutoff_high': 14.0,\n", "        'cutoff_low': 0.1,\n", "    }"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-05-05T10:17:01.338639Z", "start_time": "2025-05-05T10:17:01.077291Z"}}, "outputs": [], "execution_count": 9}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-05T10:17:47.037944Z", "start_time": "2025-05-05T10:17:02.865539Z"}}, "cell_type": "code", "source": ["# Load step01 parquet\n", "\n", "df = spark_session.read.parquet(\"20250317-ucsf06-step01.parquet\")\n", "df.show()"], "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["+-------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+------------+-----------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+\n", "|     id|                 sbp|                 dbp|                  ts|                 abp|weight_lbs|age|height_inches|     ref_sbp|    ref_dbp|hypertension_present|diabetes_present|tobacco|gender_Female|gender_Male|race_American Indian or Alaska Native|race_Asian|race_Black or African American|race_White|BMI|sampling_freq_hz|          ppg_butter|          ecg_butter|        ecg_enhanced|    ppg_butter_peaks|  ppg_butter_valleys|  ecg_enhanced_peaks|ecg_enhanced_valleys|                 pat|         hr_from_ecg|         hr_from_ppg|__index_level_0__|\n", "+-------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+------------+-----------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+\n", "| UCSF-1|[112.************...|[68.5926739259469...|[1.718099110962E1...|[82.98486025, 80....|       175| 37|           72|106.90455625|  56.530522|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[-0.6827849436773...|[1.14986560207193...|[0.08002757067067...|[42, 176, 276, 50...|[89, 231, 452, 56...|[425, 671, 885, 1...|[1, 210, 432, 677...|[NULL, NULL, NULL...|[NULL, NULL, NULL...|[NULL, NULL, NULL...|                0|\n", "| UCSF-2|[136.950034680922...|[66.8302240049938...|[1.718104514539E1...|[70.1362932500000...|       145| 24|           71|136.19641925|60.61145625|                   0|               0|      0|            0|          1|                                    0|         0|                             1|         0| 20|             200|[-0.3073071606578...|[-0.5482770879016...|[-0.0362745908545...|[150, 313, 497, 6...|[261, 441, 626, 7...|[90, 253, 433, 61...|[85, 248, 436, 61...|[NULL, NULL, NULL...|[NULL, NULL, NULL...|[NULL, NULL, NULL...|                1|\n", "| UCSF-3|[151.125032763960...|[79.3647126816047...|[1.718112638835E1...|[83.869594, 83.80...|       173| 32|           71|  146.071962|  83.837712|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[0.03840384936743...|[-1.9568796963000...|[-0.0087289797478...|[100, 262, 418, 5...|[50, 211, 370, 52...|[42, 199, 355, 50...|[45, 205, 361, 51...|[NULL, NULL, NULL...|[NULL, NULL, NULL...|[NULL, NULL, NULL...|                2|\n", "| UCSF-4|[126.943534596132...|[71.5007793929188...|[1.718117917475E1...|[79.469837, 79.85...|        91| 30|           48|  116.772128|  64.357627|                   0|               0|      0|            0|          1|                                    1|         0|                             0|         0| 28|             200|[-0.2936164151855...|[-0.1404768728982...|[0.00428655982602...|[71, 269, 493, 72...|[159, 389, 533, 6...|[71, 217, 357, 50...|[67, 212, 352, 50...|[NULL, NULL, NULL...|[NULL, NULL, NULL...|[NULL, NULL, NULL...|                3|\n", "| UCSF-5|[141.796097263252...|[60.0815927489070...|[1.718184372203E1...|[81.861009, 81.54...|       135| 36|           67|  145.880668|  62.667865|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 21|             200|[-0.6302473531021...|[0.11321591712965...|[-0.0121095081104...|[50, 219, 396, 58...|[170, 349, 535, 7...|[160, 342, 525, 6...|[163, 336, 520, 6...|[NULL, NULL, NULL...|[NULL, NULL, NULL...|[NULL, NULL, NULL...|                4|\n", "| UCSF-6|[144.042023344206...|[78.4582190365822...|[1.718187574229E1...|[118.597389249999...|       140| 27|           63| 149.4116325|81.74145075|                   0|               0|      0|            0|          1|                                    0|         1|                             0|         0| 25|             200|[-0.6493815615331...|[0.63000693450645...|[0.12586700133452...|[64, 239, 406, 58...|[177, 320, 533, 6...|[317879, 322530, ...|[7125, 9832, 1032...|[NULL, NULL, NULL...|[NULL, NULL, NULL...|[NULL, NULL, NULL...|                5|\n", "| UCSF-7|[145.341790540511...|[75.8288890401835...|[1.718191943831E1...|[105.198853, 105....|       180| 25|           71|  138.324563|  77.907604|                   0|               0|      0|            0|          1|                                    0|         0|                             1|         0| 25|             200|[-0.5894466577807...|[1.31228655435863...|[0.03366592201192...|[71, 344, 579, 78...|[222, 448, 687, 9...|[95, 237, 389, 54...|[98, 241, 393, 54...|[NULL, NULL, NULL...|[NULL, NULL, NULL...|[NULL, NULL, NULL...|                6|\n", "| UCSF-8|[120.948624405594...|[68.9369762209932...|[1.718198626037E1...|[62.763512, 63.84...|       190| 43|           74|  110.905785|  62.349042|                   0|               0|      0|            1|          0|                                    0|         0|                             1|         0| 24|             200|[-0.1775452007937...|[-0.7524114018061...|[-0.0049199242886...|[33, 252, 475, 66...|[94, 342, 582, 75...|[72, 208, 344, 48...|[75, 211, 347, 48...|[NULL, NULL, NULL...|[NULL, NULL, NULL...|[NULL, NULL, NULL...|                7|\n", "| UCSF-9|[125.287515225060...|[69.0805548388542...|[1.718202996711E1...|[84.347829, 83.86...|       116| 24|           63|  122.702236|  68.374797|                   0|               0|      0|            1|          0|                                    0|         1|                             0|         0| 21|             200|[0.36802992591004...|[-2.5045076108106...|[-0.1869539323546...|[24, 218, 462, 68...|[122, 408, 739, 8...|     [19992, 165603]|[9674, 19973, 220...|[NULL, NULL, NULL...|[NULL, NULL, NULL...|[NULL, NULL, NULL...|                8|\n", "|UCSF-11|[123.187583511110...|[61.5385916567151...|[1.718270374044E1...|[71.722438, 72.23...|       150| 24|           68|  128.472932|  64.325744|                   0|               0|      0|            1|          0|                                    0|         0|                             0|         1| 23|             200|[-0.2600693558837...|[-3.4063463350445...|[0.05746894435107...|[85, 230, 465, 61...|[127, 285, 530, 6...|[149, 292, 432, 5...|[2, 155, 287, 438...|[NULL, NULL, NULL...|[NULL, NULL, NULL...|[NULL, NULL, NULL...|                9|\n", "+-------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+------------+-----------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+\n", "\n"]}], "execution_count": 10}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-05T10:17:56.730292Z", "start_time": "2025-05-05T10:17:47.546796Z"}}, "cell_type": "code", "source": ["df = df.filter(<PERSON><PERSON>col(\"__index_level_0__\")==0)\n", "df.show()"], "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+------------+---------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+\n", "|    id|                 sbp|                 dbp|                  ts|                 abp|weight_lbs|age|height_inches|     ref_sbp|  ref_dbp|hypertension_present|diabetes_present|tobacco|gender_Female|gender_Male|race_American Indian or Alaska Native|race_Asian|race_Black or African American|race_White|BMI|sampling_freq_hz|          ppg_butter|          ecg_butter|        ecg_enhanced|    ppg_butter_peaks|  ppg_butter_valleys|  ecg_enhanced_peaks|ecg_enhanced_valleys|                 pat|         hr_from_ecg|         hr_from_ppg|__index_level_0__|\n", "+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+------------+---------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+\n", "|UCSF-1|[112.************...|[68.5926739259469...|[1.718099110962E1...|[82.98486025, 80....|       175| 37|           72|106.90455625|56.530522|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[-0.6827849436773...|[1.14986560207193...|[0.08002757067067...|[42, 176, 276, 50...|[89, 231, 452, 56...|[425, 671, 885, 1...|[1, 210, 432, 677...|[NULL, NULL, NULL...|[NULL, NULL, NULL...|[NULL, NULL, NULL...|                0|\n", "+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+------------+---------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+\n", "\n"]}], "execution_count": 11}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-05T10:20:35.258496Z", "start_time": "2025-05-05T10:19:56.275923Z"}}, "cell_type": "code", "source": ["# Apply the PAT, HR and ABP filters\n", "multi_filter = etl.annotators.signals.filtering.MultiFilteringSignal() \\\n", "    .set_filter_sequence(['pat', 'hr', 'abp']) \\\n", "    .set_ts_col('ts') \\\n", "    .set_hr_col('hr_from_ppg') \\\n", "    .set_pat_col('pat') \\\n", "    .set_abp_col('sbp')\\\n", "    .set_indices_col('ppg_butter_valleys') \\\n", "    .set_pat_thresholds(PAT_FILTER) \\\n", "    .set_hr_thresholds(HR_FILTER) \\\n", "    .set_abp_thresholds(ABP_FILTER)\\\n", "    .set_segment_length(TIMEFRAMES) \\\n", "    .set_output_cols(['valid_cut_valleys'])\\\n", "    .fit().transform(df)\n", "\n", "multi_filter.show()"], "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+------------+---------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+--------------------+--------------------+--------------------+--------------------+\n", "|    id|                 sbp|                 dbp|                  ts|                 abp|weight_lbs|age|height_inches|     ref_sbp|  ref_dbp|hypertension_present|diabetes_present|tobacco|gender_Female|gender_Male|race_American Indian or Alaska Native|race_Asian|race_Black or African American|race_White|BMI|sampling_freq_hz|          ppg_butter|          ecg_butter|        ecg_enhanced|    ppg_butter_peaks|  ppg_butter_valleys|  ecg_enhanced_peaks|ecg_enhanced_valleys|                 pat|         hr_from_ecg|         hr_from_ppg|__index_level_0__|       noise_segment|   valid_cut_valleys|       noise_segment|       noise_segment|\n", "+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+------------+---------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+--------------------+--------------------+--------------------+--------------------+\n", "|UCSF-1|[112.************...|[68.5926739259469...|[1.718099110962E1...|[82.98486025, 80....|       175| 37|           72|106.90455625|56.530522|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[-0.6827849436773...|[1.14986560207193...|[0.08002757067067...|[42, 176, 276, 50...|[89, 231, 452, 56...|[425, 671, 885, 1...|[1, 210, 432, 677...|[NULL, NULL, NULL...|[NULL, NULL, NULL...|[NULL, NULL, NULL...|                0|[913, 14229, 1441...|[1386, 1826, 2274...|[146860, 160276, ...|[25666, 26125, 26...|\n", "+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+------------+---------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+--------------------+--------------------+--------------------+--------------------+\n", "\n"]}], "execution_count": 13}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-05T10:20:48.163346Z", "start_time": "2025-05-05T10:20:35.622128Z"}}, "cell_type": "code", "source": ["multi_filter = multi_filter.drop('noise_segment')\n", "multi_filter.show()"], "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+------------+---------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+--------------------+\n", "|    id|                 sbp|                 dbp|                  ts|                 abp|weight_lbs|age|height_inches|     ref_sbp|  ref_dbp|hypertension_present|diabetes_present|tobacco|gender_Female|gender_Male|race_American Indian or Alaska Native|race_Asian|race_Black or African American|race_White|BMI|sampling_freq_hz|          ppg_butter|          ecg_butter|        ecg_enhanced|    ppg_butter_peaks|  ppg_butter_valleys|  ecg_enhanced_peaks|ecg_enhanced_valleys|                 pat|         hr_from_ecg|         hr_from_ppg|__index_level_0__|   valid_cut_valleys|\n", "+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+------------+---------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+--------------------+\n", "|UCSF-1|[112.************...|[68.5926739259469...|[1.718099110962E1...|[82.98486025, 80....|       175| 37|           72|106.90455625|56.530522|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[-0.6827849436773...|[1.14986560207193...|[0.08002757067067...|[42, 176, 276, 50...|[89, 231, 452, 56...|[425, 671, 885, 1...|[1, 210, 432, 677...|[NULL, NULL, NULL...|[NULL, NULL, NULL...|[NULL, NULL, NULL...|                0|[1386, 1826, 2274...|\n", "+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+------------+---------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+--------------------+\n", "\n"]}], "execution_count": 14}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-05T10:21:32.909276Z", "start_time": "2025-05-05T10:21:23.531295Z"}}, "cell_type": "code", "source": ["# EJECUTAR SOLO SI QUIERES QUE LOS ARRAYS DE LA ONDA SEAN MÁS PEQUEÑOS\n", "def smaller_list(input_values):\n", "    output_values = input_values[1500:2500]\n", "    return output_values\n", "\n", "def valid_cut_valleys_first(input_values):\n", "    output_values = [input_values[1]-1500]\n", "    return output_values\n", "\n", "udf_func = F.udf(smaller_list, T.ArrayType(T.FloatType()))\n", "\n", "for col in ['sbp', 'dbp', 'ts', 'abp', 'ppg_butter', 'ecg_butter', 'ecg_enhanced', 'pat', 'hr_from_ecg', 'hr_from_ppg']:\n", "    multi_filter = multi_filter.withColumn(col, udf_func(df[col]))\n", "\n", "udf_func = F.udf(smaller_list, T.ArrayType(T.IntegerType()))\n", "\n", "for col in ['ppg_butter_peaks', 'ppg_butter_valleys', 'ecg_enhanced_peaks', 'ecg_enhanced_valleys']:\n", "    multi_filter = multi_filter.withColumn(col, udf_func(df[col]))\n", "\n", "udf_func = F.udf(valid_cut_valleys_first, T.A<PERSON>yType(T.IntegerType()))\n", "multi_filter = multi_filter.withColumn('valid_cut_valleys', udf_func(multi_filter['valid_cut_valleys']))\n", "\n", "multi_filter.show()"], "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+------------+---------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+-----------------+\n", "|    id|                 sbp|                 dbp|                  ts|                 abp|weight_lbs|age|height_inches|     ref_sbp|  ref_dbp|hypertension_present|diabetes_present|tobacco|gender_Female|gender_Male|race_American Indian or Alaska Native|race_Asian|race_Black or African American|race_White|BMI|sampling_freq_hz|          ppg_butter|          ecg_butter|        ecg_enhanced|    ppg_butter_peaks|  ppg_butter_valleys|  ecg_enhanced_peaks|ecg_enhanced_valleys|                 pat|         hr_from_ecg|         hr_from_ppg|__index_level_0__|valid_cut_valleys|\n", "+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+------------+---------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+-----------------+\n", "|UCSF-1|[104.92111, 105.2...|[58.16541, 58.790...|[1.71809912E12, 1...|[62.364983, 62.34...|       175| 37|           72|106.90455625|56.530522|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[0.2950426, 0.295...|[-0.15341228, -0....|[0.07959235, 0.07...|[296080, 296281, ...|[298036, 298259, ...|[298862, 299070, ...|[296225, 296426, ...|[0.355, 0.355, 0....|[48.780487, 48.78...|[48.780487, 48.78...|                0|            [326]|\n", "+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+------------+---------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+-----------------+\n", "\n"]}], "execution_count": 15}, {"cell_type": "code", "source": ["# Dividing the signal in 10 second segments.\n", "exploder = etl.annotators.signals.rearrange.ExplodeSignal() \\\n", "    .set_input_cols(['ts', 'ppg_butter', 'ecg_butter', 'pat', 'hr_from_ecg', 'hr_from_ppg', 'sbp', 'dbp']) \\\n", "    .set_output_cols(\n", "    ['ts_episode', 'ppg_episode', 'ecg_episode', 'pat_episode', 'hr_from_ecg_episode', 'hr_from_ppg_episode',\n", "     'sbp_episode', 'dbp_episode']) \\\n", "    .set_cuts_col('valid_cut_valleys') \\\n", "    .set_split_method('defined_length') \\\n", "    .set_segment_length(TIMEFRAMES) \\\n", "    .fit().transform(multi_filter)\n", "\n", "exploder.show()"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-05-05T10:21:55.357169Z", "start_time": "2025-05-05T10:21:43.905462Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+------------+---------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+-----------------+------------+-----+---+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+\n", "|    id|                 sbp|                 dbp|                  ts|                 abp|weight_lbs|age|height_inches|     ref_sbp|  ref_dbp|hypertension_present|diabetes_present|tobacco|gender_Female|gender_Male|race_American Indian or Alaska Native|race_Asian|race_Black or African American|race_White|BMI|sampling_freq_hz|          ppg_butter|          ecg_butter|        ecg_enhanced|    ppg_butter_peaks|  ppg_butter_valleys|  ecg_enhanced_peaks|ecg_enhanced_valleys|                 pat|         hr_from_ecg|         hr_from_ppg|__index_level_0__|valid_cut_valleys|split_number|begin|end|          ts_episode|         ppg_episode|         ecg_episode|         pat_episode| hr_from_ecg_episode| hr_from_ppg_episode|         sbp_episode|         dbp_episode|\n", "+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+------------+---------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+-----------------+------------+-----+---+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+\n", "|UCSF-1|[104.92111, 105.2...|[58.16541, 58.790...|[1.71809912E12, 1...|[62.364983, 62.34...|       175| 37|           72|106.90455625|56.530522|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[0.2950426, 0.295...|[-0.15341228, -0....|[0.07959235, 0.07...|[296080, 296281, ...|[298036, 298259, ...|[298862, 299070, ...|[296225, 296426, ...|[0.355, 0.355, 0....|[48.780487, 48.78...|[48.780487, 48.78...|                0|            [326]|           0|   76|325|[1.71809912E12, 1...|[-0.49200967, -0....|[0.8268422, 0.994...|[0.355, 0.355, 0....|[48.780487, 48.78...|[48.780487, 48.78...|[104.99959, 105.3...|[61.66541, 61.040...|\n", "+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+------------+---------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+-----------------+------------+-----+---+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+\n", "\n"]}], "execution_count": 16}, {"cell_type": "code", "source": ["# Dropping empty rows\n", "drop_all_nans = etl.annotators.dataprep.missing.DropNAs()\\\n", "    .set_mode('any')\\\n", "    .fit().transform(exploder)\n", "\n", "# Summarize sbp and dbp for validating prediction results.\n", "sbp_real = etl.annotators.features.fromsignals.FeaturizeSignal() \\\n", "    .set_input_cols(['sbp_episode']) \\\n", "    .set_output_cols(['sbp']) \\\n", "    .set_modes([\"last\"]) \\\n", "    .fit().transform(drop_all_nans)\n", "\n", "dbp_real = etl.annotators.features.fromsignals.FeaturizeSignal() \\\n", "    .set_input_cols(['dbp_episode']) \\\n", "    .set_output_cols(['dbp']) \\\n", "    .set_modes([\"last\"]) \\\n", "    .fit().transform(sbp_real)\n", "\n", "sbp_real.show()"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-05-05T10:26:03.777683Z", "start_time": "2025-05-05T10:25:52.820781Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+------------+---------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+-----------------+------------+-----+---+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+------------------+\n", "|    id|                 sbp|                 dbp|                  ts|                 abp|weight_lbs|age|height_inches|     ref_sbp|  ref_dbp|hypertension_present|diabetes_present|tobacco|gender_Female|gender_Male|race_American Indian or Alaska Native|race_Asian|race_Black or African American|race_White|BMI|sampling_freq_hz|          ppg_butter|          ecg_butter|        ecg_enhanced|    ppg_butter_peaks|  ppg_butter_valleys|  ecg_enhanced_peaks|ecg_enhanced_valleys|                 pat|         hr_from_ecg|         hr_from_ppg|__index_level_0__|valid_cut_valleys|split_number|begin|end|          ts_episode|         ppg_episode|         ecg_episode|         pat_episode| hr_from_ecg_episode| hr_from_ppg_episode|         sbp_episode|         dbp_episode|  sbp_episode_last|\n", "+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+------------+---------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+-----------------+------------+-----+---+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+------------------+\n", "|UCSF-1|[104.92111, 105.2...|[58.16541, 58.790...|[1.71809912E12, 1...|[62.364983, 62.34...|       175| 37|           72|106.90455625|56.530522|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[0.2950426, 0.295...|[-0.15341228, -0....|[0.07959235, 0.07...|[296080, 296281, ...|[298036, 298259, ...|[298862, 299070, ...|[296225, 296426, ...|[0.355, 0.355, 0....|[48.780487, 48.78...|[48.780487, 48.78...|                0|            [326]|           0|   76|325|[1.71809912E12, 1...|[-0.49200967, -0....|[0.8268422, 0.994...|[0.355, 0.355, 0....|[48.780487, 48.78...|[48.780487, 48.78...|[104.99959, 105.3...|[61.66541, 61.040...|108.48617553710938|\n", "+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+------------+---------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+-----------------+------------+-----+---+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+------------------+\n", "\n"]}], "execution_count": 17}, {"cell_type": "code", "source": ["# Round the values of some signals and recast\n", "round_signals = etl.annotators.dataprep.basic.Round() \\\n", "    .set_input_cols(['hr_from_ecg_episode', 'hr_from_ppg_episode', 'ts_episode', 'sbp_episode', 'dbp_episode']) \\\n", "    .set_output_cols(['hr_from_ecg_episode', 'hr_from_ppg_episode', 'ts_episode', 'sbp_episode', 'dbp_episode']) \\\n", "    .set_decimals(0)\\\n", "    .fit().transform(dbp_real)\n", "\n", "# Calculate PAT/squared height ratio.\n", "pat_ratio_calc = etl.annotators.dataprep.basic.ApplyFunc() \\\n", "    .set_function(lambda x, y: [i / (x ** 2) if i is not None else None for i in y]) \\\n", "    .set_output_type(T.ArrayType(T.FloatType())) \\\n", "    .set_input_cols(['height_inches', 'pat_episode']) \\\n", "    .set_output_cols(['pat_ratio']) \\\n", "    .fit().transform(round_signals)\n", "\n", "# Get reference pulse pressure\n", "pp_calc = etl.annotators.features.fromvalues.CalculatePulsePressure() \\\n", "    .set_sbp_col(\"ref_sbp\") \\\n", "    .set_dbp_col(\"ref_dbp\") \\\n", "    .set_output_cols(['ref_pp'])\\\n", "    .fit().transform(pat_ratio_calc)\n", "\n", "pp_calc.show()"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-05-05T10:35:04.299549Z", "start_time": "2025-05-05T10:34:38.777138Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 94:>                                                         (0 + 1) / 1]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+-------+-------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+-----------------+------------+-----+---+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+------------------+-----------------+--------------------+------+\n", "|    id|                 sbp|                 dbp|                  ts|                 abp|weight_lbs|age|height_inches|ref_sbp|ref_dbp|hypertension_present|diabetes_present|tobacco|gender_Female|gender_Male|race_American Indian or Alaska Native|race_Asian|race_Black or African American|race_White|BMI|sampling_freq_hz|          ppg_butter|          ecg_butter|        ecg_enhanced|    ppg_butter_peaks|  ppg_butter_valleys|  ecg_enhanced_peaks|ecg_enhanced_valleys|                 pat|         hr_from_ecg|         hr_from_ppg|__index_level_0__|valid_cut_valleys|split_number|begin|end|          ts_episode|         ppg_episode|         ecg_episode|         pat_episode| hr_from_ecg_episode| hr_from_ppg_episode|         sbp_episode|         dbp_episode|  sbp_episode_last| dbp_episode_last|           pat_ratio|ref_pp|\n", "+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+-------+-------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+-----------------+------------+-----+---+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+------------------+-----------------+--------------------+------+\n", "|UCSF-1|[104.92111, 105.2...|[58.16541, 58.790...|[1.71809912E12, 1...|[62.364983, 62.34...|       175| 37|           72|    106|     56|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[0.2950426, 0.295...|[-0.15341228, -0....|[0.07959235, 0.07...|[296080, 296281, ...|[298036, 298259, ...|[298862, 299070, ...|[296225, 296426, ...|[0.355, 0.355, 0....|[48.780487, 48.78...|[48.780487, 48.78...|                0|            [326]|           0|   76|325|[1.71809912E12, 1...|[-0.49200967, -0....|[0.8268422, 0.994...|[0.355, 0.355, 0....|[49.0, 49.0, 49.0...|[49.0, 49.0, 49.0...|[105.0, 105.0, 10...|[62.0, 61.0, 61.0...|108.48617553710938|56.49734115600586|[6.847994E-5, 6.8...|    50|\n", "+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+-------+-------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+-----------------+------------+-----+---+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+------------------+-----------------+--------------------+------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "execution_count": 19}, {"cell_type": "code", "source": ["# Standardize ppg and ecg before extracting features\n", "minmax_wave_scaler = etl.annotators.signals.standardization.StandardizeSignal() \\\n", "    .set_input_cols(['ppg_episode', 'ecg_episode']) \\\n", "    .set_output_cols(['ppg_episode', 'ecg_episode']) \\\n", "    .set_mode('by_row') \\\n", "    .set_minmax(True) \\\n", "    .fit().transform(pp_calc)\n", "\n", "# Summarize signals (extract features from segments)\n", "summarize_signals = etl.annotators.features.fromsignals.FeaturizeSignal() \\\n", "    .set_input_cols(\n", "    ['ppg_episode', 'ecg_episode', 'pat_ratio', 'pat_episode', 'hr_from_ecg_episode', 'hr_from_ppg_episode']) \\\n", "    .set_modes(['min', 'max', 'mean', 'median', 'std', 'sum', 'trend']) \\\n", "    .fit().transform(minmax_wave_scaler)\n", "\n", "summarize_signals.show()"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-05-05T10:39:44.822747Z", "start_time": "2025-05-05T10:39:15.588027Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 97:>                                                         (0 + 1) / 1]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+-------+-------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+-----------------+------------+-----+---+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+------------------+-----------------+--------------------+------+---------------+---------------+------------------+------------------+-----------------+------------------+--------------------+-------------------+---------------+---------------+-------------------+------------------+-------------------+-----------------+--------------------+-------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-------------------+--------------------+--------------------+-------------------+------------------+-------------------+------------------+--------------------+----------------+--------------------+--------------------+-----------------------+-----------------------+------------------------+--------------------------+-----------------------+-----------------------+-------------------------+-------------------------+-----------------------+-----------------------+------------------------+--------------------------+-----------------------+-----------------------+-------------------------+-------------------------+\n", "|    id|                 sbp|                 dbp|                  ts|                 abp|weight_lbs|age|height_inches|ref_sbp|ref_dbp|hypertension_present|diabetes_present|tobacco|gender_Female|gender_Male|race_American Indian or Alaska Native|race_Asian|race_Black or African American|race_White|BMI|sampling_freq_hz|          ppg_butter|          ecg_butter|        ecg_enhanced|    ppg_butter_peaks|  ppg_butter_valleys|  ecg_enhanced_peaks|ecg_enhanced_valleys|                 pat|         hr_from_ecg|         hr_from_ppg|__index_level_0__|valid_cut_valleys|split_number|begin|end|          ts_episode|         ppg_episode|         ecg_episode|         pat_episode| hr_from_ecg_episode| hr_from_ppg_episode|         sbp_episode|         dbp_episode|  sbp_episode_last| dbp_episode_last|           pat_ratio|ref_pp|ppg_episode_min|ppg_episode_max|  ppg_episode_mean|ppg_episode_median|  ppg_episode_std|   ppg_episode_sum|   ppg_episode_trend|  ppg_episode_error|ecg_episode_min|ecg_episode_max|   ecg_episode_mean|ecg_episode_median|    ecg_episode_std|  ecg_episode_sum|   ecg_episode_trend|  ecg_episode_error|       pat_ratio_min|       pat_ratio_max|      pat_ratio_mean|    pat_ratio_median|       pat_ratio_std|      pat_ratio_sum|     pat_ratio_trend|     pat_ratio_error|    pat_episode_min|   pat_episode_max|   pat_episode_mean|pat_episode_median|     pat_episode_std| pat_episode_sum|   pat_episode_trend|   pat_episode_error|hr_from_ecg_episode_min|hr_from_ecg_episode_max|hr_from_ecg_episode_mean|hr_from_ecg_episode_median|hr_from_ecg_episode_std|hr_from_ecg_episode_sum|hr_from_ecg_episode_trend|hr_from_ecg_episode_error|hr_from_ppg_episode_min|hr_from_ppg_episode_max|hr_from_ppg_episode_mean|hr_from_ppg_episode_median|hr_from_ppg_episode_std|hr_from_ppg_episode_sum|hr_from_ppg_episode_trend|hr_from_ppg_episode_error|\n", "+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+-------+-------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+-----------------+------------+-----+---+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+------------------+-----------------+--------------------+------+---------------+---------------+------------------+------------------+-----------------+------------------+--------------------+-------------------+---------------+---------------+-------------------+------------------+-------------------+-----------------+--------------------+-------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-------------------+--------------------+--------------------+-------------------+------------------+-------------------+------------------+--------------------+----------------+--------------------+--------------------+-----------------------+-----------------------+------------------------+--------------------------+-----------------------+-----------------------+-------------------------+-------------------------+-----------------------+-----------------------+------------------------+--------------------------+-----------------------+-----------------------+-------------------------+-------------------------+\n", "|UCSF-1|[104.92111, 105.2...|[58.16541, 58.790...|[1.71809912E12, 1...|[62.364983, 62.34...|       175| 37|           72|    106|     56|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[0.2950426, 0.295...|[-0.15341228, -0....|[0.07959235, 0.07...|[296080, 296281, ...|[298036, 298259, ...|[298862, 299070, ...|[296225, 296426, ...|[0.355, 0.355, 0....|[48.780487, 48.78...|[48.780487, 48.78...|                0|            [326]|           0|   76|325|[1.71809912E12, 1...|[0.21708132, 0.20...|[0.40800267, 0.43...|[0.355, 0.355, 0....|[49.0, 49.0, 49.0...|[49.0, 49.0, 49.0...|[105.0, 105.0, 10...|[62.0, 61.0, 61.0...|108.48617553710938|56.49734115600586|[6.847994E-5, 6.8...|    50|            0.0|            1.0|0.4131734092372935|0.3766465336084366|0.261136245757585|103.29335230932338|-2.94736475131272...|0.25974397346625416|            0.0|            1.0|0.32163328978419303|0.2715716063976288|0.18417933124912847|80.40832244604826|-3.32499363618035...|0.18223758001623672|6.847993790870532E-5|7.137345528462902E-5|7.048225193284452E-5|7.137345528462902E-5|1.338519895886757...|0.01762056298321113|1.480134208791028...|8.021514684570672E-7|0.35499998927116394|0.3700000047683716|0.36537999999523163|0.3700000047683716|0.006938897049205625|91.3449999988079|7.673026695657836E-5|0.004158359150728386|                   49.0|                   55.0|                  54.824|                      55.0|      0.712075985296051|                13706.0|     -5.29160466567589...|        0.709623585962403|                   49.0|                   54.0|                   52.46|                      54.0|      2.312963293437047|                13115.0|      0.02557672922766764|       1.3861182848452702|\n", "+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+-------+-------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+-----------------+------------+-----+---+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+------------------+-----------------+--------------------+------+---------------+---------------+------------------+------------------+-----------------+------------------+--------------------+-------------------+---------------+---------------+-------------------+------------------+-------------------+-----------------+--------------------+-------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-------------------+--------------------+--------------------+-------------------+------------------+-------------------+------------------+--------------------+----------------+--------------------+--------------------+-----------------------+-----------------------+------------------------+--------------------------+-----------------------+-----------------------+-------------------------+-------------------------+-----------------------+-----------------------+------------------------+--------------------------+-----------------------+-----------------------+-------------------------+-------------------------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "execution_count": 20}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-05T10:41:44.213992Z", "start_time": "2025-05-05T10:41:24.062926Z"}}, "cell_type": "code", "source": "summarize_signals.show()", "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+-------+-------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+-----------------+------------+-----+---+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+------------------+-----------------+--------------------+------+---------------+---------------+------------------+------------------+-----------------+------------------+--------------------+-------------------+---------------+---------------+-------------------+------------------+-------------------+-----------------+--------------------+-------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-------------------+--------------------+--------------------+-------------------+------------------+-------------------+------------------+--------------------+----------------+--------------------+--------------------+-----------------------+-----------------------+------------------------+--------------------------+-----------------------+-----------------------+-------------------------+-------------------------+-----------------------+-----------------------+------------------------+--------------------------+-----------------------+-----------------------+-------------------------+-------------------------+\n", "|    id|                 sbp|                 dbp|                  ts|                 abp|weight_lbs|age|height_inches|ref_sbp|ref_dbp|hypertension_present|diabetes_present|tobacco|gender_Female|gender_Male|race_American Indian or Alaska Native|race_Asian|race_Black or African American|race_White|BMI|sampling_freq_hz|          ppg_butter|          ecg_butter|        ecg_enhanced|    ppg_butter_peaks|  ppg_butter_valleys|  ecg_enhanced_peaks|ecg_enhanced_valleys|                 pat|         hr_from_ecg|         hr_from_ppg|__index_level_0__|valid_cut_valleys|split_number|begin|end|          ts_episode|         ppg_episode|         ecg_episode|         pat_episode| hr_from_ecg_episode| hr_from_ppg_episode|         sbp_episode|         dbp_episode|  sbp_episode_last| dbp_episode_last|           pat_ratio|ref_pp|ppg_episode_min|ppg_episode_max|  ppg_episode_mean|ppg_episode_median|  ppg_episode_std|   ppg_episode_sum|   ppg_episode_trend|  ppg_episode_error|ecg_episode_min|ecg_episode_max|   ecg_episode_mean|ecg_episode_median|    ecg_episode_std|  ecg_episode_sum|   ecg_episode_trend|  ecg_episode_error|       pat_ratio_min|       pat_ratio_max|      pat_ratio_mean|    pat_ratio_median|       pat_ratio_std|      pat_ratio_sum|     pat_ratio_trend|     pat_ratio_error|    pat_episode_min|   pat_episode_max|   pat_episode_mean|pat_episode_median|     pat_episode_std| pat_episode_sum|   pat_episode_trend|   pat_episode_error|hr_from_ecg_episode_min|hr_from_ecg_episode_max|hr_from_ecg_episode_mean|hr_from_ecg_episode_median|hr_from_ecg_episode_std|hr_from_ecg_episode_sum|hr_from_ecg_episode_trend|hr_from_ecg_episode_error|hr_from_ppg_episode_min|hr_from_ppg_episode_max|hr_from_ppg_episode_mean|hr_from_ppg_episode_median|hr_from_ppg_episode_std|hr_from_ppg_episode_sum|hr_from_ppg_episode_trend|hr_from_ppg_episode_error|\n", "+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+-------+-------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+-----------------+------------+-----+---+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+------------------+-----------------+--------------------+------+---------------+---------------+------------------+------------------+-----------------+------------------+--------------------+-------------------+---------------+---------------+-------------------+------------------+-------------------+-----------------+--------------------+-------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-------------------+--------------------+--------------------+-------------------+------------------+-------------------+------------------+--------------------+----------------+--------------------+--------------------+-----------------------+-----------------------+------------------------+--------------------------+-----------------------+-----------------------+-------------------------+-------------------------+-----------------------+-----------------------+------------------------+--------------------------+-----------------------+-----------------------+-------------------------+-------------------------+\n", "|UCSF-1|[104.92111, 105.2...|[58.16541, 58.790...|[1.71809912E12, 1...|[62.364983, 62.34...|       175| 37|           72|    106|     56|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[0.2950426, 0.295...|[-0.15341228, -0....|[0.07959235, 0.07...|[296080, 296281, ...|[298036, 298259, ...|[298862, 299070, ...|[296225, 296426, ...|[0.355, 0.355, 0....|[48.780487, 48.78...|[48.780487, 48.78...|                0|            [326]|           0|   76|325|[1.71809912E12, 1...|[0.21708132, 0.20...|[0.40800267, 0.43...|[0.355, 0.355, 0....|[49.0, 49.0, 49.0...|[49.0, 49.0, 49.0...|[105.0, 105.0, 10...|[62.0, 61.0, 61.0...|108.48617553710938|56.49734115600586|[6.847994E-5, 6.8...|    50|            0.0|            1.0|0.4131734092372935|0.3766465336084366|0.261136245757585|103.29335230932338|-2.94736475131272...|0.25974397346625416|            0.0|            1.0|0.32163328978419303|0.2715716063976288|0.18417933124912847|80.40832244604826|-3.32499363618035...|0.18223758001623672|6.847993790870532E-5|7.137345528462902E-5|7.048225193284452E-5|7.137345528462902E-5|1.338519895886757...|0.01762056298321113|1.480134208791028...|8.021514684570672E-7|0.35499998927116394|0.3700000047683716|0.36537999999523163|0.3700000047683716|0.006938897049205625|91.3449999988079|7.673026695657836E-5|0.004158359150728386|                   49.0|                   55.0|                  54.824|                      55.0|      0.712075985296051|                13706.0|     -5.29160466567589...|        0.709623585962403|                   49.0|                   54.0|                   52.46|                      54.0|      2.312963293437047|                13115.0|      0.02557672922766764|       1.3861182848452702|\n", "+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+-------+-------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+-----------------+------------+-----+---+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+------------------+-----------------+--------------------+------+---------------+---------------+------------------+------------------+-----------------+------------------+--------------------+-------------------+---------------+---------------+-------------------+------------------+-------------------+-----------------+--------------------+-------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-------------------+--------------------+--------------------+-------------------+------------------+-------------------+------------------+--------------------+----------------+--------------------+--------------------+-----------------------+-----------------------+------------------------+--------------------------+-----------------------+-----------------------+-------------------------+-------------------------+-----------------------+-----------------------+------------------------+--------------------------+-----------------------+-----------------------+-------------------------+-------------------------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "execution_count": 22}, {"cell_type": "code", "source": ["# Find segment valleys to cut a three-beat segment\n", "beat_indices = etl.annotators.signals.characterization.RobustPeakFinder()\\\n", "    .set_ppg_col('ppg_episode')\\\n", "    .fit().transform(summarize_signals)\n", "\n", "# Extract the last three beats from each episode\n", "beat_extract = etl.annotators.signals.rearrange.SignalBeatExtractor() \\\n", "    .set_signal_col('ppg_episode') \\\n", "    .set_timestamp_col('ts_episode') \\\n", "    .set_output_cols(['last_three_beats', 'ts_last_three_beats']) \\\n", "    .set_indices_col('ppg_episode_valleys') \\\n", "    .set_how_many_segments(3)\\\n", "    .fit().transform(beat_indices)\n", "\n", "beat_extract.show()"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-05-05T10:45:31.747178Z", "start_time": "2025-05-05T10:45:04.301675Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 103:>                                                        (0 + 1) / 1]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+-------+-------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+-----------------+------------+-----+---+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+------------------+-----------------+--------------------+------+---------------+---------------+------------------+------------------+-----------------+------------------+--------------------+-------------------+---------------+---------------+-------------------+------------------+-------------------+-----------------+--------------------+-------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-------------------+--------------------+--------------------+-------------------+------------------+-------------------+------------------+--------------------+----------------+--------------------+--------------------+-----------------------+-----------------------+------------------------+--------------------------+-----------------------+-----------------------+-------------------------+-------------------------+-----------------------+-----------------------+------------------------+--------------------------+-----------------------+-----------------------+-------------------------+-------------------------+-----------------+-------------------+--------------------+--------------------+\n", "|    id|                 sbp|                 dbp|                  ts|                 abp|weight_lbs|age|height_inches|ref_sbp|ref_dbp|hypertension_present|diabetes_present|tobacco|gender_Female|gender_Male|race_American Indian or Alaska Native|race_Asian|race_Black or African American|race_White|BMI|sampling_freq_hz|          ppg_butter|          ecg_butter|        ecg_enhanced|    ppg_butter_peaks|  ppg_butter_valleys|  ecg_enhanced_peaks|ecg_enhanced_valleys|                 pat|         hr_from_ecg|         hr_from_ppg|__index_level_0__|valid_cut_valleys|split_number|begin|end|          ts_episode|         ppg_episode|         ecg_episode|         pat_episode| hr_from_ecg_episode| hr_from_ppg_episode|         sbp_episode|         dbp_episode|  sbp_episode_last| dbp_episode_last|           pat_ratio|ref_pp|ppg_episode_min|ppg_episode_max|  ppg_episode_mean|ppg_episode_median|  ppg_episode_std|   ppg_episode_sum|   ppg_episode_trend|  ppg_episode_error|ecg_episode_min|ecg_episode_max|   ecg_episode_mean|ecg_episode_median|    ecg_episode_std|  ecg_episode_sum|   ecg_episode_trend|  ecg_episode_error|       pat_ratio_min|       pat_ratio_max|      pat_ratio_mean|    pat_ratio_median|       pat_ratio_std|      pat_ratio_sum|     pat_ratio_trend|     pat_ratio_error|    pat_episode_min|   pat_episode_max|   pat_episode_mean|pat_episode_median|     pat_episode_std| pat_episode_sum|   pat_episode_trend|   pat_episode_error|hr_from_ecg_episode_min|hr_from_ecg_episode_max|hr_from_ecg_episode_mean|hr_from_ecg_episode_median|hr_from_ecg_episode_std|hr_from_ecg_episode_sum|hr_from_ecg_episode_trend|hr_from_ecg_episode_error|hr_from_ppg_episode_min|hr_from_ppg_episode_max|hr_from_ppg_episode_mean|hr_from_ppg_episode_median|hr_from_ppg_episode_std|hr_from_ppg_episode_sum|hr_from_ppg_episode_trend|hr_from_ppg_episode_error|ppg_episode_peaks|ppg_episode_valleys|    last_three_beats| ts_last_three_beats|\n", "+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+-------+-------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+-----------------+------------+-----+---+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+------------------+-----------------+--------------------+------+---------------+---------------+------------------+------------------+-----------------+------------------+--------------------+-------------------+---------------+---------------+-------------------+------------------+-------------------+-----------------+--------------------+-------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-------------------+--------------------+--------------------+-------------------+------------------+-------------------+------------------+--------------------+----------------+--------------------+--------------------+-----------------------+-----------------------+------------------------+--------------------------+-----------------------+-----------------------+-------------------------+-------------------------+-----------------------+-----------------------+------------------------+--------------------------+-----------------------+-----------------------+-------------------------+-------------------------+-----------------+-------------------+--------------------+--------------------+\n", "|UCSF-1|[104.92111, 105.2...|[58.16541, 58.790...|[1.71809912E12, 1...|[62.364983, 62.34...|       175| 37|           72|    106|     56|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[0.2950426, 0.295...|[-0.15341228, -0....|[0.07959235, 0.07...|[296080, 296281, ...|[298036, 298259, ...|[298862, 299070, ...|[296225, 296426, ...|[0.355, 0.355, 0....|[48.780487, 48.78...|[48.780487, 48.78...|                0|            [326]|           0|   76|325|[1.71809912E12, 1...|[0.21708132, 0.20...|[0.40800267, 0.43...|[0.355, 0.355, 0....|[49.0, 49.0, 49.0...|[49.0, 49.0, 49.0...|[105.0, 105.0, 10...|[62.0, 61.0, 61.0...|108.48617553710938|56.49734115600586|[6.847994E-5, 6.8...|    50|            0.0|            1.0|0.4131734092372935|0.3766465336084366|0.261136245757585|103.29335230932338|-2.94736475131272...|0.25974397346625416|            0.0|            1.0|0.32163328978419303|0.2715716063976288|0.18417933124912847|80.40832244604826|-3.32499363618035...|0.18223758001623672|6.847993790870532E-5|7.137345528462902E-5|7.048225193284452E-5|7.137345528462902E-5|1.338519895886757...|0.01762056298321113|1.480134208791028...|8.021514684570672E-7|0.35499998927116394|0.3700000047683716|0.36537999999523163|0.3700000047683716|0.006938897049205625|91.3449999988079|7.673026695657836E-5|0.004158359150728386|                   49.0|                   55.0|                  54.824|                      55.0|      0.712075985296051|                13706.0|     -5.29160466567589...|        0.709623585962403|                   49.0|                   54.0|                   52.46|                      54.0|      2.312963293437047|                13115.0|      0.02557672922766764|       1.3861182848452702|             [77]|                 []|[0.21708132326602...|[1.718099116032E1...|\n", "+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+-------+-------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+-----------------+------------+-----+---+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+------------------+-----------------+--------------------+------+---------------+---------------+------------------+------------------+-----------------+------------------+--------------------+-------------------+---------------+---------------+-------------------+------------------+-------------------+-----------------+--------------------+-------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-------------------+--------------------+--------------------+-------------------+------------------+-------------------+------------------+--------------------+----------------+--------------------+--------------------+-----------------------+-----------------------+------------------------+--------------------------+-----------------------+-----------------------+-------------------------+-------------------------+-----------------------+-----------------------+------------------------+--------------------------+-----------------------+-----------------------+-------------------------+-------------------------+-----------------+-------------------+--------------------+--------------------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "execution_count": 23}, {"cell_type": "code", "source": ["# Compare to step02 parquet\n", "\n", "beat_extract.write.parquet(\"20250317-ucsf06-step02-pyspark.parquet\")"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-05-05T10:49:03.807897Z", "start_time": "2025-05-05T10:48:13.571007Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["25/05/05 10:48:16 WARN DAGScheduler: Broadcasting large task binary with size 1092.4 KiB\n", "                                                                                \r"]}], "execution_count": 25}, {"cell_type": "code", "source": ["df = spark_session.read.parquet(\"20250317-ucsf06-step02-pyspark.parquet\")\n", "df.show()"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-05-05T10:54:11.945072Z", "start_time": "2025-05-05T10:54:06.995332Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+-------+-------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+-----------------+------------+-----+---+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+------------------+-----------------+--------------------+------+---------------+---------------+------------------+------------------+-----------------+------------------+--------------------+-------------------+---------------+---------------+-------------------+------------------+-------------------+-----------------+--------------------+-------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-------------------+--------------------+--------------------+-------------------+------------------+-------------------+------------------+--------------------+----------------+--------------------+--------------------+-----------------------+-----------------------+------------------------+--------------------------+-----------------------+-----------------------+-------------------------+-------------------------+-----------------------+-----------------------+------------------------+--------------------------+-----------------------+-----------------------+-------------------------+-------------------------+-----------------+-------------------+--------------------+--------------------+\n", "|    id|                 sbp|                 dbp|                  ts|                 abp|weight_lbs|age|height_inches|ref_sbp|ref_dbp|hypertension_present|diabetes_present|tobacco|gender_Female|gender_Male|race_American Indian or Alaska Native|race_Asian|race_Black or African American|race_White|BMI|sampling_freq_hz|          ppg_butter|          ecg_butter|        ecg_enhanced|    ppg_butter_peaks|  ppg_butter_valleys|  ecg_enhanced_peaks|ecg_enhanced_valleys|                 pat|         hr_from_ecg|         hr_from_ppg|__index_level_0__|valid_cut_valleys|split_number|begin|end|          ts_episode|         ppg_episode|         ecg_episode|         pat_episode| hr_from_ecg_episode| hr_from_ppg_episode|         sbp_episode|         dbp_episode|  sbp_episode_last| dbp_episode_last|           pat_ratio|ref_pp|ppg_episode_min|ppg_episode_max|  ppg_episode_mean|ppg_episode_median|  ppg_episode_std|   ppg_episode_sum|   ppg_episode_trend|  ppg_episode_error|ecg_episode_min|ecg_episode_max|   ecg_episode_mean|ecg_episode_median|    ecg_episode_std|  ecg_episode_sum|   ecg_episode_trend|  ecg_episode_error|       pat_ratio_min|       pat_ratio_max|      pat_ratio_mean|    pat_ratio_median|       pat_ratio_std|      pat_ratio_sum|     pat_ratio_trend|     pat_ratio_error|    pat_episode_min|   pat_episode_max|   pat_episode_mean|pat_episode_median|     pat_episode_std| pat_episode_sum|   pat_episode_trend|   pat_episode_error|hr_from_ecg_episode_min|hr_from_ecg_episode_max|hr_from_ecg_episode_mean|hr_from_ecg_episode_median|hr_from_ecg_episode_std|hr_from_ecg_episode_sum|hr_from_ecg_episode_trend|hr_from_ecg_episode_error|hr_from_ppg_episode_min|hr_from_ppg_episode_max|hr_from_ppg_episode_mean|hr_from_ppg_episode_median|hr_from_ppg_episode_std|hr_from_ppg_episode_sum|hr_from_ppg_episode_trend|hr_from_ppg_episode_error|ppg_episode_peaks|ppg_episode_valleys|    last_three_beats| ts_last_three_beats|\n", "+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+-------+-------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+-----------------+------------+-----+---+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+------------------+-----------------+--------------------+------+---------------+---------------+------------------+------------------+-----------------+------------------+--------------------+-------------------+---------------+---------------+-------------------+------------------+-------------------+-----------------+--------------------+-------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-------------------+--------------------+--------------------+-------------------+------------------+-------------------+------------------+--------------------+----------------+--------------------+--------------------+-----------------------+-----------------------+------------------------+--------------------------+-----------------------+-----------------------+-------------------------+-------------------------+-----------------------+-----------------------+------------------------+--------------------------+-----------------------+-----------------------+-------------------------+-------------------------+-----------------+-------------------+--------------------+--------------------+\n", "|UCSF-1|[104.92111, 105.2...|[58.16541, 58.790...|[1.71809912E12, 1...|[62.364983, 62.34...|       175| 37|           72|    106|     56|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[0.2950426, 0.295...|[-0.15341228, -0....|[0.07959235, 0.07...|[296080, 296281, ...|[298036, 298259, ...|[298862, 299070, ...|[296225, 296426, ...|[0.355, 0.355, 0....|[48.780487, 48.78...|[48.780487, 48.78...|                0|            [326]|           0|   76|325|[1.71809912E12, 1...|[0.21708132, 0.20...|[0.40800267, 0.43...|[0.355, 0.355, 0....|[49.0, 49.0, 49.0...|[49.0, 49.0, 49.0...|[105.0, 105.0, 10...|[62.0, 61.0, 61.0...|108.48617553710938|56.49734115600586|[6.847994E-5, 6.8...|    50|            0.0|            1.0|0.4131734092372935|0.3766465336084366|0.261136245757585|103.29335230932338|-2.94736475131272...|0.25974397346625416|            0.0|            1.0|0.32163328978419303|0.2715716063976288|0.18417933124912847|80.40832244604826|-3.32499363618035...|0.18223758001623672|6.847993790870532E-5|7.137345528462902E-5|7.048225193284452E-5|7.137345528462902E-5|1.338519895886757...|0.01762056298321113|1.480134208791028...|8.021514684570672E-7|0.35499998927116394|0.3700000047683716|0.36537999999523163|0.3700000047683716|0.006938897049205625|91.3449999988079|7.673026695657836E-5|0.004158359150728386|                   49.0|                   55.0|                  54.824|                      55.0|      0.712075985296051|                13706.0|     -5.29160466567589...|        0.709623585962403|                   49.0|                   54.0|                   52.46|                      54.0|      2.312963293437047|                13115.0|      0.02557672922766764|       1.3861182848452702|             [77]|                 []|[0.21708132326602...|[1.718099116032E1...|\n", "+------+--------------------+--------------------+--------------------+--------------------+----------+---+-------------+-------+-------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-----------------+-----------------+------------+-----+---+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+------------------+-----------------+--------------------+------+---------------+---------------+------------------+------------------+-----------------+------------------+--------------------+-------------------+---------------+---------------+-------------------+------------------+-------------------+-----------------+--------------------+-------------------+--------------------+--------------------+--------------------+--------------------+--------------------+-------------------+--------------------+--------------------+-------------------+------------------+-------------------+------------------+--------------------+----------------+--------------------+--------------------+-----------------------+-----------------------+------------------------+--------------------------+-----------------------+-----------------------+-------------------------+-------------------------+-----------------------+-----------------------+------------------------+--------------------------+-----------------------+-----------------------+-------------------------+-------------------------+-----------------+-------------------+--------------------+--------------------+\n", "\n"]}], "execution_count": 26}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-05T11:21:37.090685Z", "start_time": "2025-05-05T11:21:20.180571Z"}}, "cell_type": "code", "source": ["df_real = spark_session.read.parquet(\"20250317-ucsf06-step02.parquet\")\n", "df_real.filter(<PERSON><PERSON>col(\"__index_level_0__\")==0)\n", "df_real.show()"], "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["+------+---------+-----------+----------+---+-------------+------------+---------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+------+---------------+---------------+-----------------+-------------------+-----------------+------------------+--------------------+-------------------+---------------+---------------+-----------------+------------------+-----------------+-----------------+--------------------+-------------------+--------------------+--------------------+---------------+--------------------+--------------+-------------------+--------------------+--------------------+---------------+---------------+----------------+------------------+-----------------+-----------------+--------------------+--------------------+-----------------------+-----------------------+------------------------+--------------------------+-----------------------+-----------------------+-------------------------+-------------------------+-----------------------+-----------------------+------------------------+--------------------------+-----------------------+-----------------------+-------------------------+-------------------------+--------------------+--------------------+--------------------+--------------------+-----------------+\n", "|    id|      sbp|        dbp|weight_lbs|age|height_inches|     ref_sbp|  ref_dbp|hypertension_present|diabetes_present|tobacco|gender_Female|gender_Male|race_American Indian or Alaska Native|race_Asian|race_Black or African American|race_White|BMI|sampling_freq_hz|        ecg_enhanced|ecg_enhanced_valleys|split_number|          ts_episode|         ppg_episode|         ecg_episode|         pat_episode| hr_from_ecg_episode| hr_from_ppg_episode|         sbp_episode|         dbp_episode|           pat_ratio|ref_pp|ppg_episode_min|ppg_episode_max| ppg_episode_mean| ppg_episode_median|  ppg_episode_std|   ppg_episode_sum|   ppg_episode_trend|  ppg_episode_error|ecg_episode_min|ecg_episode_max| ecg_episode_mean|ecg_episode_median|  ecg_episode_std|  ecg_episode_sum|   ecg_episode_trend|  ecg_episode_error|       pat_ratio_min|       pat_ratio_max| pat_ratio_mean|    pat_ratio_median| pat_ratio_std|      pat_ratio_sum|     pat_ratio_trend|     pat_ratio_error|pat_episode_min|pat_episode_max|pat_episode_mean|pat_episode_median|  pat_episode_std|  pat_episode_sum|   pat_episode_trend|   pat_episode_error|hr_from_ecg_episode_min|hr_from_ecg_episode_max|hr_from_ecg_episode_mean|hr_from_ecg_episode_median|hr_from_ecg_episode_std|hr_from_ecg_episode_sum|hr_from_ecg_episode_trend|hr_from_ecg_episode_error|hr_from_ppg_episode_min|hr_from_ppg_episode_max|hr_from_ppg_episode_mean|hr_from_ppg_episode_median|hr_from_ppg_episode_std|hr_from_ppg_episode_sum|hr_from_ppg_episode_trend|hr_from_ppg_episode_error|   ppg_episode_peaks| ppg_episode_valleys|    last_three_beats| ts_last_three_beats|__index_level_0__|\n", "+------+---------+-----------+----------+---+-------------+------------+---------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+------+---------------+---------------+-----------------+-------------------+-----------------+------------------+--------------------+-------------------+---------------+---------------+-----------------+------------------+-----------------+-----------------+--------------------+-------------------+--------------------+--------------------+---------------+--------------------+--------------+-------------------+--------------------+--------------------+---------------+---------------+----------------+------------------+-----------------+-----------------+--------------------+--------------------+-----------------------+-----------------------+------------------------+--------------------------+-----------------------+-----------------------+-------------------------+-------------------------+-----------------------+-----------------------+------------------------+--------------------------+-----------------------+-----------------------+-------------------------+-------------------------+--------------------+--------------------+--------------------+--------------------+-----------------+\n", "|UCSF-1|98.461419|55.87494025|       175| 37|           72|106.90455625|56.530522|                   0|               0|      0|            0|          1|                                    0|         0|                             0|         1| 24|             200|[0.08002757067067...|[1, 210, 432, 677...|         0.0|[1.718099122337E1...|[0.04893275543791...|[0.30651167370037...|[0.35, 0.35, 0.35...|[57.0, 57.0, 57.0...|[50.0, 50.0, 50.0...|[107.0, 107.0, 10...|[59.0, 59.0, 58.0...|[6.75154320987654...|    50|            0.0|            1.0|0.460032246494388|0.45731618066802904|0.213736335408866|1150.0806162359709|-1.21632104074011...|0.21351321690717057|            0.0|            1.0|0.292829587606768|0.2611697503425767|0.159253048886697|732.0739690169211|8.429265863080948E-7|0.15922003297673373|6.751543209876543E-5|7.233796296296296E-5|7.0014660494E-5|6.944444444444444E-5|1.634213587E-6|0.17503665123456788|3.625185765230638...|1.633884616947921E-6|           0.35|          0.375|        0.362956|              0.36|0.008471763234943|907.3899999999999|1.879296300717851...|0.008470057854258034|                   48.0|                   58.0|                 53.4256|                      55.0|      3.518382617979261|               133564.0|     -0.00193302251728...|       3.2292308048194966|                   49.0|                   57.0|                 53.5336|                      55.0|      3.043250826047057|               133834.0|     -0.00120062073609...|       2.9166581972037826|[50, 262, 501, 71...|[217, 452, 666, 8...|[0.13158861651812...|[1.718099131422E1...|                0|\n", "+------+---------+-----------+----------+---+-------------+------------+---------+--------------------+----------------+-------+-------------+-----------+-------------------------------------+----------+------------------------------+----------+---+----------------+--------------------+--------------------+------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+------+---------------+---------------+-----------------+-------------------+-----------------+------------------+--------------------+-------------------+---------------+---------------+-----------------+------------------+-----------------+-----------------+--------------------+-------------------+--------------------+--------------------+---------------+--------------------+--------------+-------------------+--------------------+--------------------+---------------+---------------+----------------+------------------+-----------------+-----------------+--------------------+--------------------+-----------------------+-----------------------+------------------------+--------------------------+-----------------------+-----------------------+-------------------------+-------------------------+-----------------------+-----------------------+------------------------+--------------------------+-----------------------+-----------------------+-------------------------+-------------------------+--------------------+--------------------+--------------------+--------------------+-----------------+\n", "\n"]}], "execution_count": 32}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ""}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 0}