from pyspark.sql import SparkSession
import etl
import time
import numpy as np
import pyspark.sql.functions as F

def main():
    spark_session = SparkSession.builder \
                .appName("LocalTestApp") \
                .master("local[*]") \
                .getOrCreate()

    # Lists to store the durations for each timed step
    durations_extract = []
    durations_group = []
    durations_explode = []
    iteration_totals = []

    # Run the pipeline 10 times
    for run_index in range(10):
        iteration_start = time.time()

        # Step 1: Extract (CSV)
        start_time = time.time()
        extractor_csv_local = etl.annotators.extract.local.ExtractCSV(spark_session=spark_session) \
            .set_csv_path("../../data/empl-long/EMPL_long_01_frameLevel.csv") \
            .set_input_cols(["emp_ID", "yData"])
        df = extractor_csv_local.fit().transform()
        durations_extract.append(time.time() - start_time)

        # Step 2: Group
        start_time = time.time()
        grouper = etl.annotators.dataprep.rearrange.GroupRowsValues() \
                    .set_groupby_cols(["emp_ID"]) \
                    .fit() \
                    .transform(df)
        durations_group.append(time.time() - start_time)

        # Cutpoints assignment (not timed separately)
        grouper = grouper.withColumn('cutpoints', F.array([F.lit(i) for i in [50000, 100000, 150000]]))

        # Step 3: Explode
        start_time = time.time()
        exploder = etl.annotators.signals.rearrange.ExplodeSignal() \
                    .set_cuts_col("cutpoints") \
                    .set_input_cols(["yData"]) \
                    .set_output_cols(["segmented_signal"]) \
                    .set_split_method("all_indices") \
                    .fit() \
                    .transform(grouper)
        durations_explode.append(time.time() - start_time)

        # Record total time for this entire iteration
        iteration_totals.append(time.time() - iteration_start)

    # Compute and print mean durations
    mean_extract = np.mean(durations_extract)
    mean_group = np.mean(durations_group)
    mean_explode = np.mean(durations_explode)
    mean_total = np.mean(iteration_totals)

    print("Mean Extract time:", mean_extract, "seconds")
    print("Mean Group time:", mean_group, "seconds")
    print("Mean Explode time:", mean_explode, "seconds")
    print("Mean Total iteration time:", mean_total, "seconds")

if __name__ == "__main__":
    main()