{"cells": [{"cell_type": "code", "execution_count": 2, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["bash: warning: setlocale: LC_ALL: cannot change locale (en_US.UTF-8)\n", "bash: warning: setlocale: LC_ALL: cannot change locale (en_US.UTF-8)\n", "25/03/27 09:57:52 WARN Utils: Your hostname, PC-SANTI resolves to a loopback address: *********; using ************** instead (on interface lo)\n", "25/03/27 09:57:52 WARN Utils: Set SPARK_LOCAL_IP if you need to bind to another address\n", "Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n", "25/03/27 09:57:52 WARN NativeCodeLoader: Unable to load native-hadoop library for your platform... using builtin-java classes where applicable\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Mean Extract time: 0.8218933582305908 seconds\n", "Mean Group time: 0.008253073692321778 seconds\n", "Mean Explode time: 0.044871282577514646 seconds\n", "Mean Total iteration time: 0.8792771816253662 seconds\n"]}], "source": ["from pyspark.sql import SparkSession\n", "import etl\n", "import time\n", "import numpy as np\n", "import pyspark.sql.functions as F\n", "\n", "spark_session = SparkSession.builder \\\n", "            .appName(\"LocalTestApp\") \\\n", "            .master(\"local[*]\") \\\n", "            .getOrCreate()\n", "\n", "# Lists to store the durations for each timed step\n", "durations_extract = []\n", "durations_group = []\n", "durations_explode = []\n", "iteration_totals = []\n", "\n", "# Run the pipeline 10 times\n", "for run_index in range(10):\n", "    iteration_start = time.time()\n", "\n", "    # Step 1: Extract (CSV)\n", "    start_time = time.time()\n", "    extractor_csv_local = etl.annotators.extract.local.ExtractCSV(spark_session=spark_session) \\\n", "        .set_csv_path(\"../../data/empl-long/EMPL_long_01_frameLevel.csv\") \\\n", "        .set_input_cols([\"emp_ID\", \"yData\"])\n", "    df = extractor_csv_local.fit().transform()\n", "    durations_extract.append(time.time() - start_time)\n", "\n", "    # Step 2: Group\n", "    start_time = time.time()\n", "    grouper = etl.annotators.dataprep.rearrange.GroupRowsValues() \\\n", "                .set_groupby_cols([\"emp_ID\"]) \\\n", "                .fit() \\\n", "                .transform(df)\n", "    durations_group.append(time.time() - start_time)\n", "\n", "    # Cutpoints assignment (not timed separately)\n", "    grouper = grouper.withColumn('cutpoints', F.array([F.lit(i) for i in [50000, 100000, 150000]]))\n", "\n", "    # Step 3: Explode\n", "    start_time = time.time()\n", "    exploder = etl.annotators.signals.rearrange.ExplodeSignal() \\\n", "                .set_cuts_col(\"cutpoints\") \\\n", "                .set_input_cols([\"yData\"]) \\\n", "                .set_output_cols([\"segmented_signal\"]) \\\n", "                .set_split_method(\"all_indices\") \\\n", "                .fit() \\\n", "                .transform(grouper)\n", "    durations_explode.append(time.time() - start_time)\n", "\n", "    # Record total time for this entire iteration\n", "    iteration_totals.append(time.time() - iteration_start)\n", "\n", "# Compute and print mean durations\n", "mean_extract = np.mean(durations_extract)\n", "mean_group = np.mean(durations_group)\n", "mean_explode = np.mean(durations_explode)\n", "mean_total = np.mean(iteration_totals)\n", "\n", "print(\"Mean Extract time:\", mean_extract, \"seconds\")\n", "print(\"Mean Group time:\", mean_group, \"seconds\")\n", "print(\"Mean Explode time:\", mean_explode, \"seconds\")\n", "print(\"Mean Total iteration time:\", mean_total, \"seconds\")"], "metadata": {"collapsed": false}}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 0}