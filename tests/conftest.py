"""
Global test configuration and fixtures for FHIR-OMOP project.

This file provides shared fixtures and configuration for all tests in the project,
following the testing standards defined in docs/guides/development/unit_testing_standards.md
"""

import pytest
import sys
import os
from pathlib import Path
from unittest.mock import Mock, patch
from typing import Dict, Any

# Add project root to path for imports
PROJECT_ROOT = Path(__file__).parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

# Add src directory to path
SRC_DIR = PROJECT_ROOT / "src"
sys.path.insert(0, str(SRC_DIR))

# Add setup scripts directory to path
SETUP_SCRIPTS_DIR = PROJECT_ROOT / "servers" / "omop-database" / "scripts" / "setup"
sys.path.insert(0, str(SETUP_SCRIPTS_DIR))


@pytest.fixture
def mock_env_config():
    """Standard environment configuration for testing."""
    return {
        'OMOP_DB_HOST': 'localhost',
        'OMOP_DB_PORT': '5432',
        'OMOP_DB_NAME': 'test_omop_cdm',
        'OMOP_DB_USERNAME': 'test_omop_user',
        'OMOP_DB_PASSWORD': 'test_password',
        'POSTGRES_ADMIN_USER': 'postgres',
        'POSTGRES_ADMIN_DB': 'postgres',
        'FHIR_SERVER_URL': 'http://localhost:8080/fhir'
    }


@pytest.fixture
def temp_data_dir(tmp_path):
    """Temporary directory with test data structure."""
    data_dir = tmp_path / "test_data"
    data_dir.mkdir()
    
    # Create subdirectories that match project structure
    (data_dir / "fhir_data").mkdir()
    (data_dir / "vocabulary").mkdir()
    (data_dir / "generated_bundles").mkdir()
    
    return data_dir


@pytest.fixture
def mock_database_connection():
    """Mock database connection for module tests."""
    with patch('psycopg2.connect') as mock_conn:
        mock_cursor = Mock()
        mock_conn.return_value.cursor.return_value = mock_cursor
        mock_conn.return_value.autocommit = True
        yield mock_conn


@pytest.fixture
def sample_fhir_patient():
    """Sample FHIR Patient resource for testing."""
    return {
        "resourceType": "Patient",
        "id": "test-patient-123",
        "name": [{"family": "Doe", "given": ["John"]}],
        "gender": "male",
        "birthDate": "1990-01-01"
    }


@pytest.fixture
def sample_omop_person():
    """Standard OMOP Person record for testing."""
    return {
        "person_id": 1,
        "gender_concept_id": 8507,
        "year_of_birth": 1990,
        "month_of_birth": 1,
        "day_of_birth": 1,
        "birth_datetime": "1990-01-01 00:00:00",
        "race_concept_id": 8527,
        "ethnicity_concept_id": 38003564,
        "location_id": None,
        "provider_id": None,
        "care_site_id": None,
        "person_source_value": "test-patient-123",
        "gender_source_value": "M",
        "gender_source_concept_id": 0,
        "race_source_value": "Unknown",
        "race_source_concept_id": 0,
        "ethnicity_source_value": "Unknown",
        "ethnicity_source_concept_id": 0
    }


# Configure pytest markers
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "unit: Unit tests (fast, no external dependencies)"
    )
    config.addinivalue_line(
        "markers", "integration: Integration tests (may use test database)"
    )
    config.addinivalue_line(
        "markers", "slow: Tests that take longer than 30 seconds"
    )
    config.addinivalue_line(
        "markers", "external: Tests requiring external services"
    )
    config.addinivalue_line(
        "markers", "omop: Tests specific to OMOP database functionality"
    )
    config.addinivalue_line(
        "markers", "fhir: Tests specific to FHIR processing"
    )