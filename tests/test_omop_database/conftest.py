"""
Test configuration and fixtures for OMOP database testing.

This file provides shared fixtures and configuration for testing OMOP database
functionality following the project's testing standards.

Note: Uses OMOP-specific naming for fixtures (e.g., mock_omop_env_config) to avoid
conflicts with global fixtures in tests/conftest.py.
"""

import pytest
import sys
import os
from pathlib import Path
from unittest.mock import Mock, patch
from typing import Dict, Any

# Add the setup scripts directory to the path for imports
SETUP_SCRIPTS_DIR = Path(__file__).parent.parent.parent / "servers" / "omop-database" / "scripts" / "setup"
sys.path.insert(0, str(SETUP_SCRIPTS_DIR))

# Import modules here so they're available for all tests
from create_database import OMOPDatabaseCreator
from database_checker import get_database_status
from config import OMOP_DB_NAME, OMOP_DB_USERNAME, OMOP_CDM_EXPECTED_TABLES


@pytest.fixture
def database_creator_class():
    """Provides access to OMOPDatabaseCreator class for testing."""
    return OMOPDatabaseCreator


@pytest.fixture
def database_status_function():
    """Provides access to get_database_status function for testing."""
    return get_database_status


@pytest.fixture
def mock_omop_env_config():
    """OMOP-specific environment configuration for testing."""
    return {
        'OMOP_DB_HOST': 'localhost',
        'OMOP_DB_PORT': '5432',
        'OMOP_DB_NAME': 'test_omop_cdm',
        'OMOP_DB_USERNAME': 'test_omop_user',
        'OMOP_DB_PASSWORD': 'test_password',
        'POSTGRES_ADMIN_USER': 'postgres',
        'POSTGRES_ADMIN_DB': 'postgres'
    }


@pytest.fixture
def mock_db_connection():
    """Mock database connection with standard methods."""
    mock_conn = Mock()
    mock_cursor = Mock()
    mock_conn.cursor.return_value = mock_cursor
    mock_conn.autocommit = True
    mock_cursor.fetchone.return_value = (1,)
    mock_cursor.fetchall.return_value = [(1,)]
    return mock_conn, mock_cursor


@pytest.fixture
def sample_database_status():
    """Sample database status response for testing."""
    return {
        'user_exists': True,
        'database_exists': True,
        'tables_exist': True
    }


@pytest.fixture
def sample_ddl_execution_order():
    """Sample DDL execution order for testing."""
    return [
        'OMOPCDM_postgresql_5.4_ddl.sql',
        'OMOPCDM_postgresql_5.4_primary_keys.sql',
        'OMOPCDM_postgresql_5.4_indices.sql',
        'OMOPCDM_postgresql_5.4_constraints.sql'
    ]


@pytest.fixture
def temp_ddl_directory(tmp_path):
    """Temporary directory for DDL scripts testing."""
    ddl_dir = tmp_path / "ddl_scripts"
    ddl_dir.mkdir()
    return ddl_dir


@pytest.fixture
def mock_config_constants():
    """Mock configuration constants for testing."""
    with patch.multiple(
        'config',
        OMOP_DB_HOST='localhost',
        OMOP_DB_PORT='5432',
        OMOP_DB_NAME='test_omop_cdm',
        OMOP_DB_USERNAME='test_omop_user',
        OMOP_DB_PASSWORD='test_password',
        POSTGRES_ADMIN_USER='postgres',
        POSTGRES_ADMIN_DB='postgres',
        DDL_BASE_URL='https://raw.githubusercontent.com/OHDSI/CommonDataModel/v5.4.2/inst/ddl/5.4/postgresql',
        DDL_SCRIPTS_DIR='/tmp/ddl_scripts',
        DDL_EXECUTION_ORDER=[
            'OMOPCDM_postgresql_5.4_ddl.sql',
            'OMOPCDM_postgresql_5.4_primary_keys.sql',
            'OMOPCDM_postgresql_5.4_indices.sql',
            'OMOPCDM_postgresql_5.4_constraints.sql'
        ],
        OMOP_CDM_EXPECTED_TABLES=39
    ):
        yield


# Make constants available for tests
OMOP_CDM_EXPECTED_TABLES = 39


@pytest.fixture
def mock_successful_psql_execution():
    """Mock successful psql command execution."""
    with patch('subprocess.run') as mock_run:
        mock_run.return_value.returncode = 0
        mock_run.return_value.stdout = "Command executed successfully"
        mock_run.return_value.stderr = ""
        yield mock_run


@pytest.fixture
def mock_failed_psql_execution():
    """Mock failed psql command execution."""
    with patch('subprocess.run') as mock_run:
        mock_run.return_value.returncode = 1
        mock_run.return_value.stdout = ""
        mock_run.return_value.stderr = "psql: error: connection to server failed"
        yield mock_run
