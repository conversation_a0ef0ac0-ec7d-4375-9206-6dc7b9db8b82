from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

# Define requirements directly since we use environment.yml for dependency management
requirements = [
    "pandas>=1.5.0",
    "sqlalchemy>=1.4.0",
    "psycopg2-binary>=2.9.0",
    "python-dotenv>=0.19.0",
    "pyyaml>=6.0",
    "requests>=2.28.0",
    "fhir.resources>=8.0.0",
]

setup(
    name="fhir_omop",
    version="0.1.0",
    author="AIO FHIR-OMOP Development Team",
    author_email="<EMAIL>",
    description="FHIR to OMOP CDM Transformation Pipeline - Production-Ready ETL Components",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/AIOjPINEDA/fhir-omop",
    packages=find_packages(where="src"),
    package_dir={"":"src"},
    classifiers=[
        "Programming Language :: Python :: 3",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Healthcare Industry",
        "Topic :: Scientific/Engineering :: Medical Science Apps.",
    ],
    python_requires=">=3.11",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "abu-dhabi-etl=fhir_omop.etl.abu_dhabi_claims_mvp.run_pipeline:main",
        ],
    },
)
