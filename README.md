# FHIR to OMOP CDM Transformation Pipeline

A comprehensive toolkit developed by AIO (Artificial Intelligence Orchestrator) for transforming healthcare data from HL7 FHIR format to OMOP Common Data Model (CDM), enabling standardized analytics and research.

## Overview

This internal repository provides a modular, extensible framework for transforming FHIR (Fast Healthcare Interoperability Resources) data to the OMOP (Observational Medical Outcomes Partnership) Common Data Model. It includes Python-based ETL tools, mapping strategies, and comprehensive documentation based on extensive research of existing implementations.

## Current Implementation Status

### ✅ **Production-Ready Components**

- **OMOP Database Module**: Complete PostgreSQL deployment with Docker containerization, official OHDSI vocabulary loading, and automated management scripts
- **FHIR Server**: Local HAPI FHIR R4 server with PostgreSQL 14 database support and comprehensive data loading tools
- **Comprehensive Documentation**: Detailed implementation guides, coding standards, and technical analysis with academic methodology

### 🚧 **In Development**

- **FHIR-to-OMOP ETL Pipeline**: Core transformation engine from FHIR R4 to OMOP CDM v5.4.2
- **Unit Testing Framework**: Comprehensive testing standards defined, implementation in progress
- **Architecture Standardization**: Ongoing work to standardize FHIR and OMOP server architectures

### 📋 **Planned Features**

- **Generic FHIR Mappers**: Modular mappers for different FHIR resource types (design templates available in `archive/template-code-complete` branch)
- **Advanced Vocabulary Services**: Enhanced concept mapping and terminology services
- **Interactive Demo Application**: Streamlit-based application for visualizing FHIR data and exploring transformations

### 🗂️ **Template Code Archive**

Research-based template code and design references have been preserved in the `archive/template-code-complete` branch. This includes well-documented FHIR mapper templates, utility module designs, and comprehensive analysis of existing implementations that can guide future development.

## OMOP CDM Documentation

For comprehensive OMOP Common Data Model documentation, refer to this AI-enhanced resource:

[![Ask DeepWiki](https://deepwiki.com/badge.svg)](https://deepwiki.com/OHDSI/CommonDataModel)

This documentation provides an easy-to-read, restructured version of the official OMOP CDM repository, created with AI assistance for better accessibility and understanding.

## Architecture Overview

```mermaid
graph TD
    subgraph "Data Sources"
        A1[FHIR Server] -->|Export| A2[FHIR Resources]
        A3[FHIR Files] --> A2
    end

    subgraph "ETL Process"
        A2 -->|1 Extract| B[Resource Extraction]
        B -->|2 Transform| C[Mappers]
        D[Vocabulary Service] -->|Concept Mapping| C
        C -->|3 Load| E[OMOP CDM Database]
    end

    %% Apply styles with better contrast
    style A1 fill:#d770ad,stroke:#333,stroke-width:2px,color:#fff
    style A2 fill:#d770ad,stroke:#333,stroke-width:2px,color:#fff
    style A3 fill:#d770ad,stroke:#333,stroke-width:2px,color:#fff
    style B fill:#5d8aa8,stroke:#333,stroke-width:2px,color:#fff
    style C fill:#5d8aa8,stroke:#333,stroke-width:2px,color:#fff
    style D fill:#6b8e23,stroke:#333,stroke-width:2px,color:#fff
    style E fill:#b87333,stroke:#333,stroke-width:2px,color:#fff
```

This diagram shows the high-level flow of data through the system:

1. **Data Sources**: FHIR data comes from either a FHIR server or FHIR files
2. **Resource Extraction**: Raw FHIR resources are extracted and validated
3. **Mappers**: FHIR resources are transformed to OMOP format with help from the Vocabulary Service
4. **Vocabulary Service**: Provides standard concept mappings (e.g., SNOMED to OMOP concepts)
5. **OMOP Database**: Transformed data is loaded into OMOP CDM tables

For detailed architecture information, please see the [Reference Architecture](docs/architecture/reference_architecture.md) document.

## Repository Structure

```
fhir-omop/
├── servers/                        # Production server modules
│   ├── fhir-server/                # HAPI FHIR R4 server (Docker)
│   └── omop-database/              # OMOP CDM database (Docker)
├── src/fhir_omop/                  # ETL development area
│   └── etl/abu_dhabi_claims_mvp/   # Experimental CSV-to-OMOP pipeline
├── tests/                          # Testing framework
│   └── test_omop_database/         # OMOP database tests
├── docs/                           # Documentation
│   ├── guides/                     # Setup and development guides
│   ├── research/                   # Research and analysis
│   └── architecture/               # System architecture
├── data/                           # Data files and vocabulary
└── demo/                           # Demo applications
```

## Technology Stack

This project uses specific versions for compatibility and stability:

- **OMOP CDM**: Version 5.4.2 for all database schemas and references
- **HAPI FHIR**: R4 with `hapiproject/hapi:latest` Docker image
- **PostgreSQL**: Version 14 (recommended for HAPI FHIR compatibility)
- **Python**: 3.8+ with Conda environment management
- **Docker**: Version 20.10.x or higher with Compose v2.x

## Getting Started

### Prerequisites

- [Docker Desktop](https://www.docker.com/products/docker-desktop/) (version 20.10.x or higher)
- [Docker Compose](https://docs.docker.com/compose/install/) (version 2.x or higher)
- [Miniconda](https://docs.conda.io/en/latest/miniconda.html) or [Anaconda](https://www.anaconda.com/products/distribution)
- Python 3.11 or higher (for scripts and database operations)
- [OHDSI Athena account](https://athena.ohdsi.org/) for vocabulary downloads
- [UMLS account](https://uts.nlm.nih.gov/uts/) for CPT-4 reconstitution

### Setup

Follow our comprehensive [Setup Guide](SETUP.md) which covers:

1. Registering for required accounts (Athena, UMLS)
2. Downloading and configuring OMOP vocabularies
3. Setting up the FHIR server
   - [FHIR Server Documentation](docs/guides/fhir/server/00-index.md)
4. Setting up the OMOP database
   - [OMOP Database Setup](docs/guides/omop/database/overview.md)
5. Configuring the environment

### Installation

1. Clone the repository:
   ```bash
   git clone [internal-repository-url]/fhir-omop.git
   cd fhir-omop
   ```

2. Set up the Conda environment:
   ```bash
   # Create and activate the Conda environment
   conda env create -f environment.yml
   conda activate fhir-omop

   # Set up Jupyter kernel for this project
   python -m ipykernel install --user --name fhir-omop --display-name "Python (FHIR-OMOP)"
   ```

3. Configure the connection settings in `.env` file (created from `.env.example`)

   Note: This project uses Conda for environment management. All dependencies are defined in the `environment.yml` file.

### Basic Usage

#### Starting the FHIR Server

```bash
# Start with PostgreSQL (recommended for production)
cd servers/fhir-server
./manage-fhir-server.sh start postgres

# Or start with default configuration (for development/testing)
cd servers/fhir-server
./start-fhir-server.sh start
```

#### Loading Sample Data

```bash
# Load sample data into the FHIR server using data loading scripts
cd servers/fhir-server

# Method 1: Direct Transaction Bundles (requires server configuration)
python scripts/data_loading/ndjson_to_bundle.py \
    --input-file ../data/sample_fhir/bulk-export/Patient.000.ndjson \
    --output-file ../data/generated_bundles/Patient_bundle.json

python scripts/data_loading/send_bundle.py \
    --input-file ../data/generated_bundles/Patient_bundle.json \
    --server-url http://localhost:8080/fhir

# Method 2: Selective Loading (works with default server configuration)
python scripts/data_loading/selective_loader.py \
    --data-dir ../data/sample_fhir/bulk-export \
    --server-url http://localhost:8080/fhir \
    --verify

# Method 3: Bulk Loading with Performance Metrics
# First convert NDJSON files to transaction bundles
python scripts/data_loading/ndjson_to_bundle.py \
    --input-dir ../data/sample_fhir/bulk-export \
    --output-dir ../data/generated_bundles/bulk_export_bundles

# Then load all bundles with performance metrics
python scripts/data_loading/load_all_bundles.py \
    --bundle-dir ../data/generated_bundles/bulk_export_bundles \
    --server-url http://localhost:8080/fhir \
    --export-performance \
    --test-id "initial_load_test"
```

For detailed instructions on data loading methods, see the [FHIR Data Loading Documentation](docs/guides/fhir/data-loading/quick-reference.md) and the scripts documentation in `servers/fhir-server/scripts/data_loading/`.

#### Running the Interactive Demo Application

The Streamlit-based demo application provides an intuitive interface for interacting with the FHIR server:

```bash
# Set up and run the demo application
cd demo/demo_streamlit_app
chmod +x setup_and_run.sh
./setup_and_run.sh

# Or manually with conda
conda env create -f demo/demo_streamlit_app/environment.yml
conda activate fhir-demo
cd demo
streamlit run demo_streamlit_app/app.py
```

The application will be available at http://localhost:8501 and provides:
- Data loading interface with performance metrics
- Data exploration with FHIR queries
- Visualizations of resource distribution and patient demographics
- Server management capabilities

#### Current Development Status

**FHIR Server** (Production-Ready):
```bash
cd servers/fhir-server
./manage-fhir-server.sh start postgres
```

**OMOP Database** (Production-Ready):
```bash
cd servers/omop-database
./manage-omop-database.sh setup full
```

**ETL Pipeline**: Currently in development. The abu_dhabi_claims_mvp is an experimental CSV-to-OMOP transformer, not the final FHIR-to-OMOP ETL.

For more detailed usage, see the [Setup Guide](SETUP.md) and [FHIR Server Documentation](docs/guides/fhir/server/00-index.md) in the documentation.

## Documentation

Comprehensive documentation is available in the `docs/` directory. All code includes references to original sources and implementation guides to ensure proper attribution and facilitate troubleshooting:

### Setup and Configuration
- [Setup Guide](SETUP.md) - Complete setup process with external registrations
- [Troubleshooting](docs/guides/general/troubleshooting.md) - Solutions for common issues

### Tutorials
- [Learning Path](docs/tutorials/learning_path.md) - Comprehensive learning path for FHIR to OMOP transformation
- [Introduction to FHIR](docs/tutorials/01_Introduction_to_FHIR.ipynb) - Learn the basics of FHIR standard
- [Introduction to OMOP CDM](docs/tutorials/02_Introduction_to_OMOP_CDM.ipynb) - Learn the basics of OMOP Common Data Model
- [Mapping FHIR to OMOP](docs/tutorials/03_Mapping_FHIR_to_OMOP.ipynb) - Learn how to map FHIR resources to OMOP tables
- [Data Visualization and Analysis](docs/tutorials/04_Data_Visualization_and_Analysis.ipynb) - Visualize and analyze transformed data
- [Athena Registration](docs/tutorials/athena_registration.md) - How to register on OHDSI Athena
- [Vocabulary Download](docs/tutorials/vocabulary_download.md) - How to download OMOP vocabularies
- [UMLS Registration](docs/tutorials/umls_registration.md) - How to register for UMLS API access

### Technical Documentation
- [Reference Architecture](docs/architecture/reference_architecture.md) - Detailed system architecture and design
- [FHIR Server Documentation](docs/guides/fhir/server/00-index.md) - Comprehensive guide for the HAPI FHIR server
  - [Server Setup](docs/guides/fhir/server/03-server_setup.md) - Setting up the FHIR server with PostgreSQL
  - [Data Interaction](docs/guides/fhir/server/09-data_interaction.md) - Methods for interacting with the FHIR server
- [FHIR Query Guide](docs/guides/fhir/api/fhir_query_guide.md) - Comprehensive guide on building effective FHIR queries
- [OMOP Database Documentation](docs/guides/omop/database/overview.md) - OMOP CDM database configuration overview
  - [PostgreSQL Setup](docs/guides/omop/database/postgresql_setup.md) - Detailed PostgreSQL setup for production
  - [SQLite Setup](docs/guides/omop/database/sqlite_setup.md) - Lightweight SQLite setup for development
- [Mapping Documentation](docs/mappings/) - Details on code system mappings
- [Research Analysis](docs/research/) - Analysis of existing FHIR to OMOP approaches

## Development

This is an internal project of AIO (Artificial Intelligence Orchestrator). For development guidelines, please refer to the [Contributing Guidelines](CONTRIBUTING.md) and [Coding Standards](docs/guides/development/standards.md).

### Development Methodology

This project follows a pedagogical, academic methodology with:

- Detailed step-by-step explanations for technical implementations
- Reference to official OMOP CDM v5.4.2 and HAPI FHIR R4 documentation
- Validation against official OHDSI protocols
- Context7 MCP integration for accessing official documentation

### Developer Onboarding

- [Visualization Components Guide](demo/demo_streamlit_app/docs/VISUALIZATION_COMPONENTS_GUIDE.md) - Guide for developers working on the visualization module

### Running Tests

For detailed testing instructions, run the tests with the following command:

```bash
# From project root directory
conda activate fhir-omop

# Run OMOP database tests
python -m pytest tests/test_omop_database/ -v

# Run all tests (if more test directories exist)
python -m pytest -v

# Run tests with coverage
pytest --cov=src/fhir_omop
```

## Roadmap

- [x] Initial project setup and documentation
- [x] FHIR server implementation with PostgreSQL database
- [x] Sample data loading functionality
- [x] OMOP CDM database setup with vocabulary loading
- [ ] FHIR-to-OMOP ETL pipeline implementation
- [ ] Support for key FHIR resources (Patient, Observation, Condition, etc.)
- [ ] Validation and quality assurance tools
- [ ] Performance optimization
- [ ] Comprehensive test suite
- [ ] CI/CD pipeline integration

## Proprietary Notice

This project is proprietary to AIO (Artificial Intelligence Orchestrator). All rights reserved. Unauthorized copying, distribution, or use is strictly prohibited.

## Acknowledgments

- OHDSI community for the OMOP CDM
- HL7 for the FHIR standard
- The research conducted on open-source projects that provided valuable insights
